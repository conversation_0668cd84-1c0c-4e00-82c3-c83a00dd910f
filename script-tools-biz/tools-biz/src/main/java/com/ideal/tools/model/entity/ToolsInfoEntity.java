package com.ideal.tools.model.entity;

import java.util.Date;
import java.util.Set;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.ideal.snowflake.annotion.IdGenerator;

/**
 * 工具箱信息对象 ieai_tb_tools_info
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
public class ToolsInfoEntity implements Serializable {
    private static final long serialVersionUID=1L;

    /** 主键ID */
    @IdGenerator
    private Long id;
    /** 工具编码 */
    private String code;
    /** 工具名称 */
    private String name;
    /** 工具类型（1 描述工具，2 组合工具，3 脚本工具……） */
    private Integer type;
    /** 工具分类（1.预案、2.查询诊断、3.变更处置、4.告警自愈） */
    private Integer classification;
    /** 工具脚本名称 */
    private String scriptName;
    /** 工具脚本id */
    private String scriptIds;
    /** 工具脚本操作用户 */
    private String scriptOperatingUser;
    /** 脚本类型(1 shell 2 shell 3 peri 4 python 5 sql 6  powershell) */
    private Integer scriptType;
    /** 工具作用 */
    private String effect;
    /** 使用场景 */
    private String usageScenario;
    /** 脚本编辑 */
    private String scriptEditing;
    /** 预估运行风险（1  导致CPU升高2 内存溢出3 生产大文件 4 应用中断 5 其他） */
    private String estimateOperationalRisk;
    /** 工具状态（0 草稿 1 已修改 2、审核中3、待启动4、运行中） */
    private Integer status;
    /** 工具关键字 */
    private String keyword;
    /** 业务系统id */
    private Long businessSystemId;
    /** 业务系统名称 */
    private String businessSystemName;
    /** 业务系统 Code */
    private String businessSystemCode;
    /** 适配ip */
    private String matchIp;
    /** 一线工具描述 */
    private String firstDescribe;
    /** 描述 */
    private String describe;
    /** 操作系统类型(1、windows2、linux3、hpux4、aix) */
    private Integer osType;
    /** 一级分类id */
    private Long oneTypeId;
    /** 二级分类id */
    private Long twoTypeId;
    /** 三级分类id */
    private Long threeTypeId;
    /** 是否高危 - 0:否 1:是：选择“是”后，页面响应式显示【高危命令】、【预估运行风险】两个字段） */
    private Integer highRisk;
    /** 子id */
    private String childIds;
    /** 交付状态 0、未交付 1、待接收 2、已退回 3、已交付 */
    private Integer deliveryStatus;
    /** 交付接收人 */
    private Long deliveryAuditorId;
    /** 交付回退原因 */
    private String deliveryReturnCause;
    /** 交付接收时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date deliveryReceptionTime;
    /** 更新人ID */
    private Long updatorId;
    /** 更新人 */
    private String updatorName;
    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /** 创建人ID */
    private Long creatorId;
    /** 创建人 */
    private String creatorName;
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /** 交付接收时间 开始*/
    private String deliveryReceptionTimeStart;
    /** 交付接收时间 结束*/
    private String deliveryReceptionTimeEnd;
	    /** 高危命令 */
    private String dangerCmd;
    /** 逻辑删除标记（0：显示；1：隐藏） */
    private Integer delFlag;
    /** 审核状态（0 待审核；1：隐藏） */
    private Integer approvalState;
    /** 包含业务系统id */
    private Set<Long> businessSystemIds;

    /**
     * 退回原因
     */
    private String returnReason;
    /**
     * 不查询此名称
     */
    private String notName;
    public Set<Long> getBusinessSystemIds() {
		return businessSystemIds;
	}

	public void setBusinessSystemIds(Set<Long> businessSystemIds) {
		this.businessSystemIds = businessSystemIds;
	}

	public void setId(Long id){
        this.id = id;
    }

    public Long getId(){
        return id;
    }

    public void setCode(String code){
        this.code = code;
    }

    public String getCode(){
        return code;
    }

    public void setName(String name){
        this.name = name;
    }

    public String getName(){
        return name;
    }

    public void setType(Integer type){
        this.type = type;
    }

    public Integer getType(){
        return type;
    }

    public void setClassification(Integer classification){
        this.classification = classification;
    }

    public Integer getClassification(){
        return classification;
    }

    public void setScriptName(String scriptName){
        this.scriptName = scriptName;
    }

    public String getScriptName(){
        return scriptName;
    }

    public void setScriptIds(String scriptIds){
        this.scriptIds = scriptIds;
    }

    public String getScriptIds(){
        return scriptIds;
    }

    public void setScriptOperatingUser(String scriptOperatingUser){
        this.scriptOperatingUser = scriptOperatingUser;
    }

    public String getScriptOperatingUser(){
        return scriptOperatingUser;
    }

    public void setScriptType(Integer scriptType){
        this.scriptType = scriptType;
    }

    public Integer getScriptType(){
        return scriptType;
    }

    public void setEffect(String effect){
        this.effect = effect;
    }

    public String getEffect(){
        return effect;
    }

    public void setUsageScenario(String usageScenario){
        this.usageScenario = usageScenario;
    }

    public String getUsageScenario(){
        return usageScenario;
    }

    public String getScriptEditing() {
        return scriptEditing;
    }

    public void setScriptEditing(String scriptEditing) {
        this.scriptEditing = scriptEditing;
    }

    public void setEstimateOperationalRisk(String estimateOperationalRisk){
        this.estimateOperationalRisk = estimateOperationalRisk;
    }

    public String getEstimateOperationalRisk(){
        return estimateOperationalRisk;
    }

    public void setStatus(Integer status){
        this.status = status;
    }

    public Integer getStatus(){
        return status;
    }

    public void setKeyword(String keyword){
        this.keyword = keyword;
    }

    public String getKeyword(){
        return keyword;
    }

    public void setBusinessSystemId(Long businessSystemId){
        this.businessSystemId = businessSystemId;
    }

    public Long getBusinessSystemId(){
        return businessSystemId;
    }

    public void setBusinessSystemName(String businessSystemName){
        this.businessSystemName = businessSystemName;
    }

    public String getBusinessSystemName(){
        return businessSystemName;
    }

    public String getBusinessSystemCode() {
        return businessSystemCode;
    }

    public void setBusinessSystemCode(String businessSystemCode) {
        this.businessSystemCode = businessSystemCode;
    }

    public void setMatchIp(String matchIp){
        this.matchIp = matchIp;
    }

    public String getMatchIp(){
        return matchIp;
    }

    public void setFirstDescribe(String firstDescribe){
        this.firstDescribe = firstDescribe;
    }

    public String getFirstDescribe(){
        return firstDescribe;
    }

    public void setDescribe(String describe){
        this.describe = describe;
    }

    public String getDescribe(){
        return describe;
    }

    public void setOsType(Integer osType){
        this.osType = osType;
    }

    public Integer getOsType(){
        return osType;
    }

    public void setOneTypeId(Long oneTypeId){
        this.oneTypeId = oneTypeId;
    }

    public Long getOneTypeId(){
        return oneTypeId;
    }

    public void setTwoTypeId(Long twoTypeId){
        this.twoTypeId = twoTypeId;
    }

    public Long getTwoTypeId(){
        return twoTypeId;
    }

    public void setThreeTypeId(Long threeTypeId){
        this.threeTypeId = threeTypeId;
    }

    public Long getThreeTypeId(){
        return threeTypeId;
    }

    public void setHighRisk(Integer highRisk){
        this.highRisk = highRisk;
    }

    public Integer getHighRisk(){
        return highRisk;
    }

    public void setChildIds(String childIds){
        this.childIds = childIds;
    }

    public String getChildIds(){
        return childIds;
    }

    public void setDeliveryStatus(Integer deliveryStatus){
        this.deliveryStatus = deliveryStatus;
    }

    public Integer getDeliveryStatus(){
        return deliveryStatus;
    }

    public void setDeliveryAuditorId(Long deliveryAuditorId){
        this.deliveryAuditorId = deliveryAuditorId;
    }

    public Long getDeliveryAuditorId(){
        return deliveryAuditorId;
    }

    public void setDeliveryReturnCause(String deliveryReturnCause){
        this.deliveryReturnCause = deliveryReturnCause;
    }

    public String getDeliveryReturnCause(){
        return deliveryReturnCause;
    }

    public void setDeliveryReceptionTime(Date deliveryReceptionTime){
        this.deliveryReceptionTime = deliveryReceptionTime;
    }

    public Date getDeliveryReceptionTime(){
        return deliveryReceptionTime;
    }

    public void setUpdatorId(Long updatorId){
        this.updatorId = updatorId;
    }

    public Long getUpdatorId(){
        return updatorId;
    }

    public void setUpdatorName(String updatorName){
        this.updatorName = updatorName;
    }

    public String getUpdatorName(){
        return updatorName;
    }

    public void setUpdateTime(Date updateTime){
        this.updateTime = updateTime;
    }

    public Date getUpdateTime(){
        return updateTime;
    }

    public void setCreatorId(Long creatorId){
        this.creatorId = creatorId;
    }

    public Long getCreatorId(){
        return creatorId;
    }

    public void setCreatorName(String creatorName){
        this.creatorName = creatorName;
    }

    public String getCreatorName(){
        return creatorName;
    }

    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    public Date getCreateTime(){
        return createTime;
    }

    public String getDeliveryReceptionTimeStart() {
        return deliveryReceptionTimeStart;
    }

    public void setDeliveryReceptionTimeStart(String deliveryReceptionTimeStart) {
        this.deliveryReceptionTimeStart = deliveryReceptionTimeStart;
    }

    public String getDeliveryReceptionTimeEnd() {
        return deliveryReceptionTimeEnd;
    }

    public void setDeliveryReceptionTimeEnd(String deliveryReceptionTimeEnd) {
        this.deliveryReceptionTimeEnd = deliveryReceptionTimeEnd;
    }
	
	
	
	public void setDangerCmd(String dangerCmd){
        this.dangerCmd = dangerCmd;
    }

    public String getDangerCmd(){
        return dangerCmd;
    }

    public void setDelFlag(Integer delFlag){
        this.delFlag = delFlag;
    }

    public Integer getDelFlag(){
        return delFlag;
    }

    public Integer getApprovalState() {
        return approvalState;
    }

    public void setApprovalState(Integer approvalState) {
        this.approvalState = approvalState;
    }

    public String getReturnReason() {
        return returnReason;
    }

    public void setReturnReason(String returnReason) {
        this.returnReason = returnReason;
    }

    public String getNotName() {
		return notName;
	}

	public void setNotName(String notName) {
		this.notName = notName;
	}

	@Override
    public String toString(){
        return getClass().getSimpleName()+
                " ["+
                "Hash = "+hashCode()+
                    ",id="+getId()+
                    ",code="+getCode()+
                    ",name="+getName()+
                    ",type="+getType()+
                    ",classification="+getClassification()+
                    ",scriptName="+getScriptName()+
                    ",scriptIds="+getScriptIds()+
                    ",scriptOperatingUser="+getScriptOperatingUser()+
                    ",scriptType="+getScriptType()+
                    ",effect="+getEffect()+
                    ",usageScenario="+getUsageScenario()+
                    ",scriptEditing="+getScriptEditing()+
                    ",estimateOperationalRisk="+getEstimateOperationalRisk()+
                    ",status="+getStatus()+
                    ",keyword="+getKeyword()+
                    ",businessSystemId="+getBusinessSystemId()+
                    ",businessSystemName="+getBusinessSystemName()+
                    ",businessSystemCode="+getBusinessSystemCode()+
                    ",matchIp="+getMatchIp()+
                    ",firstDescribe="+getFirstDescribe()+
                    ",describe="+getDescribe()+
                    ",osType="+getOsType()+
                    ",oneTypeId="+getOneTypeId()+
                    ",twoTypeId="+getTwoTypeId()+
                    ",threeTypeId="+getThreeTypeId()+
                    ",highRisk="+getHighRisk()+
                    ",childIds="+getChildIds()+
                    ",deliveryStatus="+getDeliveryStatus()+
                    ",deliveryAuditorId="+getDeliveryAuditorId()+
                    ",deliveryReturnCause="+getDeliveryReturnCause()+
                    ",deliveryReceptionTime="+getDeliveryReceptionTime()+
                    ",updatorId="+getUpdatorId()+
                    ",updatorName="+getUpdatorName()+
                    ",updateTime="+getUpdateTime()+
                    ",creatorId="+getCreatorId()+
                    ",creatorName="+getCreatorName()+
                    ",createTime="+getCreateTime()+
                    ",deliveryReceptionTimeStart="+getDeliveryReceptionTimeStart()+
                    ",deliveryReceptionTimeEnd="+getDeliveryReceptionTimeEnd()+
                    ",dangerCmd="+getDangerCmd()+
                    ",delFlag="+getDelFlag()+
                    ",approvalState="+getApprovalState()+
                    ",returnReason="+getReturnReason()+
                "]";
    }
}

