# TaskIpsServiceImpl.buildTaskIpsList 方法优化设计

## 当前问题分析

### 性能瓶颈
- **N+1查询问题**：在for循环中逐个调用`agentInfoService.checkAgentInfoExists()`
- **单次插入**：每个不存在的agent都单独调用`agentInfoService.insertAgentInfo()`
- **重复查询**：存在的agent需要再次调用`selectAgentInfoByIpAndPort()`获取ID

### 业务逻辑流程
1. 遍历agentUsers列表
2. 检查每个agent是否存在于数据库
3. 如果不存在：插入新记录
4. 如果存在：查询获取ID并设置到agentInfoDto
5. 构建TaskIps对象并添加到结果列表

## 优化设计方案

### 1. 分批处理策略

```java
// 使用项目标准的批量大小，避免Oracle IN子句1000参数限制
private static final int BATCH_SIZE = 1000;

// 分批处理逻辑
for (int i = 0; i < agentUsers.size(); i += BATCH_SIZE) {
    int endIndex = Math.min(i + BATCH_SIZE, agentUsers.size());
    List<AgentInfoDto> batchAgents = agentUsers.subList(i, endIndex);
    processBatch(batchAgents, scriptExecAuditDto, taskInfo, taskIpsList);
}
```

### 2. 批量查询逻辑

```java
// 批量查询已存在的agent
List<AgentInfo> existingAgents = agentInfoService.selectAgentInfoByIpAndPort(batchAgents);

// 使用Map进行快速查找，key为"IP:Port"格式
Map<String, AgentInfo> existingAgentMap = existingAgents.stream()
    .collect(Collectors.toMap(
        agent -> agent.getAgentIp() + ":" + agent.getAgentPort(),
        agent -> agent
    ));
```

### 3. 数据比较和过滤机制

```java
// 找出需要插入的agent（不存在的agent）
List<AgentInfoDto> agentsToInsert = batchAgents.stream()
    .filter(agent -> !existingAgentMap.containsKey(
        agent.getAgentIp() + ":" + agent.getAgentPort()))
    .collect(Collectors.toList());
```

### 4. 批量插入策略

```java
// 批量插入不存在的agent
if (!agentsToInsert.isEmpty()) {
    // 重置ID属性，以IDGenerate注解为准
    agentsToInsert.forEach(agent -> agent.setId(null));
    
    // 使用Batch接口的batchData方法进行批量插入
    this.batchData(agentsToInsert, agentInfoService::insertAgentInfo);
}
```

### 5. TaskIps对象构建

```java
// 为每个agent构建TaskIps对象
for (AgentInfoDto agentInfoDto : batchAgents) {
    String key = agentInfoDto.getAgentIp() + ":" + agentInfoDto.getAgentPort();
    AgentInfo existingAgent = existingAgentMap.get(key);
    
    if (existingAgent != null) {
        // 如果agent已存在，设置其ID
        agentInfoDto.setId(existingAgent.getId());
    }
    // 如果是新插入的agent，ID已在insertAgentInfo中设置
    
    TaskIps taskIps = getTaskIps(scriptExecAuditDto, taskInfo, agentInfoDto);
    taskIpsList.add(taskIps);
}
```

## 性能优化效果

### 数据库查询次数对比
- **优化前**：O(n) 次查询（每个agent一次checkExists + 可能的一次selectByIpAndPort）
- **优化后**：O(n/1000) 次查询（每1000个agent一次批量查询）

### 插入操作优化
- **优化前**：每个不存在的agent单独插入
- **优化后**：批量插入，减少数据库连接开销

## 技术实现要点

### 1. Java 8 Stream API使用
- 使用`stream().filter()`进行数据过滤
- 使用`stream().collect(Collectors.toMap())`构建查找Map
- 使用`forEach()`进行批量操作

### 2. 异常处理
- 保持与原有代码一致的异常处理机制
- 在批量操作中正确传播ScriptException

### 3. 日志记录
- 保持原有的debug日志记录
- 可考虑添加批量处理的性能日志

### 4. 内存优化
- 使用subList避免创建新的ArrayList
- 及时清理临时Map和List对象

## 兼容性保证

### 1. 业务逻辑一致性
- 保持原有的agent存在性检查逻辑
- 保持原有的ID设置机制
- 保持原有的TaskIps构建逻辑

### 2. 异常处理一致性
- 保持原有的ScriptException抛出机制
- 保持原有的错误日志记录

### 3. 数据一致性
- 确保批量插入后的ID正确设置
- 确保TaskIps对象的数据完整性

## Oracle数据库兼容性

### IN子句限制处理
- 使用1000作为批量大小，符合Oracle限制
- 分批处理确保不会超过参数限制
- 使用OR条件组合而非IN子句（XML映射已实现）

### 事务处理
- 批量操作在同一事务中执行
- 确保数据一致性和回滚能力
