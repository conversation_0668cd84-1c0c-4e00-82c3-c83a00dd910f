package com.ideal.script.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.ideal.message.center.IPublisher;
import com.ideal.message.center.exception.CommunicationException;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.config.ScriptBusinessConfig;
import com.ideal.script.model.bean.TaskHandleParam;
import com.ideal.script.model.dto.TaskInstanceDto;
import com.ideal.script.model.dto.TaskRuntimeDto;
import com.ideal.script.service.ITaskInstanceService;
import com.ideal.script.service.impl.builders.MyScriptServiceScripts;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.stream.Stream;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * CiticExecTaskBusinessImpl类的单元测试
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class CiticExecTaskBusinessImplTest {

    @Mock
    private ITaskInstanceService taskInstanceService;

    @Mock
    private MyScriptServiceScripts myScriptServiceScripts;

    @Mock
    private ScriptBusinessConfig scriptBusinessConfig;

    @Mock
    private IPublisher iPublisher;

    @InjectMocks
    private CiticExecTaskBusinessImpl citicExecTaskBusinessImpl;

    private TaskRuntimeDto taskRuntimeDto;
    private TaskHandleParam taskHandleParam;
    private TaskInstanceDto taskInstanceDto;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setId(1L);
        taskRuntimeDto.setAgentIp("*************");
        taskRuntimeDto.setAgentPort(8080);

        taskHandleParam = new TaskHandleParam();
        taskHandleParam.setIstdout("标准输出内容");
        taskHandleParam.setIstderror("标准错误输出内容");
        taskHandleParam.setIlastline("最后一行输出");

        taskInstanceDto = new TaskInstanceDto();
        taskInstanceDto.setCallerTaskId(12345L);
    }

    /**
     * 提供参数化测试数据
     */
    static Stream<Arguments> monitorMqToSendTestParams() {
        return Stream.of(
            // 开关关闭的情况
            Arguments.of("开关关闭", false, 12345L, Constants.CRONTABS_SEND_RESULT_DEV, false, false),

            // 开关开启但callerTaskId为null的情况
            Arguments.of("开关开启但callerTaskId为null", true, null, Constants.CRONTABS_SEND_RESULT_DEV, true, false),

            // 开关开启且callerTaskId不为null - CRONTABS_SEND_RESULT_DEV
            Arguments.of("CRONTABS_SEND_RESULT_DEV场景", true, 12345L, Constants.CRONTABS_SEND_RESULT_DEV, true, true),

            // 开关开启且callerTaskId不为null - CRONTABS_EXECUTE_RESULT_DEV
            Arguments.of("CRONTABS_EXECUTE_RESULT_DEV场景", true, 12345L, Constants.CRONTABS_EXECUTE_RESULT_DEV, true, true),

            // 开关开启且callerTaskId不为null - CRONTABS_ERROR_RESULT_DEV
            Arguments.of("CRONTABS_ERROR_RESULT_DEV场景", true, 12345L, Constants.CRONTABS_ERROR_RESULT_DEV, true, true),

            // 开关开启且callerTaskId不为null - 其他typeFalg
            Arguments.of("其他typeFalg场景", true, 12345L, "OTHER_TYPE", true, false)
        );
    }

    @ParameterizedTest
    @MethodSource("monitorMqToSendTestParams")
    @DisplayName("测试monitorMqToSend方法的各种场景")
    void monitorMqToSend_test(String scenarioName, boolean monitorFlag, Long callerTaskId,
                             String typeFalg, boolean shouldCallService, boolean shouldCallPublisher) {
        // 设置mock行为
        when(myScriptServiceScripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);
        when(scriptBusinessConfig.isMonitorMessageSendMqFlag()).thenReturn(monitorFlag);

        if (monitorFlag) {
            TaskInstanceDto mockTaskInstance = new TaskInstanceDto();
            mockTaskInstance.setCallerTaskId(callerTaskId);
            when(taskInstanceService.getTaskInstanceByRuntimeId(taskRuntimeDto.getId())).thenReturn(mockTaskInstance);

            if (callerTaskId != null) {
                when(myScriptServiceScripts.getiPublisher()).thenReturn(iPublisher);
            }
        }

        // 执行测试方法
        citicExecTaskBusinessImpl.monitorMqToSend(typeFalg, taskHandleParam, taskRuntimeDto);

        // 验证方法调用
        verify(myScriptServiceScripts).getScriptBusinessConfig();
        verify(scriptBusinessConfig).isMonitorMessageSendMqFlag();

        if (shouldCallService) {
            verify(taskInstanceService).getTaskInstanceByRuntimeId(taskRuntimeDto.getId());
        } else {
            verify(taskInstanceService, never()).getTaskInstanceByRuntimeId(any());
        }

        if (shouldCallPublisher) {
            verify(myScriptServiceScripts).getiPublisher();
            try {
                verify(iPublisher).apply(eq(typeFalg), anyString());
            } catch (CommunicationException e) {
                // 不应该到这里
            }
        } else {
            verify(myScriptServiceScripts, never()).getiPublisher();
        }
    }

    @Test
    @DisplayName("测试monitorMqToSend方法异常处理")
    void monitorMqToSend_ExceptionHandling_test() throws CommunicationException {
        // 设置mock行为 - 开关开启
        when(myScriptServiceScripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);
        when(scriptBusinessConfig.isMonitorMessageSendMqFlag()).thenReturn(true);

        // 设置taskInstanceService返回有效的callerTaskId
        TaskInstanceDto mockTaskInstance = new TaskInstanceDto();
        mockTaskInstance.setCallerTaskId(12345L);
        when(taskInstanceService.getTaskInstanceByRuntimeId(taskRuntimeDto.getId())).thenReturn(mockTaskInstance);

        // 设置iPublisher抛出异常
        when(myScriptServiceScripts.getiPublisher()).thenReturn(iPublisher);
        doThrow(new CommunicationException("MQ发送失败")).when(iPublisher).apply(anyString(), anyString());

        // 执行测试方法 - 不应该抛出异常，应该被catch住
        citicExecTaskBusinessImpl.monitorMqToSend(Constants.CRONTABS_SEND_RESULT_DEV, taskHandleParam, taskRuntimeDto);

        // 验证方法调用
        verify(myScriptServiceScripts).getScriptBusinessConfig();
        verify(scriptBusinessConfig).isMonitorMessageSendMqFlag();
        verify(taskInstanceService).getTaskInstanceByRuntimeId(taskRuntimeDto.getId());
        verify(myScriptServiceScripts).getiPublisher();
        verify(iPublisher).apply(eq(Constants.CRONTABS_SEND_RESULT_DEV), anyString());
    }

    @Test
    @DisplayName("测试CRONTABS_SEND_RESULT_DEV场景的JSON内容")
    void monitorMqToSend_CRONTABS_SEND_RESULT_DEV_JsonContent_test() throws CommunicationException {
        // 设置mock行为
        when(myScriptServiceScripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);
        when(scriptBusinessConfig.isMonitorMessageSendMqFlag()).thenReturn(true);

        TaskInstanceDto mockTaskInstance = new TaskInstanceDto();
        mockTaskInstance.setCallerTaskId(12345L);
        when(taskInstanceService.getTaskInstanceByRuntimeId(taskRuntimeDto.getId())).thenReturn(mockTaskInstance);
        when(myScriptServiceScripts.getiPublisher()).thenReturn(iPublisher);

        // 执行测试方法
        citicExecTaskBusinessImpl.monitorMqToSend(Constants.CRONTABS_SEND_RESULT_DEV, taskHandleParam, taskRuntimeDto);

        // 验证JSON内容
        verify(iPublisher).apply(eq(Constants.CRONTABS_SEND_RESULT_DEV), argThat(jsonString -> {
            JSONObject json = JSONObject.parseObject((String) jsonString);
            return json.getLong("runtimeId").equals(12345L) &&
                   json.getString("agentIp").equals("*************") &&
                   json.getInteger("agentPort").equals(8080);
        }));
    }

    @Test
    @DisplayName("测试CRONTABS_EXECUTE_RESULT_DEV场景的JSON内容")
    void monitorMqToSend_CRONTABS_EXECUTE_RESULT_DEV_JsonContent_test() throws CommunicationException {
        // 设置mock行为
        when(myScriptServiceScripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);
        when(scriptBusinessConfig.isMonitorMessageSendMqFlag()).thenReturn(true);

        TaskInstanceDto mockTaskInstance = new TaskInstanceDto();
        mockTaskInstance.setCallerTaskId(12345L);
        when(taskInstanceService.getTaskInstanceByRuntimeId(taskRuntimeDto.getId())).thenReturn(mockTaskInstance);
        when(myScriptServiceScripts.getiPublisher()).thenReturn(iPublisher);

        // 执行测试方法
        citicExecTaskBusinessImpl.monitorMqToSend(Constants.CRONTABS_EXECUTE_RESULT_DEV, taskHandleParam, taskRuntimeDto);

        // 验证JSON内容
        verify(iPublisher).apply(eq(Constants.CRONTABS_EXECUTE_RESULT_DEV), argThat(jsonString -> {
            JSONObject json = JSONObject.parseObject((String) jsonString);
            return json.getLong("runtimeId").equals(12345L) &&
                   json.getString("agentIp").equals("*************") &&
                   json.getInteger("agentPort").equals(8080) &&
                   json.getString("ret").equals("1") &&
                   json.getString("stdout").equals("标准输出内容") &&
                   json.getString("stderr").equals("标准错误输出内容") &&
                   json.getString("lastLine").equals("最后一行输出") &&
                   json.getBoolean("isTimeout").equals(false);
        }));
    }

    @Test
    @DisplayName("测试CRONTABS_ERROR_RESULT_DEV场景的JSON内容")
    void monitorMqToSend_CRONTABS_ERROR_RESULT_DEV_JsonContent_test() throws CommunicationException {
        // 设置mock行为
        when(myScriptServiceScripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);
        when(scriptBusinessConfig.isMonitorMessageSendMqFlag()).thenReturn(true);

        TaskInstanceDto mockTaskInstance = new TaskInstanceDto();
        mockTaskInstance.setCallerTaskId(12345L);
        when(taskInstanceService.getTaskInstanceByRuntimeId(taskRuntimeDto.getId())).thenReturn(mockTaskInstance);
        when(myScriptServiceScripts.getiPublisher()).thenReturn(iPublisher);

        // 执行测试方法
        citicExecTaskBusinessImpl.monitorMqToSend(Constants.CRONTABS_ERROR_RESULT_DEV, taskHandleParam, taskRuntimeDto);

        // 验证JSON内容
        verify(iPublisher).apply(eq(Constants.CRONTABS_ERROR_RESULT_DEV), argThat(jsonString -> {
            JSONObject json = JSONObject.parseObject((String) jsonString);
            return json.getLong("runtimeId").equals(12345L) &&
                   json.getString("agentIp").equals("*************") &&
                   json.getInteger("agentPort").equals(8080);
        }));
    }
}
