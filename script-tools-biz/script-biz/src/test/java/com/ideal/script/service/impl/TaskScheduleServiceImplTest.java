package com.ideal.script.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ideal.script.mapper.TaskScheduleMapper;
import com.ideal.script.model.dto.TaskScheduleDto;
import com.ideal.script.model.dto.TaskScheduleQueryDto;
import com.ideal.script.model.entity.TaskScheduleEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Timestamp;
import java.time.LocalDateTime;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TaskScheduleServiceImplTest {

    @Mock
    private TaskScheduleMapper mockTaskScheduleMapper;

    private TaskScheduleServiceImpl taskScheduleServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        taskScheduleServiceImplUnderTest = new TaskScheduleServiceImpl(mockTaskScheduleMapper);
    }

    @Test
    void testSelectTaskScheduleById() {
        // Setup
        // Configure TaskScheduleMapper.selectTaskScheduleById(...).
        final TaskScheduleEntity taskScheduleEntity = new TaskScheduleEntity();
        taskScheduleEntity.setId(0L);
        taskScheduleEntity.setScriptTaskId(0L);
        taskScheduleEntity.setScheduleId(0L);
        taskScheduleEntity.setState(0);
        taskScheduleEntity.setCreateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockTaskScheduleMapper.selectTaskScheduleById(0L)).thenReturn(taskScheduleEntity);

        // Run the test
        final TaskScheduleDto result = taskScheduleServiceImplUnderTest.selectTaskScheduleById(0L);
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testSelectTaskScheduleList() {
        // Setup
        final TaskScheduleQueryDto taskScheduleQueryDto = new TaskScheduleQueryDto();
        taskScheduleQueryDto.setId(0L);
        taskScheduleQueryDto.setScriptTaskId(0L);
        taskScheduleQueryDto.setScheduleId(0L);
        taskScheduleQueryDto.setState(0);
        taskScheduleQueryDto.setCreateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));

        // Configure TaskScheduleMapper.selectTaskScheduleList(...).
        final TaskScheduleEntity taskScheduleEntity = new TaskScheduleEntity();
        taskScheduleEntity.setId(0L);
        taskScheduleEntity.setScriptTaskId(0L);
        taskScheduleEntity.setScheduleId(0L);
        taskScheduleEntity.setState(0);
        taskScheduleEntity.setCreateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        Page<TaskScheduleEntity> page = new Page<>();
        page.add(taskScheduleEntity);
        when(mockTaskScheduleMapper.selectTaskScheduleList(any(TaskScheduleEntity.class)))
                .thenReturn(page);

        // Run the test
        final PageInfo<TaskScheduleDto> result = taskScheduleServiceImplUnderTest.selectTaskScheduleList(
                taskScheduleQueryDto, 0, 0);
        assertNotNull(result);
        // Verify the results
    }


    @Test
    void testInsertTaskSchedule() {
        // Setup
        final TaskScheduleDto taskScheduleDto = new TaskScheduleDto();
        taskScheduleDto.setId(0L);
        taskScheduleDto.setScriptTaskId(0L);
        taskScheduleDto.setScheduleId(0L);
        taskScheduleDto.setState(0);
        taskScheduleDto.setCreateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));

        when(mockTaskScheduleMapper.insertTaskSchedule(any(TaskScheduleEntity.class))).thenReturn(0);

        // Run the test
        final int result = taskScheduleServiceImplUnderTest.insertTaskSchedule(taskScheduleDto);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testUpdateTaskSchedule() {
        // Setup
        final TaskScheduleDto taskScheduleDto = new TaskScheduleDto();
        taskScheduleDto.setId(0L);
        taskScheduleDto.setScriptTaskId(0L);
        taskScheduleDto.setScheduleId(0L);
        taskScheduleDto.setState(0);
        taskScheduleDto.setCreateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));

        when(mockTaskScheduleMapper.updateTaskSchedule(any(TaskScheduleEntity.class))).thenReturn(0);

        // Run the test
        final int result = taskScheduleServiceImplUnderTest.updateTaskSchedule(taskScheduleDto);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testUpdateTaskScheduleByScheduleId() {
        // Setup
        final TaskScheduleDto taskScheduleDto = new TaskScheduleDto();
        taskScheduleDto.setId(0L);
        taskScheduleDto.setScriptTaskId(0L);
        taskScheduleDto.setScheduleId(0L);
        taskScheduleDto.setState(0);
        taskScheduleDto.setCreateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));

        when(mockTaskScheduleMapper.updateTaskScheduleByScheduleId(any(TaskScheduleEntity.class))).thenReturn(0);

        // Run the test
        final int result = taskScheduleServiceImplUnderTest.updateTaskScheduleByScheduleId(taskScheduleDto);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testDeleteTaskScheduleByIds() {
        // Setup
        when(mockTaskScheduleMapper.deleteTaskScheduleByIds(any(Long[].class))).thenReturn(0);

        // Run the test
        final int result = taskScheduleServiceImplUnderTest.deleteTaskScheduleByIds(new Long[]{0L});

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void selectTaskScheduleByTaskId() {

        final TaskScheduleDto result = taskScheduleServiceImplUnderTest.selectTaskScheduleByTaskId(0L);
        assertNotNull(result);
    }
}
