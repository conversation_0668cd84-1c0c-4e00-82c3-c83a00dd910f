package com.ideal.script.controller;

import com.ideal.common.dto.R;
import com.ideal.common.util.spring.MessageUtil;
import com.ideal.common.util.spring.SpringUtil;
import com.ideal.sc.util.ValidationUtils;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.ScriptDeleteDto;
import com.ideal.script.model.dto.TaskAuthorityDto;
import com.ideal.script.service.IMyScriptService;
import com.ideal.script.service.ITaskAuthorityService;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TaskAuthorityControllerTest {

    @Mock
    private ITaskAuthorityService mockTaskAuthorityService;
    @Mock
    private IMyScriptService mockScriptService;

    private TaskAuthorityController taskAuthorityControllerUnderTest;
    
    private static MockedStatic<MessageUtil> messageUtilMock;
    private static MockedStatic<SpringUtil> springUtilMock;
    private static MockedStatic<ValidationUtils> validationUtilsMock;

    @BeforeAll
    static void setUp() {
        // Mock MessageUtil静态方法
        messageUtilMock = Mockito.mockStatic(MessageUtil.class);
        messageUtilMock.when(() -> MessageUtil.message(anyString())).thenReturn("Mocked Message");
        
        // Mock SpringUtil静态方法
        springUtilMock = Mockito.mockStatic(SpringUtil.class);
        springUtilMock.when(() -> SpringUtil.getBean(any(Class.class))).thenReturn(new Object());
        
        // Mock ValidationUtils静态方法，但不拦截其真实实现
        validationUtilsMock = Mockito.mockStatic(ValidationUtils.class);
        validationUtilsMock.when(() -> ValidationUtils.customFailResult(anyString(), anyString()))
                .thenAnswer(invocation -> R.fail(ValidationUtils.RESPONSE_VALIDATE_CODE, 
                            invocation.getArgument(0), invocation.getArgument(1)));
    }
    
    @AfterAll
    static void tearDown() {
        messageUtilMock.close();
        springUtilMock.close();
        validationUtilsMock.close();
    }

    @BeforeEach
    void setUpEach() {
        taskAuthorityControllerUnderTest = new TaskAuthorityController(mockTaskAuthorityService, mockScriptService);
    }



    @Test
    @DisplayName("测试更新使用状态")
    void testUpdateUseState() throws Exception {
        // Setup
        final TaskAuthorityDto taskAuthorityDto = new TaskAuthorityDto();
        taskAuthorityDto.setScriptInfoId(0L);
        taskAuthorityDto.setUniqueUuid("uniqueUuid");
        taskAuthorityDto.setScriptNameZh("scriptNameZh");
        taskAuthorityDto.setScriptName("scriptName");
        taskAuthorityDto.setScriptType("scriptType");

        when(mockTaskAuthorityService.updateUseState(any(TaskAuthorityDto.class))).thenReturn(false);

        // Run the test
        final R<Object> result = taskAuthorityControllerUnderTest.updateUseState(taskAuthorityDto);

        // Verify the results
    }

    @Test
    @DisplayName("测试更新使用状态 - 抛出异常")
    void testUpdateUseState_ITaskAuthorityServiceThrowsScriptException() throws Exception {
        // Setup
        final TaskAuthorityDto taskAuthorityDto = new TaskAuthorityDto();
        taskAuthorityDto.setScriptInfoId(0L);
        taskAuthorityDto.setUniqueUuid("uniqueUuid");
        taskAuthorityDto.setScriptNameZh("scriptNameZh");
        taskAuthorityDto.setScriptName("scriptName");
        taskAuthorityDto.setScriptType("scriptType");

        when(mockTaskAuthorityService.updateUseState(any(TaskAuthorityDto.class))).thenThrow(ScriptException.class);

        // Run the test
        final R<Object> result = taskAuthorityControllerUnderTest.updateUseState(taskAuthorityDto);

        // Verify the results
    }

    @Test
    @DisplayName("测试批量更新使用状态")
    void testBatchUpdateUseState() throws Exception {
        // Setup
        final TaskAuthorityDto taskAuthorityDto = new TaskAuthorityDto();
        taskAuthorityDto.setScriptInfoId(0L);
        taskAuthorityDto.setUniqueUuid("uniqueUuid");
        taskAuthorityDto.setScriptNameZh("scriptNameZh");
        taskAuthorityDto.setScriptName("scriptName");
        taskAuthorityDto.setScriptType("scriptType");

        when(mockTaskAuthorityService.batchUpdateUseState(any(TaskAuthorityDto.class))).thenReturn(false);

        // Run the test
        final R<Object> result = taskAuthorityControllerUnderTest.batchUpdateUseState(taskAuthorityDto);

        // Verify the results
    }

    @Test
    @DisplayName("测试批量更新使用状态 - 抛出异常")
    void testBatchUpdateUseState_ITaskAuthorityServiceThrowsScriptException() throws Exception {
        // Setup
        final TaskAuthorityDto taskAuthorityDto = new TaskAuthorityDto();
        taskAuthorityDto.setScriptInfoId(0L);
        taskAuthorityDto.setUniqueUuid("uniqueUuid");
        taskAuthorityDto.setScriptNameZh("scriptNameZh");
        taskAuthorityDto.setScriptName("scriptName");
        taskAuthorityDto.setScriptType("scriptType");

        when(mockTaskAuthorityService.batchUpdateUseState(any(TaskAuthorityDto.class)))
                .thenThrow(ScriptException.class);

        // Run the test
        final R<Object> result = taskAuthorityControllerUnderTest.batchUpdateUseState(taskAuthorityDto);

        // Verify the results
    }

    @Test
    @DisplayName("测试删除脚本")
    void testDeleteScript() throws Exception {
        // Setup
        final ScriptDeleteDto scriptDeleteDto = new ScriptDeleteDto();
        scriptDeleteDto.setIds(new Long[]{0L});
        scriptDeleteDto.setAuditorName("auditorName");
        scriptDeleteDto.setAuditorId(0L);
        scriptDeleteDto.setDeleteDesc("deleteDesc");

        // Run the test
        final R<Object> result = taskAuthorityControllerUnderTest.deleteScript(scriptDeleteDto);

        // Verify the results
        verify(mockScriptService).deleteMyScript(any(ScriptDeleteDto.class), eq(true));
    }

    @Test
    @DisplayName("测试删除脚本 - 抛出异常")
    void testDeleteScript_IMyScriptServiceThrowsScriptException() throws Exception {
        // Setup
        final ScriptDeleteDto scriptDeleteDto = new ScriptDeleteDto();
        scriptDeleteDto.setIds(new Long[]{0L});
        scriptDeleteDto.setAuditorName("auditorName");
        scriptDeleteDto.setAuditorId(0L);
        scriptDeleteDto.setDeleteDesc("deleteDesc");

        doThrow(ScriptException.class).when(mockScriptService).deleteMyScript(any(ScriptDeleteDto.class), eq(true));

        // Run the test
        final R<Object> result = taskAuthorityControllerUnderTest.deleteScript(scriptDeleteDto);

        // Verify the results
    }

    @Test
    @DisplayName("测试检查是否存在运行任务")
    void testCheckExistRunTask() throws Exception {
        // Setup
        when(mockTaskAuthorityService.checkExistRunTask(anyLong())).thenReturn(false);

        // Run the test
        final R<Object> result = taskAuthorityControllerUnderTest.checkExistRunTask(0L);

        // Verify the results
    }

    @Test
    @DisplayName("测试检查是否存在运行任务 - 抛出异常")
    void testCheckExistRunTask_ITaskAuthorityServiceThrowsScriptException() throws Exception {
        // Setup
        when(mockTaskAuthorityService.checkExistRunTask(anyLong())).thenThrow(ScriptException.class);

        // Run the test
        final R<Object> result = taskAuthorityControllerUnderTest.checkExistRunTask(0L);

        // Verify the results
    }
}
