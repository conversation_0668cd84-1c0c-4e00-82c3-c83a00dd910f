package com.ideal.script.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.AgentInfoDto;
import com.ideal.script.model.dto.IssuerecordDto;
import com.ideal.script.model.dto.IssuerecordQueryDto;
import com.ideal.script.service.IIssuerecordService;
import com.ideal.system.common.component.model.CurrentUser;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;

@ExtendWith(MockitoExtension.class)
class IssuerecordControllerTest {

    @Mock
    private IIssuerecordService mockIssuerecordService;

    private IssuerecordController issuerecordControllerUnderTest;

    @BeforeEach
    void setUp() {
        issuerecordControllerUnderTest = new IssuerecordController(mockIssuerecordService);
    }

    @Test
    @DisplayName("测试查询脚本下发列表成功")
    void testList() {
        // Setup
        final TableQueryDto<IssuerecordQueryDto> tableQueryDto = new TableQueryDto<>();
        tableQueryDto.setPageNum(0);
        tableQueryDto.setPageSize(0);
        final IssuerecordQueryDto issuerecordQueryDto = new IssuerecordQueryDto();
        issuerecordQueryDto.setBizId("bizId");
        issuerecordQueryDto.setBatchNumber("batchNumber");
        tableQueryDto.setQueryParam(issuerecordQueryDto);

        // Configure IIssuerecordService.selectIssuerecordList(...).
        final IssuerecordDto issuerecordDto = new IssuerecordDto();
        issuerecordDto.setScriptNameZh("scriptNameZh");
        issuerecordDto.setScriptName("scriptName");
        issuerecordDto.setBizId("bizId");
        issuerecordDto.setBatchNumber("batchNumber");
        issuerecordDto.setId(0L);
        final PageInfo<IssuerecordDto> issuerecordDtoPageInfo = new PageInfo<>(Arrays.asList(issuerecordDto), 0);
        doReturn(issuerecordDtoPageInfo).when(mockIssuerecordService).selectIssuerecordList(any(IssuerecordQueryDto.class), anyInt(), anyInt());

        // Run the test
        final R<PageInfo<IssuerecordDto>> result = issuerecordControllerUnderTest.list(tableQueryDto);

        // Verify the results
    }

    @Test
    @DisplayName("测试查询脚本下发列表返回空结果")
    void testList_IIssuerecordServiceReturnsNoItem() {
        // Setup
        final TableQueryDto<IssuerecordQueryDto> tableQueryDto = new TableQueryDto<>();
        tableQueryDto.setPageNum(0);
        tableQueryDto.setPageSize(0);
        final IssuerecordQueryDto issuerecordQueryDto = new IssuerecordQueryDto();
        issuerecordQueryDto.setBizId("bizId");
        issuerecordQueryDto.setBatchNumber("batchNumber");
        tableQueryDto.setQueryParam(issuerecordQueryDto);

        doReturn(PageInfo.emptyPageInfo()).when(mockIssuerecordService).selectIssuerecordList(any(IssuerecordQueryDto.class), anyInt(), anyInt());

        // Run the test
        final R<PageInfo<IssuerecordDto>> result = issuerecordControllerUnderTest.list(tableQueryDto);

        // Verify the results
    }

    @Test
    @DisplayName("测试发送脚本到代理成功")
    void testSendScriptToAgents() throws Exception {
        // Setup
        final IssuerecordQueryDto issuerecordQueryDto = new IssuerecordQueryDto();
        issuerecordQueryDto.setBizId("bizId");
        issuerecordQueryDto.setBatchNumber("batchNumber");
        issuerecordQueryDto.setSrcScriptUuids(new String[]{"srcScriptUuids"});
        final AgentInfoDto agentInfoDto = new AgentInfoDto();
        agentInfoDto.setCenterId(0L);
        issuerecordQueryDto.setAgentInfoDtoList(Arrays.asList(agentInfoDto));

        doReturn("result").when(mockIssuerecordService).sendScriptToAgents(any(IssuerecordQueryDto.class), any(CurrentUser.class));

        // Run the test
        final R<String> result = issuerecordControllerUnderTest.sendScriptToAgents(issuerecordQueryDto);

        // Verify the results
    }
}
