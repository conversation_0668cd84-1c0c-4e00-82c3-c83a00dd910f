package com.ideal.script.common.util;

import com.ideal.script.config.ScriptBusinessConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.util.unit.DataSize;

import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class FileSizeValidUtilTest {

    @Mock
    private ScriptBusinessConfig mockScriptBusinessConfig;

    private FileSizeValidUtil fileSizeValidUtilUnderTest;

    @BeforeEach
    void setUp() {
        fileSizeValidUtilUnderTest = new FileSizeValidUtil(mockScriptBusinessConfig);
    }

    @Test
    void testValidateFileSize() throws Exception {
        // Setup
        when(mockScriptBusinessConfig.getEachScriptAttachmentLimitSize()).thenReturn(DataSize.ofBytes(0L));

        // Run the test
        fileSizeValidUtilUnderTest.validateFileSize(0L);

        // Verify the results
    }
}
