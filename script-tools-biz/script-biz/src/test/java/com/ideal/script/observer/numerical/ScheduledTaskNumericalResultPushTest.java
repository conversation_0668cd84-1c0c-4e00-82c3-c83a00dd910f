package com.ideal.script.observer.numerical;

import com.ideal.common.util.spring.SpringUtil;
import com.ideal.notification.api.IWarn;
import com.ideal.script.config.PsbcProperties;
import com.ideal.script.mapper.TaskMapper;
import com.ideal.script.mapper.TaskRuntimeMapper;
import com.ideal.script.model.entity.Task;
import com.ideal.script.model.entity.TaskRuntime;
import com.ideal.script.service.impl.builders.MyScriptServiceScripts;
import com.ideal.system.api.IUserInfo;
import com.ideal.system.dto.UserInfoApiDto;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationContext;

import java.lang.reflect.Method;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * 任务执行完毕，异步推送统计信息单元测试
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class ScheduledTaskNumericalResultPushTest {

    @Mock
    private TaskMapper mockTaskMapper;
    
    @Mock
    private MyScriptServiceScripts mockScripts;
    
    @Mock
    private TaskRuntimeMapper mockTaskRuntimeMapper;
    
    @Mock
    private ApplicationContext mockApplicationContext;
    
    @InjectMocks
    private ScheduledTaskNumericalResultPush scheduledTaskNumericalResultPushUnderTest;
    
    private static MockedStatic<SpringUtil> springUtilMockedStatic;
    
    @BeforeAll
    static void setUpStaticMethod() {
        // 在所有测试方法执行前，Mock 静态方法
        springUtilMockedStatic = Mockito.mockStatic(SpringUtil.class);
    }
    
    @AfterAll
    static void tearDownStaticMethod() {
        // 在所有测试方法执行后，关闭 Mock 静态方法
        springUtilMockedStatic.close();
    }
    
    @BeforeEach
    void setUp() {
        // 每个测试方法执行前的准备工作
    }
    
    @Test
    @DisplayName("当PsbcProperties Bean不存在时不推送消息")
    void pushMessage_WhenPsbcPropertiesBeanNotExist_ShouldNotPushMessage() {
        // 准备测试数据
        Long taskRuntimeId = 1L;
        
        // 配置mock行为
        when(mockApplicationContext.getBeanNamesForType(PsbcProperties.class)).thenReturn(new String[0]);
        
        // 调用被测方法
        scheduledTaskNumericalResultPushUnderTest.pushMessage(taskRuntimeId);
        
        // 验证结果
        verify(mockTaskMapper, never()).selectTaskByRuntimeId(anyLong());
    }
    
    @Test
    @DisplayName("当PsbcProperties Bean存在但SpringUtil.getBean返回null时不推送消息")
    void pushMessage_WhenPsbcPropertiesBeanExistButSpringUtilGetBeanReturnsNull_ShouldNotPushMessage() {
        // 准备测试数据
        Long taskRuntimeId = 1L;
        
        // 配置mock行为
        when(mockApplicationContext.getBeanNamesForType(PsbcProperties.class)).thenReturn(new String[]{"psbcProperties"});
        springUtilMockedStatic.when(() -> SpringUtil.getBean(PsbcProperties.class)).thenReturn(null);
        
        // 调用被测方法
        scheduledTaskNumericalResultPushUnderTest.pushMessage(taskRuntimeId);
        
        // 验证结果
        verify(mockTaskMapper, never()).selectTaskByRuntimeId(anyLong());
    }
    
    @Test
    @DisplayName("当任务不是定时任务时不推送消息")
    void pushMessage_WhenTaskIsNotScheduledTask_ShouldNotPushMessage() {
        // 准备测试数据
        Long taskRuntimeId = 1L;
        Task task = new Task();
        task.setId(1L);
        task.setTaskName("测试任务");
        task.setTaskScheduler(1); // 非定时任务标识
        
        // 配置mock行为
        when(mockApplicationContext.getBeanNamesForType(PsbcProperties.class)).thenReturn(new String[]{"psbcProperties"});
        PsbcProperties psbcProperties = new PsbcProperties();
        springUtilMockedStatic.when(() -> SpringUtil.getBean(PsbcProperties.class)).thenReturn(psbcProperties);
        when(mockTaskMapper.selectTaskByRuntimeId(taskRuntimeId)).thenReturn(task);
        
        // 调用被测方法
        scheduledTaskNumericalResultPushUnderTest.pushMessage(taskRuntimeId);
        
        // 验证结果
        verify(mockTaskRuntimeMapper, never()).selectTaskRunTimeByTaskId(anyLong());
    }
    
    @Test
    @DisplayName("当任务是定时任务但没有TaskRuntime时不推送消息")
    void pushMessage_WhenTaskIsScheduledTaskButNoTaskRuntime_ShouldNotPushMessage() {
        // 准备测试数据
        Long taskRuntimeId = 1L;
        Task task = new Task();
        task.setId(1L);
        task.setTaskName("测试任务");
        task.setTaskScheduler(2); // 定时任务标识
        
        // 配置mock行为
        when(mockApplicationContext.getBeanNamesForType(PsbcProperties.class)).thenReturn(new String[]{"psbcProperties"});
        PsbcProperties psbcProperties = new PsbcProperties();
        springUtilMockedStatic.when(() -> SpringUtil.getBean(PsbcProperties.class)).thenReturn(psbcProperties);
        when(mockTaskMapper.selectTaskByRuntimeId(taskRuntimeId)).thenReturn(task);
        when(mockTaskRuntimeMapper.selectTaskRunTimeByTaskId(task.getId())).thenReturn(Collections.emptyList());
        
        // 调用被测方法
        scheduledTaskNumericalResultPushUnderTest.pushMessage(taskRuntimeId);
        
        // 验证结果
        verify(mockScripts, never()).getWarn();
    }
    
    @ParameterizedTest
    @ValueSource(ints = {20, 30})
    @DisplayName("当任务是定时任务且有TaskRuntime时推送消息")
    void pushMessage_WhenTaskIsScheduledTaskAndHasTaskRuntime_ShouldPushMessage(int state) {
        // 准备测试数据
        Long taskRuntimeId = 1L;
        Task task = new Task();
        task.setId(1L);
        task.setTaskName("测试任务");
        task.setTaskScheduler(2); // 定时任务标识
        task.setCreatorId(100L);
        task.setSrcScriptUuid("script-uuid-123");
        
        // 创建TaskRuntime列表
        List<TaskRuntime> taskRuntimes = new ArrayList<>();
        TaskRuntime taskRuntime = new TaskRuntime();
        taskRuntime.setId(1L);
        taskRuntime.setState(state); // 设置状态
        taskRuntime.setTimeoutValue(10L); // 10秒超时，修改为Long类型
        
        // 设置开始和结束时间，修改为Timestamp类型
        long currentTime = System.currentTimeMillis();
        Timestamp startTime = new Timestamp(currentTime - 20000); // 20秒前
        Timestamp endTime = new Timestamp(currentTime); // 现在
        taskRuntime.setStartTime(startTime);
        taskRuntime.setEndTime(endTime);
        
        taskRuntimes.add(taskRuntime);
        
        // 配置mock行为
        when(mockApplicationContext.getBeanNamesForType(PsbcProperties.class)).thenReturn(new String[]{"psbcProperties"});
        PsbcProperties psbcProperties = new PsbcProperties();
        springUtilMockedStatic.when(() -> SpringUtil.getBean(PsbcProperties.class)).thenReturn(psbcProperties);
        when(mockTaskMapper.selectTaskByRuntimeId(taskRuntimeId)).thenReturn(task);
        when(mockTaskRuntimeMapper.selectTaskRunTimeByTaskId(task.getId())).thenReturn(taskRuntimes);
        
        // Mock IUserInfo和IWarn
        IUserInfo iUserInfo = mock(IUserInfo.class);
        IWarn iWarn = mock(IWarn.class);
        when(mockScripts.getiUserInfo()).thenReturn(iUserInfo);
        when(mockScripts.getWarn()).thenReturn(iWarn);
        
        // Mock getUserInfoList方法
        List<UserInfoApiDto> userInfoList = new ArrayList<>();
        UserInfoApiDto userInfoApiDto = new UserInfoApiDto();
        userInfoApiDto.setId(100L);
        userInfoApiDto.setLoginName("testUser");
        userInfoList.add(userInfoApiDto);
        when(iUserInfo.getUserInfoList(anyList())).thenReturn(userInfoList);
        
        // 调用被测方法
        scheduledTaskNumericalResultPushUnderTest.pushMessage(taskRuntimeId);
        
        // 验证结果
        verify(iWarn, times(1)).receiveWarn(anyString());
    }
    
    @Test
    @DisplayName("测试agentResultStatistics方法")
    void testAgentResultStatistics() throws Exception {
        // 使用反射调用私有方法
        Method agentResultStatisticsMethod = ScheduledTaskNumericalResultPush.class.getDeclaredMethod("agentResultStatistics", Task.class);
        agentResultStatisticsMethod.setAccessible(true);
        
        // 准备测试数据
        Task task = new Task();
        task.setId(1L);
        task.setTaskName("测试任务");
        
        // 创建不同状态的TaskRuntime
        List<TaskRuntime> taskRuntimes = new ArrayList<>();
        
        // 正常完成的TaskRuntime
        TaskRuntime normalTaskRuntime = new TaskRuntime();
        normalTaskRuntime.setState(20); // 完成状态
        normalTaskRuntime.setTimeoutValue(10L); // 10秒超时，修改为Long类型
        
        long currentTime = System.currentTimeMillis();
        // 修改为Timestamp类型
        Timestamp normalStartTime = new Timestamp(currentTime - 5000); // 5秒前
        Timestamp normalEndTime = new Timestamp(currentTime); // 现在
        normalTaskRuntime.setStartTime(normalStartTime);
        normalTaskRuntime.setEndTime(normalEndTime);
        taskRuntimes.add(normalTaskRuntime);
        
        // 超时完成的TaskRuntime
        TaskRuntime timeoutTaskRuntime = new TaskRuntime();
        timeoutTaskRuntime.setState(20); // 完成状态
        timeoutTaskRuntime.setTimeoutValue(5L); // 5秒超时，修改为Long类型
        
        // 修改为Timestamp类型
        Timestamp timeoutStartTime = new Timestamp(currentTime - 10000); // 10秒前
        Timestamp timeoutEndTime = new Timestamp(currentTime); // 现在
        timeoutTaskRuntime.setStartTime(timeoutStartTime);
        timeoutTaskRuntime.setEndTime(timeoutEndTime);
        taskRuntimes.add(timeoutTaskRuntime);
        
        // 异常的TaskRuntime
        TaskRuntime errorTaskRuntime = new TaskRuntime();
        errorTaskRuntime.setState(30); // 异常状态
        errorTaskRuntime.setTimeoutValue(10L); // 10秒超时，修改为Long类型
        
        // 修改为Timestamp类型
        Timestamp errorStartTime = new Timestamp(currentTime - 5000); // 5秒前
        Timestamp errorEndTime = new Timestamp(currentTime); // 现在
        errorTaskRuntime.setStartTime(errorStartTime);
        errorTaskRuntime.setEndTime(errorEndTime);
        taskRuntimes.add(errorTaskRuntime);
        
        // 配置mock行为
        when(mockTaskRuntimeMapper.selectTaskRunTimeByTaskId(task.getId())).thenReturn(taskRuntimes);
        
        // 调用私有方法
        String result = (String) agentResultStatisticsMethod.invoke(scheduledTaskNumericalResultPushUnderTest, task);
        
        // 验证结果包含正确的信息
        assert result.contains("共执行Agent:3台");
        assert result.contains("正常完成1台");
        assert result.contains("异常1台");
        assert result.contains("超时完成1台");
    }
    
    @Test
    @DisplayName("测试statisticsMessage方法")
    void testStatisticsMessage() throws Exception {
        // 使用反射调用私有方法
        Method statisticsMessageMethod = ScheduledTaskNumericalResultPush.class.getDeclaredMethod("statisticsMessage", String.class, Long.class, String.class);
        statisticsMessageMethod.setAccessible(true);
        
        // 准备测试数据
        String message = "测试消息";
        Long userId = 100L;
        String uuid = "script-uuid-123";
        
        // Mock IUserInfo和IWarn
        IUserInfo iUserInfo = mock(IUserInfo.class);
        IWarn iWarn = mock(IWarn.class);
        when(mockScripts.getiUserInfo()).thenReturn(iUserInfo);
        when(mockScripts.getWarn()).thenReturn(iWarn);
        
        // Mock getUserInfoList方法
        List<UserInfoApiDto> userInfoList = new ArrayList<>();
        UserInfoApiDto userInfoApiDto = new UserInfoApiDto();
        userInfoApiDto.setId(100L);
        userInfoApiDto.setLoginName("testUser");
        userInfoList.add(userInfoApiDto);
        when(iUserInfo.getUserInfoList(anyList())).thenReturn(userInfoList);
        
        // 调用私有方法
        statisticsMessageMethod.invoke(scheduledTaskNumericalResultPushUnderTest, message, userId, uuid);
        
        // 验证结果
        verify(iWarn, times(1)).receiveWarn(anyString());
    }
}
