package com.ideal.script.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ideal.script.mapper.TaskStatementMapper;
import com.ideal.script.model.bean.TaskStatementBean;
import com.ideal.script.model.dto.TaskStatementDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TaskStatementServiceImplTest {

    @Mock
    private TaskStatementMapper mockTaskStatementMapper;
    @Mock
    private CategoryServiceImpl mockCategoryService;

    private TaskStatementServiceImpl taskStatementServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        taskStatementServiceImplUnderTest = new TaskStatementServiceImpl(mockTaskStatementMapper, mockCategoryService);
    }

    @Test
    void testSelectTaskStatementPage() {
        // Setup
        final TaskStatementDto taskStatementDto = new TaskStatementDto();
        taskStatementDto.setStartTimeRange(Collections.singletonList(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0))));
        taskStatementDto.setCategoryId(0L);
        taskStatementDto.setScriptNameZh("scriptNameZh");
        taskStatementDto.setScriptName("scriptName");
        taskStatementDto.setCategoryPath("categoryPath");

        when(mockCategoryService.buildCategoryFullPath(0L)).thenReturn("categoryPath");
        when(mockCategoryService.handleCategoryPath("categoryPath")).thenReturn("escapedLikeCategoryPath");

        // Configure TaskStatementMapper.selectTaskStatementPage(...).
        final TaskStatementBean taskStatementBean = new TaskStatementBean();
        taskStatementBean.setStartTimeRange(Collections.singletonList(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0))));
        taskStatementBean.setEscapedLikeCategoryPath("escapedLikeCategoryPath");
        taskStatementBean.setScriptNameZh("scriptNameZh");
        taskStatementBean.setScriptName("scriptName");
        taskStatementBean.setCategoryPath("categoryPath");

        Page<TaskStatementBean> page = new Page<>();
        page.add(taskStatementBean);
        when(mockTaskStatementMapper.selectTaskStatementPage(any(TaskStatementBean.class)))
                .thenReturn(page);

        // Run the test
        final PageInfo<TaskStatementDto> result = taskStatementServiceImplUnderTest.selectTaskStatementPage(
                taskStatementDto, 0, 0);

        // Verify the results
        verify(mockTaskStatementMapper).selectTaskStatementPage(any());
    }

    @Test
    void testSelectTaskStatementPage_TaskStatementMapperReturnsNoItems() {
        // Setup
        final TaskStatementDto taskStatementDto = new TaskStatementDto();
        taskStatementDto.setStartTimeRange(Collections.singletonList(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0))));
        taskStatementDto.setCategoryId(0L);
        taskStatementDto.setScriptNameZh("scriptNameZh");
        taskStatementDto.setScriptName("scriptName");
        taskStatementDto.setCategoryPath("categoryPath");

        when(mockCategoryService.buildCategoryFullPath(0L)).thenReturn("categoryPath");
        when(mockCategoryService.handleCategoryPath("categoryPath")).thenReturn("escapedLikeCategoryPath");
        when(mockTaskStatementMapper.selectTaskStatementPage(any(TaskStatementBean.class)))
                .thenReturn(new Page<>());

        // Run the test
        final PageInfo<TaskStatementDto> result = taskStatementServiceImplUnderTest.selectTaskStatementPage(
                taskStatementDto, 0, 0);

        // Verify the results
        verify(mockTaskStatementMapper).selectTaskStatementPage(any());
    }
}
