package com.ideal.script.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.script.model.dto.ScriptVersionShareDto;
import com.ideal.script.model.dto.interaction.agent.UserInfoDto;
import com.ideal.script.service.IScriptVersionShareService;
import com.ideal.system.common.component.model.CurrentUser;
import com.ideal.system.dto.RoleApiDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * ScriptVersionShareController单元测试类
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class ScriptVersionShareControllerTest {

    @Mock
    private IScriptVersionShareService scriptVersionShareService;

    @InjectMocks
    private ScriptVersionShareController scriptVersionShareController;

    private ScriptVersionShareDto scriptVersionShareDto;
    private TableQueryDto<ScriptVersionShareDto> tableQueryDto;
    private PageInfo<ScriptVersionShareDto> sharePageInfo;
    private PageInfo<UserInfoDto> userPageInfo;
    private PageInfo<RoleApiDto> rolePageInfo;
    private List<ScriptVersionShareDto> shareList;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        scriptVersionShareDto = new ScriptVersionShareDto();
        scriptVersionShareDto.setIid(1L);
        scriptVersionShareDto.setShareType((short) 0);
        scriptVersionShareDto.setScriptInfoId(100L);
        scriptVersionShareDto.setShareObjectId("user123");
        scriptVersionShareDto.setShareObjectName("测试用户");
        scriptVersionShareDto.setScriptNameZh("测试脚本");
        scriptVersionShareDto.setFullName("张三");
        scriptVersionShareDto.setCreatedTime(new Timestamp(System.currentTimeMillis()));

        shareList = Arrays.asList(scriptVersionShareDto);

        tableQueryDto = new TableQueryDto<>();
        tableQueryDto.setPageNum(1);
        tableQueryDto.setPageSize(10);
        tableQueryDto.setQueryParam(scriptVersionShareDto);

        // 初始化分页信息
        sharePageInfo = new PageInfo<>();
        sharePageInfo.setList(shareList);
        sharePageInfo.setTotal(1);
        sharePageInfo.setPageNum(1);
        sharePageInfo.setPageSize(10);

        userPageInfo = new PageInfo<>();
        UserInfoDto userInfoDto = new UserInfoDto();
        userInfoDto.setId(1L);
        userInfoDto.setLoginName("testuser");
        userInfoDto.setFullName("测试用户");
        userInfoDto.setEmail("<EMAIL>");
        userPageInfo.setList(Arrays.asList(userInfoDto));
        userPageInfo.setTotal(1);

        rolePageInfo = new PageInfo<>();
        RoleApiDto roleApiDto = new RoleApiDto();
        roleApiDto.setId(1L);
        roleApiDto.setName("测试角色");
        roleApiDto.setDataShare(1);
        rolePageInfo.setList(Arrays.asList(roleApiDto));
        rolePageInfo.setTotal(1);
    }

    @Test
    @DisplayName("保存脚本版本共享-成功")
    void saveInfoVersion_success() {
        // Mock service方法
        doNothing().when(scriptVersionShareService).insertScriptVersionShare(shareList);

        // 执行测试方法
        R<Object> result = scriptVersionShareController.saveInfoVersion(shareList);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("save.success", result.getMessage());
        assertEquals("", result.getData());
        
        // 验证service方法被调用
        verify(scriptVersionShareService, times(1)).insertScriptVersionShare(shareList);
    }

    @Test
    @DisplayName("保存脚本版本共享-service抛出异常")
    void saveInfoVersion_serviceException() {
        // Mock service方法抛出异常
        doThrow(new RuntimeException("Database error")).when(scriptVersionShareService).insertScriptVersionShare(shareList);

        // 执行测试方法并验证异常
        assertThrows(RuntimeException.class, () -> {
            scriptVersionShareController.saveInfoVersion(shareList);
        });

        // 验证service方法被调用
        verify(scriptVersionShareService, times(1)).insertScriptVersionShare(shareList);
    }

    @Test
    @DisplayName("查询共享脚本数据-成功")
    void selectShareScriptData_success() {
        // Mock service方法
        doReturn(sharePageInfo).when(scriptVersionShareService).selectShareScriptData(any(ScriptVersionShareDto.class), eq(1), eq(10));

        // 执行测试方法
        R<PageInfo<ScriptVersionShareDto>> result = scriptVersionShareController.selectShareScriptData(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("list.success", result.getMessage());
        assertEquals(sharePageInfo, result.getData());
        assertEquals(1, result.getData().getTotal());
        
        // 验证service方法被调用
        verify(scriptVersionShareService, times(1)).selectShareScriptData(any(ScriptVersionShareDto.class), eq(1), eq(10));
    }

    @Test
    @DisplayName("查询共享脚本数据-空结果")
    void selectShareScriptData_emptyResult() {
        // 创建空的分页结果
        PageInfo<ScriptVersionShareDto> emptyPageInfo = new PageInfo<>();
        emptyPageInfo.setList(Collections.emptyList());
        emptyPageInfo.setTotal(0);

        // Mock service方法
        doReturn(emptyPageInfo).when(scriptVersionShareService).selectShareScriptData(any(ScriptVersionShareDto.class), eq(1), eq(10));

        // 执行测试方法
        R<PageInfo<ScriptVersionShareDto>> result = scriptVersionShareController.selectShareScriptData(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("list.success", result.getMessage());
        assertEquals(0, result.getData().getTotal());
        assertTrue(result.getData().getList().isEmpty());
        
        // 验证service方法被调用
        verify(scriptVersionShareService, times(1)).selectShareScriptData(any(ScriptVersionShareDto.class), eq(1), eq(10));
    }

    @Test
    @DisplayName("获取共享用户-成功")
    void getShareUser_success() {
        // Mock CurrentUserUtil
        try (MockedStatic<com.ideal.sc.util.CurrentUserUtil> mockedCurrentUserUtil = mockStatic(com.ideal.sc.util.CurrentUserUtil.class)) {
            CurrentUser currentUser = new CurrentUser();
            currentUser.setId(1L);
            mockedCurrentUserUtil.when(com.ideal.sc.util.CurrentUserUtil::getCurrentUser).thenReturn(currentUser);

            // Mock service方法
            doReturn(userPageInfo).when(scriptVersionShareService).getShareUser(eq(1L), eq(1), eq(10), any(ScriptVersionShareDto.class));

            // 执行测试方法
            R<PageInfo<UserInfoDto>> result = scriptVersionShareController.getShareUser(tableQueryDto);

            // 验证结果
            assertNotNull(result);
            assertEquals("10000", result.getCode());
            assertEquals("list.success", result.getMessage());
            assertEquals(userPageInfo, result.getData());
            assertEquals(1, result.getData().getTotal());
            
            // 验证service方法被调用
            verify(scriptVersionShareService, times(1)).getShareUser(eq(1L), eq(1), eq(10), any(ScriptVersionShareDto.class));
        }
    }

    @Test
    @DisplayName("删除脚本版本共享-成功")
    void removeInfoVersion_success() {
        // 准备测试数据
        Long[] ids = {1L, 2L, 3L};

        // Mock service方法
        doNothing().when(scriptVersionShareService).deleteScriptVersionShareByIds(ids);

        // 执行测试方法
        R<Object> result = scriptVersionShareController.removeInfoVersion(ids);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("remove.success", result.getMessage());
        
        // 验证service方法被调用
        verify(scriptVersionShareService, times(1)).deleteScriptVersionShareByIds(ids);
    }

    @Test
    @DisplayName("获取未共享角色列表-成功")
    void getNotShareRoles_success() {
        // Mock service方法
        doReturn(rolePageInfo).when(scriptVersionShareService).getNotShareRoles(eq(1), eq(10), any(ScriptVersionShareDto.class));

        // 执行测试方法
        R<PageInfo<RoleApiDto>> result = scriptVersionShareController.getNotShareRoles(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("list.success", result.getMessage());
        assertEquals(rolePageInfo, result.getData());
        assertEquals(1, result.getData().getTotal());
        
        // 验证service方法被调用
        verify(scriptVersionShareService, times(1)).getNotShareRoles(eq(1), eq(10), any(ScriptVersionShareDto.class));
    }

    @Test
    @DisplayName("保存脚本版本共享-空列表")
    void saveInfoVersion_emptyList() {
        // 准备空列表
        List<ScriptVersionShareDto> emptyList = Collections.emptyList();

        // Mock service方法
        doNothing().when(scriptVersionShareService).insertScriptVersionShare(emptyList);

        // 执行测试方法
        R<Object> result = scriptVersionShareController.saveInfoVersion(emptyList);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("save.success", result.getMessage());
        
        // 验证service方法被调用
        verify(scriptVersionShareService, times(1)).insertScriptVersionShare(emptyList);
    }

    @Test
    @DisplayName("删除脚本版本共享-空ID数组")
    void removeInfoVersion_emptyIds() {
        // 准备空ID数组
        Long[] emptyIds = {};

        // Mock service方法
        doNothing().when(scriptVersionShareService).deleteScriptVersionShareByIds(emptyIds);

        // 执行测试方法
        R<Object> result = scriptVersionShareController.removeInfoVersion(emptyIds);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("remove.success", result.getMessage());
        
        // 验证service方法被调用
        verify(scriptVersionShareService, times(1)).deleteScriptVersionShareByIds(emptyIds);
    }
}
