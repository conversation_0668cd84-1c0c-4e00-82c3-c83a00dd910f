package com.ideal.script.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ideal.script.common.util.FileSizeValidUtil;
import com.ideal.script.config.ScriptBusinessConfig;
import com.ideal.script.dto.*;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.AttachmentMapper;
import com.ideal.script.model.entity.Attachment;
import com.ideal.script.model.entity.InfoVersion;
import com.ideal.script.model.entity.InfoVersionText;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AttachmentServiceImplTest {

    @Mock
    private AttachmentMapper mockAttachmentMapper;
    @Mock
    private  ScriptBusinessConfig mockScriptBusinessConfig;
    @InjectMocks
    private AttachmentServiceImpl attachmentServiceImplUnderTest;

    @Mock
    private  FileSizeValidUtil fileSizeValidUtil;


    @Test
    void testSelectAttachmentById() {
        // Setup
        // Configure AttachmentMapper.selectAttachmentById(...).
        final Attachment attachment = new Attachment();
        attachment.setId(1L);
        attachment.setName("name");
        attachment.setSize(1L);
        attachment.setContents("content".getBytes());
        when(mockAttachmentMapper.selectAttachmentById(1L)).thenReturn(attachment);

        // Run the test
        final AttachmentDto result = attachmentServiceImplUnderTest.selectAttachmentById(1L);
        assertNotNull(result);
        assertEquals(1,result.getId());
        assertEquals("name",result.getName());
        // Verify the results
        Mockito.verify(mockAttachmentMapper,times(1)).selectAttachmentById(anyLong());

    }

    @Test
    void testSelectAttachmentList() {
        // Setup
        final AttachmentDto attachmentDto = new AttachmentDto();
        attachmentDto.setId(1L);
        attachmentDto.setSrcScriptUuid("srcScriptUuid");
        attachmentDto.setName("name");
        attachmentDto.setSize(1L);

        // Configure AttachmentMapper.selectAttachmentList(...).
        final Attachment attachment = new Attachment();
        attachment.setId(1L);
        attachment.setSrcScriptUuid("srcScriptUuid");
        attachment.setName("name");
        attachment.setSize(1L);
        Page<Attachment> page = new Page<>();
        page.add(attachment);

        when(mockAttachmentMapper.selectAttachmentList(any(Attachment.class))).thenReturn(page);

        // Run the test
        final PageInfo<AttachmentDto> result = attachmentServiceImplUnderTest.selectAttachmentList(attachmentDto, 1, 50);
        System.out.println(result);
        assertNotNull(result);
        // Verify the results
    }



    @Test
    void testInsertAttachment() {
        // Setup
        final AttachmentDto attachmentDto = new AttachmentDto();
        attachmentDto.setId(1L);
        attachmentDto.setSrcScriptUuid("srcScriptUuid");
        attachmentDto.setName("name");
        attachmentDto.setSize(1L);


        // Run the test
        int value = attachmentServiceImplUnderTest.insertAttachment(attachmentDto);
        assertThat(value).isZero();
        // Verify the results
        verify(mockAttachmentMapper).insertAttachment(any(Attachment.class));
    }

    @Test
    void testUpdateAttachment() {
        // Setup
        final AttachmentDto attachmentDto = new AttachmentDto();
        attachmentDto.setId(1L);
        attachmentDto.setSrcScriptUuid("srcScriptUuid");
        attachmentDto.setName("name");
        attachmentDto.setSize(1L);

        // Run the test
        attachmentServiceImplUnderTest.updateAttachment(attachmentDto);

        // Verify the results
        verify(mockAttachmentMapper).updateAttachment(any(Attachment.class));
    }

    @Test
    void testDeleteAttachmentByIds() {
        // Setup
        // Run the test
        attachmentServiceImplUnderTest.deleteAttachmentByIds(new Long[]{1L});

        // Verify the results
        verify(mockAttachmentMapper).deleteAttachmentByIds(any(Long[].class));
    }

    @Test
    void testDeleteAttachmentById() {
        // Setup
        when(mockAttachmentMapper.deleteAttachmentById(1L)).thenReturn(1);

        // Run the test
        final int result = attachmentServiceImplUnderTest.deleteAttachmentById(1L);

        // Verify the results
        assertThat(result).isEqualTo(1);
    }

    @Test
    void testUploadAttachment() throws Exception {
        // Setup
//        final MultipartFile file = new MockMultipartFile("name","name","1","content".getBytes());
        AttachmentDto attachmentDto = new AttachmentDto();
        attachmentDto.setName("name");
        attachmentDto.setSize(1L);
        attachmentDto.setContents("content".getBytes());

        // Run the test
        final AttachmentDto result = attachmentServiceImplUnderTest.uploadAttachment(attachmentDto);

        // Verify the results
        assertNotNull(result);
        verify(mockAttachmentMapper).insertAttachment(any(Attachment.class));
    }



    @Test
    void testUploadAttachment_largeFile() throws ScriptException {
        // Setup
//        MultipartFile file = Mockito.mock(MultipartFile.class);
        AttachmentDto attachmentDto = new AttachmentDto();
        attachmentDto.setSize((long) (7 * 1024 * 1024));
        doThrow(new ScriptException()).when(fileSizeValidUtil).validateFileSize(attachmentDto.getSize());
        // Run the test
        assertThrows(ScriptException.class, ()->{
            attachmentServiceImplUnderTest.uploadAttachment(attachmentDto);
        });
    }

    @ParameterizedTest
    @ValueSource(booleans = {true,false})
    void testCreateAttachments(boolean flag) {
        // Setup
        final ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        final ScriptVersionDto scriptVersionDto = new ScriptVersionDto();
        final AttachmentUploadDto attachmentUploadDto = new AttachmentUploadDto();
        attachmentUploadDto.setResponse(new AttachmentResponseDto());
        attachmentUploadDto.getResponse().setId(1L);
        attachmentUploadDto.setName("name");

        attachmentUploadDto.setSize(50L);

        List<AttachmentUploadDto> attachmentUploadDtoList = new ArrayList<>();
        attachmentUploadDtoList.add(attachmentUploadDto);
        scriptVersionDto.setAttachmentUploadDtoList(attachmentUploadDtoList);
        scriptInfoDto.setScriptVersionDto(scriptVersionDto);
        scriptVersionDto.setSrcScriptUuid("srcScriptUuid");
        scriptInfoDto.setScriptVersionDto(scriptVersionDto);

        //发布修改
        if (!flag) {
            Attachment attachment = new Attachment();
            when(mockAttachmentMapper.selectAttachmentById(eq(1L))).thenReturn(attachment);
        }
        // Run the test
        attachmentServiceImplUnderTest.createAttachments(scriptInfoDto,flag);

        // Verify the results
        if(flag){
            verify(mockAttachmentMapper).updateAttachment(any(Attachment.class));
        }else{
            verify(mockAttachmentMapper).insertAttachment(any(Attachment.class));
        }


    }

    @Test
    void testUpdateAttachments_hasAttachment() {
        // Setup
        final ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        final ScriptVersionDto scriptVersionDto = new ScriptVersionDto();
        final AttachmentUploadDto attachmentUploadDto = new AttachmentUploadDto();
        attachmentUploadDto.setResponse(new AttachmentResponseDto());
        attachmentUploadDto.getResponse().setId(1L);
        attachmentUploadDto.setName("name");

        attachmentUploadDto.setSize(1L);
        List<AttachmentUploadDto> attachmentUploadDtoList = new ArrayList<>();
        attachmentUploadDtoList.add(attachmentUploadDto);
        scriptVersionDto.setAttachmentUploadDtoList(attachmentUploadDtoList);
        scriptInfoDto.setScriptVersionDto(scriptVersionDto);
        scriptVersionDto.setSrcScriptUuid("srcScriptUuid");
        scriptInfoDto.setScriptVersionDto(scriptVersionDto);

        // Run the test
        final boolean result = attachmentServiceImplUnderTest.updateAttachments(scriptInfoDto);

        // Verify the results
        assertThat(result).isTrue();
    }
    @Test
    void testUpdateAttachments_noAttachment() {
        // Setup
        final ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        final ScriptVersionDto scriptVersionDto = new ScriptVersionDto();
        List<AttachmentUploadDto> attachmentUploadDtoList = new ArrayList<>();

        scriptVersionDto.setAttachmentUploadDtoList(attachmentUploadDtoList);
        scriptInfoDto.setScriptVersionDto(scriptVersionDto);
        scriptVersionDto.setSrcScriptUuid("srcScriptUuid");
        scriptInfoDto.setScriptVersionDto(scriptVersionDto);
        // Run the test
        final boolean result = attachmentServiceImplUnderTest.updateAttachments(scriptInfoDto);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockAttachmentMapper,times(0)).updateAttachment(any(Attachment.class));
        verify(mockAttachmentMapper,times(0)).deleteAttachmentByIdAndUuid("srcScriptUuid", Collections.singletonList(1L));
    }

    @Test
    void testUpdateAttachments_attachment_isNull() {
        // Setup
        final ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        final ScriptVersionDto scriptVersionDto = new ScriptVersionDto();
        scriptVersionDto.setAttachmentUploadDtoList(new ArrayList<>());
        scriptInfoDto.setScriptVersionDto(scriptVersionDto);
        scriptVersionDto.setSrcScriptUuid("srcScriptUuid");
        scriptInfoDto.setScriptVersionDto(scriptVersionDto);
        // Run the test
        final boolean result = attachmentServiceImplUnderTest.updateAttachments(scriptInfoDto);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockAttachmentMapper,times(1)).deleteAttachmentByScriptUuids(any(String[].class));
        verify(mockAttachmentMapper,times(0)).updateAttachment(any(Attachment.class));
        verify(mockAttachmentMapper,times(0)).deleteAttachmentByIdAndUuid("srcScriptUuid", Collections.singletonList(1L));
    }

    @Test
    void testGetAttachmentUploadDtos() {
        // Setup
        final InfoVersionText infoVersionText = new InfoVersionText();
        infoVersionText.setId(1L);
        infoVersionText.setSrcScriptUuid("srcScriptUuid");
        infoVersionText.setContent("content");
        infoVersionText.setCreatorId(1L);
        infoVersionText.setCreatorName("creatorName");

        // Configure AttachmentMapper.selectAttachmentList(...).
        final Attachment attachment = new Attachment();
        attachment.setId(1L);
        attachment.setSrcScriptUuid("srcScriptUuid");
        attachment.setName("name");
        attachment.setSize(1L);
        attachment.setContents("content".getBytes());
        final List<Attachment> attachments = Collections.singletonList(attachment);
        when(mockAttachmentMapper.selectAttachmentList(any(Attachment.class))).thenReturn(attachments);

        // Run the test
        final List<AttachmentUploadDto> result = attachmentServiceImplUnderTest.getAttachmentUploadDtos(new InfoVersion());
        assertNotNull(result);
        // Verify the results
        verify(mockAttachmentMapper).selectAttachmentList(any(Attachment.class));
    }


    @Test
    void getAttachmentByUuid() {

        final List<AttachmentDto> result = attachmentServiceImplUnderTest.getAttachmentByUuid(anyString());
        assertNotNull(result);

    }

    @Test
    void selectAttachmentByIds() {
        List<AttachmentDto> attachmentDtos = attachmentServiceImplUnderTest.selectAttachmentByIds(new Long[]{1L, 2L});
        assertNotNull(attachmentDtos);
    }
}
