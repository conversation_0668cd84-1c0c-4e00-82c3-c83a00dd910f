package com.ideal.script.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.script.model.dto.FunctionpublishDto;
import com.ideal.script.model.dto.VarAndFuncForEditDto;
import com.ideal.script.service.IFunctionpublishService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * FunctionpublishController单元测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class FunctionpublishControllerTest {

    @Mock
    private IFunctionpublishService functionpublishService;

    @InjectMocks
    private FunctionpublishController functionpublishController;

    private TableQueryDto<VarAndFuncForEditDto> tableQueryDto;
    private VarAndFuncForEditDto varAndFuncForEditDto;
    private PageInfo<FunctionpublishDto> pageInfo;
    private FunctionpublishDto functionpublishDto;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        varAndFuncForEditDto = new VarAndFuncForEditDto();
        varAndFuncForEditDto.setBindState(1);
        varAndFuncForEditDto.setBindIds(new Long[]{1L, 2L});
        varAndFuncForEditDto.setKeyword("test");

        tableQueryDto = new TableQueryDto<>();
        tableQueryDto.setQueryParam(varAndFuncForEditDto);
        tableQueryDto.setPageNum(1);
        tableQueryDto.setPageSize(10);

        functionpublishDto = new FunctionpublishDto();
        functionpublishDto.setId(1L);
        functionpublishDto.setName("testFunction");
        functionpublishDto.setLanguagetype(1);
        functionpublishDto.setDesc("测试函数");
        functionpublishDto.setAttribute(1);
        functionpublishDto.setStatus(2);
        functionpublishDto.setGlobal(1);
        functionpublishDto.setUserglobal(1);
        functionpublishDto.setClassid(1L);
        functionpublishDto.setFunctionMd("test-md-123");
        functionpublishDto.setCreatorId(1L);
        functionpublishDto.setCreatorName("测试用户");
        functionpublishDto.setUpdatorId(1L);
        functionpublishDto.setUpdatorName("测试用户");
        functionpublishDto.setCreateTime(new Timestamp(System.currentTimeMillis()));
        functionpublishDto.setUpdateTime(new Timestamp(System.currentTimeMillis()));

        List<FunctionpublishDto> functionList = new ArrayList<>();
        functionList.add(functionpublishDto);
        pageInfo = new PageInfo<>(functionList);
        pageInfo.setTotal(1);
        pageInfo.setPages(1);
    }

    @Test
    @DisplayName("测试查询函数库基础发布列表 - 正常情况")
    void listFunctionpublishForScriptEdit_success() {
        // Mock方法
        when(functionpublishService.selectFunctionpublishListForScriptEdit(
                any(VarAndFuncForEditDto.class), anyInt(), anyInt())).thenReturn(pageInfo);

        // 执行测试
        R<PageInfo<FunctionpublishDto>> result = functionpublishController.listFunctionpublishForScriptEdit(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals(pageInfo, result.getData());
        assertEquals("list.success", result.getMessage());
        verify(functionpublishService, times(1)).selectFunctionpublishListForScriptEdit(
                varAndFuncForEditDto, 1, 10);
    }

    @Test
    @DisplayName("测试查询函数库基础发布列表 - 返回空列表")
    void listFunctionpublishForScriptEdit_emptyList() {
        // 准备空列表数据
        PageInfo<FunctionpublishDto> emptyPageInfo = new PageInfo<>(Collections.emptyList());
        emptyPageInfo.setTotal(0);
        emptyPageInfo.setPages(0);

        // Mock方法
        when(functionpublishService.selectFunctionpublishListForScriptEdit(
                any(VarAndFuncForEditDto.class), anyInt(), anyInt())).thenReturn(emptyPageInfo);

        // 执行测试
        R<PageInfo<FunctionpublishDto>> result = functionpublishController.listFunctionpublishForScriptEdit(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals(emptyPageInfo, result.getData());
        assertEquals("list.success", result.getMessage());
        assertEquals(0, result.getData().getTotal());
        verify(functionpublishService, times(1)).selectFunctionpublishListForScriptEdit(
                varAndFuncForEditDto, 1, 10);
    }

    @Test
    @DisplayName("测试查询函数库基础发布列表 - 异常情况")
    void listFunctionpublishForScriptEdit_exception() {
        // Mock方法抛出异常
        when(functionpublishService.selectFunctionpublishListForScriptEdit(
                any(VarAndFuncForEditDto.class), anyInt(), anyInt()))
                .thenThrow(new RuntimeException("数据库连接异常"));

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            functionpublishController.listFunctionpublishForScriptEdit(tableQueryDto);
        });

        // 验证异常信息
        assertEquals("数据库连接异常", exception.getMessage());
        verify(functionpublishService, times(1)).selectFunctionpublishListForScriptEdit(
                varAndFuncForEditDto, 1, 10);
    }

    @Test
    @DisplayName("测试查询函数库基础发布列表 - null查询参数")
    void listFunctionpublishForScriptEdit_nullQueryParam() {
        // 准备null查询参数
        TableQueryDto<VarAndFuncForEditDto> nullParamTableQuery = new TableQueryDto<>();
        nullParamTableQuery.setQueryParam(null);
        nullParamTableQuery.setPageNum(1);
        nullParamTableQuery.setPageSize(10);

        // Mock方法
        when(functionpublishService.selectFunctionpublishListForScriptEdit(
                any(), anyInt(), anyInt())).thenReturn(pageInfo);

        // 执行测试
        R<PageInfo<FunctionpublishDto>> result = functionpublishController.listFunctionpublishForScriptEdit(nullParamTableQuery);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals(pageInfo, result.getData());
        assertEquals("list.success", result.getMessage());
        verify(functionpublishService, times(1)).selectFunctionpublishListForScriptEdit(
                null, 1, 10);
    }

    @ParameterizedTest
    @ValueSource(ints = {1, 2, 5, 10, 20})
    @DisplayName("测试查询函数库基础发布列表 - 不同页码大小")
    void listFunctionpublishForScriptEdit_differentPageSizes(int pageSize) {
        // 准备数据
        tableQueryDto.setPageSize(pageSize);

        // Mock方法
        when(functionpublishService.selectFunctionpublishListForScriptEdit(
                any(VarAndFuncForEditDto.class), anyInt(), anyInt())).thenReturn(pageInfo);

        // 执行测试
        R<PageInfo<FunctionpublishDto>> result = functionpublishController.listFunctionpublishForScriptEdit(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals(pageInfo, result.getData());
        assertEquals("list.success", result.getMessage());
        verify(functionpublishService, times(1)).selectFunctionpublishListForScriptEdit(
                varAndFuncForEditDto, 1, pageSize);
    }

    @ParameterizedTest
    @MethodSource("provideVarAndFuncForEditDtoData")
    @DisplayName("测试查询函数库基础发布列表 - 不同查询条件")
    void listFunctionpublishForScriptEdit_differentQueryConditions(VarAndFuncForEditDto queryDto, String description) {
        // 准备数据
        tableQueryDto.setQueryParam(queryDto);

        // Mock方法
        when(functionpublishService.selectFunctionpublishListForScriptEdit(
                any(), anyInt(), anyInt())).thenReturn(pageInfo);

        // 执行测试
        R<PageInfo<FunctionpublishDto>> result = functionpublishController.listFunctionpublishForScriptEdit(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals(pageInfo, result.getData());
        assertEquals("list.success", result.getMessage());
        verify(functionpublishService, times(1)).selectFunctionpublishListForScriptEdit(
                queryDto, 1, 10);
    }

    static Stream<Arguments> provideVarAndFuncForEditDtoData() {
        // 场景1：绑定状态为1，有绑定ID
        VarAndFuncForEditDto dto1 = new VarAndFuncForEditDto();
        dto1.setBindState(1);
        dto1.setBindIds(new Long[]{1L, 2L, 3L});
        dto1.setKeyword("function");

        // 场景2：绑定状态为2，有绑定ID
        VarAndFuncForEditDto dto2 = new VarAndFuncForEditDto();
        dto2.setBindState(2);
        dto2.setBindIds(new Long[]{4L, 5L});
        dto2.setKeyword("test");

        // 场景3：绑定状态为0，无绑定ID
        VarAndFuncForEditDto dto3 = new VarAndFuncForEditDto();
        dto3.setBindState(0);
        dto3.setBindIds(new Long[]{});
        dto3.setKeyword("");

        // 场景4：只有关键字搜索
        VarAndFuncForEditDto dto4 = new VarAndFuncForEditDto();
        dto4.setBindState(0);
        dto4.setBindIds(null);
        dto4.setKeyword("search");

        // 场景5：空的查询条件
        VarAndFuncForEditDto dto5 = new VarAndFuncForEditDto();
        dto5.setBindState(0);
        dto5.setBindIds(null);
        dto5.setKeyword(null);

        return Stream.of(
                Arguments.of(dto1, "绑定状态1，有绑定ID"),
                Arguments.of(dto2, "绑定状态2，有绑定ID"),
                Arguments.of(dto3, "绑定状态0，无绑定ID"),
                Arguments.of(dto4, "只有关键字搜索"),
                Arguments.of(dto5, "空的查询条件")
        );
    }

    @Test
    @DisplayName("测试查询函数库基础发布列表 - 大数据量分页")
    void listFunctionpublishForScriptEdit_largeDataPaging() {
        // 准备大数据量分页数据
        List<FunctionpublishDto> largeList = new ArrayList<>();
        for (int i = 1; i <= 100; i++) {
            FunctionpublishDto dto = new FunctionpublishDto();
            dto.setId((long) i);
            dto.setName("function" + i);
            dto.setLanguagetype(1);
            dto.setDesc("测试函数" + i);
            largeList.add(dto);
        }
        PageInfo<FunctionpublishDto> largePageInfo = new PageInfo<>(largeList);
        largePageInfo.setTotal(1000);
        largePageInfo.setPages(10);

        tableQueryDto.setPageNum(5);
        tableQueryDto.setPageSize(100);

        // Mock方法
        when(functionpublishService.selectFunctionpublishListForScriptEdit(
                any(VarAndFuncForEditDto.class), anyInt(), anyInt())).thenReturn(largePageInfo);

        // 执行测试
        R<PageInfo<FunctionpublishDto>> result = functionpublishController.listFunctionpublishForScriptEdit(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals(largePageInfo, result.getData());
        assertEquals("list.success", result.getMessage());
        assertEquals(1000, result.getData().getTotal());
        assertEquals(100, result.getData().getList().size());
        verify(functionpublishService, times(1)).selectFunctionpublishListForScriptEdit(
                varAndFuncForEditDto, 5, 100);
    }

    @Test
    @DisplayName("测试查询函数库基础发布列表 - 边界页码测试")
    void listFunctionpublishForScriptEdit_boundaryPageNumbers() {
        // 测试第一页
        tableQueryDto.setPageNum(1);
        tableQueryDto.setPageSize(1);

        // Mock方法
        when(functionpublishService.selectFunctionpublishListForScriptEdit(
                any(VarAndFuncForEditDto.class), anyInt(), anyInt())).thenReturn(pageInfo);

        // 执行测试
        R<PageInfo<FunctionpublishDto>> result = functionpublishController.listFunctionpublishForScriptEdit(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals(pageInfo, result.getData());
        assertEquals("list.success", result.getMessage());
        verify(functionpublishService, times(1)).selectFunctionpublishListForScriptEdit(
                varAndFuncForEditDto, 1, 1);
    }
}
