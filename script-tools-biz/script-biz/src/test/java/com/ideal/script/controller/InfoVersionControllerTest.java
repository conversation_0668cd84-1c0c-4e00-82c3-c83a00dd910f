package com.ideal.script.controller;

import com.ideal.common.dto.R;
import com.ideal.script.dto.ScriptInfoDto;
import com.ideal.script.dto.ScriptVersionDto;
import com.ideal.script.service.IInfoVersionService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * InfoVersionController单元测试类
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class InfoVersionControllerTest {

    @Mock
    private IInfoVersionService infoVersionService;

    @InjectMocks
    private InfoVersionController infoVersionController;

    private ScriptVersionDto scriptVersionDto;
    private ScriptInfoDto scriptInfoDto;

    @BeforeEach
    void setUp() {
        // 初始化ScriptVersionDto测试数据
        scriptVersionDto = new ScriptVersionDto();
        scriptVersionDto.setId(1L);
        scriptVersionDto.setInfoUniqueUuid("test-info-uuid");
        scriptVersionDto.setSrcScriptUuid("test-script-uuid");
        scriptVersionDto.setVersion("1.0.0");
        scriptVersionDto.setEditState(1);
        scriptVersionDto.setCreatorId(10L);
        scriptVersionDto.setCreatorName("测试创建人");
        scriptVersionDto.setCreateTime(new Timestamp(System.currentTimeMillis()));

        // 初始化ScriptInfoDto测试数据
        scriptInfoDto = new ScriptInfoDto();
        scriptInfoDto.setCustomerName("测试客户");
        scriptInfoDto.setInsertedLabels(Arrays.asList("新增标签1", "新增标签2"));
        scriptInfoDto.setRemovedLabels(Arrays.asList("删除标签1"));
        scriptInfoDto.setScriptNameZh("测试脚本中文名");
        scriptInfoDto.setScriptName("test_script_name");
        scriptInfoDto.setScriptType("sh");
        scriptInfoDto.setScriptLabel("测试,脚本");
        scriptInfoDto.setCategoryId(100L);
    }

    @Test
    @DisplayName("获取脚本版本信息列表_正常情况")
    void testGetInfoVersionInfoList_Success() {
        // Setup
        List<ScriptVersionDto> expectedList = Arrays.asList(scriptVersionDto);
        when(infoVersionService.getInfoVersionInfoList(any(Long[].class))).thenReturn(expectedList);

        // Run the test
        R<List<ScriptVersionDto>> result = infoVersionController.getInfoVersionInfoList(new Long[]{1L, 2L});

        // Verify the results
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertNotNull(result.getData());
        assertEquals(1, result.getData().size());
        assertEquals(scriptVersionDto.getId(), result.getData().get(0).getId());
        assertEquals(scriptVersionDto.getVersion(), result.getData().get(0).getVersion());

        verify(infoVersionService).getInfoVersionInfoList(any(Long[].class));
    }

    @Test
    @DisplayName("获取脚本版本信息列表_返回空列表")
    void testGetInfoVersionInfoList_EmptyList() {
        // Setup
        when(infoVersionService.getInfoVersionInfoList(any(Long[].class))).thenReturn(Collections.emptyList());

        // Run the test
        R<List<ScriptVersionDto>> result = infoVersionController.getInfoVersionInfoList(new Long[]{1L});

        // Verify the results
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertNotNull(result.getData());
        assertTrue(result.getData().isEmpty());

        verify(infoVersionService).getInfoVersionInfoList(any(Long[].class));
    }

    @Test
    @DisplayName("获取脚本版本信息列表_服务异常")
    void testGetInfoVersionInfoList_ServiceException() {
        // Setup
        when(infoVersionService.getInfoVersionInfoList(any(Long[].class)))
                .thenThrow(new RuntimeException("服务异常"));

        // Run the test & Verify
        assertThrows(RuntimeException.class, () -> {
            infoVersionController.getInfoVersionInfoList(new Long[]{1L});
        });

        verify(infoVersionService).getInfoVersionInfoList(any(Long[].class));
    }

    @Test
    @DisplayName("根据版本UUID获取脚本信息_正常情况")
    void testGetInfoByVersionUuid_Success() {
        // Setup
        String versionUuid = "test-version-uuid";
        when(infoVersionService.getInfoByVersionUuid(versionUuid)).thenReturn(scriptInfoDto);

        // Run the test
        R<ScriptInfoDto> result = infoVersionController.getInfoByVersionUuid(versionUuid);

        // Verify the results
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertNotNull(result.getData());
        assertEquals(scriptInfoDto.getScriptNameZh(), result.getData().getScriptNameZh());
        assertEquals(scriptInfoDto.getScriptName(), result.getData().getScriptName());
        assertEquals(scriptInfoDto.getCustomerName(), result.getData().getCustomerName());

        verify(infoVersionService).getInfoByVersionUuid(versionUuid);
    }

    @Test
    @DisplayName("根据版本UUID获取脚本信息_版本不存在")
    void testGetInfoByVersionUuid_NotFound() {
        // Setup
        String versionUuid = "non-existent-uuid";
        when(infoVersionService.getInfoByVersionUuid(versionUuid)).thenReturn(null);

        // Run the test
        R<ScriptInfoDto> result = infoVersionController.getInfoByVersionUuid(versionUuid);

        // Verify the results
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertNull(result.getData());

        verify(infoVersionService).getInfoByVersionUuid(versionUuid);
    }

    @Test
    @DisplayName("根据版本UUID获取脚本信息_服务异常")
    void testGetInfoByVersionUuid_ServiceException() {
        // Setup
        String versionUuid = "test-version-uuid";
        when(infoVersionService.getInfoByVersionUuid(versionUuid))
                .thenThrow(new RuntimeException("数据库连接异常"));

        // Run the test
        R<ScriptInfoDto> result = infoVersionController.getInfoByVersionUuid(versionUuid);

        // Verify the results - 控制器捕获异常并返回失败响应
        assertNotNull(result);
        assertEquals("10602", result.getCode());
        assertEquals("数据库连接异常", result.getMessage());
        assertNull(result.getData());

        verify(infoVersionService).getInfoByVersionUuid(versionUuid);
    }

    @Test
    @DisplayName("根据ID数组获取版本ID列表_正常情况")
    void testGetVersionIdByIds_Success() {
        // Setup
        Long[] ids = {1L, 2L, 3L};
        List<Long> expectedVersionIds = Arrays.asList(101L, 102L, 103L);
        when(infoVersionService.getVersionIdByIds(any(Long[].class))).thenReturn(expectedVersionIds);

        // Run the test
        R<List<Long>> result = infoVersionController.getVersionIdByIds(ids);

        // Verify the results
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertNotNull(result.getData());
        assertEquals(3, result.getData().size());
        assertEquals(expectedVersionIds, result.getData());

        verify(infoVersionService).getVersionIdByIds(any(Long[].class));
    }

    @Test
    @DisplayName("根据ID数组获取版本ID列表_返回空列表")
    void testGetVersionIdByIds_EmptyList() {
        // Setup
        Long[] ids = {999L};
        when(infoVersionService.getVersionIdByIds(any(Long[].class))).thenReturn(Collections.emptyList());

        // Run the test
        R<List<Long>> result = infoVersionController.getVersionIdByIds(ids);

        // Verify the results
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertNotNull(result.getData());
        assertTrue(result.getData().isEmpty());

        verify(infoVersionService).getVersionIdByIds(any(Long[].class));
    }

    @Test
    @DisplayName("根据ID数组获取版本ID列表_服务异常")
    void testGetVersionIdByIds_ServiceException() {
        // Setup
        Long[] ids = {1L, 2L};
        when(infoVersionService.getVersionIdByIds(any(Long[].class)))
                .thenThrow(new RuntimeException("数据库查询异常"));

        // Run the test
        R<List<Long>> result = infoVersionController.getVersionIdByIds(ids);

        // Verify the results - 控制器捕获异常并返回失败响应
        assertNotNull(result);
        assertEquals("10602", result.getCode());
        assertEquals("数据库查询异常", result.getMessage());
        assertNull(result.getData());

        verify(infoVersionService).getVersionIdByIds(any(Long[].class));
    }

    @Test
    @DisplayName("根据版本ID获取任务数量_正常情况")
    void testGetTaskCountByVersionId_Success() {
        // Setup
        Long versionId = 100L;
        Integer expectedCount = 5;
        when(infoVersionService.getTaskCountByVersionId(versionId)).thenReturn(expectedCount);

        // Run the test
        R<Integer> result = infoVersionController.getTaskCountByVersionId(versionId);

        // Verify the results
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertNotNull(result.getData());
        assertEquals(expectedCount, result.getData());

        verify(infoVersionService).getTaskCountByVersionId(versionId);
    }

    @Test
    @DisplayName("根据版本ID获取任务数量_版本不存在")
    void testGetTaskCountByVersionId_VersionNotFound() {
        // Setup
        Long versionId = 999L;
        when(infoVersionService.getTaskCountByVersionId(versionId)).thenReturn(0);

        // Run the test
        R<Integer> result = infoVersionController.getTaskCountByVersionId(versionId);

        // Verify the results
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertNotNull(result.getData());
        assertEquals(0, result.getData());

        verify(infoVersionService).getTaskCountByVersionId(versionId);
    }

    @Test
    @DisplayName("根据版本ID获取任务数量_服务异常")
    void testGetTaskCountByVersionId_ServiceException() {
        // Setup
        Long versionId = 100L;
        when(infoVersionService.getTaskCountByVersionId(versionId))
                .thenThrow(new RuntimeException("统计查询异常"));

        // Run the test
        R<Integer> result = infoVersionController.getTaskCountByVersionId(versionId);

        // Verify the results - 控制器捕获异常并返回失败响应
        assertNotNull(result);
        assertEquals("10602", result.getCode());
        assertEquals("统计查询异常", result.getMessage());
        assertNull(result.getData());

        verify(infoVersionService).getTaskCountByVersionId(versionId);
    }

    @Test
    @DisplayName("根据源脚本UUID获取版本信息ID_正常情况")
    void testGetScriptVersionInfoIds_Success() {
        // Setup
        String srcVersionUuid = "test-src-version-uuid";
        Long expectedId = 100L;
        when(infoVersionService.selectIdBySrcScriptUuid(srcVersionUuid)).thenReturn(expectedId);

        // Run the test
        R<Long> result = infoVersionController.getScriptVersionInfoIds(srcVersionUuid);

        // Verify the results
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertNotNull(result.getData());
        assertEquals(expectedId, result.getData());

        verify(infoVersionService).selectIdBySrcScriptUuid(srcVersionUuid);
    }

    @Test
    @DisplayName("根据源脚本UUID获取版本信息ID_UUID不存在")
    void testGetScriptVersionInfoIds_UuidNotFound() {
        // Setup
        String srcVersionUuid = "non-existent-uuid";
        when(infoVersionService.selectIdBySrcScriptUuid(srcVersionUuid)).thenReturn(null);

        // Run the test
        R<Long> result = infoVersionController.getScriptVersionInfoIds(srcVersionUuid);

        // Verify the results
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertNull(result.getData());

        verify(infoVersionService).selectIdBySrcScriptUuid(srcVersionUuid);
    }

    @Test
    @DisplayName("根据源脚本UUID获取版本信息ID_服务异常")
    void testGetScriptVersionInfoIds_ServiceException() {
        // Setup
        String srcVersionUuid = "test-src-version-uuid";
        when(infoVersionService.selectIdBySrcScriptUuid(srcVersionUuid))
                .thenThrow(new RuntimeException("数据库查询异常"));

        // Run the test & Verify
        assertThrows(RuntimeException.class, () -> {
            infoVersionController.getScriptVersionInfoIds(srcVersionUuid);
        });

        verify(infoVersionService).selectIdBySrcScriptUuid(srcVersionUuid);
    }
}
