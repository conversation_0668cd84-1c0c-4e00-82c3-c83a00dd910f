package com.ideal.script.service.impl;

import com.ideal.script.dto.ScriptVersionDto;
import com.ideal.script.model.dto.AuditRelationDto;
import com.ideal.script.model.dto.ScriptExecAuditDto;
import com.ideal.script.model.dto.TaskDto;
import com.ideal.script.service.IAuditRelationService;
import com.ideal.script.service.IInfoVersionService;
import com.ideal.script.service.ITaskService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TaskApplySourceTest {

    @Mock
    private IAuditRelationService mockAuditRelationService;
    @Mock
    private IInfoVersionService mockInfoVersionService;
    @Mock
    private ITaskService mockTaskService;

    private TaskApplySource taskApplySourceUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        taskApplySourceUnderTest = new TaskApplySource(mockAuditRelationService, mockInfoVersionService,
                mockTaskService);
    }

    @Test
    void testPreHandle() {
        taskApplySourceUnderTest.preHandle(new ScriptExecAuditDto());

    }

    @Test
    void testGetRelationId() throws Exception {
        // Setup
        final ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        scriptExecAuditDto.setInvokeId("invokeId");
        scriptExecAuditDto.setWorkOrderNum("workOrderNum");
        scriptExecAuditDto.setScriptInfoVersionId(0L);
        scriptExecAuditDto.setAuditUser("auditUser");
        scriptExecAuditDto.setAuditUserId(0L);

        final TaskDto taskInfo = new TaskDto();
        taskInfo.setScriptTaskSource(0);
        taskInfo.setStartType(0);
        taskInfo.setReadyToExecute(0);
        taskInfo.setId(0L);
        taskInfo.setSrcScriptUuid("srcScriptUuid");

        // Run the test
        final Long result = taskApplySourceUnderTest.getRelationId(scriptExecAuditDto, taskInfo, "srcScriptUuid");

        // Verify the results
        verify(mockAuditRelationService).insertAuditRelation(any(AuditRelationDto.class));
    }

    @Test
    void testBindTaskId() {
        taskApplySourceUnderTest.bindTaskId(new ScriptExecAuditDto());
    }

    @Test
    void testIsInWhiteList() {
        // Setup
        final ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        scriptExecAuditDto.setInvokeId("invokeId");
        scriptExecAuditDto.setWorkOrderNum("workOrderNum");
        scriptExecAuditDto.setScriptInfoVersionId(0L);
        scriptExecAuditDto.setAuditUser("auditUser");
        scriptExecAuditDto.setAuditUserId(0L);

        // Configure IInfoVersionService.selectInfoVersionById(...).
        final ScriptVersionDto scriptVersionDto = new ScriptVersionDto();
        scriptVersionDto.setId(0L);
        scriptVersionDto.setInfoUniqueUuid("infoUniqueUuid");
        scriptVersionDto.setSrcScriptUuid("srcScriptUuid");
        scriptVersionDto.setVersion("version");
        scriptVersionDto.setEditState(0);
        when(mockInfoVersionService.selectInfoVersionById(0L)).thenReturn(scriptVersionDto);

        when(mockInfoVersionService.isInWhiteList(0L)).thenReturn(false);

        // Run the test
        final boolean result = taskApplySourceUnderTest.isInWhiteList(scriptExecAuditDto);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    void testIsInWhiteList_IInfoVersionServiceIsInWhiteListReturnsTrue() {
        // Setup
        final ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        scriptExecAuditDto.setInvokeId("invokeId");
        scriptExecAuditDto.setWorkOrderNum("workOrderNum");
        scriptExecAuditDto.setScriptInfoVersionId(0L);
        scriptExecAuditDto.setAuditUser("auditUser");
        scriptExecAuditDto.setAuditUserId(0L);

        // Configure IInfoVersionService.selectInfoVersionById(...).
        final ScriptVersionDto scriptVersionDto = new ScriptVersionDto();
        scriptVersionDto.setId(0L);
        scriptVersionDto.setInfoUniqueUuid("infoUniqueUuid");
        scriptVersionDto.setSrcScriptUuid("srcScriptUuid");
        scriptVersionDto.setVersion("version");
        scriptVersionDto.setEditState(0);
        when(mockInfoVersionService.selectInfoVersionById(0L)).thenReturn(scriptVersionDto);

        when(mockInfoVersionService.isInWhiteList(0L)).thenReturn(true);

        // Run the test
        final boolean result = taskApplySourceUnderTest.isInWhiteList(scriptExecAuditDto);

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    void testSaveOrUpdateTask() throws Exception {
        // Setup
        final ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        scriptExecAuditDto.setInvokeId("invokeId");
        scriptExecAuditDto.setWorkOrderNum("workOrderNum");
        scriptExecAuditDto.setScriptInfoVersionId(0L);
        scriptExecAuditDto.setAuditUser("auditUser");
        scriptExecAuditDto.setAuditUserId(0L);

        final TaskDto taskDto = new TaskDto();
        taskDto.setScriptTaskSource(0);
        taskDto.setStartType(0);
        taskDto.setReadyToExecute(0);
        taskDto.setId(0L);
        taskDto.setSrcScriptUuid("srcScriptUuid");

        // Run the test
        taskApplySourceUnderTest.saveOrUpdateTask(scriptExecAuditDto, taskDto);

        // Verify the results
        verify(mockTaskService).insertTask(any(TaskDto.class));
    }

    @Test
    void testPreHandleAttachment() {
        taskApplySourceUnderTest.preHandleAttachment(0L);
    }
}
