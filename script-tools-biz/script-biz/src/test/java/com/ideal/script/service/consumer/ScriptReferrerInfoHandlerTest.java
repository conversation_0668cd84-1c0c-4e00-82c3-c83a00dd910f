package com.ideal.script.service.consumer;

import com.ideal.script.exception.ScriptException;
import com.ideal.script.service.resulthandler.IScriptReferrerInfoHandlerService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * ScriptReferrerInfoHandler单元测试类
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class ScriptReferrerInfoHandlerTest {

    @Mock
    private IScriptReferrerInfoHandlerService scriptReferrerInfoHandlerService;

    @InjectMocks
    private ScriptReferrerInfoHandler scriptReferrerInfoHandler;

    private String testJsonMessage;

    @BeforeEach
    void setUp() {
        // 初始化测试JSON消息
        testJsonMessage = "[{\"bizType\":\"SAVE\",\"referrerBizId\":\"test-biz-123\",\"scriptUuid\":\"script-uuid-456\",\"referrerModuleName\":\"测试模块\"}]";
    }

    @Test
    @DisplayName("处理String消息_正常情况")
    void testNotice_WithStringMessage_Success() throws ScriptException {
        // Setup
        doNothing().when(scriptReferrerInfoHandlerService).scriptReferrerInfoHandler(anyString());

        // Run the test
        scriptReferrerInfoHandler.notice(testJsonMessage);

        // Verify the results
        verify(scriptReferrerInfoHandlerService).scriptReferrerInfoHandler(testJsonMessage);
    }

    @Test
    @DisplayName("处理String消息_服务抛出异常_异常被捕获")
    void testNotice_ServiceThrowsException_ExceptionCaught() throws ScriptException {
        // Setup
        doThrow(new ScriptException("处理脚本引用信息异常"))
                .when(scriptReferrerInfoHandlerService).scriptReferrerInfoHandler(anyString());

        // Run the test - 异常被捕获，不会抛出
        scriptReferrerInfoHandler.notice(testJsonMessage);

        // Verify the results
        verify(scriptReferrerInfoHandlerService).scriptReferrerInfoHandler(testJsonMessage);
    }

    @Test
    @DisplayName("处理空字符串消息_正常情况")
    void testNotice_WithEmptyString_Success() throws ScriptException {
        // Setup
        String emptyMessage = "";
        doNothing().when(scriptReferrerInfoHandlerService).scriptReferrerInfoHandler(anyString());

        // Run the test
        scriptReferrerInfoHandler.notice(emptyMessage);

        // Verify the results
        verify(scriptReferrerInfoHandlerService).scriptReferrerInfoHandler(emptyMessage);
    }

    @Test
    @DisplayName("处理null消息_类型转换异常被捕获")
    void testNotice_WithNullMessage_ClassCastExceptionCaught() throws ScriptException {
        // Run the test - 类型转换异常被捕获
        scriptReferrerInfoHandler.notice(null);

        // Verify the results - 不应该调用服务方法，因为会抛出ClassCastException
//        verify(scriptReferrerInfoHandlerService, never()).scriptReferrerInfoHandler(anyString());
    }

    @Test
    @DisplayName("处理非String消息_类型转换异常被捕获")
    void testNotice_WithNonStringMessage_ClassCastExceptionCaught() throws ScriptException {
        // Setup
        Integer nonStringMessage = 123;

        // Run the test - 类型转换异常被捕获
        scriptReferrerInfoHandler.notice(nonStringMessage);

        // Verify the results - 不应该调用服务方法，因为会抛出ClassCastException
//        verify(scriptReferrerInfoHandlerService, never()).scriptReferrerInfoHandler(anyString());
    }

    @Test
    @DisplayName("处理复杂JSON消息_正常情况")
    void testNotice_WithComplexJsonMessage_Success() throws ScriptException {
        // Setup
        String complexJsonMessage = "[{\"bizType\":\"SAVE\",\"referrerBizId\":\"complex-biz-789\",\"scriptUuid\":\"script-uuid-abc\",\"referrerModuleName\":\"复杂测试模块\",\"createTime\":\"2025-07-15T10:00:00\"}]";
        doNothing().when(scriptReferrerInfoHandlerService).scriptReferrerInfoHandler(anyString());

        // Run the test
        scriptReferrerInfoHandler.notice(complexJsonMessage);

        // Verify the results
        verify(scriptReferrerInfoHandlerService).scriptReferrerInfoHandler(complexJsonMessage);
    }

    @Test
    @DisplayName("处理删除类型JSON消息_正常情况")
    void testNotice_WithDeleteTypeMessage_Success() throws ScriptException {
        // Setup
        String deleteJsonMessage = "[{\"bizType\":\"DELETE\",\"referrerBizId\":\"delete-biz-456\",\"scriptUuid\":\"script-uuid-def\",\"referrerModuleName\":\"删除测试模块\"}]";
        doNothing().when(scriptReferrerInfoHandlerService).scriptReferrerInfoHandler(anyString());

        // Run the test
        scriptReferrerInfoHandler.notice(deleteJsonMessage);

        // Verify the results
        verify(scriptReferrerInfoHandlerService).scriptReferrerInfoHandler(deleteJsonMessage);
    }

    @Test
    @DisplayName("处理无效JSON格式消息_服务层处理异常")
    void testNotice_WithInvalidJsonMessage_ServiceHandlesException() throws ScriptException {
        // Setup
        String invalidJsonMessage = "{invalid json format";
        doThrow(new ScriptException("JSON解析异常"))
                .when(scriptReferrerInfoHandlerService).scriptReferrerInfoHandler(anyString());

        // Run the test - 异常被捕获
        scriptReferrerInfoHandler.notice(invalidJsonMessage);

        // Verify the results
        verify(scriptReferrerInfoHandlerService).scriptReferrerInfoHandler(invalidJsonMessage);
    }

    @Test
    @DisplayName("处理多条记录JSON消息_正常情况")
    void testNotice_WithMultipleRecordsMessage_Success() throws ScriptException {
        // Setup
        String multipleRecordsMessage = "[{\"bizType\":\"SAVE\",\"referrerBizId\":\"biz-1\",\"scriptUuid\":\"script-1\",\"referrerModuleName\":\"模块1\"},{\"bizType\":\"SAVE\",\"referrerBizId\":\"biz-2\",\"scriptUuid\":\"script-2\",\"referrerModuleName\":\"模块2\"}]";
        doNothing().when(scriptReferrerInfoHandlerService).scriptReferrerInfoHandler(anyString());

        // Run the test
        scriptReferrerInfoHandler.notice(multipleRecordsMessage);

        // Verify the results
        verify(scriptReferrerInfoHandlerService).scriptReferrerInfoHandler(multipleRecordsMessage);
    }

    @Test
    @DisplayName("处理运行时异常_异常被捕获并记录")
    void testNotice_WithRuntimeException_ExceptionCaughtAndLogged() throws ScriptException {
        // Setup
        doThrow(new RuntimeException("运行时异常"))
                .when(scriptReferrerInfoHandlerService).scriptReferrerInfoHandler(anyString());

        // Run the test - 异常被捕获
        scriptReferrerInfoHandler.notice(testJsonMessage);

        // Verify the results
        verify(scriptReferrerInfoHandlerService).scriptReferrerInfoHandler(testJsonMessage);
    }

    @Test
    @DisplayName("处理包含特殊字符的消息_正常情况")
    void testNotice_WithSpecialCharactersMessage_Success() throws ScriptException {
        // Setup
        String specialCharsMessage = "[{\"bizType\":\"SAVE\",\"referrerBizId\":\"测试-业务-ID\",\"scriptUuid\":\"脚本-UUID\",\"referrerModuleName\":\"包含特殊字符的模块名称@#$%\"}]";
        doNothing().when(scriptReferrerInfoHandlerService).scriptReferrerInfoHandler(anyString());

        // Run the test
        scriptReferrerInfoHandler.notice(specialCharsMessage);

        // Verify the results
        verify(scriptReferrerInfoHandlerService).scriptReferrerInfoHandler(specialCharsMessage);
    }

    @Test
    @DisplayName("处理长文本消息_正常情况")
    void testNotice_WithLongTextMessage_Success() throws ScriptException {
        // Setup
        StringBuilder longMessageBuilder = new StringBuilder("[{\"bizType\":\"SAVE\",\"referrerBizId\":\"long-biz-id\",\"scriptUuid\":\"long-script-uuid\",\"referrerModuleName\":\"");
        for (int i = 0; i < 100; i++) {
            longMessageBuilder.append("长文本模块名称").append(i);
        }
        longMessageBuilder.append("\"}]");
        String longMessage = longMessageBuilder.toString();
        
        doNothing().when(scriptReferrerInfoHandlerService).scriptReferrerInfoHandler(anyString());

        // Run the test
        scriptReferrerInfoHandler.notice(longMessage);

        // Verify the results
        verify(scriptReferrerInfoHandlerService).scriptReferrerInfoHandler(longMessage);
    }

    @Test
    @DisplayName("处理Object类型消息_强制类型转换")
    void testNotice_WithObjectTypeMessage_ForceCasting() throws ScriptException {
        // Setup
        Object objectMessage = testJsonMessage; // Object类型但实际是String
        doNothing().when(scriptReferrerInfoHandlerService).scriptReferrerInfoHandler(anyString());

        // Run the test
        scriptReferrerInfoHandler.notice(objectMessage);

        // Verify the results
        verify(scriptReferrerInfoHandlerService).scriptReferrerInfoHandler(testJsonMessage);
    }
}
