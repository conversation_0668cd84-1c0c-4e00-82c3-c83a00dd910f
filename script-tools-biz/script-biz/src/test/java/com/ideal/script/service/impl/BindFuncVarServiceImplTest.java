package com.ideal.script.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ideal.script.dto.*;
import com.ideal.script.mapper.BindFuncVarMapper;
import com.ideal.script.model.entity.BindFuncVar;
import com.ideal.script.model.entity.InfoVersion;
import com.ideal.script.model.entity.InfoVersionText;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BindFuncVarServiceImplTest {

    @Mock
    private BindFuncVarMapper mockBindFuncVarMapper;

    @InjectMocks
    private BindFuncVarServiceImpl bindFuncVarServiceImplUnderTest;


    @Test
    void testSelectBindFuncVarById() {
        // Setup
        // Configure BindFuncVarMapper.selectBindFuncVarById(...).
        final BindFuncVar bindFuncVar = new BindFuncVar();
        bindFuncVar.setId(1L);
        bindFuncVar.setSrcScriptUuid("srcScriptUuid");
        bindFuncVar.setBindObjId(1L);
        bindFuncVar.setBindType(1);
        bindFuncVar.setObjName("objName");
        when(mockBindFuncVarMapper.selectBindFuncVarById(1L)).thenReturn(bindFuncVar);

        // Run the test
        final BindFuncVarDto result = bindFuncVarServiceImplUnderTest.selectBindFuncVarById(1L);
        assertNotNull(result);
        System.out.println(result);
        // Verify the results
        Mockito.verify(mockBindFuncVarMapper, times(1)).selectBindFuncVarById(any(Long.class));
    }

    @Test
    void testSelectBindFuncVarList() {
        // Setup
        final BindFuncVarDto bindFuncVarDto = new BindFuncVarDto();
        bindFuncVarDto.setId(1L);
        bindFuncVarDto.setSrcScriptUuid("srcScriptUuid");
        bindFuncVarDto.setBindObjId(1L);
        bindFuncVarDto.setBindType(1);
        bindFuncVarDto.setObjName("objName");

        // Configure BindFuncVarMapper.selectBindFuncVarList(...).
        final BindFuncVar bindFuncVar = new BindFuncVar();
        bindFuncVar.setId(1L);
        bindFuncVar.setSrcScriptUuid("srcScriptUuid");
        bindFuncVar.setBindObjId(1L);
        bindFuncVar.setBindType(1);
        bindFuncVar.setObjName("objName");
        Page<BindFuncVar> page = new Page<>();
        page.add(bindFuncVar);
        when(mockBindFuncVarMapper.selectBindFuncVarList(any(BindFuncVar.class))).thenReturn(page);

        // Run the test
        final PageInfo<BindFuncVarDto> result = bindFuncVarServiceImplUnderTest.selectBindFuncVarList(bindFuncVarDto, 1,
                50);
        assertNotNull(result);
        System.out.println(result);
        // Verify the results
    }


    @Test
    void testInsertBindFuncVar() {
        // Setup
        final BindFuncVarDto bindFuncVarDto = new BindFuncVarDto();
        bindFuncVarDto.setId(1L);
        bindFuncVarDto.setSrcScriptUuid("srcScriptUuid");
        bindFuncVarDto.setBindObjId(1L);
        bindFuncVarDto.setBindType(1);
        bindFuncVarDto.setObjName("objName");

        // Run the test
        bindFuncVarServiceImplUnderTest.insertBindFuncVar(bindFuncVarDto);

        // Verify the results
        verify(mockBindFuncVarMapper).insertBindFuncVar(any(BindFuncVar.class));
    }

    @Test
    void testUpdateBindFuncVar() {
        // Setup
        final BindFuncVarDto bindFuncVarDto = new BindFuncVarDto();
        bindFuncVarDto.setId(1L);
        bindFuncVarDto.setSrcScriptUuid("srcScriptUuid");
        bindFuncVarDto.setBindObjId(1L);
        bindFuncVarDto.setBindType(1);
        bindFuncVarDto.setObjName("objName");

        // Run the test
        bindFuncVarServiceImplUnderTest.updateBindFuncVar(bindFuncVarDto);

        // Verify the results
        verify(mockBindFuncVarMapper).updateBindFuncVar(any(BindFuncVar.class));
    }

    @Test
    void testDeleteBindFuncVarByIds() {
        // Setup
        // Run the test
        bindFuncVarServiceImplUnderTest.deleteBindFuncVarByIds(new Long[]{1L});

        // Verify the results
        verify(mockBindFuncVarMapper).deleteBindFuncVarByIds(any(Long[].class));
    }

    @Test
    void testDeleteBindFuncVarById() {
        // Setup
        when(mockBindFuncVarMapper.deleteBindFuncVarById(1L)).thenReturn(1);

        // Run the test
        final int result = bindFuncVarServiceImplUnderTest.deleteBindFuncVarById(1L);

        // Verify the results
        assertThat(result).isEqualTo(1);
    }

    @Test
    void testCreateBindFuncVars() {
        // Setup
        final ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        final ScriptVersionDto scriptVersionDto = new ScriptVersionDto();
        final BindFuncVarDto bindFuncVarDto = new BindFuncVarDto();
        bindFuncVarDto.setBindObjId(1L);
        bindFuncVarDto.setObjName("objName");
        scriptVersionDto.setVariableList(new BindFuncVarDto[]{bindFuncVarDto});
        final BindFuncVarDto bindFuncVarDto1 = new BindFuncVarDto();
        bindFuncVarDto1.setBindObjId(1L);
        bindFuncVarDto1.setObjName("objName");
        scriptVersionDto.setFunctionList(new BindFuncVarDto[]{bindFuncVarDto1});
        scriptInfoDto.setScriptVersionDto(scriptVersionDto);
        scriptVersionDto.setSrcScriptUuid("srcScriptUuid");
        scriptInfoDto.setScriptVersionDto(scriptVersionDto);

        // Run the test
        bindFuncVarServiceImplUnderTest.createBindFuncVars(scriptInfoDto);

        // Verify the results
        verify(mockBindFuncVarMapper,times(2)).insertBindFuncVar(any(BindFuncVar.class));
    }

    @Test
    void testGetBindFuncVarDtos() {
        // Setup
        final InfoVersionText infoVersionText = new InfoVersionText();
        infoVersionText.setId(1L);
        infoVersionText.setSrcScriptUuid("srcScriptUuid");
        infoVersionText.setContent("content");
        infoVersionText.setCreatorId(1L);
        infoVersionText.setCreatorName("creatorName");

        // Configure BindFuncVarMapper.selectBindFuncVarList(...).
        final BindFuncVar bindFuncVar = new BindFuncVar();
        bindFuncVar.setId(1L);
        bindFuncVar.setSrcScriptUuid("srcScriptUuid");
        bindFuncVar.setBindObjId(1L);
        bindFuncVar.setBindType(1);
        bindFuncVar.setObjName("objName");
        final List<BindFuncVar> bindFuncVars = Collections.singletonList(bindFuncVar);
        when(mockBindFuncVarMapper.selectBindFuncVarList(any(BindFuncVar.class))).thenReturn(bindFuncVars);

        // Run the test
        final BindFuncVarDto[] result = bindFuncVarServiceImplUnderTest.getBindFuncVarDtos(new InfoVersion(), 1);
        assertNotNull(result);
        // Verify the results
        verify(mockBindFuncVarMapper).selectBindFuncVarList(any(BindFuncVar.class));
    }


}
