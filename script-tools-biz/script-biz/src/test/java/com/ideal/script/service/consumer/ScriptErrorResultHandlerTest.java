package com.ideal.script.service.consumer;

import com.ideal.script.exception.AgentGatewayError;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.service.resulthandler.IScriptResultHandlerService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * ScriptErrorResultHandler单元测试类
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class ScriptErrorResultHandlerTest {

    @Mock
    private IScriptResultHandlerService scriptResultHandlerService;

    @InjectMocks
    private ScriptErrorResultHandler scriptErrorResultHandler;

    private AgentGatewayError agentGatewayError;

    @BeforeEach
    void setUp() {
        // 初始化AgentGatewayError测试数据
        agentGatewayError = new AgentGatewayError();
        agentGatewayError.setBizId("test-biz-id-123");
        agentGatewayError.setTaskId(100L);
        agentGatewayError.setMessage("测试错误消息");
        agentGatewayError.setErrorType("AGENT_ERROR");
    }

    @Test
    @DisplayName("处理AgentGatewayError消息_正常情况")
    void testNotice_WithAgentGatewayError_Success() throws ScriptException {
        // Setup
        doNothing().when(scriptResultHandlerService)
                .handleScriptErrorResult(anyString(), anyString(), anyLong());

        // Run the test
        scriptErrorResultHandler.notice(agentGatewayError);

        // Verify the results
        verify(scriptResultHandlerService).handleScriptErrorResult(
                "测试错误消息", 
                "test-biz-id-123", 
                100L
        );
    }

    @Test
    @DisplayName("处理AgentGatewayError消息_服务异常")
    void testNotice_WithAgentGatewayError_ServiceException() throws ScriptException {
        // Setup
        doThrow(new ScriptException("处理错误结果异常"))
                .when(scriptResultHandlerService)
                .handleScriptErrorResult(anyString(), anyString(), anyLong());

        // Run the test - 异常被捕获，不会抛出
        scriptErrorResultHandler.notice(agentGatewayError);

        // Verify the results
        verify(scriptResultHandlerService).handleScriptErrorResult(
                "测试错误消息", 
                "test-biz-id-123", 
                100L
        );
    }

    @Test
    @DisplayName("处理非AgentGatewayError消息_抛出异常")
    void testNotice_WithInvalidMessageType_ThrowsException() throws ScriptException {
        // Setup
        String invalidMessage = "invalid message type";

        // Run the test - 异常被捕获，不会抛出
        scriptErrorResultHandler.notice(invalidMessage);

        // Verify the results - 不应该调用服务方法
        verify(scriptResultHandlerService, never())
                .handleScriptErrorResult(anyString(), anyString(), anyLong());
    }

    @Test
    @DisplayName("处理null消息_抛出异常")
    void testNotice_WithNullMessage_ThrowsException() throws ScriptException {
        // Run the test - 异常被捕获，不会抛出
        scriptErrorResultHandler.notice(null);

        // Verify the results - 不应该调用服务方法
        verify(scriptResultHandlerService, never())
                .handleScriptErrorResult(anyString(), anyString(), anyLong());
    }

    @Test
    @DisplayName("处理AgentGatewayError消息_bizId为null")
    void testNotice_WithNullBizId_ThrowsException() throws ScriptException {
        // Setup
        agentGatewayError.setBizId(null);

        // Run the test - 异常被捕获，不会抛出
        scriptErrorResultHandler.notice(agentGatewayError);

        // Verify the results - 不应该调用服务方法，因为会在Optional.of()处抛出异常
        verify(scriptResultHandlerService, never())
                .handleScriptErrorResult(anyString(), anyString(), anyLong());
    }

    @Test
    @DisplayName("处理AgentGatewayError消息_taskId为null")
    void testNotice_WithNullTaskId_ThrowsException() throws ScriptException {
        // Setup
        agentGatewayError.setTaskId(null);

        // Run the test - 异常被捕获，不会抛出
        scriptErrorResultHandler.notice(agentGatewayError);

        // Verify the results - 不应该调用服务方法，因为会在Optional.of()处抛出异常
        verify(scriptResultHandlerService, never())
                .handleScriptErrorResult(anyString(), anyString(), anyLong());
    }

    @Test
    @DisplayName("处理AgentGatewayError消息_message为null")
    void testNotice_WithNullMessage_HandlesGracefully() throws ScriptException {
        // Setup
        agentGatewayError.setMessage(null);

        // Run the test - 异常被捕获，不会抛出
        scriptErrorResultHandler.notice(agentGatewayError);

        // Verify the results - 不应该调用服务方法，因为会在Optional.of()处抛出异常
        verify(scriptResultHandlerService, never())
                .handleScriptErrorResult(anyString(), anyString(), anyLong());
    }

    @Test
    @DisplayName("处理AgentGatewayError消息_完整数据验证")
    void testNotice_WithCompleteData_VerifyAllParameters() throws ScriptException {
        // Setup
        agentGatewayError.setBizId("agent-script-error-456");
        agentGatewayError.setTaskId(789L);
        agentGatewayError.setMessage("详细错误信息");
        agentGatewayError.setErrorType("CONNECTION_ERROR");

        doNothing().when(scriptResultHandlerService)
                .handleScriptErrorResult(anyString(), anyString(), anyLong());

        // Run the test
        scriptErrorResultHandler.notice(agentGatewayError);

        // Verify the results - 验证传递的参数是否正确
        verify(scriptResultHandlerService).handleScriptErrorResult(
                "详细错误信息", 
                "agent-script-error-456", 
                789L
        );
    }
}
