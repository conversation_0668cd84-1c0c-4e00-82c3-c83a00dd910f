package com.ideal.script.service.impl;

import com.ideal.script.mapper.TaskRuntimeStdoutMapper;
import com.ideal.script.model.bean.TaskHandleParam;
import com.ideal.script.model.dto.TaskRuntimeDto;
import com.ideal.script.model.entity.TaskRuntimeStdout;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class TaskRuntimeStdoutServiceImplTest {

    @Mock
    private TaskRuntimeStdoutMapper mockTaskRuntimeStdoutMapper;

    @InjectMocks
    private TaskRuntimeStdoutServiceImpl taskRuntimeStdoutServiceImplUnderTest;
    // 使用 @Captor 注解可以更简洁地初始化 ArgumentCaptor
    @Captor
    private ArgumentCaptor<TaskRuntimeStdout> stdoutCaptor;


    @ParameterizedTest
    @ValueSource(strings = {"agent-script-retry-123", "agent-script-start-456"})
    void testSaveRuntimeStdoutStdError(String bizId) {
        // Setup
        final TaskHandleParam taskHandleParam = new TaskHandleParam();
        taskHandleParam.setIlastline("lastLine");
        taskHandleParam.setIstdout("stdout");
        taskHandleParam.setIstderror("stdError");

        final TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setRetry(false);
        taskRuntimeDto.setScriptTaskIpsId(0L);
        taskRuntimeDto.setAgentTaskId(0L);
        taskRuntimeDto.setId(99L);
        taskRuntimeDto.setTaskInstanceId(0L);
        taskRuntimeDto.setBizId(bizId);
        // Run the test
        taskRuntimeStdoutServiceImplUnderTest.saveRuntimeStdoutStdError(taskHandleParam, bizId, taskRuntimeDto);

        if (bizId.contains("retry")) {
            // 验证 update 方法被调用，且 insert 方法未被调用
            verify(mockTaskRuntimeStdoutMapper).updateByRunTimeId(stdoutCaptor.capture());
            verify(mockTaskRuntimeStdoutMapper, never()).insert(any(TaskRuntimeStdout.class));

        } else if (bizId.contains("start")) {
            // 验证 insert 方法被调用，且 update 方法未被调用
            verify(mockTaskRuntimeStdoutMapper).insert(stdoutCaptor.capture());
            verify(mockTaskRuntimeStdoutMapper, never()).updateByRunTimeId(any(TaskRuntimeStdout.class));

        }
    }
}
