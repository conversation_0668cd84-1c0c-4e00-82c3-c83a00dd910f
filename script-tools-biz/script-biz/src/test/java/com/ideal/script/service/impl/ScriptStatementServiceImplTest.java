package com.ideal.script.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ideal.script.mapper.StatementMapper;
import com.ideal.script.model.bean.StatementBean;
import com.ideal.script.model.dto.StatementDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletResponse;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ScriptStatementServiceImplTest {

    @Mock
    private StatementMapper mockStatementMapper;
    @Mock
    private CategoryServiceImpl mockCategoryService;

    private ScriptStatementServiceImpl scriptStatementServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        scriptStatementServiceImplUnderTest = new ScriptStatementServiceImpl(mockStatementMapper, mockCategoryService);
    }

    @Test
    void testSelectScriptStatementPage() {
        // Setup
        final StatementDto statementDto = new StatementDto();
        statementDto.setCategoryId(0L);
        statementDto.setId(0L);
        statementDto.setInfoId(0L);
        statementDto.setInfoVersionId(0L);
        statementDto.setScriptNameZh("scriptNameZh");

        when(mockCategoryService.buildCategoryFullPath(0L)).thenReturn("categoryPath");
        when(mockCategoryService.handleCategoryPath("categoryPath")).thenReturn("escapedLikeCategoryPath");

        // Configure StatementMapper.selectScriptStatementPage(...).
        final StatementBean statementBean = new StatementBean();
        statementBean.setEscapedLikeCategoryPath("escapedLikeCategoryPath");
        statementBean.setId(0L);
        statementBean.setInfoId(0L);
        statementBean.setInfoVersionId(0L);
        statementBean.setCategoryPath("categoryPath");
//        final List<StatementBean> statementBeanList = Collections.singletonList(statementBean);
        Page<StatementBean> page = new Page<>();
        page.add(statementBean);
        when(mockStatementMapper.selectScriptStatementPage(any(StatementBean.class))).thenReturn(page);

        // Run the test
        final PageInfo<StatementDto> result = scriptStatementServiceImplUnderTest.selectScriptStatementPage(
                statementDto, 0, 0);

        // Verify the results
        verify(mockStatementMapper).selectScriptStatementPage(any());
    }

    @Test
    void testSelectScriptStatementPage_StatementMapperReturnsNoItems() {
        // Setup
        final StatementDto statementDto = new StatementDto();
        statementDto.setCategoryId(0L);
        statementDto.setId(0L);
        statementDto.setInfoId(0L);
        statementDto.setInfoVersionId(0L);
        statementDto.setScriptNameZh("scriptNameZh");

        when(mockCategoryService.buildCategoryFullPath(0L)).thenReturn("categoryPath");
        when(mockCategoryService.handleCategoryPath("categoryPath")).thenReturn("escapedLikeCategoryPath");
        when(mockStatementMapper.selectScriptStatementPage(any(StatementBean.class)))
                .thenReturn(new Page<>());

        // Run the test
        final PageInfo<StatementDto> result = scriptStatementServiceImplUnderTest.selectScriptStatementPage(
                statementDto, 0, 0);

        // Verify the results
        verify(mockStatementMapper).selectScriptStatementPage(any());
    }

    @Test
    void testExportExcel() {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Configure StatementMapper.selectScriptStatementByIds(...).
        final StatementBean statementBean = new StatementBean();
        statementBean.setEscapedLikeCategoryPath("escapedLikeCategoryPath");
        statementBean.setId(0L);
        statementBean.setInfoId(0L);
        statementBean.setInfoVersionId(0L);
        statementBean.setCategoryPath("categoryPath");
        final List<StatementBean> statementBeanList = Collections.singletonList(statementBean);
        when(mockStatementMapper.selectScriptStatementByIds(Collections.singletonList(0L))).thenReturn(statementBeanList);

        // Run the test
        scriptStatementServiceImplUnderTest.exportExcel(Collections.singletonList(0L), response);

        // Verify the results
        verify(mockStatementMapper).selectScriptStatementByIds(Collections.singletonList(0L));
    }

    @Test
    void testExportExcel_StatementMapperReturnsNoItems() {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockStatementMapper.selectScriptStatementByIds(Collections.singletonList(0L))).thenReturn(Collections.emptyList());

        // Run the test
        scriptStatementServiceImplUnderTest.exportExcel(Collections.singletonList(0L), response);

        // Verify the results
        verify(mockStatementMapper).selectScriptStatementByIds(Collections.singletonList(0L));
    }

    @Test
    void testSelectScriptStatementByIds() {
        // Setup
        // Configure StatementMapper.selectScriptStatementByIds(...).
        final StatementBean statementBean = new StatementBean();
        statementBean.setEscapedLikeCategoryPath("escapedLikeCategoryPath");
        statementBean.setId(0L);
        statementBean.setInfoId(0L);
        statementBean.setInfoVersionId(0L);
        statementBean.setCategoryPath("categoryPath");
        final List<StatementBean> statementBeanList = Collections.singletonList(statementBean);
        when(mockStatementMapper.selectScriptStatementByIds(Collections.singletonList(0L))).thenReturn(statementBeanList);

        // Run the test
        final List<StatementBean> result = scriptStatementServiceImplUnderTest.selectScriptStatementByIds(
                Collections.singletonList(0L));

        // Verify the results
        verify(mockStatementMapper).selectScriptStatementByIds(Collections.singletonList(0L));
    }

    @Test
    void testSelectScriptStatementByIds_StatementMapperReturnsNoItems() {
        // Setup
        when(mockStatementMapper.selectScriptStatementByIds(Collections.singletonList(0L))).thenReturn(Collections.emptyList());

        // Run the test
        final List<StatementBean> result = scriptStatementServiceImplUnderTest.selectScriptStatementByIds(
                Collections.singletonList(0L));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testUpdateScriptStatementByIds() {
        // Setup
        final StatementDto statementDto = new StatementDto();
        statementDto.setCategoryId(0L);
        statementDto.setId(0L);
        statementDto.setInfoId(0L);
        statementDto.setInfoVersionId(0L);
        statementDto.setScriptNameZh("scriptNameZh");

        // Run the test
        scriptStatementServiceImplUnderTest.updateScriptStatementByIds(statementDto, Collections.singletonList(0L));

        // Verify the results
        verify(mockStatementMapper).updateScriptStatementByIds(any(StatementBean.class), eq(Collections.singletonList(0L)));
    }

    @Test
    void testGetScriptStatementByInfoId() {
        // Setup
        // Configure StatementMapper.getScriptStatementByInfoId(...).
        final StatementBean statementBean = new StatementBean();
        statementBean.setEscapedLikeCategoryPath("escapedLikeCategoryPath");
        statementBean.setId(0L);
        statementBean.setInfoId(0L);
        statementBean.setInfoVersionId(0L);
        statementBean.setCategoryPath("categoryPath");
        when(mockStatementMapper.getScriptStatementByInfoId(0L)).thenReturn(statementBean);

        // Run the test
        final StatementDto result = scriptStatementServiceImplUnderTest.getScriptStatementByInfoId(0L);

        // Verify the results
        verify(mockStatementMapper).getScriptStatementByInfoId(0L);
    }
}
