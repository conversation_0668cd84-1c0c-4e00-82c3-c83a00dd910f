package com.ideal.script.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ideal.script.common.util.BatchDataUtil;
import com.ideal.script.dto.AttachmentDto;
import com.ideal.script.mapper.TaskGroupsMapper;
import com.ideal.script.model.dto.*;
import com.ideal.script.model.dto.interaction.agent.SystemComputerGroupDto;
import com.ideal.script.model.dto.interaction.agent.SystemComputerGroupQueryDto;
import com.ideal.script.model.entity.TaskGroups;
import com.ideal.system.api.IAgentInfo;
import com.ideal.system.api.IComputerGroup;
import com.ideal.system.dto.AgentInfoApiDto;
import com.ideal.system.dto.ComputerGroupApiDto;
import com.ideal.system.dto.SystemAgentInfoApiDto;
import org.apache.ibatis.session.SqlSession;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TaskGroupsServiceImplTest {

    @Mock
    private TaskGroupsMapper mockTaskGroupsMapper;
    @Mock
    private IAgentInfo mockIAgentInfo;
    @Mock
    private BatchDataUtil mockBatchDataUtil;
    @Mock
    private IComputerGroup mockComputerGroup;

    private TaskGroupsServiceImpl taskGroupsServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        taskGroupsServiceImplUnderTest = new TaskGroupsServiceImpl(mockTaskGroupsMapper, mockIAgentInfo,
                mockBatchDataUtil, mockComputerGroup);
    }

    @Test
    void testSelectTaskGroupsById() {
        // Setup
        // Configure TaskGroupsMapper.selectTaskGroupsById(...).
        final TaskGroups taskGroups = new TaskGroups();
        taskGroups.setId(0L);
        taskGroups.setScriptTaskId(0L);
        taskGroups.setSysmComputerGroupId(0L);
        taskGroups.setCpname("cpname");
        taskGroups.setCreateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockTaskGroupsMapper.selectTaskGroupsById(0L)).thenReturn(taskGroups);

        // Run the test
        final TaskGroupsDto result = taskGroupsServiceImplUnderTest.selectTaskGroupsById(0L);
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testSelectTaskGroupsList() {
        // Setup
        final TaskGroupsDto taskGroupsDto = new TaskGroupsDto();
        taskGroupsDto.setId(0L);
        taskGroupsDto.setScriptTaskId(0L);
        taskGroupsDto.setSysmComputerGroupId(0L);
        taskGroupsDto.setCpname("cpname");
        taskGroupsDto.setCreateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));

        // Configure TaskGroupsMapper.selectTaskGroupsList(...).
        final TaskGroups taskGroups1 = new TaskGroups();
        taskGroups1.setId(0L);
        taskGroups1.setScriptTaskId(0L);
        taskGroups1.setSysmComputerGroupId(0L);
        taskGroups1.setCpname("cpname");
        taskGroups1.setCreateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        Page<TaskGroups> page = new Page<>();
        page.add(taskGroups1);
        //final List<TaskGroups> taskGroups = Collections.singletonList(taskGroups1);
        when(mockTaskGroupsMapper.selectTaskGroupsList(any(TaskGroups.class))).thenReturn(page);

        // Run the test
        final PageInfo<TaskGroupsDto> result = taskGroupsServiceImplUnderTest.selectTaskGroupsList(taskGroupsDto, 0, 0);
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testInsertTaskGroups() {
        // Setup
        final TaskGroupsDto taskGroupsDto = new TaskGroupsDto();
        taskGroupsDto.setId(0L);
        taskGroupsDto.setScriptTaskId(0L);
        taskGroupsDto.setSysmComputerGroupId(0L);
        taskGroupsDto.setCpname("cpname");
        taskGroupsDto.setCreateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));

        when(mockTaskGroupsMapper.insertTaskGroups(any(TaskGroups.class))).thenReturn(0);

        // Run the test
        final int result = taskGroupsServiceImplUnderTest.insertTaskGroups(taskGroupsDto);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testUpdateTaskGroups() {
        // Setup
        final TaskGroupsDto taskGroupsDto = new TaskGroupsDto();
        taskGroupsDto.setId(0L);
        taskGroupsDto.setScriptTaskId(0L);
        taskGroupsDto.setSysmComputerGroupId(0L);
        taskGroupsDto.setCpname("cpname");
        taskGroupsDto.setCreateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));

        when(mockTaskGroupsMapper.updateTaskGroups(any(TaskGroups.class))).thenReturn(0);

        // Run the test
        final int result = taskGroupsServiceImplUnderTest.updateTaskGroups(taskGroupsDto);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testDeleteTaskGroupsByIds() {
        // Setup
        when(mockTaskGroupsMapper.deleteTaskGroupsByIds(any(Long[].class))).thenReturn(0);

        // Run the test
        final int result = taskGroupsServiceImplUnderTest.deleteTaskGroupsByIds(new Long[]{0L});

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testDeleteTaskGroupsById() {
        // Setup
        when(mockTaskGroupsMapper.deleteTaskGroupsById(0L)).thenReturn(0);

        // Run the test
        final int result = taskGroupsServiceImplUnderTest.deleteTaskGroupsById(0L);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testSelectTaskGroupsByServiceId() {
        // Setup
        // Configure TaskGroupsMapper.selectTaskGroupsByServiceId(...).
        final TaskGroups taskGroups1 = new TaskGroups();
        taskGroups1.setId(0L);
        taskGroups1.setScriptTaskId(0L);
        taskGroups1.setSysmComputerGroupId(0L);
        taskGroups1.setCpname("cpname");
        taskGroups1.setCreateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        final List<TaskGroups> taskGroups = Collections.singletonList(taskGroups1);
        when(mockTaskGroupsMapper.selectTaskGroupsByServiceId(0L,0L)).thenReturn(taskGroups);

        // Run the test
        final List<TaskGroupsDto> result = taskGroupsServiceImplUnderTest.selectTaskGroupsByServiceId(0L,0L);
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testSelectTaskGroupsByServiceId_TaskGroupsMapperReturnsNoItems() {
        // Setup
        when(mockTaskGroupsMapper.selectTaskGroupsByServiceId(0L,0L)).thenReturn(Collections.emptyList());

        // Run the test
        final List<TaskGroupsDto> result = taskGroupsServiceImplUnderTest.selectTaskGroupsByServiceId(0L,0L);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testQueryAgentPageListByGroupId() {
        // Setup
        final SystemComputerGroupQueryDto systemComputerGroupQueryDto = new SystemComputerGroupQueryDto();
        systemComputerGroupQueryDto.setCpName("cpName");
        systemComputerGroupQueryDto.setSysmComputerGroupId(0L);

        // Configure IAgentInfo.queryAgentPageListByGroupId(...).
        final SystemAgentInfoApiDto systemAgentInfoApiDto = new SystemAgentInfoApiDto();

        systemAgentInfoApiDto.setAgentName("agentName");
        systemAgentInfoApiDto.setAgentIp("agentIp");
        systemAgentInfoApiDto.setAgentPort("8080");

        final PageInfo<SystemAgentInfoApiDto> systemAgentInfoApiDtoPageInfo = new PageInfo<>(Collections.singletonList(systemAgentInfoApiDto));
        when(mockIAgentInfo.queryAgentInfoGroupRole(any())).thenReturn(systemAgentInfoApiDtoPageInfo);

        // Run the test
        final PageInfo<AgentInfoDto> result = taskGroupsServiceImplUnderTest.queryAgentPageListByGroupId(
                systemComputerGroupQueryDto, 0, 0);
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testQueryAgentPageListByGroupId_IAgentInfoReturnsNoItem() {
        // Setup
        final SystemComputerGroupQueryDto systemComputerGroupQueryDto = new SystemComputerGroupQueryDto();
        systemComputerGroupQueryDto.setCpName("cpName");
        systemComputerGroupQueryDto.setSysmComputerGroupId(0L);


        when(mockIAgentInfo.queryAgentInfoGroupRole(any())).thenReturn(PageInfo.emptyPageInfo());

        // Run the test
        final PageInfo<AgentInfoDto> result = taskGroupsServiceImplUnderTest.queryAgentPageListByGroupId(
                systemComputerGroupQueryDto, 0, 0);
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testConvertToAgentInfoDto1() {
        // Setup
        final AgentInfoApiDto agentInfo = new AgentInfoApiDto();
        agentInfo.setId(0L);
        agentInfo.setName("agentName");
        agentInfo.setIp("agentIp");
        agentInfo.setPort(0);
        agentInfo.setRegisterState(0);

        final AgentInfoDto expectedResult = new AgentInfoDto();
        expectedResult.setSysmAgentInfoId(0L);
        expectedResult.setAgentIp("agentIp");
        expectedResult.setAgentName("agentName");
        expectedResult.setAgentPort(0);
        expectedResult.setAgentState(0);
        expectedResult.setDeviceName("deviceName");
        expectedResult.setOsName("osName");

        // Run the test
        final AgentInfoDto result = taskGroupsServiceImplUnderTest.convertToAgentInfoDto(agentInfo);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testSaveTaskGroups() throws Exception {
        // Setup
        final ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        final AttachmentDto attachmentDto = new AttachmentDto();
        scriptExecAuditDto.setScriptTempAttachments(Collections.singletonList(attachmentDto));
        final TaskGroupsDto taskGroupsDto = new TaskGroupsDto();
        taskGroupsDto.setId(0L);
        taskGroupsDto.setScriptTaskId(0L);
        taskGroupsDto.setSysmComputerGroupId(0L);
        scriptExecAuditDto.setChosedResGroups(Collections.singletonList(taskGroupsDto));

        final TaskDto taskInfo = new TaskDto();
        taskInfo.setScriptTaskSource(0);
        taskInfo.setStartType(0);
        taskInfo.setReadyToExecute(0);
        taskInfo.setId(0L);
        taskInfo.setSrcScriptUuid("srcScriptUuid");

        final SqlSession sqlSession = mock(SqlSession.class);

        // Run the test
        taskGroupsServiceImplUnderTest.saveTaskGroups(scriptExecAuditDto, taskInfo, sqlSession);

        // Verify the results
        // Confirm BatchDataUtil.batchData(...).
        final TaskGroups taskGroups = new TaskGroups();
        taskGroups.setId(0L);
        taskGroups.setScriptTaskId(0L);
        taskGroups.setSysmComputerGroupId(0L);
        taskGroups.setCpname("cpname");
        taskGroups.setCreateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        final List<TaskGroups> listEntity = Collections.singletonList(taskGroups);
        assertNotNull(listEntity);
    }

    @Test
    void testRetrieveUniqueAgentInfoList() {
        // Setup
        final TaskGroupsDto taskGroupsDto = new TaskGroupsDto();
        taskGroupsDto.setId(0L);
        taskGroupsDto.setScriptTaskId(0L);
        taskGroupsDto.setSysmComputerGroupId(0L);
        taskGroupsDto.setCpname("cpname");
        taskGroupsDto.setCreateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        final List<TaskGroupsDto> taskGroupsDtoList = Collections.singletonList(taskGroupsDto);
        final AgentInfoDto agentInfoDto = new AgentInfoDto();

        agentInfoDto.setAgentIp("agentIp");
        agentInfoDto.setAgentName("agentName");
        agentInfoDto.setAgentPort(8080);
        final List<AgentInfoDto> expectedResult = Collections.singletonList(agentInfoDto);

        // Configure IAgentInfo.queryAgentPageListByGroupId(...).
        final SystemAgentInfoApiDto systemAgentInfoApiDto = new SystemAgentInfoApiDto();

        systemAgentInfoApiDto.setAgentName("agentName");
        systemAgentInfoApiDto.setAgentIp("agentIp");
        systemAgentInfoApiDto.setAgentPort("8080");

        final PageInfo<SystemAgentInfoApiDto> systemAgentInfoApiDtoPageInfo = new PageInfo<>(Collections.singletonList(systemAgentInfoApiDto));
        when(mockIAgentInfo.queryAgentInfoGroupRole(any())).thenReturn(systemAgentInfoApiDtoPageInfo);

        // Run the test
        final List<AgentInfoDto> result = taskGroupsServiceImplUnderTest.retrieveUniqueAgentInfoList(taskGroupsDtoList);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testRetrieveUniqueAgentInfoList_IAgentInfoReturnsNoItem() {
        // Setup
        final TaskGroupsDto taskGroupsDto = new TaskGroupsDto();
        taskGroupsDto.setId(0L);
        taskGroupsDto.setScriptTaskId(0L);
        taskGroupsDto.setSysmComputerGroupId(0L);
        taskGroupsDto.setCpname("cpname");
        taskGroupsDto.setCreateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        final List<TaskGroupsDto> taskGroupsDtoList = Collections.singletonList(taskGroupsDto);

        when(mockIAgentInfo.queryAgentInfoGroupRole(any())).thenReturn(PageInfo.emptyPageInfo());

        // Run the test
        final List<AgentInfoDto> result = taskGroupsServiceImplUnderTest.retrieveUniqueAgentInfoList(taskGroupsDtoList);

        // Verify the results
        assertThat(result).isEqualTo(new ArrayList<>());
    }

    @Test
    void testQueryAgentInfoGroupRole() {
        // Setup
        final AgentInfoQueryDto agentInfoQueryDto = new AgentInfoQueryDto();
        agentInfoQueryDto.setAgentIp("agentIp");
        agentInfoQueryDto.setAgentName("agentName");
        agentInfoQueryDto.setAgentPort(0);
        agentInfoQueryDto.setDeviceName("deviceName");
        agentInfoQueryDto.setOsName("osName");

        final SystemAgentInfoApiDto systemAgentInfoApiDto = new SystemAgentInfoApiDto();
        systemAgentInfoApiDto.setAgentId(0L);
        systemAgentInfoApiDto.setAgentName("agentName");
        systemAgentInfoApiDto.setAgentIp("agentIp");
        systemAgentInfoApiDto.setAgentPort("0");
        systemAgentInfoApiDto.setAgentState(0);
        systemAgentInfoApiDto.setComputerListName("deviceName");
        systemAgentInfoApiDto.setComputerListOsVersion("osName");
        final PageInfo<SystemAgentInfoApiDto> systemAgentInfoApiDtoPageInfo = new PageInfo<>(
                Collections.singletonList(systemAgentInfoApiDto));

        when(mockIAgentInfo.queryAgentInfoGroupRole(any())).thenReturn(systemAgentInfoApiDtoPageInfo);

        // Run the test
        final PageInfo<AgentInfoDto> result = taskGroupsServiceImplUnderTest.queryAgentInfoGroupRole(agentInfoQueryDto,
                0, 100);
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testQueryAgentInfoGroupRole_IAgentInfoReturnsNoItem() {
        // Setup
        final AgentInfoQueryDto agentInfoQueryDto = new AgentInfoQueryDto();
        agentInfoQueryDto.setAgentIp("agentIp");
        agentInfoQueryDto.setAgentName("agentName");
        agentInfoQueryDto.setAgentPort(0);
        agentInfoQueryDto.setDeviceName("deviceName");
        agentInfoQueryDto.setOsName("osName");

        when(mockIAgentInfo.queryAgentInfoGroupRole(any())).thenReturn(PageInfo.emptyPageInfo());

        // Run the test
        final PageInfo<AgentInfoDto> result = taskGroupsServiceImplUnderTest.queryAgentInfoGroupRole(agentInfoQueryDto,
                0, 100);
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testConvertToAgentInfoDto2() {
        // Setup
        final SystemAgentInfoApiDto systemAgentInfoApiDto = new SystemAgentInfoApiDto();
        systemAgentInfoApiDto.setAgentId(0L);
        systemAgentInfoApiDto.setAgentName("agentName");
        systemAgentInfoApiDto.setAgentIp("agentIp");
        systemAgentInfoApiDto.setAgentPort("0");
        systemAgentInfoApiDto.setAgentState(0);
        systemAgentInfoApiDto.setComputerListName("deviceName");
        systemAgentInfoApiDto.setComputerListOsVersion("osName");

        final AgentInfoDto expectedResult = new AgentInfoDto();
        expectedResult.setSysmAgentInfoId(0L);
        expectedResult.setAgentIp("agentIp");
        expectedResult.setAgentName("agentName");
        expectedResult.setAgentPort(0);
        expectedResult.setAgentState(0);
        expectedResult.setDeviceName("deviceName");
        expectedResult.setOsName("osName");

        // Run the test
        final AgentInfoDto result = taskGroupsServiceImplUnderTest.convertToAgentInfoDto(systemAgentInfoApiDto);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testQueryAgentGroupPageList() {
        // Setup
        final SystemComputerGroupQueryDto systemComputerGroupQueryDto = new SystemComputerGroupQueryDto();
        systemComputerGroupQueryDto.setCpName("cpName");
        systemComputerGroupQueryDto.setSysmComputerGroupId(0L);

        // Configure IComputerGroup.queryAgentGroupPageList(...).
        final ComputerGroupApiDto computerGroupApiDto = new ComputerGroupApiDto();
        computerGroupApiDto.setId(0L);
        computerGroupApiDto.setName("cpName");
        computerGroupApiDto.setEnName("enName");
        computerGroupApiDto.setDescription("description");
        final PageInfo<ComputerGroupApiDto> computerGroupApiDtoPageInfo = new PageInfo<>(
                Collections.singletonList(computerGroupApiDto));
        when(mockComputerGroup.queryCpGroupList(any(), eq(0), eq(0))).thenReturn(computerGroupApiDtoPageInfo);

        // Run the test
        final PageInfo<SystemComputerGroupDto> result = taskGroupsServiceImplUnderTest.queryAgentGroupPageList(
                systemComputerGroupQueryDto, 0, 0);
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testQueryAgentGroupPageList_IComputerGroupReturnsNoItem() {
        // Setup
        final SystemComputerGroupQueryDto systemComputerGroupQueryDto = new SystemComputerGroupQueryDto();
        systemComputerGroupQueryDto.setCpName("cpName");
        systemComputerGroupQueryDto.setSysmComputerGroupId(0L);

        when(mockComputerGroup.queryCpGroupList(any(), eq(0), eq(0))).thenReturn(PageInfo.emptyPageInfo());

        // Run the test
        final PageInfo<SystemComputerGroupDto> result = taskGroupsServiceImplUnderTest.queryAgentGroupPageList(
                systemComputerGroupQueryDto, 0, 0);
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testConvertToSystemComputerGroupDto() {
        // Setup
        final ComputerGroupApiDto computerGroupDto = new ComputerGroupApiDto();
        computerGroupDto.setId(0L);
        computerGroupDto.setName("cpName");
        computerGroupDto.setEnName("enName");
        computerGroupDto.setDescription("description");

        // Run the test
        final SystemComputerGroupDto result = taskGroupsServiceImplUnderTest.convertToSystemComputerGroupDto(
                computerGroupDto);
        assertNotNull(result);
        // Verify the results
    }
}
