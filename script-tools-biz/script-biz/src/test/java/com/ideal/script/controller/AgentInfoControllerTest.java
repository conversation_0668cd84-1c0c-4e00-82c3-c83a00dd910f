package com.ideal.script.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.script.model.dto.AgentInfoDto;
import com.ideal.script.model.dto.QueryAgentInfoDto;
import com.ideal.script.service.IAgentInfoService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AgentInfoControllerTest {

    @Mock
    private IAgentInfoService mockAgentInfoService;

    private AgentInfoController agentInfoControllerUnderTest;

    @BeforeEach
    void setUp() {
        agentInfoControllerUnderTest = new AgentInfoController(mockAgentInfoService);
    }

    @Test
    void testGetTaskBindAgentInfo() {
        // Setup
        final TableQueryDto<QueryAgentInfoDto> tableQueryDTO = new TableQueryDto<>();
        tableQueryDTO.setPageNum(0);
        tableQueryDTO.setPageSize(10);
        final QueryAgentInfoDto queryAgentInfoDto = new QueryAgentInfoDto();
        queryAgentInfoDto.setAgentIp("agentIp");
        queryAgentInfoDto.setAgentName("agentName");
        tableQueryDTO.setQueryParam(queryAgentInfoDto);

        // Configure IAgentInfoService.getTaskBindAgentInfo(...).
        final AgentInfoDto agentInfoDto = new AgentInfoDto();
        agentInfoDto.setCenterId(0L);
        agentInfoDto.setCenterName("centerName");
        agentInfoDto.setBusinessSystemNameList(Arrays.asList("value"));
        agentInfoDto.setTaskIpsId(0L);
        agentInfoDto.setId(0L);
        final PageInfo<AgentInfoDto> agentInfoDtoPageInfo = new PageInfo<>(Arrays.asList(agentInfoDto));
        when(mockAgentInfoService.getTaskBindAgentInfo(any(QueryAgentInfoDto.class), eq(0), eq(10)))
                .thenReturn(agentInfoDtoPageInfo);

        // Run the test
        final R<PageInfo<AgentInfoDto>> result = agentInfoControllerUnderTest.getTaskBindAgentInfo(tableQueryDTO);

        // Verify the results
    }

    @Test
    void testGetTaskBindAgentInfo_IAgentInfoServiceReturnsNoItem() {
        // Setup
        final TableQueryDto<QueryAgentInfoDto> tableQueryDTO = new TableQueryDto<>();
        tableQueryDTO.setPageNum(0);
        tableQueryDTO.setPageSize(10);
        final QueryAgentInfoDto queryAgentInfoDto = new QueryAgentInfoDto();
        queryAgentInfoDto.setAgentIp("agentIp");
        queryAgentInfoDto.setAgentName("agentName");
        tableQueryDTO.setQueryParam(queryAgentInfoDto);

        when(mockAgentInfoService.getTaskBindAgentInfo(any(QueryAgentInfoDto.class), eq(0), eq(10)))
                .thenReturn(PageInfo.emptyPageInfo());

        // Run the test
        final R<PageInfo<AgentInfoDto>> result = agentInfoControllerUnderTest.getTaskBindAgentInfo(tableQueryDTO);

        // Verify the results
    }

    @Test
    void testGetTaskAllAgentInfo() {
        // Setup
        final TableQueryDto<QueryAgentInfoDto> tableQueryDto = new TableQueryDto<>();
        tableQueryDto.setPageNum(0);
        tableQueryDto.setPageSize(10);
        final QueryAgentInfoDto queryAgentInfoDto = new QueryAgentInfoDto();
        queryAgentInfoDto.setAgentIp("agentIp");
        queryAgentInfoDto.setAgentName("agentName");
        tableQueryDto.setQueryParam(queryAgentInfoDto);

        // Configure IAgentInfoService.getTaskAllAgentInfo(...).
        final AgentInfoDto agentInfoDto = new AgentInfoDto();
        agentInfoDto.setCenterId(0L);
        agentInfoDto.setCenterName("centerName");
        agentInfoDto.setBusinessSystemNameList(Arrays.asList("value"));
        agentInfoDto.setTaskIpsId(0L);
        agentInfoDto.setId(0L);
        final PageInfo<AgentInfoDto> agentInfoDtoPageInfo = new PageInfo<>(Arrays.asList(agentInfoDto));
        when(mockAgentInfoService.getTaskAllAgentInfo(any(QueryAgentInfoDto.class), eq(0), eq(10)))
                .thenReturn(agentInfoDtoPageInfo);

        // Run the test
        final R<PageInfo<AgentInfoDto>> result = agentInfoControllerUnderTest.getTaskAllAgentInfo(tableQueryDto);

        // Verify the results
    }

    @Test
    void testGetTaskAllAgentInfo_IAgentInfoServiceReturnsNoItem() {
        // Setup
        final TableQueryDto<QueryAgentInfoDto> tableQueryDto = new TableQueryDto<>();
        tableQueryDto.setPageNum(0);
        tableQueryDto.setPageSize(10);
        final QueryAgentInfoDto queryAgentInfoDto = new QueryAgentInfoDto();
        queryAgentInfoDto.setAgentIp("agentIp");
        queryAgentInfoDto.setAgentName("agentName");
        tableQueryDto.setQueryParam(queryAgentInfoDto);

        when(mockAgentInfoService.getTaskAllAgentInfo(any(QueryAgentInfoDto.class), eq(0), eq(10)))
                .thenReturn(PageInfo.emptyPageInfo());

        // Run the test
        final R<PageInfo<AgentInfoDto>> result = agentInfoControllerUnderTest.getTaskAllAgentInfo(tableQueryDto);

        // Verify the results
    }
}
