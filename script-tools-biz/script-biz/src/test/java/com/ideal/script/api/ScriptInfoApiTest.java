package com.ideal.script.api;

import com.github.pagehelper.PageInfo;
import com.ideal.script.dto.AttachmentDto;
import com.ideal.script.dto.CategoryApiDto;
import com.ideal.script.dto.CategoryDto;
import com.ideal.script.dto.CategoryQueryDto;
import com.ideal.script.dto.PublishDto;
import com.ideal.script.dto.RetryScriptInstanceApiDto;
import com.ideal.script.dto.TaskRuntimeApiDto;
import com.ideal.script.dto.TaskRuntimeQueryApiDto;
import com.ideal.script.model.dto.ValidationResultDto;
import com.ideal.script.dto.ScriptAfterUploadAttachmentTempApiDto;
import com.ideal.script.dto.ScriptAttachmentTempApiDto;
import com.ideal.script.dto.ScriptAttachmentTempMegDto;
import com.ideal.script.dto.ScriptCategoryIconDto;
import com.ideal.script.dto.ScriptDubboInfoDto;
import com.ideal.script.dto.ScriptFileAttachmentTempApiDto;
import com.ideal.script.dto.ScriptFileImportExportApiDto;
import com.ideal.script.dto.ScriptInfoApiDto;
import com.ideal.script.dto.ScriptInfoDto;
import com.ideal.script.dto.ScriptVersionDto;
import com.ideal.script.dto.ScriptInfoQueryDto;

import com.ideal.script.exception.ScriptException;
import com.ideal.script.service.IAttachmentService;
import com.ideal.script.service.ICategoryService;
import com.ideal.script.service.IInfoService;
import com.ideal.script.service.IMyScriptService;
import com.ideal.script.service.IReleaseMediaService;
import com.ideal.script.service.ITaskRuntimeService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;
import java.sql.Timestamp;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ScriptInfoApi单元测试类
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class ScriptInfoApiTest {

    @Mock
    private IInfoService infoService;

    @Mock
    private ICategoryService categoryService;

    @Mock
    private IMyScriptService iMyScriptService;

    @Mock
    private IReleaseMediaService releaseMediaService;

    @Mock
    private ITaskRuntimeService taskRuntimeService;

    @Mock
    private IAttachmentService attachmentService;

    @InjectMocks
    private ScriptInfoApi scriptInfoApi;

    @BeforeEach
    void setUp() {
        // 初始化设置，如果需要的话
    }

    @Test
    @DisplayName("测试getScriptInfo方法 - 正常流程")
    void testGetScriptInfo_Normal() throws ScriptException {
        // 准备测试数据
        ScriptInfoQueryDto queryDto = new ScriptInfoQueryDto();
        queryDto.setSrcScriptUuid("test-uuid-123");
        queryDto.setScriptInfoId(1L);
        queryDto.setQueryScriptContentFlag(true);
        queryDto.setQueryScriptParamsFlag(true);
        queryDto.setQueryScriptAttachmentFlag(true);

        ScriptDubboInfoDto expectedResult = new ScriptDubboInfoDto();
        expectedResult.setId(1L);
        expectedResult.setScriptNameZh("测试脚本");
        expectedResult.setScriptName("test_script");
        expectedResult.setScriptType("shell");
        expectedResult.setExecuser("root");
        expectedResult.setContent("echo 'hello world'");
        expectedResult.setVersion("1.0");
        expectedResult.setUniqueUuid("unique-uuid-456");
        expectedResult.setSrcScriptUuid("test-uuid-123");
        expectedResult.setPlatform("Linux");
        expectedResult.setParamList(new ArrayList<>());
        expectedResult.setAttachmentList(new ArrayList<>());

        // Mock方法调用
        doReturn(expectedResult).when(iMyScriptService).getScriptDetailForDubbo(any(ScriptInfoQueryDto.class));

        // 执行测试
        ScriptDubboInfoDto result = scriptInfoApi.getScriptInfo(queryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedResult.getId(), result.getId());
        assertEquals(expectedResult.getScriptNameZh(), result.getScriptNameZh());
        assertEquals(expectedResult.getScriptName(), result.getScriptName());
        assertEquals(expectedResult.getScriptType(), result.getScriptType());
        assertEquals(expectedResult.getExecuser(), result.getExecuser());
        assertEquals(expectedResult.getContent(), result.getContent());
        assertEquals(expectedResult.getVersion(), result.getVersion());
        assertEquals(expectedResult.getUniqueUuid(), result.getUniqueUuid());
        assertEquals(expectedResult.getSrcScriptUuid(), result.getSrcScriptUuid());
        assertEquals(expectedResult.getPlatform(), result.getPlatform());

        // 验证方法调用
        verify(iMyScriptService, times(1)).getScriptDetailForDubbo(queryDto);
    }

    @Test
    @DisplayName("测试getScriptInfo方法 - 异常流程")
    void testGetScriptInfo_Exception() throws ScriptException {
        // 准备测试数据
        ScriptInfoQueryDto queryDto = new ScriptInfoQueryDto();
        queryDto.setSrcScriptUuid("invalid-uuid");

        String errorMessage = "脚本不存在";

        // Mock方法抛出异常
        doThrow(new ScriptException(errorMessage)).when(iMyScriptService).getScriptDetailForDubbo(any(ScriptInfoQueryDto.class));

        // 执行测试并验证异常
        ScriptException exception = assertThrows(ScriptException.class, () -> {
            scriptInfoApi.getScriptInfo(queryDto);
        });

        // 验证异常信息
        assertEquals(errorMessage, exception.getMessage());

        // 验证方法调用
        verify(iMyScriptService, times(1)).getScriptDetailForDubbo(queryDto);
    }

    @Test
    @DisplayName("测试getScriptInfo方法 - null参数")
    void testGetScriptInfo_NullParameter() throws ScriptException {
        // Mock方法调用
        doReturn(null).when(iMyScriptService).getScriptDetailForDubbo(any());

        // 执行测试
        ScriptDubboInfoDto result = scriptInfoApi.getScriptInfo(null);

        // 验证结果
        assertNull(result);

        // 验证方法调用
        verify(iMyScriptService, times(1)).getScriptDetailForDubbo(null);
    }

    @Test
    @DisplayName("测试getCategoryTree方法 - 正常流程")
    void testGetCategoryTree_Normal() {
        // 准备测试数据
        CategoryQueryDto queryDto = new CategoryQueryDto();
        queryDto.setLevel(1);
        queryDto.setParentId(0L);
        queryDto.setQueryChildren(true);
        queryDto.setName("测试分类");

        // 创建父分类
        CategoryApiDto parentCategory = new CategoryApiDto();
        parentCategory.setId(1L);
        parentCategory.setName("一级分类");
        parentCategory.setLevel(1);
        parentCategory.setParentId(0L);
        parentCategory.setCode("LEVEL1");
        parentCategory.setSort(1);
        parentCategory.setDescription("一级分类描述");

        // 创建子分类
        CategoryApiDto childCategory = new CategoryApiDto();
        childCategory.setId(2L);
        childCategory.setName("二级分类");
        childCategory.setLevel(2);
        childCategory.setParentId(1L);
        childCategory.setCode("LEVEL2");
        childCategory.setSort(1);
        childCategory.setDescription("二级分类描述");

        List<CategoryApiDto> children = new ArrayList<>();
        children.add(childCategory);
        parentCategory.setChildren(children);

        List<CategoryApiDto> expectedResult = new ArrayList<>();
        expectedResult.add(parentCategory);

        // Mock方法调用
        doReturn(expectedResult).when(infoService).getCategoryTreeApi(any(CategoryQueryDto.class));

        // 执行测试
        List<CategoryApiDto> result = scriptInfoApi.getCategoryTree(queryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());

        CategoryApiDto resultParent = result.get(0);
        assertEquals(parentCategory.getId(), resultParent.getId());
        assertEquals(parentCategory.getName(), resultParent.getName());
        assertEquals(parentCategory.getLevel(), resultParent.getLevel());
        assertEquals(parentCategory.getParentId(), resultParent.getParentId());
        assertEquals(parentCategory.getCode(), resultParent.getCode());
        assertEquals(parentCategory.getSort(), resultParent.getSort());
        assertEquals(parentCategory.getDescription(), resultParent.getDescription());

        // 验证子分类
        assertNotNull(resultParent.getChildren());
        assertEquals(1, resultParent.getChildren().size());
        CategoryApiDto resultChild = resultParent.getChildren().get(0);
        assertEquals(childCategory.getId(), resultChild.getId());
        assertEquals(childCategory.getName(), resultChild.getName());
        assertEquals(childCategory.getLevel(), resultChild.getLevel());
        assertEquals(childCategory.getParentId(), resultChild.getParentId());

        // 验证方法调用
        verify(infoService, times(1)).getCategoryTreeApi(queryDto);
    }

    @Test
    @DisplayName("测试getCategoryTree方法 - 空列表返回")
    void testGetCategoryTree_EmptyList() {
        // 准备测试数据
        CategoryQueryDto queryDto = new CategoryQueryDto();
        queryDto.setLevel(1);
        queryDto.setParentId(999L); // 不存在的父分类ID

        List<CategoryApiDto> expectedResult = new ArrayList<>();

        // Mock方法调用
        doReturn(expectedResult).when(infoService).getCategoryTreeApi(any(CategoryQueryDto.class));

        // 执行测试
        List<CategoryApiDto> result = scriptInfoApi.getCategoryTree(queryDto);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证方法调用
        verify(infoService, times(1)).getCategoryTreeApi(queryDto);
    }

    @Test
    @DisplayName("测试getCategoryTree方法 - null参数")
    void testGetCategoryTree_NullParameter() {
        // Mock方法调用
        doReturn(null).when(infoService).getCategoryTreeApi(any());

        // 执行测试
        List<CategoryApiDto> result = scriptInfoApi.getCategoryTree(null);

        // 验证结果
        assertNull(result);

        // 验证方法调用
        verify(infoService, times(1)).getCategoryTreeApi(null);
    }

    @Test
    @DisplayName("测试getCategoryTree方法 - 单层分类")
    void testGetCategoryTree_SingleLevel() {
        // 准备测试数据
        CategoryQueryDto queryDto = new CategoryQueryDto();
        queryDto.setLevel(1);
        queryDto.setQueryChildren(false);

        CategoryApiDto category1 = new CategoryApiDto();
        category1.setId(1L);
        category1.setName("分类1");
        category1.setLevel(1);
        category1.setCode("CAT1");

        CategoryApiDto category2 = new CategoryApiDto();
        category2.setId(2L);
        category2.setName("分类2");
        category2.setLevel(1);
        category2.setCode("CAT2");

        List<CategoryApiDto> expectedResult = new ArrayList<>();
        expectedResult.add(category1);
        expectedResult.add(category2);

        // Mock方法调用
        doReturn(expectedResult).when(infoService).getCategoryTreeApi(any(CategoryQueryDto.class));

        // 执行测试
        List<CategoryApiDto> result = scriptInfoApi.getCategoryTree(queryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("分类1", result.get(0).getName());
        assertEquals("分类2", result.get(1).getName());

        // 验证方法调用
        verify(infoService, times(1)).getCategoryTreeApi(queryDto);
    }

    /**
     * 提供getScriptInfoList方法的测试参数
     */
    static Stream<Arguments> provideScriptInfoListTestData() {
        return Stream.of(
            // scriptSource为null的情况，应该被设置为0
            Arguments.of(null, 0, "scriptSource为null时应设置为0"),
            // scriptSource为0的情况，应该保持不变
            Arguments.of(0, 0, "scriptSource为0时应保持不变"),
            // scriptSource为1的情况，应该保持不变
            Arguments.of(1, 1, "scriptSource为1时应保持不变"),
            // scriptSource为其他值的情况，应该保持不变
            Arguments.of(2, 2, "scriptSource为其他值时应保持不变")
        );
    }

    @ParameterizedTest
    @MethodSource("provideScriptInfoListTestData")
    @DisplayName("测试getScriptInfoList方法 - 参数化测试scriptSource条件分支")
    void testGetScriptInfoList_ParameterizedScriptSource(Integer inputScriptSource, Integer expectedScriptSource, String description) {
        // 准备测试数据
        ScriptInfoQueryDto queryDto = new ScriptInfoQueryDto();
        queryDto.setScriptNameZh("测试脚本");
        queryDto.setScriptName("test_script");
        queryDto.setScriptType("shell");
        queryDto.setCategoryId(1L);
        queryDto.setEditState(1);
        queryDto.setScriptSource(inputScriptSource); // 设置测试的scriptSource值

        // 创建期望的返回结果
        ScriptInfoApiDto scriptInfo1 = new ScriptInfoApiDto();
        scriptInfo1.setScriptInfoId(1L);
        scriptInfo1.setUniqueUuid("uuid-001");
        scriptInfo1.setScriptNameZh("测试脚本1");
        scriptInfo1.setScriptName("test_script_1");
        scriptInfo1.setScriptType("shell");
        scriptInfo1.setExecuser("root");
        scriptInfo1.setPlatform("Linux");
        scriptInfo1.setEditState(1);
        scriptInfo1.setCategoryId(1L);
        scriptInfo1.setLevel(0);
        scriptInfo1.setScriptSource(expectedScriptSource);
        scriptInfo1.setCreatorName("测试用户");
        scriptInfo1.setScriptLabel("测试,标签");
        scriptInfo1.setCategoryPath("/一级分类");
        scriptInfo1.setScriptCategoryName("一级分类");
        scriptInfo1.setScriptInfoVersionId(1L);
        scriptInfo1.setSrcScriptUuid("version-uuid-001");
        scriptInfo1.setVersion("1.0");
        scriptInfo1.setIsDefault(1);
        scriptInfo1.setDescription("测试脚本描述");
        scriptInfo1.setTimeout(300);
        scriptInfo1.setExpectType(1);
        scriptInfo1.setExpectLastline("success");

        ScriptInfoApiDto scriptInfo2 = new ScriptInfoApiDto();
        scriptInfo2.setScriptInfoId(2L);
        scriptInfo2.setUniqueUuid("uuid-002");
        scriptInfo2.setScriptNameZh("测试脚本2");
        scriptInfo2.setScriptName("test_script_2");
        scriptInfo2.setScriptType("python");
        scriptInfo2.setExecuser("admin");
        scriptInfo2.setPlatform("Windows");
        scriptInfo2.setEditState(1);
        scriptInfo2.setCategoryId(2L);
        scriptInfo2.setLevel(1);
        scriptInfo2.setScriptSource(expectedScriptSource);
        scriptInfo2.setCreatorName("测试用户2");
        scriptInfo2.setScriptLabel("python,自动化");
        scriptInfo2.setCategoryPath("/一级分类/二级分类");
        scriptInfo2.setScriptCategoryName("二级分类");
        scriptInfo2.setScriptInfoVersionId(2L);
        scriptInfo2.setSrcScriptUuid("version-uuid-002");
        scriptInfo2.setVersion("2.0");
        scriptInfo2.setIsDefault(1);
        scriptInfo2.setDescription("Python测试脚本");
        scriptInfo2.setTimeout(600);
        scriptInfo2.setExpectType(2);
        scriptInfo2.setExpectLastline("0");

        List<ScriptInfoApiDto> expectedResult = new ArrayList<>();
        expectedResult.add(scriptInfo1);
        expectedResult.add(scriptInfo2);

        // Mock方法调用
        doReturn(expectedResult).when(iMyScriptService).selectScriptList(any(ScriptInfoQueryDto.class));

        // 执行测试
        List<ScriptInfoApiDto> result = scriptInfoApi.getScriptInfoList(queryDto);

        // 验证结果
        assertNotNull(result, description);
        assertEquals(2, result.size(), description);
        assertEquals(expectedResult.get(0).getScriptInfoId(), result.get(0).getScriptInfoId(), description);
        assertEquals(expectedResult.get(1).getScriptInfoId(), result.get(1).getScriptInfoId(), description);

        // 验证scriptSource被正确设置
        assertEquals(expectedScriptSource, queryDto.getScriptSource(), description + " - scriptSource应该被正确设置");

        // 验证方法调用
        verify(iMyScriptService, times(1)).selectScriptList(queryDto);
    }

    @Test
    @DisplayName("测试getScriptInfoList方法 - 空列表返回")
    void testGetScriptInfoList_EmptyList() {
        // 准备测试数据
        ScriptInfoQueryDto queryDto = new ScriptInfoQueryDto();
        queryDto.setScriptNameZh("不存在的脚本");
        queryDto.setScriptSource(null); // 测试null值

        List<ScriptInfoApiDto> expectedResult = new ArrayList<>();

        // Mock方法调用
        doReturn(expectedResult).when(iMyScriptService).selectScriptList(any(ScriptInfoQueryDto.class));

        // 执行测试
        List<ScriptInfoApiDto> result = scriptInfoApi.getScriptInfoList(queryDto);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证scriptSource被设置为0
        assertEquals(0, queryDto.getScriptSource());

        // 验证方法调用
        verify(iMyScriptService, times(1)).selectScriptList(queryDto);
    }

    /**
     * 提供getScriptInfoPageList方法的测试参数
     */
    static Stream<Arguments> provideScriptInfoPageListTestData() {
        return Stream.of(
            // scriptSource为null的情况，应该被设置为0
            Arguments.of(null, 0, "scriptSource为null时应设置为0"),
            // scriptSource为0的情况，应该保持不变
            Arguments.of(0, 0, "scriptSource为0时应保持不变"),
            // scriptSource为1的情况，应该保持不变
            Arguments.of(1, 1, "scriptSource为1时应保持不变"),
            // scriptSource为其他值的情况，应该保持不变
            Arguments.of(2, 2, "scriptSource为其他值时应保持不变")
        );
    }

    @ParameterizedTest
    @MethodSource("provideScriptInfoPageListTestData")
    @DisplayName("测试getScriptInfoPageList方法 - 参数化测试scriptSource条件分支")
    void testGetScriptInfoPageList_ParameterizedScriptSource(Integer inputScriptSource, Integer expectedScriptSource, String description) {
        // 准备测试数据
        ScriptInfoQueryDto queryDto = new ScriptInfoQueryDto();
        queryDto.setScriptNameZh("测试脚本分页");
        queryDto.setScriptName("test_script_page");
        queryDto.setScriptType("python");
        queryDto.setCategoryId(2L);
        queryDto.setEditState(1);
        queryDto.setPageNum(1);
        queryDto.setPageSize(10);
        queryDto.setScriptSource(inputScriptSource); // 设置测试的scriptSource值

        // 创建期望的返回结果
        ScriptInfoApiDto scriptInfo1 = new ScriptInfoApiDto();
        scriptInfo1.setScriptInfoId(10L);
        scriptInfo1.setUniqueUuid("page-uuid-001");
        scriptInfo1.setScriptNameZh("分页测试脚本1");
        scriptInfo1.setScriptName("page_test_script_1");
        scriptInfo1.setScriptType("python");
        scriptInfo1.setExecuser("admin");
        scriptInfo1.setPlatform("Linux");
        scriptInfo1.setEditState(1);
        scriptInfo1.setCategoryId(2L);
        scriptInfo1.setLevel(1);
        scriptInfo1.setScriptSource(expectedScriptSource);
        scriptInfo1.setCreatorName("分页测试用户");
        scriptInfo1.setScriptLabel("分页,测试");
        scriptInfo1.setCategoryPath("/一级分类/二级分类");
        scriptInfo1.setScriptCategoryName("二级分类");
        scriptInfo1.setScriptInfoVersionId(10L);
        scriptInfo1.setSrcScriptUuid("page-version-uuid-001");
        scriptInfo1.setVersion("1.0");
        scriptInfo1.setIsDefault(1);
        scriptInfo1.setDescription("分页测试脚本描述");
        scriptInfo1.setTimeout(300);
        scriptInfo1.setExpectType(1);
        scriptInfo1.setExpectLastline("success");

        ScriptInfoApiDto scriptInfo2 = new ScriptInfoApiDto();
        scriptInfo2.setScriptInfoId(11L);
        scriptInfo2.setUniqueUuid("page-uuid-002");
        scriptInfo2.setScriptNameZh("分页测试脚本2");
        scriptInfo2.setScriptName("page_test_script_2");
        scriptInfo2.setScriptType("shell");
        scriptInfo2.setExecuser("root");
        scriptInfo2.setPlatform("Windows");
        scriptInfo2.setEditState(1);
        scriptInfo2.setCategoryId(3L);
        scriptInfo2.setLevel(0);
        scriptInfo2.setScriptSource(expectedScriptSource);
        scriptInfo2.setCreatorName("分页测试用户2");
        scriptInfo2.setScriptLabel("shell,分页");
        scriptInfo2.setCategoryPath("/一级分类/三级分类");
        scriptInfo2.setScriptCategoryName("三级分类");
        scriptInfo2.setScriptInfoVersionId(11L);
        scriptInfo2.setSrcScriptUuid("page-version-uuid-002");
        scriptInfo2.setVersion("2.1");
        scriptInfo2.setIsDefault(1);
        scriptInfo2.setDescription("Shell分页测试脚本");
        scriptInfo2.setTimeout(600);
        scriptInfo2.setExpectType(2);
        scriptInfo2.setExpectLastline("0");

        List<ScriptInfoApiDto> scriptList = new ArrayList<>();
        scriptList.add(scriptInfo1);
        scriptList.add(scriptInfo2);

        // 创建PageInfo对象
        PageInfo<ScriptInfoApiDto> expectedPageInfo = new PageInfo<>(scriptList);
        expectedPageInfo.setPageNum(1);
        expectedPageInfo.setPageSize(10);
        expectedPageInfo.setTotal(2L);
        expectedPageInfo.setPages(1);
        expectedPageInfo.setHasNextPage(false);
        expectedPageInfo.setHasPreviousPage(false);

        // Mock方法调用
        doReturn(expectedPageInfo).when(iMyScriptService).selectScriptPageList(any(ScriptInfoQueryDto.class));

        // 执行测试
        PageInfo<ScriptInfoApiDto> result = scriptInfoApi.getScriptInfoPageList(queryDto);

        // 验证结果
        assertNotNull(result, description);
        assertEquals(2, result.getList().size(), description);
        assertEquals(1, result.getPageNum(), description);
        assertEquals(10, result.getPageSize(), description);
        assertEquals(2L, result.getTotal(), description);
        assertEquals(1, result.getPages(), description);
        assertFalse(result.isHasNextPage(), description);
        assertFalse(result.isHasPreviousPage(), description);

        // 验证返回的脚本信息
        assertEquals(expectedPageInfo.getList().get(0).getScriptInfoId(), result.getList().get(0).getScriptInfoId(), description);
        assertEquals(expectedPageInfo.getList().get(1).getScriptInfoId(), result.getList().get(1).getScriptInfoId(), description);

        // 验证scriptSource被正确设置
        assertEquals(expectedScriptSource, queryDto.getScriptSource(), description + " - scriptSource应该被正确设置");

        // 验证方法调用
        verify(iMyScriptService, times(1)).selectScriptPageList(queryDto);
    }

    @Test
    @DisplayName("测试getScriptInfoPageList方法 - 空分页返回")
    void testGetScriptInfoPageList_EmptyPage() {
        // 准备测试数据
        ScriptInfoQueryDto queryDto = new ScriptInfoQueryDto();
        queryDto.setScriptNameZh("不存在的分页脚本");
        queryDto.setPageNum(1);
        queryDto.setPageSize(10);
        queryDto.setScriptSource(null); // 测试null值

        // 创建空的PageInfo对象
        List<ScriptInfoApiDto> emptyList = new ArrayList<>();
        PageInfo<ScriptInfoApiDto> expectedPageInfo = new PageInfo<>(emptyList);
        expectedPageInfo.setPageNum(1);
        expectedPageInfo.setPageSize(10);
        expectedPageInfo.setTotal(0L);
        expectedPageInfo.setPages(0);
        expectedPageInfo.setHasNextPage(false);
        expectedPageInfo.setHasPreviousPage(false);

        // Mock方法调用
        doReturn(expectedPageInfo).when(iMyScriptService).selectScriptPageList(any(ScriptInfoQueryDto.class));

        // 执行测试
        PageInfo<ScriptInfoApiDto> result = scriptInfoApi.getScriptInfoPageList(queryDto);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getList().isEmpty());
        assertEquals(1, result.getPageNum());
        assertEquals(10, result.getPageSize());
        assertEquals(0L, result.getTotal());
        assertEquals(0, result.getPages());
        assertFalse(result.isHasNextPage());
        assertFalse(result.isHasPreviousPage());

        // 验证scriptSource被设置为0
        assertEquals(0, queryDto.getScriptSource());

        // 验证方法调用
        verify(iMyScriptService, times(1)).selectScriptPageList(queryDto);
    }

    @Test
    @DisplayName("测试getScriptInfoPageList方法 - 多页数据")
    void testGetScriptInfoPageList_MultiplePages() {
        // 准备测试数据
        ScriptInfoQueryDto queryDto = new ScriptInfoQueryDto();
        queryDto.setScriptNameZh("多页测试");
        queryDto.setPageNum(2);
        queryDto.setPageSize(5);
        queryDto.setScriptSource(1); // 工具箱脚本

        // 创建测试数据
        ScriptInfoApiDto scriptInfo = new ScriptInfoApiDto();
        scriptInfo.setScriptInfoId(20L);
        scriptInfo.setUniqueUuid("multi-page-uuid");
        scriptInfo.setScriptNameZh("多页测试脚本");
        scriptInfo.setScriptName("multi_page_script");
        scriptInfo.setScriptType("bat");
        scriptInfo.setScriptSource(1); // 工具箱脚本

        List<ScriptInfoApiDto> scriptList = new ArrayList<>();
        scriptList.add(scriptInfo);

        // 创建多页PageInfo对象
        PageInfo<ScriptInfoApiDto> expectedPageInfo = new PageInfo<>(scriptList);
        expectedPageInfo.setPageNum(2);
        expectedPageInfo.setPageSize(5);
        expectedPageInfo.setTotal(12L);
        expectedPageInfo.setPages(3);
        expectedPageInfo.setHasNextPage(true);
        expectedPageInfo.setHasPreviousPage(true);

        // Mock方法调用
        doReturn(expectedPageInfo).when(iMyScriptService).selectScriptPageList(any(ScriptInfoQueryDto.class));

        // 执行测试
        PageInfo<ScriptInfoApiDto> result = scriptInfoApi.getScriptInfoPageList(queryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getList().size());
        assertEquals(2, result.getPageNum());
        assertEquals(5, result.getPageSize());
        assertEquals(12L, result.getTotal());
        assertEquals(3, result.getPages());
        assertTrue(result.isHasNextPage());
        assertTrue(result.isHasPreviousPage());

        // 验证scriptSource保持不变
        assertEquals(1, queryDto.getScriptSource());

        // 验证方法调用
        verify(iMyScriptService, times(1)).selectScriptPageList(queryDto);
    }

    @Test
    @DisplayName("测试getMultiCategoryList方法 - 正常流程")
    void testGetMultiCategoryList_Normal() {
        // 准备测试数据
        CategoryQueryDto queryDto = new CategoryQueryDto();
        queryDto.setLevel(1);
        queryDto.setParentId(0L);
        queryDto.setName("测试多级分类");

        // 创建CategoryDto列表（Service层返回的数据）
        CategoryDto categoryDto1 = new CategoryDto();
        categoryDto1.setId(1L);
        categoryDto1.setParentId(0L);
        categoryDto1.setCode("CAT001");
        categoryDto1.setName("一级分类1");
        categoryDto1.setSort(1);
        categoryDto1.setLevel(1);
        categoryDto1.setDescription("一级分类1描述");
        categoryDto1.setCreatorId(100L);
        categoryDto1.setCreatorName("创建者1");
        categoryDto1.setUpdatorId(101L);
        categoryDto1.setUpdatorName("修改者1");

        CategoryDto categoryDto2 = new CategoryDto();
        categoryDto2.setId(2L);
        categoryDto2.setParentId(0L);
        categoryDto2.setCode("CAT002");
        categoryDto2.setName("一级分类2");
        categoryDto2.setSort(2);
        categoryDto2.setLevel(1);
        categoryDto2.setDescription("一级分类2描述");
        categoryDto2.setCreatorId(102L);
        categoryDto2.setCreatorName("创建者2");
        categoryDto2.setUpdatorId(103L);
        categoryDto2.setUpdatorName("修改者2");

        List<CategoryDto> categoryDtoList = new ArrayList<>();
        categoryDtoList.add(categoryDto1);
        categoryDtoList.add(categoryDto2);

        // Mock方法调用
        doReturn(categoryDtoList).when(categoryService).listMultiCategory(anyInt(), anyLong());

        // 执行测试
        List<CategoryApiDto> result = scriptInfoApi.getMultiCategoryList(queryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());

        // 验证第一个分类
        CategoryApiDto resultCategory1 = result.get(0);
        assertEquals(categoryDto1.getId(), resultCategory1.getId());
        assertEquals(categoryDto1.getParentId(), resultCategory1.getParentId());
        assertEquals(categoryDto1.getCode(), resultCategory1.getCode());
        assertEquals(categoryDto1.getName(), resultCategory1.getName());
        assertEquals(categoryDto1.getSort(), resultCategory1.getSort());
        assertEquals(categoryDto1.getLevel(), resultCategory1.getLevel());
        assertEquals(categoryDto1.getDescription(), resultCategory1.getDescription());
        assertEquals(categoryDto1.getCreatorId(), resultCategory1.getCreatorId());
        assertEquals(categoryDto1.getCreatorName(), resultCategory1.getCreatorName());
        assertEquals(categoryDto1.getUpdatorId(), resultCategory1.getUpdatorId());
        assertEquals(categoryDto1.getUpdatorName(), resultCategory1.getUpdatorName());

        // 验证第二个分类
        CategoryApiDto resultCategory2 = result.get(1);
        assertEquals(categoryDto2.getId(), resultCategory2.getId());
        assertEquals(categoryDto2.getName(), resultCategory2.getName());
        assertEquals(categoryDto2.getLevel(), resultCategory2.getLevel());
        assertEquals(categoryDto2.getCode(), resultCategory2.getCode());

        // 验证方法调用
        verify(categoryService, times(1)).listMultiCategory(queryDto.getLevel(), queryDto.getParentId());
    }

    @Test
    @DisplayName("测试getMultiCategoryList方法 - 空列表返回")
    void testGetMultiCategoryList_EmptyList() {
        // 准备测试数据
        CategoryQueryDto queryDto = new CategoryQueryDto();
        queryDto.setLevel(3);
        queryDto.setParentId(999L); // 不存在的父分类ID

        List<CategoryDto> emptyList = new ArrayList<>();

        // Mock方法调用
        doReturn(emptyList).when(categoryService).listMultiCategory(anyInt(), anyLong());

        // 执行测试
        List<CategoryApiDto> result = scriptInfoApi.getMultiCategoryList(queryDto);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证方法调用
        verify(categoryService, times(1)).listMultiCategory(queryDto.getLevel(), queryDto.getParentId());
    }

    @Test
    @DisplayName("测试getMultiCategoryList方法 - level和parentId为null")
    void testGetMultiCategoryList_NullParameters() {
        // 准备测试数据
        CategoryQueryDto queryDto = new CategoryQueryDto();
        queryDto.setLevel(null);
        queryDto.setParentId(null);
        queryDto.setName("测试null参数");

        // 创建测试返回数据
        CategoryDto categoryDto = new CategoryDto();
        categoryDto.setId(10L);
        categoryDto.setName("默认分类");
        categoryDto.setLevel(1);
        categoryDto.setCode("DEFAULT");

        List<CategoryDto> categoryDtoList = new ArrayList<>();
        categoryDtoList.add(categoryDto);

        // Mock方法调用
        doReturn(categoryDtoList).when(categoryService).listMultiCategory(any(), any());

        // 执行测试
        List<CategoryApiDto> result = scriptInfoApi.getMultiCategoryList(queryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("默认分类", result.get(0).getName());
        assertEquals("DEFAULT", result.get(0).getCode());

        // 验证方法调用（传入null值）
        verify(categoryService, times(1)).listMultiCategory(null, null);
    }

    @Test
    @DisplayName("测试getMultiCategoryList方法 - 二级分类查询")
    void testGetMultiCategoryList_SecondLevel() {
        // 准备测试数据
        CategoryQueryDto queryDto = new CategoryQueryDto();
        queryDto.setLevel(2);
        queryDto.setParentId(1L); // 查询一级分类ID为1的子分类

        // 创建二级分类数据
        CategoryDto subCategory1 = new CategoryDto();
        subCategory1.setId(11L);
        subCategory1.setParentId(1L);
        subCategory1.setCode("SUB001");
        subCategory1.setName("二级分类1");
        subCategory1.setSort(1);
        subCategory1.setLevel(2);
        subCategory1.setDescription("二级分类1描述");

        CategoryDto subCategory2 = new CategoryDto();
        subCategory2.setId(12L);
        subCategory2.setParentId(1L);
        subCategory2.setCode("SUB002");
        subCategory2.setName("二级分类2");
        subCategory2.setSort(2);
        subCategory2.setLevel(2);
        subCategory2.setDescription("二级分类2描述");

        List<CategoryDto> subCategoryList = new ArrayList<>();
        subCategoryList.add(subCategory1);
        subCategoryList.add(subCategory2);

        // Mock方法调用
        doReturn(subCategoryList).when(categoryService).listMultiCategory(anyInt(), anyLong());

        // 执行测试
        List<CategoryApiDto> result = scriptInfoApi.getMultiCategoryList(queryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());

        // 验证二级分类属性
        CategoryApiDto resultSub1 = result.get(0);
        assertEquals(11L, resultSub1.getId());
        assertEquals(1L, resultSub1.getParentId());
        assertEquals("二级分类1", resultSub1.getName());
        assertEquals(2, resultSub1.getLevel());

        CategoryApiDto resultSub2 = result.get(1);
        assertEquals(12L, resultSub2.getId());
        assertEquals(1L, resultSub2.getParentId());
        assertEquals("二级分类2", resultSub2.getName());
        assertEquals(2, resultSub2.getLevel());

        // 验证方法调用
        verify(categoryService, times(1)).listMultiCategory(2, 1L);
    }

    /**
     * 提供saveMyScript和updateMyScript方法的测试参数
     */
    static Stream<Arguments> provideSaveAndUpdateScriptTestData() {
        return Stream.of(
            Arguments.of("saveMyScript", "保存脚本测试", "save-script-uuid-001"),
            Arguments.of("updateMyScript", "更新脚本测试", "update-script-uuid-002")
        );
    }

    @ParameterizedTest
    @MethodSource("provideSaveAndUpdateScriptTestData")
    @DisplayName("测试saveMyScript和updateMyScript方法 - 参数化正常流程")
    void testSaveAndUpdateMyScript_ParameterizedNormal(String methodName, String description, String expectedUuid) throws ScriptException {
        // 准备测试数据
        ScriptInfoDto scriptInfoDto = createScriptInfoDto(expectedUuid, description);

        // 根据方法名选择不同的mock策略
        if ("saveMyScript".equals(methodName)) {
            // Mock saveScript方法（返回ValidationResultDto）
            doReturn(new ValidationResultDto()).when(iMyScriptService).saveScript(any(ScriptInfoDto.class));

            // 执行测试
            String result = scriptInfoApi.saveMyScript(scriptInfoDto);

            // 验证结果（API方法返回DTO中的UUID）
            assertNotNull(result);
            assertEquals(expectedUuid, result);

            // 验证方法调用
            verify(iMyScriptService, times(1)).saveScript(scriptInfoDto);
        } else if ("updateMyScript".equals(methodName)) {
            // Mock updateMyScript方法（返回ValidationResultDto）
            doReturn(new ValidationResultDto()).when(iMyScriptService).updateMyScript(any(ScriptInfoDto.class));

            // 执行测试
            String result = scriptInfoApi.updateMyScript(scriptInfoDto);

            // 验证结果（API方法返回DTO中的UUID）
            assertNotNull(result);
            assertEquals(expectedUuid, result);

            // 验证方法调用
            verify(iMyScriptService, times(1)).updateMyScript(scriptInfoDto);
        }
    }

    @Test
    @DisplayName("测试saveMyScript方法 - 正常流程")
    void testSaveMyScript_Normal() throws ScriptException {
        // 准备测试数据
        String expectedUuid = "save-test-uuid-123";
        ScriptInfoDto scriptInfoDto = createScriptInfoDto(expectedUuid, "保存脚本正常流程测试");

        // Mock方法调用
        doReturn(new ValidationResultDto()).when(iMyScriptService).saveScript(any(ScriptInfoDto.class));

        // 执行测试
        String result = scriptInfoApi.saveMyScript(scriptInfoDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedUuid, result);

        // 验证方法调用
        verify(iMyScriptService, times(1)).saveScript(scriptInfoDto);
    }

    @Test
    @DisplayName("测试saveMyScript方法 - 异常流程")
    void testSaveMyScript_Exception() throws ScriptException {
        // 准备测试数据
        ScriptInfoDto scriptInfoDto = createScriptInfoDto("error-uuid", "保存脚本异常测试");
        String errorMessage = "保存脚本失败";

        // Mock方法抛出异常
        doThrow(new ScriptException(errorMessage)).when(iMyScriptService).saveScript(any(ScriptInfoDto.class));

        // 执行测试并验证异常
        ScriptException exception = assertThrows(ScriptException.class, () -> {
            scriptInfoApi.saveMyScript(scriptInfoDto);
        });

        // 验证异常信息
        assertEquals(errorMessage, exception.getMessage());

        // 验证方法调用
        verify(iMyScriptService, times(1)).saveScript(scriptInfoDto);
    }

    @Test
    @DisplayName("测试updateMyScript方法 - 正常流程")
    void testUpdateMyScript_Normal() throws ScriptException {
        // 准备测试数据
        String expectedUuid = "update-test-uuid-456";
        ScriptInfoDto scriptInfoDto = createScriptInfoDto(expectedUuid, "更新脚本正常流程测试");

        // Mock方法调用
        doReturn(new ValidationResultDto()).when(iMyScriptService).updateMyScript(any(ScriptInfoDto.class));

        // 执行测试
        String result = scriptInfoApi.updateMyScript(scriptInfoDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedUuid, result);

        // 验证方法调用
        verify(iMyScriptService, times(1)).updateMyScript(scriptInfoDto);
    }

    @Test
    @DisplayName("测试updateMyScript方法 - 异常流程")
    void testUpdateMyScript_Exception() throws ScriptException {
        // 准备测试数据
        ScriptInfoDto scriptInfoDto = createScriptInfoDto("error-update-uuid", "更新脚本异常测试");
        String errorMessage = "更新脚本失败";

        // Mock方法抛出异常
        doThrow(new ScriptException(errorMessage)).when(iMyScriptService).updateMyScript(any(ScriptInfoDto.class));

        // 执行测试并验证异常
        ScriptException exception = assertThrows(ScriptException.class, () -> {
            scriptInfoApi.updateMyScript(scriptInfoDto);
        });

        // 验证异常信息
        assertEquals(errorMessage, exception.getMessage());

        // 验证方法调用
        verify(iMyScriptService, times(1)).updateMyScript(scriptInfoDto);
    }

    /**
     * 创建ScriptInfoDto测试数据的辅助方法
     */
    private ScriptInfoDto createScriptInfoDto(String srcScriptUuid, String description) {
        // 创建ScriptVersionDto
        ScriptVersionDto scriptVersionDto = new ScriptVersionDto();
        scriptVersionDto.setId(1L);
        scriptVersionDto.setInfoUniqueUuid("info-unique-uuid-123");
        scriptVersionDto.setSrcScriptUuid(srcScriptUuid);
        scriptVersionDto.setVersion("1.0");
        scriptVersionDto.setEditState(0); // 草稿状态
        scriptVersionDto.setUseState(1); // 启用状态
        scriptVersionDto.setDeleted(0); // 未删除
        scriptVersionDto.setIsDefault(1); // 默认版本
        scriptVersionDto.setDescription(description);
        scriptVersionDto.setTimeout(300);
        scriptVersionDto.setExpectType(1);
        scriptVersionDto.setExpectLastline("success");
        scriptVersionDto.setParamflag(1);
        scriptVersionDto.setLevel(0);
        scriptVersionDto.setCreatorId(100L);
        scriptVersionDto.setCreatorName("测试创建者");
        scriptVersionDto.setUpdatorId(101L);
        scriptVersionDto.setUpdatorName("测试修改者");

        // 创建ScriptInfoDto
        ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        scriptInfoDto.setId(1L);
        scriptInfoDto.setUniqueUuid("info-unique-uuid-123");
        scriptInfoDto.setScriptNameZh("测试脚本中文名");
        scriptInfoDto.setScriptName("test_script");
        scriptInfoDto.setScriptType("shell");
        scriptInfoDto.setExecuser("root");
        scriptInfoDto.setEditState(0);
        scriptInfoDto.setDeleted(0);
        scriptInfoDto.setScriptLabel("测试,标签");
        scriptInfoDto.setCategoryId(1L);
        scriptInfoDto.setCheckBeforeExec(1);
        scriptInfoDto.setShare(0);
        scriptInfoDto.setWhiteCommand(0);
        scriptInfoDto.setVisibleType(0);
        scriptInfoDto.setFemscript(0);
        scriptInfoDto.setCreatorId(100L);
        scriptInfoDto.setCreatorName("测试创建者");
        scriptInfoDto.setUpdatorId(101L);
        scriptInfoDto.setUpdatorName("测试修改者");
        scriptInfoDto.setPlatform("Linux");
        scriptInfoDto.setScriptSource(0);
        scriptInfoDto.setOrgCode("TEST_ORG");
        scriptInfoDto.setCategoryPath("/一级分类");
        scriptInfoDto.setIgnoreTipCmd(0);
        scriptInfoDto.setHisVersionCount(1);

        // 设置平台列表
        List<String> platforms = new ArrayList<>();
        platforms.add("Linux");
        platforms.add("Unix");
        scriptInfoDto.setPlatforms(platforms);

        // 关联ScriptVersionDto
        scriptInfoDto.setScriptVersionDto(scriptVersionDto);

        return scriptInfoDto;
    }

    /**
     * 提供publishScript和publishScriptAuto方法的测试参数
     */
    static Stream<Arguments> providePublishScriptTestData() {
        return Stream.of(
            Arguments.of("publishScript", "脚本发布测试", "script"),
            Arguments.of("publishScriptAuto", "脚本自动发布测试", "tools")
        );
    }

    @ParameterizedTest
    @MethodSource("providePublishScriptTestData")
    @DisplayName("测试publishScript和publishScriptAuto方法 - 参数化正常流程")
    void testPublishScript_ParameterizedNormal(String methodName, String description, String scriptSource) throws ScriptException {
        // 准备测试数据
        PublishDto publishDto = createPublishDto(description, scriptSource);

        // 根据方法名选择不同的mock策略
        if ("publishScript".equals(methodName)) {
            // Mock publishScript方法
            doNothing().when(iMyScriptService).publishScript(any(PublishDto.class));

            // 执行测试
            assertDoesNotThrow(() -> {
                scriptInfoApi.publishScript(publishDto);
            });

            // 验证方法调用
            verify(iMyScriptService, times(1)).publishScript(publishDto);
        } else if ("publishScriptAuto".equals(methodName)) {
            // Mock publishScriptAuto方法
            doNothing().when(iMyScriptService).publishScriptAuto(any(PublishDto.class));

            // 执行测试
            assertDoesNotThrow(() -> {
                scriptInfoApi.publishScriptAuto(publishDto);
            });

            // 验证方法调用
            verify(iMyScriptService, times(1)).publishScriptAuto(publishDto);
        }
    }

    @Test
    @DisplayName("测试publishScript方法 - 正常流程")
    void testPublishScript_Normal() throws ScriptException {
        // 准备测试数据
        PublishDto publishDto = createPublishDto("脚本发布正常流程测试", "script");

        // Mock方法调用
        doNothing().when(iMyScriptService).publishScript(any(PublishDto.class));

        // 执行测试
        assertDoesNotThrow(() -> {
            scriptInfoApi.publishScript(publishDto);
        });

        // 验证方法调用
        verify(iMyScriptService, times(1)).publishScript(publishDto);
    }

    @Test
    @DisplayName("测试publishScript方法 - 异常流程")
    void testPublishScript_Exception() throws ScriptException {
        // 准备测试数据
        PublishDto publishDto = createPublishDto("脚本发布异常测试", "script");
        String errorMessage = "脚本发布失败";

        // Mock方法抛出异常
        doThrow(new ScriptException(errorMessage)).when(iMyScriptService).publishScript(any(PublishDto.class));

        // 执行测试并验证异常
        ScriptException exception = assertThrows(ScriptException.class, () -> {
            scriptInfoApi.publishScript(publishDto);
        });

        // 验证异常信息
        assertEquals(errorMessage, exception.getMessage());

        // 验证方法调用
        verify(iMyScriptService, times(1)).publishScript(publishDto);
    }

    @Test
    @DisplayName("测试publishScriptAuto方法 - 正常流程")
    void testPublishScriptAuto_Normal() throws ScriptException {
        // 准备测试数据
        PublishDto publishDto = createPublishDto("脚本自动发布正常流程测试", "tools");

        // Mock方法调用
        doNothing().when(iMyScriptService).publishScriptAuto(any(PublishDto.class));

        // 执行测试
        assertDoesNotThrow(() -> {
            scriptInfoApi.publishScriptAuto(publishDto);
        });

        // 验证方法调用
        verify(iMyScriptService, times(1)).publishScriptAuto(publishDto);
    }

    @Test
    @DisplayName("测试publishScriptAuto方法 - 异常流程")
    void testPublishScriptAuto_Exception() throws ScriptException {
        // 准备测试数据
        PublishDto publishDto = createPublishDto("脚本自动发布异常测试", "tools");
        String errorMessage = "脚本自动发布失败";

        // Mock方法抛出异常
        doThrow(new ScriptException(errorMessage)).when(iMyScriptService).publishScriptAuto(any(PublishDto.class));

        // 执行测试并验证异常
        ScriptException exception = assertThrows(ScriptException.class, () -> {
            scriptInfoApi.publishScriptAuto(publishDto);
        });

        // 验证异常信息
        assertEquals(errorMessage, exception.getMessage());

        // 验证方法调用
        verify(iMyScriptService, times(1)).publishScriptAuto(publishDto);
    }

    @Test
    @DisplayName("测试publishScript方法 - 高风险级别发布")
    void testPublishScript_HighRiskLevel() throws ScriptException {
        // 准备测试数据
        PublishDto publishDto = createPublishDto("高风险脚本发布测试", "script");
        publishDto.setLevel(1); // 高风险级别
        publishDto.setIsForbidden(1); // 禁用旧版本

        // Mock方法调用
        doNothing().when(iMyScriptService).publishScript(any(PublishDto.class));

        // 执行测试
        assertDoesNotThrow(() -> {
            scriptInfoApi.publishScript(publishDto);
        });

        // 验证方法调用
        verify(iMyScriptService, times(1)).publishScript(publishDto);
    }

    @Test
    @DisplayName("测试publishScriptAuto方法 - 工具箱脚本自动发布")
    void testPublishScriptAuto_ToolboxScript() throws ScriptException {
        // 准备测试数据
        PublishDto publishDto = createPublishDto("工具箱脚本自动发布测试", "tools");
        publishDto.setLevel(0); // 白名单级别
        publishDto.setApprWorkitemId(null); // 自动发布无需审批工单

        // Mock方法调用
        doNothing().when(iMyScriptService).publishScriptAuto(any(PublishDto.class));

        // 执行测试
        assertDoesNotThrow(() -> {
            scriptInfoApi.publishScriptAuto(publishDto);
        });

        // 验证方法调用
        verify(iMyScriptService, times(1)).publishScriptAuto(publishDto);
    }

    /**
     * 创建PublishDto测试数据的辅助方法
     */
    private PublishDto createPublishDto(String description, String scriptSource) {
        PublishDto publishDto = new PublishDto();

        // 设置脚本版本ID数组
        Long[] ids = {1L, 2L, 3L};
        publishDto.setIds(ids);

        // 设置审核人信息
        publishDto.setAuditorName("测试审核人");
        publishDto.setAuditorId(200L);

        // 设置发布配置
        publishDto.setIsForbidden(0); // 不禁用旧版本
        publishDto.setPublicDesc(description);
        publishDto.setLevel(0); // 白名单级别

        // 设置脚本信息
        publishDto.setSrcScriptUuid("publish-test-uuid-123");
        publishDto.setScriptSource(scriptSource);

        // 设置双人复核工单ID
        publishDto.setApprWorkitemId(1000L);

        return publishDto;
    }

    @Test
    @DisplayName("测试exportScriptProduction方法 - 正常流程")
    void testExportScriptProduction_Normal() {
        // 准备测试数据
        List<String> srcScriptUuids = new ArrayList<>();
        srcScriptUuids.add("export-uuid-001");
        srcScriptUuids.add("export-uuid-002");
        srcScriptUuids.add("export-uuid-003");

        // 创建期望的返回结果
        ScriptFileImportExportApiDto expectedResult = createScriptFileImportExportApiDto(
            "exported_scripts", "zip", "导出的脚本文件内容".getBytes()
        );

        // Mock方法调用
        doReturn(expectedResult).when(releaseMediaService).exportScriptProductionApi(anyList());

        // 执行测试
        ScriptFileImportExportApiDto result = scriptInfoApi.exportScriptProduction(srcScriptUuids);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedResult.getFileName(), result.getFileName());
        assertEquals(expectedResult.getFileSuffix(), result.getFileSuffix());
        assertArrayEquals((byte[]) expectedResult.getFileContentByte(), (byte[]) result.getFileContentByte());

        // 验证方法调用
        verify(releaseMediaService, times(1)).exportScriptProductionApi(srcScriptUuids);
    }

    @Test
    @DisplayName("测试exportScriptProduction方法 - 空列表")
    void testExportScriptProduction_EmptyList() {
        // 准备测试数据
        List<String> emptyList = new ArrayList<>();

        // 创建空的返回结果
        ScriptFileImportExportApiDto expectedResult = createScriptFileImportExportApiDto(
            "empty_export", "zip", new byte[0]
        );

        // Mock方法调用
        doReturn(expectedResult).when(releaseMediaService).exportScriptProductionApi(anyList());

        // 执行测试
        ScriptFileImportExportApiDto result = scriptInfoApi.exportScriptProduction(emptyList);

        // 验证结果
        assertNotNull(result);
        assertEquals("empty_export", result.getFileName());
        assertEquals("zip", result.getFileSuffix());
        assertEquals(0, ((byte[]) result.getFileContentByte()).length);

        // 验证方法调用
        verify(releaseMediaService, times(1)).exportScriptProductionApi(emptyList);
    }

    @Test
    @DisplayName("测试exportScriptProduction方法 - null参数")
    void testExportScriptProduction_NullParameter() {
        // Mock方法调用
        doReturn(null).when(releaseMediaService).exportScriptProductionApi(any());

        // 执行测试
        ScriptFileImportExportApiDto result = scriptInfoApi.exportScriptProduction(null);

        // 验证结果
        assertNull(result);

        // 验证方法调用
        verify(releaseMediaService, times(1)).exportScriptProductionApi(null);
    }

    @Test
    @DisplayName("测试importScriptProduction方法 - 正常流程")
    void testImportScriptProduction_Normal() {
        // 准备测试数据
        ScriptFileImportExportApiDto importDto = createScriptFileImportExportApiDto(
            "import_scripts", "zip", "导入的脚本文件内容".getBytes()
        );

        // 创建期望的返回结果
        Map<String, String> expectedResult = new HashMap<>();
        expectedResult.put("script-uuid-001", "success");
        expectedResult.put("script-uuid-002", "success");
        expectedResult.put("script-uuid-003", "success");
        expectedResult.put("total", "3");
        expectedResult.put("success_count", "3");
        expectedResult.put("fail_count", "0");

        // Mock方法调用
        doReturn(expectedResult).when(releaseMediaService).importScriptProduction(any(ScriptFileImportExportApiDto.class));

        // 执行测试
        Map<String, String> result = scriptInfoApi.importScriptProduction(importDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(6, result.size());
        assertEquals("success", result.get("script-uuid-001"));
        assertEquals("success", result.get("script-uuid-002"));
        assertEquals("success", result.get("script-uuid-003"));
        assertEquals("3", result.get("total"));
        assertEquals("3", result.get("success_count"));
        assertEquals("0", result.get("fail_count"));

        // 验证方法调用
        verify(releaseMediaService, times(1)).importScriptProduction(importDto);
    }

    @Test
    @DisplayName("测试importScriptProduction方法 - 部分失败")
    void testImportScriptProduction_PartialFailure() {
        // 准备测试数据
        ScriptFileImportExportApiDto importDto = createScriptFileImportExportApiDto(
            "import_scripts_partial_fail", "zip", "部分失败的脚本文件内容".getBytes()
        );

        // 创建期望的返回结果（部分失败）
        Map<String, String> expectedResult = new HashMap<>();
        expectedResult.put("script-uuid-001", "success");
        expectedResult.put("script-uuid-002", "fail");
        expectedResult.put("script-uuid-003", "success");
        expectedResult.put("total", "3");
        expectedResult.put("success_count", "2");
        expectedResult.put("fail_count", "1");
        expectedResult.put("error_message", "script-uuid-002导入失败：脚本格式不正确");

        // Mock方法调用
        doReturn(expectedResult).when(releaseMediaService).importScriptProduction(any(ScriptFileImportExportApiDto.class));

        // 执行测试
        Map<String, String> result = scriptInfoApi.importScriptProduction(importDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(7, result.size());
        assertEquals("success", result.get("script-uuid-001"));
        assertEquals("fail", result.get("script-uuid-002"));
        assertEquals("success", result.get("script-uuid-003"));
        assertEquals("3", result.get("total"));
        assertEquals("2", result.get("success_count"));
        assertEquals("1", result.get("fail_count"));
        assertTrue(result.get("error_message").contains("导入失败"));

        // 验证方法调用
        verify(releaseMediaService, times(1)).importScriptProduction(importDto);
    }

    @Test
    @DisplayName("测试importScriptProduction方法 - 空结果")
    void testImportScriptProduction_EmptyResult() {
        // 准备测试数据
        ScriptFileImportExportApiDto importDto = createScriptFileImportExportApiDto(
            "empty_import", "zip", new byte[0]
        );

        // 创建空的返回结果
        Map<String, String> expectedResult = new HashMap<>();
        expectedResult.put("total", "0");
        expectedResult.put("success_count", "0");
        expectedResult.put("fail_count", "0");

        // Mock方法调用
        doReturn(expectedResult).when(releaseMediaService).importScriptProduction(any(ScriptFileImportExportApiDto.class));

        // 执行测试
        Map<String, String> result = scriptInfoApi.importScriptProduction(importDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("0", result.get("total"));
        assertEquals("0", result.get("success_count"));
        assertEquals("0", result.get("fail_count"));

        // 验证方法调用
        verify(releaseMediaService, times(1)).importScriptProduction(importDto);
    }

    @Test
    @DisplayName("测试importScriptProduction方法 - null参数")
    void testImportScriptProduction_NullParameter() {
        // Mock方法调用
        doReturn(null).when(releaseMediaService).importScriptProduction(any());

        // 执行测试
        Map<String, String> result = scriptInfoApi.importScriptProduction(null);

        // 验证结果
        assertNull(result);

        // 验证方法调用
        verify(releaseMediaService, times(1)).importScriptProduction(null);
    }

    /**
     * 创建ScriptFileImportExportApiDto测试数据的辅助方法
     */
    private ScriptFileImportExportApiDto createScriptFileImportExportApiDto(String fileName, String fileSuffix, byte[] fileContent) {
        ScriptFileImportExportApiDto dto = new ScriptFileImportExportApiDto();
        dto.setFileName(fileName);
        dto.setFileSuffix(fileSuffix);
        dto.setFileContentByte(fileContent);
        return dto;
    }

    @Test
    @DisplayName("测试getTaskRuntimeInfoByInstanceId方法 - 正常流程")
    void testGetTaskRuntimeInfoByInstanceId_Normal() {
        // 准备测试数据
        TaskRuntimeQueryApiDto queryDto = new TaskRuntimeQueryApiDto();
        queryDto.setTaskInstanceId(1001L);
        queryDto.setPageNum(1);
        queryDto.setPageSize(10);

        // 创建期望的返回结果
        TaskRuntimeApiDto runtime1 = createTaskRuntimeApiDto(1L, "*************", 8080, "test_script_1", 20);
        TaskRuntimeApiDto runtime2 = createTaskRuntimeApiDto(2L, "*************", 8080, "test_script_2", 10);

        List<TaskRuntimeApiDto> runtimeList = new ArrayList<>();
        runtimeList.add(runtime1);
        runtimeList.add(runtime2);

        PageInfo<TaskRuntimeApiDto> expectedPageInfo = new PageInfo<>(runtimeList);
        expectedPageInfo.setPageNum(1);
        expectedPageInfo.setPageSize(10);
        expectedPageInfo.setTotal(2L);
        expectedPageInfo.setPages(1);
        expectedPageInfo.setHasNextPage(false);
        expectedPageInfo.setHasPreviousPage(false);

        // Mock方法调用
        doReturn(expectedPageInfo).when(taskRuntimeService).getTaskRuntimeInfoByInstanceId(any(TaskRuntimeQueryApiDto.class));

        // 执行测试
        PageInfo<TaskRuntimeApiDto> result = scriptInfoApi.getTaskRuntimeInfoByInstanceId(queryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getList().size());
        assertEquals(1, result.getPageNum());
        assertEquals(10, result.getPageSize());
        assertEquals(2L, result.getTotal());
        assertEquals(1, result.getPages());
        assertFalse(result.isHasNextPage());
        assertFalse(result.isHasPreviousPage());

        // 验证返回的任务运行时信息
        TaskRuntimeApiDto resultRuntime1 = result.getList().get(0);
        assertEquals(1L, resultRuntime1.getId());
        assertEquals("*************", resultRuntime1.getAgentIp());
        assertEquals(8080, resultRuntime1.getAgentPort());
        assertEquals("test_script_1", resultRuntime1.getScriptName());
        assertEquals(20, resultRuntime1.getState()); // 完成状态

        TaskRuntimeApiDto resultRuntime2 = result.getList().get(1);
        assertEquals(2L, resultRuntime2.getId());
        assertEquals("*************", resultRuntime2.getAgentIp());
        assertEquals(10, resultRuntime2.getState()); // 运行中状态

        // 验证方法调用
        verify(taskRuntimeService, times(1)).getTaskRuntimeInfoByInstanceId(queryDto);
    }

    @Test
    @DisplayName("测试getTaskRuntimeInfoByInstanceId方法 - 空结果")
    void testGetTaskRuntimeInfoByInstanceId_EmptyResult() {
        // 准备测试数据
        TaskRuntimeQueryApiDto queryDto = new TaskRuntimeQueryApiDto();
        queryDto.setTaskInstanceId(9999L); // 不存在的实例ID
        queryDto.setPageNum(1);
        queryDto.setPageSize(10);

        // 创建空的PageInfo对象
        List<TaskRuntimeApiDto> emptyList = new ArrayList<>();
        PageInfo<TaskRuntimeApiDto> expectedPageInfo = new PageInfo<>(emptyList);
        expectedPageInfo.setPageNum(1);
        expectedPageInfo.setPageSize(10);
        expectedPageInfo.setTotal(0L);
        expectedPageInfo.setPages(0);
        expectedPageInfo.setHasNextPage(false);
        expectedPageInfo.setHasPreviousPage(false);

        // Mock方法调用
        doReturn(expectedPageInfo).when(taskRuntimeService).getTaskRuntimeInfoByInstanceId(any(TaskRuntimeQueryApiDto.class));

        // 执行测试
        PageInfo<TaskRuntimeApiDto> result = scriptInfoApi.getTaskRuntimeInfoByInstanceId(queryDto);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getList().isEmpty());
        assertEquals(1, result.getPageNum());
        assertEquals(10, result.getPageSize());
        assertEquals(0L, result.getTotal());
        assertEquals(0, result.getPages());
        assertFalse(result.isHasNextPage());
        assertFalse(result.isHasPreviousPage());

        // 验证方法调用
        verify(taskRuntimeService, times(1)).getTaskRuntimeInfoByInstanceId(queryDto);
    }

    @Test
    @DisplayName("测试getStdoutByTaskRuntimeId方法 - 正常流程")
    void testGetStdoutByTaskRuntimeId_Normal() throws ScriptException {
        // 准备测试数据
        Long taskRuntimeId = 1001L;
        String expectedStdout = "脚本执行成功\n输出结果：Hello World\n执行完成";

        // Mock方法调用
        doReturn(expectedStdout).when(taskRuntimeService).getOutPutMessage(anyLong());

        // 执行测试
        String result = scriptInfoApi.getStdoutByTaskRuntimeId(taskRuntimeId);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedStdout, result);
        assertTrue(result.contains("脚本执行成功"));
        assertTrue(result.contains("Hello World"));
        assertTrue(result.contains("执行完成"));

        // 验证方法调用
        verify(taskRuntimeService, times(1)).getOutPutMessage(taskRuntimeId);
    }

    @Test
    @DisplayName("测试getStdoutByTaskRuntimeId方法 - 异常流程")
    void testGetStdoutByTaskRuntimeId_Exception() throws ScriptException {
        // 准备测试数据
        Long taskRuntimeId = 1002L;
        String errorMessage = "获取标准输出失败";

        // Mock方法抛出异常
        doThrow(new ScriptException(errorMessage)).when(taskRuntimeService).getOutPutMessage(anyLong());

        // 执行测试并验证异常
        ScriptException exception = assertThrows(ScriptException.class, () -> {
            scriptInfoApi.getStdoutByTaskRuntimeId(taskRuntimeId);
        });

        // 验证异常信息
        assertEquals(errorMessage, exception.getMessage());

        // 验证方法调用
        verify(taskRuntimeService, times(1)).getOutPutMessage(taskRuntimeId);
    }

    @Test
    @DisplayName("测试getStdoutByTaskRuntimeId方法 - 空输出")
    void testGetStdoutByTaskRuntimeId_EmptyOutput() throws ScriptException {
        // 准备测试数据
        Long taskRuntimeId = 1003L;
        String emptyStdout = "";

        // Mock方法调用
        doReturn(emptyStdout).when(taskRuntimeService).getOutPutMessage(anyLong());

        // 执行测试
        String result = scriptInfoApi.getStdoutByTaskRuntimeId(taskRuntimeId);

        // 验证结果
        assertNotNull(result);
        assertEquals("", result);
        assertTrue(result.isEmpty());

        // 验证方法调用
        verify(taskRuntimeService, times(1)).getOutPutMessage(taskRuntimeId);
    }

    @Test
    @DisplayName("测试getAgentStdoutByTaskIdAndAddress方法 - 正常流程")
    void testGetAgentStdoutByTaskIdAndAddress_Normal() {
        // 准备测试数据
        RetryScriptInstanceApiDto retryDto = createRetryScriptInstanceApiDto(2001L, "*************", 8080);
        String expectedStdout = "Agent执行结果：\n任务ID: 2001\n执行状态: 成功\n输出内容: 脚本执行完成";

        // Mock方法调用
        doReturn(expectedStdout).when(taskRuntimeService).getAgentStdoutByTaskIdAndAddress(any(RetryScriptInstanceApiDto.class));

        // 执行测试
        String result = scriptInfoApi.getAgentStdoutByTaskIdAndAddress(retryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedStdout, result);
        assertTrue(result.contains("Agent执行结果"));
        assertTrue(result.contains("任务ID: 2001"));
        assertTrue(result.contains("执行状态: 成功"));

        // 验证方法调用
        verify(taskRuntimeService, times(1)).getAgentStdoutByTaskIdAndAddress(retryDto);
    }

    @Test
    @DisplayName("测试getAgentStdoutByTaskIdAndAddress方法 - 空结果")
    void testGetAgentStdoutByTaskIdAndAddress_EmptyResult() {
        // 准备测试数据
        RetryScriptInstanceApiDto retryDto = createRetryScriptInstanceApiDto(2002L, "*************", 8080);
        String emptyStdout = "";

        // Mock方法调用
        doReturn(emptyStdout).when(taskRuntimeService).getAgentStdoutByTaskIdAndAddress(any(RetryScriptInstanceApiDto.class));

        // 执行测试
        String result = scriptInfoApi.getAgentStdoutByTaskIdAndAddress(retryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("", result);
        assertTrue(result.isEmpty());

        // 验证方法调用
        verify(taskRuntimeService, times(1)).getAgentStdoutByTaskIdAndAddress(retryDto);
    }

    @Test
    @DisplayName("测试getAgentStdoutByTaskIdAndAddress方法 - null参数")
    void testGetAgentStdoutByTaskIdAndAddress_NullParameter() {
        // Mock方法调用
        doReturn(null).when(taskRuntimeService).getAgentStdoutByTaskIdAndAddress(any());

        // 执行测试
        String result = scriptInfoApi.getAgentStdoutByTaskIdAndAddress(null);

        // 验证结果
        assertNull(result);

        // 验证方法调用
        verify(taskRuntimeService, times(1)).getAgentStdoutByTaskIdAndAddress(null);
    }

    /**
     * 创建TaskRuntimeApiDto测试数据的辅助方法
     */
    private TaskRuntimeApiDto createTaskRuntimeApiDto(Long id, String agentIp, Integer agentPort, String scriptName, Integer state) {
        TaskRuntimeApiDto dto = new TaskRuntimeApiDto();
        dto.setId(id);
        dto.setScriptTaskId(100L + id);
        dto.setTaskInstanceId(1000L + id);
        dto.setSrcScriptUuid("runtime-uuid-" + id);
        dto.setState(state); // 5-跳过 10-运行中 20-完成 30-失败 60-终止
        dto.setScriptName(scriptName);
        dto.setAgentIp(agentIp);
        dto.setAgentPort(agentPort);
        dto.setExecUser("root");
        dto.setProviderIp("***********");
        dto.setProviderPort(9090);

        // 设置时间戳
        long currentTime = System.currentTimeMillis();
        dto.setStartTime(new Timestamp(currentTime - 60000)); // 1分钟前开始
        if (state == 20 || state == 30) { // 完成或失败状态设置结束时间
            dto.setEndTime(new Timestamp(currentTime));
            dto.setElapsedTime("00:01:00");
        }

        dto.setExpectLastline("success");
        dto.setExpectType(1); // lastLine类型
        dto.setTimeout(0); // 未超时
        dto.setTimeoutValue(300L); // 5分钟超时
        dto.setStartType(0); // 脚本服务化
        dto.setCreateTime(new Timestamp(currentTime - 120000)); // 2分钟前创建
        dto.setAgentTaskId(3000L + id);
        dto.setBizId("biz-" + id);
        dto.setScriptTaskIpsId(4000L + id);
        dto.setRetry(false);

        return dto;
    }

    /**
     * 创建RetryScriptInstanceApiDto测试数据的辅助方法
     */
    private RetryScriptInstanceApiDto createRetryScriptInstanceApiDto(Long callerTaskId, String agentIp, Integer agentPort) {
        RetryScriptInstanceApiDto dto = new RetryScriptInstanceApiDto();
        dto.setCallerTaskId(callerTaskId);
        dto.setAgentIp(agentIp);
        dto.setAgentPort(agentPort);

        // 创建用户信息（如果需要的话）
        // CurrentUserDto currentUserDto = new CurrentUserDto();
        // currentUserDto.setUserId(1000L);
        // currentUserDto.setUserName("测试用户");
        // dto.setCurrentUserDto(currentUserDto);

        return dto;
    }

    @Test
    @DisplayName("测试getDefaultScriptInfo方法 - 正常流程")
    void testGetDefaultScriptInfo_Normal() throws ScriptException {
        // 准备测试数据
        ScriptInfoQueryDto queryDto = new ScriptInfoQueryDto();
        queryDto.setSrcScriptUuid("default-script-uuid-123");
        queryDto.setScriptInfoId(1L);
        queryDto.setQueryScriptContentFlag(true);
        queryDto.setQueryScriptParamsFlag(true);
        queryDto.setQueryScriptAttachmentFlag(true);

        ScriptDubboInfoDto expectedResult = new ScriptDubboInfoDto();
        expectedResult.setId(1L);
        expectedResult.setScriptNameZh("默认脚本");
        expectedResult.setScriptName("default_script");
        expectedResult.setScriptType("shell");
        expectedResult.setExecuser("root");
        expectedResult.setContent("echo 'default script'");
        expectedResult.setVersion("1.0");
        expectedResult.setUniqueUuid("default-unique-uuid-456");
        expectedResult.setSrcScriptUuid("default-script-uuid-123");
        expectedResult.setPlatform("Linux");
        expectedResult.setParamList(new ArrayList<>());
        expectedResult.setAttachmentList(new ArrayList<>());

        // Mock方法调用
        doReturn(expectedResult).when(iMyScriptService).getDefaultScriptInfoApi(any(ScriptInfoQueryDto.class));

        // 执行测试
        ScriptDubboInfoDto result = scriptInfoApi.getDefaultScriptInfo(queryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedResult.getId(), result.getId());
        assertEquals(expectedResult.getScriptNameZh(), result.getScriptNameZh());
        assertEquals(expectedResult.getScriptName(), result.getScriptName());
        assertEquals(expectedResult.getScriptType(), result.getScriptType());
        assertEquals(expectedResult.getExecuser(), result.getExecuser());
        assertEquals(expectedResult.getContent(), result.getContent());
        assertEquals(expectedResult.getVersion(), result.getVersion());
        assertEquals(expectedResult.getUniqueUuid(), result.getUniqueUuid());
        assertEquals(expectedResult.getSrcScriptUuid(), result.getSrcScriptUuid());
        assertEquals(expectedResult.getPlatform(), result.getPlatform());

        // 验证方法调用
        verify(iMyScriptService, times(1)).getDefaultScriptInfoApi(queryDto);
    }

    @Test
    @DisplayName("测试getDefaultScriptInfo方法 - 异常流程")
    void testGetDefaultScriptInfo_Exception() throws ScriptException {
        // 准备测试数据
        ScriptInfoQueryDto queryDto = new ScriptInfoQueryDto();
        queryDto.setSrcScriptUuid("invalid-default-uuid");

        String errorMessage = "默认脚本不存在";

        // Mock方法抛出异常
        doThrow(new ScriptException(errorMessage)).when(iMyScriptService).getDefaultScriptInfoApi(any(ScriptInfoQueryDto.class));

        // 执行测试并验证异常
        ScriptException exception = assertThrows(ScriptException.class, () -> {
            scriptInfoApi.getDefaultScriptInfo(queryDto);
        });

        // 验证异常信息
        assertEquals(errorMessage, exception.getMessage());

        // 验证方法调用
        verify(iMyScriptService, times(1)).getDefaultScriptInfoApi(queryDto);
    }

    @Test
    @DisplayName("测试uploadAttachment方法 - 正常流程")
    void testUploadAttachment_Normal() throws ScriptException, IOException {
        // 准备测试数据
        AttachmentDto inputDto = createAttachmentDto("test_attachment.txt", "测试附件内容".getBytes());
        AttachmentDto expectedResult = createAttachmentDto("test_attachment.txt", "测试附件内容".getBytes());
        expectedResult.setId(1001L); // 设置上传后的ID

        // Mock方法调用
        doReturn(expectedResult).when(attachmentService).uploadAttachment(any(AttachmentDto.class));

        // 执行测试
        AttachmentDto result = scriptInfoApi.uploadAttachment(inputDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedResult.getId(), result.getId());
        assertEquals(expectedResult.getName(), result.getName());
        assertEquals(expectedResult.getSrcScriptUuid(), result.getSrcScriptUuid());
        assertEquals(expectedResult.getSize(), result.getSize());
        assertArrayEquals(expectedResult.getContents(), result.getContents());

        // 验证方法调用
        verify(attachmentService, times(1)).uploadAttachment(inputDto);
    }

    @Test
    @DisplayName("测试uploadAttachment方法 - ScriptException异常")
    void testUploadAttachment_ScriptException() throws ScriptException, IOException {
        // 准备测试数据
        AttachmentDto inputDto = createAttachmentDto("error_attachment.txt", "错误附件内容".getBytes());
        String errorMessage = "附件上传失败";

        // Mock方法抛出ScriptException
        doThrow(new ScriptException(errorMessage)).when(attachmentService).uploadAttachment(any(AttachmentDto.class));

        // 执行测试并验证异常
        ScriptException exception = assertThrows(ScriptException.class, () -> {
            scriptInfoApi.uploadAttachment(inputDto);
        });

        // 验证异常信息
        assertEquals(errorMessage, exception.getMessage());

        // 验证方法调用
        verify(attachmentService, times(1)).uploadAttachment(inputDto);
    }

    @Test
    @DisplayName("测试uploadAttachment方法 - IOException异常")
    void testUploadAttachment_IOException() throws ScriptException, IOException {
        // 准备测试数据
        AttachmentDto inputDto = createAttachmentDto("io_error_attachment.txt", "IO错误附件内容".getBytes());
        String errorMessage = "文件读写异常";

        // Mock方法抛出IOException
        doThrow(new IOException(errorMessage)).when(attachmentService).uploadAttachment(any(AttachmentDto.class));

        // 执行测试并验证异常
        IOException exception = assertThrows(IOException.class, () -> {
            scriptInfoApi.uploadAttachment(inputDto);
        });

        // 验证异常信息
        assertEquals(errorMessage, exception.getMessage());

        // 验证方法调用
        verify(attachmentService, times(1)).uploadAttachment(inputDto);
    }

    @Test
    @DisplayName("测试uploadAttachmentTemp方法 - 正常流程")
    void testUploadAttachmentTemp_Normal() throws ScriptException, IOException {
        // 准备测试数据
        List<ScriptFileAttachmentTempApiDto> inputList = createScriptFileAttachmentTempApiDtoList();
        ScriptAttachmentTempMegDto expectedResult = createScriptAttachmentTempMegDto(true, "上传成功");

        // Mock方法调用
        doReturn(expectedResult).when(attachmentService).uploadAttachmentTemp(anyList());

        // 执行测试
        ScriptAttachmentTempMegDto result = scriptInfoApi.uploadAttachmentTemp(inputList);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals("上传成功", result.getMessage());
        assertNotNull(result.getData());
        assertEquals(2, result.getData().size());

        // 验证方法调用
        verify(attachmentService, times(1)).uploadAttachmentTemp(inputList);
    }

    @Test
    @DisplayName("测试uploadAttachmentTemp方法 - ScriptException异常")
    void testUploadAttachmentTemp_ScriptException() throws ScriptException, IOException {
        // 准备测试数据
        List<ScriptFileAttachmentTempApiDto> inputList = createScriptFileAttachmentTempApiDtoList();
        String errorMessage = "临时附件上传失败";

        // Mock方法抛出ScriptException
        doThrow(new ScriptException(errorMessage)).when(attachmentService).uploadAttachmentTemp(anyList());

        // 执行测试并验证异常
        ScriptException exception = assertThrows(ScriptException.class, () -> {
            scriptInfoApi.uploadAttachmentTemp(inputList);
        });

        // 验证异常信息
        assertEquals(errorMessage, exception.getMessage());

        // 验证方法调用
        verify(attachmentService, times(1)).uploadAttachmentTemp(inputList);
    }

    @Test
    @DisplayName("测试uploadAttachmentTemp方法 - IOException异常")
    void testUploadAttachmentTemp_IOException() throws ScriptException, IOException {
        // 准备测试数据
        List<ScriptFileAttachmentTempApiDto> inputList = createScriptFileAttachmentTempApiDtoList();
        String errorMessage = "临时附件文件读写异常";

        // Mock方法抛出IOException
        doThrow(new IOException(errorMessage)).when(attachmentService).uploadAttachmentTemp(anyList());

        // 执行测试并验证异常
        IOException exception = assertThrows(IOException.class, () -> {
            scriptInfoApi.uploadAttachmentTemp(inputList);
        });

        // 验证异常信息
        assertEquals(errorMessage, exception.getMessage());

        // 验证方法调用
        verify(attachmentService, times(1)).uploadAttachmentTemp(inputList);
    }

    @Test
    @DisplayName("测试getScriptCategoryIconList方法 - 正常流程")
    void testGetScriptCategoryIconList_Normal() {
        // 准备测试数据
        List<String> srcScriptUuids = new ArrayList<>();
        srcScriptUuids.add("icon-script-uuid-001");
        srcScriptUuids.add("icon-script-uuid-002");
        srcScriptUuids.add("icon-script-uuid-003");

        // 创建期望的返回结果
        List<ScriptCategoryIconDto> expectedResult = createScriptCategoryIconDtoList();

        // Mock方法调用
        doReturn(expectedResult).when(infoService).getScriptCategoryIconList(anyList());

        // 执行测试
        List<ScriptCategoryIconDto> result = scriptInfoApi.getScriptCategoryIconList(srcScriptUuids);

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size());

        // 验证第一个图标信息
        ScriptCategoryIconDto icon1 = result.get(0);
        assertEquals("icon-script-uuid-001", icon1.getScriptSrcUuid());
        assertEquals("/一级分类/二级分类1", icon1.getCategoryPath());
        assertEquals("shell-icon", icon1.getIcon());

        // 验证第二个图标信息
        ScriptCategoryIconDto icon2 = result.get(1);
        assertEquals("icon-script-uuid-002", icon2.getScriptSrcUuid());
        assertEquals("/一级分类/二级分类2", icon2.getCategoryPath());
        assertEquals("python-icon", icon2.getIcon());

        // 验证第三个图标信息
        ScriptCategoryIconDto icon3 = result.get(2);
        assertEquals("icon-script-uuid-003", icon3.getScriptSrcUuid());
        assertEquals("/一级分类/二级分类3", icon3.getCategoryPath());
        assertEquals("bat-icon", icon3.getIcon());

        // 验证方法调用
        verify(infoService, times(1)).getScriptCategoryIconList(srcScriptUuids);
    }

    @Test
    @DisplayName("测试getScriptCategoryIconList方法 - 空列表")
    void testGetScriptCategoryIconList_EmptyList() {
        // 准备测试数据
        List<String> emptyList = new ArrayList<>();
        List<ScriptCategoryIconDto> expectedResult = new ArrayList<>();

        // Mock方法调用
        doReturn(expectedResult).when(infoService).getScriptCategoryIconList(anyList());

        // 执行测试
        List<ScriptCategoryIconDto> result = scriptInfoApi.getScriptCategoryIconList(emptyList);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证方法调用
        verify(infoService, times(1)).getScriptCategoryIconList(emptyList);
    }

    @Test
    @DisplayName("测试getScriptCategoryIconList方法 - null参数")
    void testGetScriptCategoryIconList_NullParameter() {
        // Mock方法调用
        doReturn(null).when(infoService).getScriptCategoryIconList(any());

        // 执行测试
        List<ScriptCategoryIconDto> result = scriptInfoApi.getScriptCategoryIconList(null);

        // 验证结果
        assertNull(result);

        // 验证方法调用
        verify(infoService, times(1)).getScriptCategoryIconList(null);
    }

    /**
     * 创建AttachmentDto测试数据的辅助方法
     */
    private AttachmentDto createAttachmentDto(String fileName, byte[] contents) {
        AttachmentDto dto = new AttachmentDto();
        dto.setSrcScriptUuid("attachment-script-uuid-123");
        dto.setName(fileName);
        dto.setSize((long) contents.length);
        dto.setUploadtime(new Timestamp(System.currentTimeMillis()));
        dto.setContents(contents);
        dto.setIsTempFlag(0); // 非临时附件
        return dto;
    }

    /**
     * 创建ScriptFileAttachmentTempApiDto列表测试数据的辅助方法
     */
    private List<ScriptFileAttachmentTempApiDto> createScriptFileAttachmentTempApiDtoList() {
        List<ScriptFileAttachmentTempApiDto> list = new ArrayList<>();

        // 第一个临时附件
        ScriptFileAttachmentTempApiDto dto1 = new ScriptFileAttachmentTempApiDto();
        dto1.setSrcScriptUuid("temp-script-uuid-001");

        List<ScriptAttachmentTempApiDto> attachments1 = new ArrayList<>();
        ScriptAttachmentTempApiDto attachment1 = new ScriptAttachmentTempApiDto();
        attachment1.setFileName("temp_file_1");
        attachment1.setFileSuffix("txt");
        attachment1.setFileContentByte("临时附件1内容".getBytes());
        attachments1.add(attachment1);

        dto1.setAttachment(attachments1);
        list.add(dto1);

        // 第二个临时附件
        ScriptFileAttachmentTempApiDto dto2 = new ScriptFileAttachmentTempApiDto();
        dto2.setSrcScriptUuid("temp-script-uuid-002");

        List<ScriptAttachmentTempApiDto> attachments2 = new ArrayList<>();
        ScriptAttachmentTempApiDto attachment2 = new ScriptAttachmentTempApiDto();
        attachment2.setFileName("temp_file_2");
        attachment2.setFileSuffix("log");
        attachment2.setFileContentByte("临时附件2内容".getBytes());
        attachments2.add(attachment2);

        dto2.setAttachment(attachments2);
        list.add(dto2);

        return list;
    }

    /**
     * 创建ScriptAttachmentTempMegDto测试数据的辅助方法
     */
    private ScriptAttachmentTempMegDto createScriptAttachmentTempMegDto(boolean success, String message) {
        ScriptAttachmentTempMegDto dto = new ScriptAttachmentTempMegDto();
        dto.setSuccess(success);
        dto.setMessage(message);

        if (success) {
            List<ScriptAfterUploadAttachmentTempApiDto> dataList = new ArrayList<>();

            // 第一个上传结果
            ScriptAfterUploadAttachmentTempApiDto data1 = new ScriptAfterUploadAttachmentTempApiDto(
                "temp-script-uuid-001", new Long[]{1001L, 1002L}
            );
            dataList.add(data1);

            // 第二个上传结果
            ScriptAfterUploadAttachmentTempApiDto data2 = new ScriptAfterUploadAttachmentTempApiDto(
                "temp-script-uuid-002", new Long[]{1003L, 1004L}
            );
            dataList.add(data2);

            dto.setData(dataList);
        }

        return dto;
    }

    /**
     * 创建ScriptCategoryIconDto列表测试数据的辅助方法
     */
    private List<ScriptCategoryIconDto> createScriptCategoryIconDtoList() {
        List<ScriptCategoryIconDto> list = new ArrayList<>();

        // 第一个图标信息
        ScriptCategoryIconDto icon1 = new ScriptCategoryIconDto();
        icon1.setScriptSrcUuid("icon-script-uuid-001");
        icon1.setCategoryPath("/一级分类/二级分类1");
        icon1.setIcon("shell-icon");
        list.add(icon1);

        // 第二个图标信息
        ScriptCategoryIconDto icon2 = new ScriptCategoryIconDto();
        icon2.setScriptSrcUuid("icon-script-uuid-002");
        icon2.setCategoryPath("/一级分类/二级分类2");
        icon2.setIcon("python-icon");
        list.add(icon2);

        // 第三个图标信息
        ScriptCategoryIconDto icon3 = new ScriptCategoryIconDto();
        icon3.setScriptSrcUuid("icon-script-uuid-003");
        icon3.setCategoryPath("/一级分类/二级分类3");
        icon3.setIcon("bat-icon");
        list.add(icon3);

        return list;
    }
}
