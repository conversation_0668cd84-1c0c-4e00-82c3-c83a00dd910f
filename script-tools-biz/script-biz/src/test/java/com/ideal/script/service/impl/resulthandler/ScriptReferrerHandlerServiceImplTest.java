package com.ideal.script.service.impl.resulthandler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ideal.common.util.batch.BatchHandler;
import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.ScriptReferrerInfoDto;
import com.ideal.script.service.IScriptReferrerInfoService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ScriptReferrerHandlerServiceImpl Unit Test
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class ScriptReferrerHandlerServiceImplTest {
    @Mock
    private IScriptReferrerInfoService scriptReferrerInfoService;
    @Mock
    private BatchHandler batchHandler;

    @InjectMocks
    private ScriptReferrerHandlerServiceImpl handler;

    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
    }

    /**
     * Parameterized test data source
     */
    static Stream<Object[]> testDataProvider() {
        return Stream.of(
            // Normal SAVE flow - multiple UUIDs
            new Object[]{
                createSaveDto(new String[]{"uuid1", "uuid2"}),
                "Normal SAVE flow - multiple UUIDs",
                true,
                false
            },
            // Normal SAVE flow - single UUID
            new Object[]{
                createSaveDto(new String[]{"uuid1"}),
                "Normal SAVE flow - single UUID",
                true,
                false
            },
            // Normal DELETE flow
            new Object[]{
                createDeleteDto(),
                "Normal DELETE flow",
                true,
                false
            }
        );
    }

    @ParameterizedTest
    @MethodSource("testDataProvider")
    @DisplayName("Parameterized test - various business scenarios")
    void testScriptReferrerInfoHandler_parameterized(ScriptReferrerInfoDto dto, String testName, boolean shouldSucceed, boolean shouldThrowException) throws Exception {
        List<ScriptReferrerInfoDto> dtoList = Collections.singletonList(dto);
        String json = objectMapper.writeValueAsString(dtoList);

        // Mock batchHandler
        doNothing().when(batchHandler).batchData(anyList(), any());

        if (shouldSucceed) {
            assertDoesNotThrow(() -> handler.scriptReferrerInfoHandler(json));
            
            // Verify batchHandler is called (if there is data to process)
            if (Enums.ScriptReferrerType.SAVE.getValue().equals(dto.getBizType()) && 
                dto.getScriptSrcUuid() != null && dto.getScriptSrcUuid().length > 0) {
                verify(batchHandler, atLeastOnce()).batchData(anyList(), any());
            } else if (Enums.ScriptReferrerType.DELETE.getValue().equals(dto.getBizType())) {
                verify(batchHandler, atLeastOnce()).batchData(anyList(), any());
            }
        } else {
            assertThrows(ScriptException.class, () -> handler.scriptReferrerInfoHandler(json));
        }
    }

    @Test
    @DisplayName("Exception flow - JSON parsing exception")
    void testScriptReferrerInfoHandler_jsonException() {
        String invalidJson = "not a json";
        ScriptException ex = assertThrows(ScriptException.class, () -> handler.scriptReferrerInfoHandler(invalidJson));
        assertTrue(ex.getMessage().contains("script.monitor.referrer.mq.analysis.error"));
    }

    @Test
    @DisplayName("Exception flow - batchHandler throws exception")
    void testScriptReferrerInfoHandler_batchHandlerException() throws Exception {
        ScriptReferrerInfoDto dto = createSaveDto(new String[]{"uuid1"});
        List<ScriptReferrerInfoDto> dtoList = Collections.singletonList(dto);
        String json = objectMapper.writeValueAsString(dtoList);

        doThrow(new RuntimeException("batch error")).when(batchHandler).batchData(anyList(), any());

        ScriptException ex = assertThrows(ScriptException.class, () -> handler.scriptReferrerInfoHandler(json));
        assertTrue(ex.getMessage().contains("script.monitor.referrer.mq.analysis.error"));
    }

    @Test
    @DisplayName("Boundary case - empty message")
    void testScriptReferrerInfoHandler_emptyMessage() {
        assertDoesNotThrow(() -> handler.scriptReferrerInfoHandler("[]"));
    }

    @Test
    @DisplayName("Boundary case - null message")
    void testScriptReferrerInfoHandler_nullMessage() {
        assertThrows(ScriptException.class, () -> handler.scriptReferrerInfoHandler(null));
    }

    @Test
    @DisplayName("Boundary case - empty list")
    void testScriptReferrerInfoHandler_emptyList() {
        assertDoesNotThrow(() -> handler.scriptReferrerInfoHandler("[]"));
    }

    /**
     * Create SAVE type DTO
     */
    private static ScriptReferrerInfoDto createSaveDto(String[] scriptSrcUuid) {
        ScriptReferrerInfoDto dto = new ScriptReferrerInfoDto();
        dto.setBizType(Enums.ScriptReferrerType.SAVE.getValue());
        dto.setScriptSrcUuid(scriptSrcUuid);
        dto.setReferrerBizId(1L);
        dto.setSceneName("Test Scene");
        dto.setFlowName("Test Flow");
        dto.setFlowVersion("1.0");
        dto.setReferrerName("Test Referrer");
        return dto;
    }

    /**
     * Create DELETE type DTO
     */
    private static ScriptReferrerInfoDto createDeleteDto() {
        ScriptReferrerInfoDto dto = new ScriptReferrerInfoDto();
        dto.setBizType(Enums.ScriptReferrerType.DELETE.getValue());
        dto.setReferrerBizId(1L);
        dto.setSceneName("Test Scene");
        dto.setFlowName("Test Flow");
        dto.setFlowVersion("1.0");
        dto.setReferrerName("Test Referrer");
        return dto;
    }

    /**
     * Create unknown business type DTO
     */
    private static ScriptReferrerInfoDto createUnknownBizTypeDto() {
        ScriptReferrerInfoDto dto = new ScriptReferrerInfoDto();
        dto.setBizType("unknown");
        dto.setReferrerBizId(1L);
        dto.setSceneName("Test Scene");
        dto.setFlowName("Test Flow");
        dto.setFlowVersion("1.0");
        dto.setReferrerName("Test Referrer");
        return dto;
    }
} 