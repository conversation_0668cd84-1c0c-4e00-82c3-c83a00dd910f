package com.ideal.script.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ideal.script.dto.ScriptInfoDto;
import com.ideal.script.dto.ScriptVersionDto;
import com.ideal.script.mapper.InfoMapper;
import com.ideal.script.mapper.InfoVersionMapper;
import com.ideal.script.model.bean.ScriptInfoQueryBean;
import com.ideal.script.model.bean.TaskApplyBean;
import com.ideal.script.model.dto.interaction.agent.UserInfoDto;
import com.ideal.script.model.entity.Info;
import com.ideal.script.model.entity.InfoVersion;
import com.ideal.system.api.IUserInfo;
import com.ideal.system.dto.UserInfoApiDto;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InfoVersionServiceImplTest {

    @Mock
    private InfoVersionMapper mockInfoVersionMapper;
    @Mock
    private InfoMapper mockInfoMapper;
    @Mock
    private IUserInfo iUserInfo;
    @InjectMocks
    private InfoVersionServiceImpl infoVersionServiceImplUnderTest;


    @Test
    void testSelectInfoVersionById() {
        // Setup
        // Configure InfoVersionMapper.selectInfoVersionById(...).
        final InfoVersion infoVersion = new InfoVersion();
        infoVersion.setId(1L);
        infoVersion.setInfoUniqueUuid("infoUniqueUuid");
        infoVersion.setSrcScriptUuid("srcScriptUuid");
        infoVersion.setVersion("version");
        infoVersion.setEditState(1);
        when(mockInfoVersionMapper.selectInfoVersionById(1L)).thenReturn(infoVersion);

        // Run the test
        final ScriptVersionDto result = infoVersionServiceImplUnderTest.selectInfoVersionById(1L);
        assertNotNull(result);
        System.out.println(result);
        // Verify the results
        Mockito.verify(mockInfoVersionMapper,times(1)).selectInfoVersionById(1L);
    }

    @Test
    void testSelectInfoVersionBySrcScriptUuid() {
        // Setup
        // Configure InfoVersionMapper.selectInfoVersionBysrcScriptUuid(...).
        final InfoVersion infoVersion = new InfoVersion();
        infoVersion.setId(1L);
        infoVersion.setInfoUniqueUuid("infoUniqueUuid");
        infoVersion.setSrcScriptUuid("srcScriptUuid");
        infoVersion.setVersion("version");
        infoVersion.setEditState(1);
        when(mockInfoVersionMapper.selectInfoVersionBysrcScriptUuid("srcScriptUuid")).thenReturn(infoVersion);

        // Run the test
        final ScriptVersionDto result = infoVersionServiceImplUnderTest.selectInfoVersionBySrcScriptUuid("srcScriptUuid");

        assertNotNull(result);
        System.out.println(result);
        // Verify the results
        Mockito.verify(mockInfoVersionMapper,times(1)).selectInfoVersionBysrcScriptUuid("srcScriptUuid");
    }

    @Test
    void testSelectInfoVersionList() {
        // Setup
        final ScriptVersionDto infoVersionDto = new ScriptVersionDto();
        infoVersionDto.setId(1L);
        infoVersionDto.setInfoUniqueUuid("infoUniqueUuid");
        infoVersionDto.setSrcScriptUuid("srcScriptUuid");
        infoVersionDto.setVersion("version");
        infoVersionDto.setEditState(1);

        // Configure InfoVersionMapper.selectInfoVersionList(...).
        final InfoVersion infoVersion = new InfoVersion();
        infoVersion.setId(1L);
        infoVersion.setInfoUniqueUuid("infoUniqueUuid");
        infoVersion.setSrcScriptUuid("srcScriptUuid");
        infoVersion.setVersion("version");
        infoVersion.setEditState(1);
        Page<InfoVersion> page  = new Page<>();
        page.add(infoVersion);
        when(mockInfoVersionMapper.selectInfoVersionList(any(InfoVersion.class))).thenReturn(page);

        // Run the test
        final PageInfo<ScriptVersionDto> result = infoVersionServiceImplUnderTest.selectInfoVersionList(infoVersionDto, 1,
                50);
        assertNotNull(result);
        System.out.println(result);
        // Verify the results
        Mockito.verify(mockInfoVersionMapper,times(1)).selectInfoVersionList(any(InfoVersion.class));
        // Verify the results
    }



    @Test
    void testInsertInfoVersion() {
        // Setup
        final ScriptVersionDto infoVersionDto = new ScriptVersionDto();
        infoVersionDto.setId(1L);
        infoVersionDto.setInfoUniqueUuid("infoUniqueUuid");
        infoVersionDto.setSrcScriptUuid("srcScriptUuid");
        infoVersionDto.setVersion("version");
        infoVersionDto.setEditState(0);

        // Run the test
        infoVersionServiceImplUnderTest.insertInfoVersion(infoVersionDto);

        // Verify the results
        verify(mockInfoVersionMapper).insertInfoVersion(any(InfoVersion.class));
    }

    @Test
    void testUpdateInfoVersion() {
        // Setup
        final ScriptVersionDto infoVersionDto = new ScriptVersionDto();
        infoVersionDto.setId(1L);
        infoVersionDto.setInfoUniqueUuid("infoUniqueUuid");
        infoVersionDto.setSrcScriptUuid("srcScriptUuid");
        infoVersionDto.setVersion("version");
        infoVersionDto.setEditState(1);

        // Run the test
        infoVersionServiceImplUnderTest.updateInfoVersion(infoVersionDto);

        // Verify the results
        verify(mockInfoVersionMapper).updateInfoVersion(any(InfoVersion.class));
    }

    @Test
    void testDeleteInfoVersionByIds() {
        // Setup
        // Run the test
        infoVersionServiceImplUnderTest.deleteInfoVersionByIds(new Long[]{1L,2L});

        // Verify the results
        verify(mockInfoVersionMapper).deleteInfoVersionByIds(any(Long[].class));
    }

    @Test
    void testDeleteInfoVersionById() {
        // Setup
        when(mockInfoVersionMapper.deleteInfoVersionById(1L)).thenReturn(1);

        // Run the test
        final int result = infoVersionServiceImplUnderTest.deleteInfoVersionById(1L);

        // Verify the results
        assertThat(result).isEqualTo(1);
    }

    @Test
    void testGetScriptTypeBySrcScriptUuid() {
        // Setup
        when(mockInfoVersionMapper.getScriptTypeBySrcScriptUuid("srcScriptUuid")).thenReturn("result");

        // Run the test
        final String result = infoVersionServiceImplUnderTest.getScriptTypeBySrcScriptUuid("srcScriptUuid");

        // Verify the results
        assertThat(result).isEqualTo("result");
    }
    @Test
    void isInWhiteList(){
        // Setup
        when(mockInfoVersionMapper.isInWhiteList(1L)).thenReturn(true);

        // Run the test
        final boolean result = infoVersionServiceImplUnderTest.isInWhiteList(1L);

        // Verify the results
        assertThat(result).isTrue();
    }


    @Test
    void validSrcScriptUuidExist() {

        final boolean result = infoVersionServiceImplUnderTest.validSrcScriptUuidExist("srcScriptUuid");
        verify(mockInfoVersionMapper).validSrcScriptUuidExist(anyString());
    }

    @Test
    void updateInfoVersionDefaultValue() {
        final int  result = infoVersionServiceImplUnderTest.updateInfoVersionDefaultValue(mock(InfoVersion.class));
        verify(mockInfoVersionMapper).updateInfoVersionDefaultValue(any(InfoVersion.class));
    }

    @Test
    void queryGroupUserInfoListByUserId() {
        List<UserInfoApiDto> userInfoApiDtoList = new ArrayList<>();
        UserInfoApiDto userInfoApiDto = new UserInfoApiDto();
        userInfoApiDto.setId(1L);
        userInfoApiDto.setLoginName("ideal");
        userInfoApiDtoList.add(userInfoApiDto);
        when(iUserInfo.queryGroupUserInfoListByUserId(anyLong())).thenReturn(userInfoApiDtoList);
        final List<UserInfoDto> result = infoVersionServiceImplUnderTest.queryGroupUserInfoListByUserId(1L);
        assertNotNull(result);
    }

    @Test
    void getInfoVersionList() {
        when(mockInfoVersionMapper.getInfoVersionList(any(ScriptInfoQueryBean.class))).thenReturn(new ArrayList<>());
        final List<TaskApplyBean> result =  infoVersionServiceImplUnderTest.getInfoVersionList(new ScriptInfoQueryBean());
        assertNotNull(result);
    }

    @Test
    void getInfoDefaultVersion() {
        when(mockInfoVersionMapper.getInfoDefaultVersion(any(ScriptInfoQueryBean.class))).thenReturn(new TaskApplyBean());
        final TaskApplyBean result = infoVersionServiceImplUnderTest.getInfoDefaultVersion(new ScriptInfoQueryBean());
        assertNotNull(result);
    }

    @Test
    void getInfoVersion() {
        when(mockInfoVersionMapper.getInfoVersion(any(ScriptInfoQueryBean.class))).thenReturn(new TaskApplyBean());
        final TaskApplyBean result = infoVersionServiceImplUnderTest.getInfoVersion(new ScriptInfoQueryBean());
        assertNotNull(result);
    }

    @Test
    void getInfoByVersionUuid() {
        String srcScriptUuid = "srcScriptUuid";

        InfoVersion infoVersion = new InfoVersion();
        infoVersion.setInfoUniqueUuid("infoUniqueUuid");
        Info info = new Info();
        info.setUniqueUuid("infoUniqueUuid");

        when(mockInfoVersionMapper.selectInfoVersionBysrcScriptUuid(srcScriptUuid)).thenReturn(infoVersion);
        when(mockInfoMapper.selectInfoByUniqueUuid("infoUniqueUuid")).thenReturn(info);
        ScriptInfoDto infoByVersionUuid = infoVersionServiceImplUnderTest.getInfoByVersionUuid(srcScriptUuid);

        assertNotNull(infoByVersionUuid);
    }

    @Test
    void testGetInfoVersionList() {
        List<ScriptVersionDto> infoVersionInfoList = infoVersionServiceImplUnderTest.getInfoVersionInfoList(new Long[]{0L});
        assertNotNull(infoVersionInfoList);

    }

    @Test
    void getVersionIdByIds() {
        Long[] ids = new Long[]{1L,2L,3L};

        //返回列表
        Info info = new Info();
        info.setId(1L);
        info.setEditState(0);


        Info info1 = new Info();
        info1.setId(2L);
        info1.setEditState(1);

        Info info2 = new Info();
        info2.setId(3L);
        info2.setEditState(1);

        List<Info> infoList = new ArrayList<>();
        infoList.add(info);
        infoList.add(info1);
        infoList.add(info2);

        List<Long> idList1 = new ArrayList<>();
        idList1.add(1L);

        List<Long> idList2 = new ArrayList<>();
        idList2.add(2L);
        idList2.add(3L);

        List<String> list1 = new ArrayList<>();
        list1.add("uuid1");

        List<String> list2 = new ArrayList<>();
        list2.add("uuid2");
        list2.add("uuid3");


        List<InfoVersion> infoVersionList1 = new ArrayList<>();
        InfoVersion infoVersion = new InfoVersion();
        infoVersion.setId(1L);
        infoVersionList1.add(infoVersion);

        List<InfoVersion> infoVersionList2= new ArrayList<>();
        InfoVersion infoVersion1 = new InfoVersion();
        infoVersion1.setId(1L);
        InfoVersion infoVersion2 = new InfoVersion();
        infoVersion2.setId(1L);

        infoVersionList2.add(infoVersion1);
        infoVersionList2.add(infoVersion2);



        when(mockInfoMapper.getUniqueUuidByIds(eq(idList1),eq("edit"))).thenReturn(list1);
        when(mockInfoMapper.getUniqueUuidByIds(eq(idList2),eq("publish"))).thenReturn(list2);

        when(mockInfoVersionMapper.selectInfoVersionListForEdit(eq(list1))).thenReturn(infoVersionList1);
        when(mockInfoVersionMapper.selectInfoVersionListForDefault(eq(list2))).thenReturn(infoVersionList2);


        when(mockInfoMapper.selectInfoByIds(ids)).thenReturn(infoList);

        List<Long> versionIdByIds = infoVersionServiceImplUnderTest.getVersionIdByIds(ids);
        assertNotNull(versionIdByIds);
    }

    @Test
    void getInfoDefaultVersionByUuid() {
        ScriptVersionDto infoDefaultVersionByUuid = infoVersionServiceImplUnderTest.getInfoDefaultVersionByUuid("srcScriptUuid");
        assertNotNull(infoDefaultVersionByUuid);
    }

    @Test
    public void testSelectIdBySrcScriptUuid() {
        // Arrange
        String srcScriptUuid = "test-uuid";
        Long expectedId = 123L;
        when(mockInfoVersionMapper.selectIdBySrcScriptUuid(srcScriptUuid)).thenReturn(expectedId);

        // Act
        Long actualId = infoVersionServiceImplUnderTest.selectIdBySrcScriptUuid(srcScriptUuid);

        // Assert
        assertEquals(expectedId, actualId);
        verify(mockInfoVersionMapper, times(1)).selectIdBySrcScriptUuid(srcScriptUuid);
    }
}
