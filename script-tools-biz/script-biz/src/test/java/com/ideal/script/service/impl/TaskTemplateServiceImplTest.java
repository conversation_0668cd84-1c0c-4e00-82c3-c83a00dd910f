package com.ideal.script.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.spring.SpringUtil;
import com.ideal.sc.util.CurrentUserUtil;
import com.ideal.script.common.util.FileSizeValidUtil;
import com.ideal.script.dto.AttachmentDto;
import com.ideal.script.dto.AttachmentUploadDto;
import com.ideal.script.dto.ParameterValidationDto;
import com.ideal.script.dto.ScriptInfoDto;
import com.ideal.script.dto.ScriptInfoQueryDto;
import com.ideal.script.dto.ScriptVersionDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.TaskAttachmentMapper;
import com.ideal.script.mapper.TaskAttachmentTemplateMapper;
import com.ideal.script.mapper.TaskGroupsTemplateMapper;
import com.ideal.script.mapper.TaskIpsMapper;
import com.ideal.script.mapper.TaskIpsTemplateMapper;
import com.ideal.script.mapper.TaskParamsMapper;
import com.ideal.script.mapper.TaskParamsTemplateMapper;
import com.ideal.script.mapper.TaskTemplateMapper;
import com.ideal.script.model.bean.TaskCloneBean;
import com.ideal.script.model.dto.AgentInfoDto;
import com.ideal.script.model.dto.AuditRelationDto;
import com.ideal.script.model.dto.ParameterDto;
import com.ideal.script.model.dto.ScriptExecAuditDto;
import com.ideal.script.model.dto.ScriptTaskApplyResDto;
import com.ideal.script.model.dto.StartCommonTaskDto;
import com.ideal.script.model.dto.TaskApplyQueryDto;
import com.ideal.script.model.dto.TaskAttachmentDto;
import com.ideal.script.model.dto.TaskDto;
import com.ideal.script.model.dto.TaskGroupsDto;
import com.ideal.script.model.dto.TaskStartDto;
import com.ideal.script.model.dto.TaskTemplateDto;
import com.ideal.script.model.dto.interaction.agent.SystemComputerGroupDto;
import com.ideal.script.model.entity.AgentInfo;
import com.ideal.script.model.entity.Task;
import com.ideal.script.model.entity.TaskAttachment;
import com.ideal.script.model.entity.TaskGroups;
import com.ideal.script.model.entity.TaskIps;
import com.ideal.script.model.entity.TaskParams;
import com.ideal.script.service.AuditSource;
import com.ideal.script.service.IAgentInfoService;
import com.ideal.script.service.IAuditRelationService;
import com.ideal.script.service.ICategoryService;
import com.ideal.script.service.IInfoVersionService;
import com.ideal.script.service.IMyScriptService;
import com.ideal.script.service.ITaskApplyService;
import com.ideal.script.service.ITaskGroupsService;
import com.ideal.script.service.ITaskService;
import com.ideal.script.service.ITaskTemplateService;
import com.ideal.script.service.impl.builders.MyScriptServiceScripts;
import com.ideal.system.api.IUserInfo;
import com.ideal.system.common.component.model.CurrentUser;
import com.ideal.system.dto.PermissionUserInfoApiDto;
import com.ideal.system.dto.UserInfoApiDto;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.argThat;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class TaskTemplateServiceImplTest {

    private static MockedStatic<SpringUtil> springUtilMockedStatic;

    @Mock
    private ITaskService mockTaskService;
    @Mock
    private TaskParamsMapper mockTaskParamsMapper;
    @Mock
    private TaskIpsMapper mockTaskIpsMapper;
    @Mock
    private TaskAttachmentMapper mockTaskAttachmentMapper;
    @Mock
    private ICategoryService mockCategoryService;
    @Mock
    private ITaskGroupsService mockTaskGroupsService;
    @Mock
    private TaskTemplateMapper mockTaskTemplateMapper;
    @Mock
    private TaskAttachmentTemplateMapper mockTaskAttachmentTemplateMapper;
    @Mock
    private TaskParamsTemplateMapper mockTaskParamsTemplateMapper;
    @Mock
    private TaskIpsTemplateMapper mockTaskIpsTemplateMapper;
    @Mock
    private TaskGroupsTemplateMapper mockTaskGroupsTemplateMapper;
    @Mock
    private IMyScriptService mockMyScriptService;
    @Mock
    private ITaskApplyService mockTaskApplyService;
    @Mock
    private AuditSource mockTaskApplySource;
    @Mock
    private IAgentInfoService mockAgentInfoService;
    @Mock
    private FileSizeValidUtil mockFileSizeValidUtil;
    @Mock
    private IAuditRelationService mockAuditRelationService;
    @Mock
    private IInfoVersionService mockInfoVersionService;

    @Mock
    private MyScriptServiceScripts myScriptServiceScripts;

    @Mock
    private IUserInfo userInfo;

    @Mock
    private ITaskTemplateService mockTaskTemplateService;

    @Spy
    @InjectMocks
    private TaskTemplateServiceImpl taskTemplateServiceUnderTest;

    private static MockedStatic<CurrentUserUtil> currentUserUtilMockedStatic;
    private static MockedStatic<BeanUtils> beanUtilsMockedStatic;

    @BeforeAll
    static void setUpStaticMethod() {
        CurrentUser currentUser = new CurrentUser();
        currentUser.setId(1L);
        currentUser.setLoginName("test");
        currentUser.setFullName("测试用户");
        currentUser.setOrgId(1L);
        currentUser.setOrgCode("orgCode1#");
        currentUser.setSupervisor(false);

        currentUserUtilMockedStatic = Mockito.mockStatic(CurrentUserUtil.class);
        currentUserUtilMockedStatic.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);

        beanUtilsMockedStatic = Mockito.mockStatic(BeanUtils.class);
        // 添加SpringUtil的静态mock
        springUtilMockedStatic = Mockito.mockStatic(SpringUtil.class);
        springUtilMockedStatic.when(() -> SpringUtil.getBean(anyString())).thenReturn(null);
    }

    @AfterAll
    static void tearDownStaticMethod() {
        currentUserUtilMockedStatic.close();
        beanUtilsMockedStatic.close();
        springUtilMockedStatic.close();
    }

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(taskTemplateServiceUnderTest, "customerName", "ideal");
    }

    @Test
    @DisplayName("测试创建克隆任务-成功场景")
    void testCreateCloneTask_Success() throws ScriptException {
        // Setup
        TaskStartDto taskStartDto = new TaskStartDto();
        taskStartDto.setScriptTaskId(1L);
        taskStartDto.setTaskName("Test Task");
        taskStartDto.setTaskType(1);

        CurrentUser user = new CurrentUser();
        user.setId(1L);

        // Mock TaskDto
        TaskDto taskDto = new TaskDto();
        taskDto.setId(1L);
        when(mockTaskService.selectTaskById(anyLong())).thenReturn(taskDto);

        // Mock AuditRelationDto
        AuditRelationDto auditRelationDto = new AuditRelationDto();
        auditRelationDto.setAuditUserId(2L);
        when(mockAuditRelationService.selectAuditRelationByTaskId(anyLong())).thenReturn(auditRelationDto);

        // Mock Task
        Task task = new Task();
        task.setId(1L);
        when(mockTaskTemplateMapper.insertTaskTemplate(any(Task.class))).thenReturn(1);
        beanUtilsMockedStatic.when(() -> BeanUtils.copy(any(TaskDto.class), eq(Task.class)))
                .thenReturn(task);

        // Mock TaskParams
        List<TaskParams> taskParamsList = new ArrayList<>();
        TaskParams taskParams = new TaskParams();
        taskParams.setId(1L);
        taskParams.setScriptTaskId(1L);
        taskParamsList.add(taskParams);
        when(mockTaskParamsMapper.selectTaskParamsList(any())).thenReturn(taskParamsList);

        // Mock TaskIps
        List<TaskIps> taskIpsList = new ArrayList<>();
        TaskIps taskIps = new TaskIps();
        taskIps.setId(1L);
        taskIps.setScriptTaskId(1L);
        taskIps.setAlreadyimpFlag(1);
        taskIpsList.add(taskIps);
        when(mockTaskIpsMapper.selectTaskIpsList(any())).thenReturn(taskIpsList);

        // Mock TaskGroups
        List<TaskGroupsDto> taskGroupsList = new ArrayList<>();
        TaskGroupsDto taskGroupsDto = new TaskGroupsDto();
        taskGroupsDto.setId(1L);
        taskGroupsDto.setScriptTaskId(1L);
        taskGroupsList.add(taskGroupsDto);
        when(mockTaskGroupsService.selectTaskGroupsByServiceId(any(), anyLong())).thenReturn(taskGroupsList);

        // Mock BeanUtils.copy for TaskGroups
        TaskGroups taskGroups = new TaskGroups();
        taskGroups.setId(null);
        taskGroups.setScriptTaskId(1L);
        beanUtilsMockedStatic.when(() -> BeanUtils.copy(any(TaskGroupsDto.class), eq(TaskGroups.class)))
                .thenReturn(taskGroups);

        // Mock TaskAttachment
        TaskAttachment attachment = new TaskAttachment();
        attachment.setId(1L);
        attachment.setScriptTaskId(1L);
        attachment.setName("test.txt");
        List<TaskAttachment> attachments = Collections.singletonList(attachment);
        when(mockTaskAttachmentMapper.selectTaskAttachmentList(any())).thenReturn(attachments);

        // Mock batchData method
        ArgumentCaptor<List<?>> listCaptor = ArgumentCaptor.forClass((Class) List.class);
        doNothing().when(taskTemplateServiceUnderTest).batchData(anyList(), any());

        // Execute
        taskTemplateServiceUnderTest.createCloneTask(taskStartDto, user);

        // Verify
        verify(mockTaskTemplateMapper).insertTaskTemplate(any(Task.class));
        verify(taskTemplateServiceUnderTest, times(4)).batchData(listCaptor.capture(), any());

        // Verify captured lists
        List<List<?>> capturedLists = listCaptor.getAllValues();

        // 根据实际的调用顺序验证
        // 第一个是TaskAttachment
        List<?> attachmentCapture = capturedLists.get(0);
        assertTrue(attachmentCapture.get(0) instanceof TaskAttachment);
        TaskAttachment modifiedAttachment = (TaskAttachment) attachmentCapture.get(0);
        assertNull(modifiedAttachment.getId());
        assertEquals(1L, modifiedAttachment.getScriptTaskId());

        // 第二个是TaskParams
        List<?> taskParamsCapture = capturedLists.get(1);
        assertTrue(taskParamsCapture.get(0) instanceof TaskParams);
        TaskParams modifiedTaskParams = (TaskParams) taskParamsCapture.get(0);
        assertNull(modifiedTaskParams.getId());
        assertEquals(1L, modifiedTaskParams.getScriptTaskId());

        // 第三个是TaskIps
        List<?> taskIpsCapture = capturedLists.get(2);
        assertTrue(taskIpsCapture.get(0) instanceof TaskIps);
        TaskIps modifiedTaskIps = (TaskIps) taskIpsCapture.get(0);
        assertNull(modifiedTaskIps.getId());
        assertEquals(1L, modifiedTaskIps.getScriptTaskId());
        assertEquals(0, modifiedTaskIps.getAlreadyimpFlag());

        // 第四个是TaskGroups
        List<?> taskGroupsCapture = capturedLists.get(3);
        assertTrue(taskGroupsCapture.get(0) instanceof TaskGroups);
        TaskGroups modifiedTaskGroups = (TaskGroups) taskGroupsCapture.get(0);
        assertNull(modifiedTaskGroups.getId());
        assertEquals(1L, modifiedTaskGroups.getScriptTaskId());
    }

    @Test
    @DisplayName("测试创建克隆任务-任务ID为空")
    void testCreateCloneTask_NullTaskId() {
        // Setup
        TaskStartDto taskStartDto = new TaskStartDto();
        CurrentUser user = new CurrentUser();

        // Execute & Verify
        assertThrows(ScriptException.class, () ->
                taskTemplateServiceUnderTest.createCloneTask(taskStartDto, user)
        );
    }

    @Test
    @DisplayName("测试从任务申请创建克隆任务-成功场景")
    void testCreateCloneTaskFromTaskApply_Success() throws ScriptException {
        // Setup
        ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        scriptExecAuditDto.setScriptInfoVersionId(1L);
        scriptExecAuditDto.setExecuser("testUser");

        // 设置TaskInfo，确保taskId不为空
        TaskDto taskInfo = new TaskDto();
        taskInfo.setId(1L);
        taskInfo.setTaskName("testTask");
        scriptExecAuditDto.setTaskInfo(taskInfo);

        // 设置附件列表
        List<AttachmentDto> attachments = new ArrayList<>();
        AttachmentDto attachment = new AttachmentDto();
        attachment.setId(1L);
        attachments.add(attachment);
        scriptExecAuditDto.setScriptTempAttachments(attachments);

        // 设置Agent信息
        List<AgentInfoDto> agentInfoDtos = new ArrayList<>();
        AgentInfoDto agentInfoDto = new AgentInfoDto();
        agentInfoDto.setAgentIp("***********");
        agentInfoDto.setAgentPort(8080);
        agentInfoDtos.add(agentInfoDto);
        scriptExecAuditDto.setChosedAgentUsers(agentInfoDtos);

        // 设置资源组信息
        List<TaskGroupsDto> resGroups = new ArrayList<>();
        TaskGroupsDto groupDto = new TaskGroupsDto();
        groupDto.setSysmComputerGroupId(1L);
        groupDto.setCpname("TestGroup");
        resGroups.add(groupDto);
        scriptExecAuditDto.setChosedResGroups(resGroups);

        // Mock Task insertion
        Task task = new Task();
        task.setId(1L);
        when(mockTaskTemplateMapper.insertTaskTemplate(any(Task.class))).thenAnswer(invocation -> {
            Task t = invocation.getArgument(0);
            t.setId(1L);
            return 1;
        });

        // Mock BeanUtils.copy
        beanUtilsMockedStatic.when(() -> BeanUtils.copy(any(TaskDto.class), eq(Task.class)))
                .thenReturn(task);
        beanUtilsMockedStatic.when(() -> BeanUtils.copy(any(), eq(List.class)))
                .thenReturn(Collections.emptyList());

        // Mock AgentInfo查询
        AgentInfo agentInfo = new AgentInfo();
        agentInfo.setId(1L);
        when(mockAgentInfoService.selectAgentInfoByIpAndPort(any(AgentInfoDto.class)))
                .thenReturn(agentInfo);

        // Mock ScriptVersionDto
        ScriptVersionDto scriptVersionDto = new ScriptVersionDto();
        scriptVersionDto.setSrcScriptUuid("test-uuid");
        when(mockInfoVersionService.selectInfoVersionById(anyLong())).thenReturn(scriptVersionDto);

        // Mock batchData方法
        doNothing().when(taskTemplateServiceUnderTest).batchData(anyList(), any());

        // 设置当前用户
        CurrentUser user = new CurrentUser();
        user.setId(1L);
        user.setLoginName("testUser");
        user.setFullName("Test User");

        // Mock batchUpdateByIds
        when(mockTaskAttachmentTemplateMapper.batchUpdateByIds(eq(1L), any(Long[].class))).thenReturn(1);

        // Execute
        taskTemplateServiceUnderTest.createCloneTaskFromTaskApply(scriptExecAuditDto, user);

        // Verify basic operations
        verify(mockTaskTemplateMapper).insertTaskTemplate(any(Task.class));
        verify(mockTaskAttachmentTemplateMapper).batchUpdateByIds(eq(1L), any(Long[].class));
        verify(mockAgentInfoService).selectAgentInfoByIpAndPort(any(AgentInfoDto.class));

        // 验证batchData的调用
        @SuppressWarnings("unchecked")
        ArgumentCaptor<List<Object>> listCaptor = ArgumentCaptor.forClass((Class) List.class);
        @SuppressWarnings("unchecked")
        ArgumentCaptor<Consumer<Object>> consumerCaptor = ArgumentCaptor.forClass((Class) Consumer.class);

        verify(taskTemplateServiceUnderTest, times(2)).batchData(listCaptor.capture(), consumerCaptor.capture());

        // 获取所有捕获的参数
        List<List<Object>> capturedLists = listCaptor.getAllValues();

        // 验证第一次调用是保存IPs信息
        List<Object> firstList = capturedLists.get(0);
        assertTrue(firstList.stream().allMatch(item -> item instanceof TaskIps));

        // 验证第二次调用是保存设备组信息
        List<Object> secondList = capturedLists.get(1);
        assertTrue(secondList.stream().allMatch(item -> item instanceof TaskGroups));
    }

    @Test
    @DisplayName("测试删除任务模板")
    void testDeleteTaskTemplate() throws ScriptException {
        // Setup
        Long taskId = 1L;

        // Execute
        taskTemplateServiceUnderTest.deleteTaskTemplate(taskId);

        // Verify
        verify(mockTaskTemplateMapper).deleteById(taskId);
        verify(mockTaskParamsTemplateMapper).deleteByTaskId(taskId);
        verify(mockTaskAttachmentTemplateMapper).deleteByTaskId(taskId);
        verify(mockTaskGroupsTemplateMapper).deleteByTaskId(taskId);
        verify(mockTaskIpsTemplateMapper).deleteByTaskId(taskId);
    }

    @Test
    @DisplayName("测试上传附件")
    void testUploadAttachment() throws ScriptException {
        // Setup
        TaskAttachmentDto inputDto = new TaskAttachmentDto();
        inputDto.setName("test.txt");
        inputDto.setSize(1000L);
        inputDto.setContents(new byte[]{1, 2, 3});
        inputDto.setScriptTaskId(1L);

        TaskAttachment savedAttachment = new TaskAttachment();
        savedAttachment.setId(1L);
        savedAttachment.setName("test.txt");

        when(mockTaskAttachmentTemplateMapper.insertTaskAttachment(any(TaskAttachment.class)))
                .thenReturn(1);

        // Execute
        TaskAttachmentDto result = taskTemplateServiceUnderTest.uploadAttachment(inputDto);

        // Verify
        verify(mockFileSizeValidUtil).validateFileSize(inputDto.getSize());
        verify(mockTaskAttachmentTemplateMapper).insertTaskAttachment(any(TaskAttachment.class));
        assertNull(result);
    }

    @Test
    @DisplayName("测试获取任务模板附件")
    void testGetTaskTemplateAttachment() {
        // Setup
        Long scriptTaskId = 1L;
        List<TaskAttachment> mockAttachments = Arrays.asList(
                createMockTaskAttachment(1L, "file1.txt", 100L),
                createMockTaskAttachment(2L, "file2.txt", 200L)
        );

        when(mockTaskAttachmentTemplateMapper.selectTaskAttachmentByTaskId(scriptTaskId))
                .thenReturn(mockAttachments);

        // Execute
        List<AttachmentUploadDto> result = taskTemplateServiceUnderTest.getTaskTemplateAttachment(scriptTaskId);

        // Verify
        verify(mockTaskAttachmentTemplateMapper).selectTaskAttachmentByTaskId(scriptTaskId);
        assertEquals(2, result.size());
        assertEquals("file1.txt", result.get(0).getName());
        assertEquals("file2.txt", result.get(1).getName());
    }

    @Test
    @DisplayName("测试删除附件模板")
    void testDeleteAttachmentTemplate() {
        // Setup
        Long attachmentId = 1L;

        // Execute
        taskTemplateServiceUnderTest.deleteAttachmentTemplate(attachmentId);

        // Verify
        verify(mockTaskAttachmentTemplateMapper).deleteById(attachmentId);
    }

    @Test
    @DisplayName("测试根据ID选择附件")
    void testSelectAttachmentById() {
        // Setup
        Long attachmentId = 1L;
        TaskAttachment mockAttachment = createMockTaskAttachment(attachmentId, "test.txt", 100L);

        when(mockTaskAttachmentTemplateMapper.selectAttachmentById(attachmentId))
                .thenReturn(mockAttachment);

        // Execute
        TaskAttachmentDto result = taskTemplateServiceUnderTest.selectAttachmentById(attachmentId);

        // Verify
        verify(mockTaskAttachmentTemplateMapper).selectAttachmentById(attachmentId);
        assertNull(result);
    }

    @Test
    @DisplayName("测试执行审核模板任务")
    void testExecAuditTemplateTask() throws ScriptException {
        // Setup
        ScriptExecAuditDto inputDto = new ScriptExecAuditDto();
        TaskDto taskInfo = new TaskDto();
        taskInfo.setId(1L);
        inputDto.setTaskInfo(taskInfo);

        CurrentUser mockUser = new CurrentUser();
        mockUser.setId(1L);

        // 使用已经存在的静态 mock，而不是创建新的
        currentUserUtilMockedStatic.when(CurrentUserUtil::getCurrentUser).thenReturn(mockUser);

        TaskCloneBean mockCloneBean = new TaskCloneBean();
        mockCloneBean.setTaskType(1); // 克隆任务

        List<TaskAttachment> taskAttachments = new ArrayList<>();
        TaskAttachment taskAttachment = new TaskAttachment();
        taskAttachment.setId(1L);
        taskAttachment.setContents(new byte[]{1, 2, 3});
        taskAttachment.setScriptTaskId(1L);
        taskAttachment.setName("test.txt");
        taskAttachment.setSize(1000L);
        taskAttachments.add(taskAttachment);

        when(mockTaskAttachmentTemplateMapper.selectTaskAttachmentByTaskId(anyLong()))
                .thenReturn(taskAttachments);
        when(mockTaskTemplateMapper.selectTaskTemplateById(anyLong()))
                .thenReturn(mockCloneBean);
        Map<String, Long> res = new HashMap<>();
        res.put("taskId", 2L);
        res.put("auditId", 2L);
        when(mockTaskApplyService.scriptExecAuditingForWeb(any(), any(), any()))
                .thenReturn(res);

        // Execute
        res = taskTemplateServiceUnderTest.execAuditTemplateTask(inputDto);

        // Verify
        verify(mockTaskAttachmentTemplateMapper).selectTaskAttachmentByTaskId(anyLong());
        verify(mockTaskTemplateMapper).selectTaskTemplateById(anyLong());
        verify(mockTaskApplyService).scriptExecAuditingForWeb(any(), any(), any());
        assertEquals(2L, res.get("taskId"));
    }

    // Helper method to create mock TaskAttachment
    private TaskAttachment createMockTaskAttachment(Long id, String name, Long size) {
        TaskAttachment attachment = new TaskAttachment();
        attachment.setId(id);
        attachment.setName(name);
        attachment.setSize(size);
        attachment.setContents(new byte[]{1, 2, 3});
        return attachment;
    }

    @Test
    @DisplayName("测试更新附件 - 成功场景")
    void testUpdateAttachment_Success() throws Exception {
        // 使用反射获取private方法
        Method updateAttachmentMethod = TaskTemplateServiceImpl.class.getDeclaredMethod(
                "updateAttachment",
                ScriptExecAuditDto.class
        );
        updateAttachmentMethod.setAccessible(true);

        // Setup
        ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();

        // 设置任务信息
        TaskDto taskInfo = new TaskDto();
        taskInfo.setId(1L);
        scriptExecAuditDto.setTaskInfo(taskInfo);

        // 设置临时附件列表
        List<AttachmentDto> attachments = new ArrayList<>();
        AttachmentDto attachment1 = new AttachmentDto();
        attachment1.setId(1L);
        attachment1.setName("test1.txt");

        AttachmentDto attachment2 = new AttachmentDto();
        attachment2.setId(2L);
        attachment2.setName("test2.txt");

        attachments.add(attachment1);
        attachments.add(attachment2);
        scriptExecAuditDto.setScriptTempAttachments(attachments);

        // Mock taskAttachmentTemplateMapper的行为
        when(mockTaskAttachmentTemplateMapper.batchUpdateByIds(eq(1L), any(Long[].class))).thenReturn(2);

        // Execute
        updateAttachmentMethod.invoke(taskTemplateServiceUnderTest, scriptExecAuditDto);

        // Verify
        verify(mockTaskAttachmentTemplateMapper).batchUpdateByIds(eq(1L),
                argThat(ids -> Arrays.equals(ids, new Long[]{1L, 2L})));
    }

    @Test
    @DisplayName("测试更新附件 - 空附件列表")
    void testUpdateAttachment_EmptyAttachments() throws Exception {
        // 使用反射获取private方法
        Method updateAttachmentMethod = TaskTemplateServiceImpl.class.getDeclaredMethod(
                "updateAttachment",
                ScriptExecAuditDto.class
        );
        updateAttachmentMethod.setAccessible(true);

        // Setup
        ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();

        TaskDto taskInfo = new TaskDto();
        taskInfo.setId(1L);
        scriptExecAuditDto.setTaskInfo(taskInfo);

        // 设置空的附件列表
        scriptExecAuditDto.setScriptTempAttachments(new ArrayList<>());

        // Execute
        updateAttachmentMethod.invoke(taskTemplateServiceUnderTest, scriptExecAuditDto);

        // Verify - 确保mapper方法没有被调用
        verify(mockTaskAttachmentTemplateMapper, never()).batchUpdateByIds(anyLong(), any(Long[].class));
    }

    @Test
    @DisplayName("测试更新附件 - 数据库异常")
    void testUpdateAttachment_DatabaseException() throws Exception {
        // 使用反射获取private方法
        Method updateAttachmentMethod = TaskTemplateServiceImpl.class.getDeclaredMethod(
                "updateAttachment",
                ScriptExecAuditDto.class
        );
        updateAttachmentMethod.setAccessible(true);

        // Setup
        ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();

        TaskDto taskInfo = new TaskDto();
        taskInfo.setId(1L);
        scriptExecAuditDto.setTaskInfo(taskInfo);

        List<AttachmentDto> attachments = new ArrayList<>();
        AttachmentDto attachment = new AttachmentDto();
        attachment.setId(1L);
        attachments.add(attachment);
        scriptExecAuditDto.setScriptTempAttachments(attachments);

        // Mock数据库异常
        when(mockTaskAttachmentTemplateMapper.batchUpdateByIds(anyLong(), any(Long[].class)))
                .thenThrow(new RuntimeException("Database error"));

        // Execute & Verify
        assertThrows(Exception.class, () ->
                updateAttachmentMethod.invoke(taskTemplateServiceUnderTest, scriptExecAuditDto));
    }

    @Test
    @DisplayName("测试更新附件 - 任务ID为空")
    void testUpdateAttachment_NullTaskId() throws Exception {
        // 使用反射获取private方法
        Method updateAttachmentMethod = TaskTemplateServiceImpl.class.getDeclaredMethod(
                "updateAttachment",
                ScriptExecAuditDto.class
        );
        updateAttachmentMethod.setAccessible(true);

        // Setup
        ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();

        // 设置空的任务信息
        TaskDto taskInfo = new TaskDto();
        scriptExecAuditDto.setTaskInfo(taskInfo);

        List<AttachmentDto> attachments = new ArrayList<>();
        AttachmentDto attachment = new AttachmentDto();
        attachment.setId(1L);
        attachments.add(attachment);
        scriptExecAuditDto.setScriptTempAttachments(attachments);
        updateAttachmentMethod.invoke(taskTemplateServiceUnderTest, scriptExecAuditDto);
    }

    @Test
    @DisplayName("测试更新任务参数 - 成功场景")
    void testUpdateTaskParams_Success() throws Exception {
        // 使用反射获取private方法
        Method updateTaskParamsMethod = TaskTemplateServiceImpl.class.getDeclaredMethod(
                "updateTaskParams",
                ScriptExecAuditDto.class
        );
        updateTaskParamsMethod.setAccessible(true);

        // Setup
        ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();

        // 设置任务信息
        TaskDto taskInfo = new TaskDto();
        taskInfo.setId(1L);
        scriptExecAuditDto.setTaskInfo(taskInfo);

        // 设置参数列表
        List<ParameterDto> params = new ArrayList<>();
        ParameterDto param1 = new ParameterDto();
        param1.setParamOrder(1);
        param1.setParamDefaultValue("value1");

        ParameterDto param2 = new ParameterDto();
        param2.setParamOrder(2);
        param2.setParamDefaultValue("value2");

        params.add(param1);
        params.add(param2);
        scriptExecAuditDto.setParams(params);

        // Mock现有任务参数
        List<TaskParams> existingParams = new ArrayList<>();
        TaskParams taskParam1 = new TaskParams();
        taskParam1.setOrder(1);
        taskParam1.setValue("oldValue1");

        TaskParams taskParam2 = new TaskParams();
        taskParam2.setOrder(2);
        taskParam2.setValue("oldValue2");

        existingParams.add(taskParam1);
        existingParams.add(taskParam2);

        // Mock taskParamsTemplateMapper的行为
        when(mockTaskParamsTemplateMapper.selectTaskParamsByTaskId(1L)).thenReturn(existingParams);
        when(mockTaskParamsTemplateMapper.batchUpdateByIds(anyList())).thenReturn(2);

        // Execute
        updateTaskParamsMethod.invoke(taskTemplateServiceUnderTest, scriptExecAuditDto);

        // Verify
        verify(mockTaskParamsTemplateMapper).selectTaskParamsByTaskId(1L);
        verify(mockTaskParamsTemplateMapper).batchUpdateByIds(argThat(taskParams -> {
            List<TaskParams> params1 = (List<TaskParams>) taskParams;
            return params1.size() == 2
                    && "value1".equals(params1.get(0).getValue())
                    && "value2".equals(params1.get(1).getValue());
        }));
    }

    @Test
    @DisplayName("测试更新任务参数 - 空参数列表")
    void testUpdateTaskParams_EmptyParams() throws Exception {
        // 使用反射获取private方法
        Method updateTaskParamsMethod = TaskTemplateServiceImpl.class.getDeclaredMethod(
                "updateTaskParams",
                ScriptExecAuditDto.class
        );
        updateTaskParamsMethod.setAccessible(true);

        // Setup
        ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();

        TaskDto taskInfo = new TaskDto();
        taskInfo.setId(1L);
        scriptExecAuditDto.setTaskInfo(taskInfo);

        // 设置空的参数列表
        scriptExecAuditDto.setParams(new ArrayList<>());

        // Execute
        updateTaskParamsMethod.invoke(taskTemplateServiceUnderTest, scriptExecAuditDto);

        // Verify - 确保mapper方法没有被调用
        verify(mockTaskParamsTemplateMapper, never()).selectTaskParamsByTaskId(anyLong());
        verify(mockTaskParamsTemplateMapper, never()).batchUpdateByIds(anyList());
    }

    @Test
    @DisplayName("测试更新任务参数 - 数据库查询异常")
    void testUpdateTaskParams_SelectException() throws Exception {
        // 使用反射获取private方法
        Method updateTaskParamsMethod = TaskTemplateServiceImpl.class.getDeclaredMethod(
                "updateTaskParams",
                ScriptExecAuditDto.class
        );
        updateTaskParamsMethod.setAccessible(true);

        // Setup
        ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();

        TaskDto taskInfo = new TaskDto();
        taskInfo.setId(1L);
        scriptExecAuditDto.setTaskInfo(taskInfo);

        List<ParameterDto> params = new ArrayList<>();
        ParameterDto param = new ParameterDto();
        param.setParamOrder(1);
        params.add(param);
        scriptExecAuditDto.setParams(params);

        // Mock查询异常
        when(mockTaskParamsTemplateMapper.selectTaskParamsByTaskId(anyLong()))
                .thenThrow(new RuntimeException("Database query error"));

        // Execute & Verify
        assertThrows(Exception.class, () ->
                updateTaskParamsMethod.invoke(taskTemplateServiceUnderTest, scriptExecAuditDto));
    }

    @Test
    @DisplayName("测试更新任务参数 - 数据库更新异常")
    void testUpdateTaskParams_UpdateException() throws Exception {
        // 使用反射获取private方法
        Method updateTaskParamsMethod = TaskTemplateServiceImpl.class.getDeclaredMethod(
                "updateTaskParams",
                ScriptExecAuditDto.class
        );
        updateTaskParamsMethod.setAccessible(true);

        // Setup
        ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();

        TaskDto taskInfo = new TaskDto();
        taskInfo.setId(1L);
        scriptExecAuditDto.setTaskInfo(taskInfo);

        List<ParameterDto> params = new ArrayList<>();
        ParameterDto param = new ParameterDto();
        param.setParamOrder(1);
        params.add(param);
        scriptExecAuditDto.setParams(params);

        // Mock现有任务参数
        List<TaskParams> existingParams = new ArrayList<>();
        TaskParams taskParam = new TaskParams();
        taskParam.setOrder(1);
        existingParams.add(taskParam);

        when(mockTaskParamsTemplateMapper.selectTaskParamsByTaskId(1L)).thenReturn(existingParams);
        when(mockTaskParamsTemplateMapper.batchUpdateByIds(anyList()))
                .thenThrow(new RuntimeException("Database update error"));

        // Execute & Verify
        assertThrows(Exception.class, () ->
                updateTaskParamsMethod.invoke(taskTemplateServiceUnderTest, scriptExecAuditDto));
    }

    @Test
    @DisplayName("测试更新资源组 - 成功场景")
    void testUpdateChoseResGroups_Success() throws Exception {
        // 使用反射获取private方法
        Method updateChoseResGroupsMethod = TaskTemplateServiceImpl.class.getDeclaredMethod(
                "updateChoseResGroups",
                ScriptExecAuditDto.class
        );
        updateChoseResGroupsMethod.setAccessible(true);

        // Setup
        ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();

        // 设置任务信息
        TaskDto taskInfo = new TaskDto();
        taskInfo.setId(1L);
        scriptExecAuditDto.setTaskInfo(taskInfo);

        // 设置资源组列表
        List<TaskGroupsDto> chosedResGroups = new ArrayList<>();
        TaskGroupsDto group1 = new TaskGroupsDto();
        group1.setSysmComputerGroupId(101L);
        group1.setCpname("Group1");

        TaskGroupsDto group2 = new TaskGroupsDto();
        group2.setSysmComputerGroupId(102L);
        group2.setCpname("Group2");

        chosedResGroups.add(group1);
        chosedResGroups.add(group2);
        scriptExecAuditDto.setChosedResGroups(chosedResGroups);

        // Mock taskGroupsTemplateMapper的行为
        when(mockTaskGroupsTemplateMapper.deleteByTaskId(anyLong())).thenReturn(1);

        // Mock batchData方法
        doNothing().when(taskTemplateServiceUnderTest).batchData(anyList(), any());

        // Execute
        updateChoseResGroupsMethod.invoke(taskTemplateServiceUnderTest, scriptExecAuditDto);

        // Verify
        verify(mockTaskGroupsTemplateMapper).deleteByTaskId(1L);

        // 验证batchData的调用
        @SuppressWarnings("unchecked")
        ArgumentCaptor<List<TaskGroups>> listCaptor = ArgumentCaptor.forClass(List.class);
        verify(taskTemplateServiceUnderTest).batchData(listCaptor.capture(), any());

        // 验证传入的参数列表
        List<TaskGroups> capturedList = listCaptor.getValue();
        assertEquals(2, capturedList.size());

        // 验证第一个TaskGroups对象
        TaskGroups firstGroup = capturedList.get(0);
        assertEquals(1L, firstGroup.getScriptTaskId().longValue());
        assertEquals(101L, firstGroup.getSysmComputerGroupId().longValue());
        assertEquals("Group1", firstGroup.getCpname());

        // 验证第二个TaskGroups对象
        TaskGroups secondGroup = capturedList.get(1);
        assertEquals(1L, secondGroup.getScriptTaskId().longValue());
        assertEquals(102L, secondGroup.getSysmComputerGroupId().longValue());
        assertEquals("Group2", secondGroup.getCpname());
    }

    @Test
    @DisplayName("测试更新资源组 - 空资源组列表")
    void testUpdateChoseResGroups_EmptyGroups() throws Exception {
        // 使用反射获取private方法
        Method updateChoseResGroupsMethod = TaskTemplateServiceImpl.class.getDeclaredMethod(
                "updateChoseResGroups",
                ScriptExecAuditDto.class
        );
        updateChoseResGroupsMethod.setAccessible(true);

        // Setup
        ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();

        TaskDto taskInfo = new TaskDto();
        taskInfo.setId(1L);
        scriptExecAuditDto.setTaskInfo(taskInfo);

        // 设置空的资源组列表
        scriptExecAuditDto.setChosedResGroups(new ArrayList<>());

        // Execute
        updateChoseResGroupsMethod.invoke(taskTemplateServiceUnderTest, scriptExecAuditDto);

        // Verify
        verify(mockTaskGroupsTemplateMapper).deleteByTaskId(1L);
        verify(mockTaskGroupsTemplateMapper, never()).insertTaskGroups(any(TaskGroups.class));
    }

    @Test
    @DisplayName("测试更新资源组 - 删除异常")
    void testUpdateChoseResGroups_DeleteException() throws Exception {
        // 使用反射获取private方法
        Method updateChoseResGroupsMethod = TaskTemplateServiceImpl.class.getDeclaredMethod(
                "updateChoseResGroups",
                ScriptExecAuditDto.class
        );
        updateChoseResGroupsMethod.setAccessible(true);

        // Setup
        ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();

        TaskDto taskInfo = new TaskDto();
        taskInfo.setId(1L);
        scriptExecAuditDto.setTaskInfo(taskInfo);

        List<TaskGroupsDto> chosedResGroups = new ArrayList<>();
        TaskGroupsDto group = new TaskGroupsDto();
        group.setSysmComputerGroupId(101L);
        chosedResGroups.add(group);
        scriptExecAuditDto.setChosedResGroups(chosedResGroups);

        // Mock删除异常
        doThrow(new RuntimeException("Delete error"))
                .when(mockTaskGroupsTemplateMapper).deleteByTaskId(anyLong());

        // Execute & Verify
        assertThrows(Exception.class, () ->
                updateChoseResGroupsMethod.invoke(taskTemplateServiceUnderTest, scriptExecAuditDto));
    }

    @Test
    @DisplayName("测试更新Agent IPs - 成功场景")
    void testUpdateChoseAgentIps_Success() throws Exception {
        // 使用反射获取private方法
        Method updateChoseAgentIpsMethod = TaskTemplateServiceImpl.class.getDeclaredMethod(
                "updateChoseAgentIps",
                ScriptExecAuditDto.class
        );
        updateChoseAgentIpsMethod.setAccessible(true);

        // Setup
        ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();

        // 设置任务信息
        TaskDto taskInfo = new TaskDto();
        taskInfo.setId(1L);
        scriptExecAuditDto.setTaskInfo(taskInfo);

        // 设置Agent列表
        List<AgentInfoDto> chosedAgentUsers = new ArrayList<>();
        AgentInfoDto agent1 = new AgentInfoDto();
        agent1.setId(101L);
        agent1.setAgentIp("***********");
        agent1.setAgentPort(8080);

        AgentInfoDto agent2 = new AgentInfoDto();
        agent2.setId(102L);
        agent2.setAgentIp("***********");
        agent2.setAgentPort(8081);

        chosedAgentUsers.add(agent1);
        chosedAgentUsers.add(agent2);
        scriptExecAuditDto.setChosedAgentUsers(chosedAgentUsers);

        // Mock agentInfoService的行为
        List<AgentInfo> mockAgentInfos = new ArrayList<>();
        AgentInfo agentInfo1 = new AgentInfo();
        agentInfo1.setId(101L);
        agentInfo1.setAgentIp("***********");
        agentInfo1.setAgentPort(8080);

        AgentInfo agentInfo2 = new AgentInfo();
        agentInfo2.setId(102L);
        agentInfo2.setAgentIp("***********");
        agentInfo2.setAgentPort(8081);

        mockAgentInfos.add(agentInfo1);
        mockAgentInfos.add(agentInfo2);

        when(mockAgentInfoService.selectAgentInfoByIpAndPort(anyList())).thenReturn(mockAgentInfos);

        // Mock taskIpsTemplateMapper的行为
        when(mockTaskIpsTemplateMapper.deleteByTaskId(anyLong())).thenReturn(1);

        // Mock batchData方法
        doNothing().when(taskTemplateServiceUnderTest).batchData(anyList(), any());

        // Execute
        updateChoseAgentIpsMethod.invoke(taskTemplateServiceUnderTest, scriptExecAuditDto);

        // Verify
        verify(mockTaskIpsTemplateMapper).deleteByTaskId(1L);
        verify(mockAgentInfoService).selectAgentInfoByIpAndPort(chosedAgentUsers);

        // 验证batchData的调用
        @SuppressWarnings("unchecked")
        ArgumentCaptor<List<TaskIps>> listCaptor = ArgumentCaptor.forClass(List.class);
        verify(taskTemplateServiceUnderTest).batchData(listCaptor.capture(), any());

        // 验证传入的参数列表
        List<TaskIps> capturedList = listCaptor.getValue();
        assertEquals(2, capturedList.size());

        // 验证第一个TaskIps对象
        TaskIps firstTaskIps = capturedList.get(0);
        assertEquals(1L, firstTaskIps.getScriptTaskId().longValue());
        assertEquals(101L, firstTaskIps.getScriptAgentinfoId().longValue());

        // 验证第二个TaskIps对象
        TaskIps secondTaskIps = capturedList.get(1);
        assertEquals(1L, secondTaskIps.getScriptTaskId().longValue());
        assertEquals(102L, secondTaskIps.getScriptAgentinfoId().longValue());
    }

    @Test
    @DisplayName("测试更新Agent IPs - 空列表场景")
    void testUpdateChoseAgentIps_EmptyList() throws Exception {
        // 使用反射获取private方法
        Method updateChoseAgentIpsMethod = TaskTemplateServiceImpl.class.getDeclaredMethod(
                "updateChoseAgentIps",
                ScriptExecAuditDto.class
        );
        updateChoseAgentIpsMethod.setAccessible(true);

        // Setup
        ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();

        // 设置任务信息
        TaskDto taskInfo = new TaskDto();
        taskInfo.setId(1L);
        scriptExecAuditDto.setTaskInfo(taskInfo);

        // 设置空的Agent列表
        scriptExecAuditDto.setChosedAgentUsers(new ArrayList<>());

        // Mock taskIpsTemplateMapper的行为
        when(mockTaskIpsTemplateMapper.deleteByTaskId(anyLong())).thenReturn(1);

        // Mock agentInfoService的行为，返回空列表
        when(mockAgentInfoService.selectAgentInfoByIpAndPort(anyList())).thenReturn(new ArrayList<>());

        // Execute
        updateChoseAgentIpsMethod.invoke(taskTemplateServiceUnderTest, scriptExecAuditDto);

        // Verify
        verify(mockTaskIpsTemplateMapper).deleteByTaskId(1L);
        verify(mockAgentInfoService).selectAgentInfoByIpAndPort(anyList());
        verify(taskTemplateServiceUnderTest, never()).batchData(anyList(), any());

        // 验证传入selectAgentInfoByIpAndPort的是空列表
        ArgumentCaptor<List<AgentInfoDto>> agentListCaptor = ArgumentCaptor.forClass(List.class);
        verify(mockAgentInfoService).selectAgentInfoByIpAndPort(agentListCaptor.capture());
        assertTrue(agentListCaptor.getValue().isEmpty());
    }

    @Test
    @DisplayName("测试更新Agent IPs - 异常场景")
    void testUpdateChoseAgentIps_Exception() throws Exception {
        // 使用反射获取private方法
        Method updateChoseAgentIpsMethod = TaskTemplateServiceImpl.class.getDeclaredMethod(
                "updateChoseAgentIps",
                ScriptExecAuditDto.class
        );
        updateChoseAgentIpsMethod.setAccessible(true);

        // Setup
        ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();

        // 设置任务信息
        TaskDto taskInfo = new TaskDto();
        taskInfo.setId(1L);
        scriptExecAuditDto.setTaskInfo(taskInfo);

        // 设置Agent列表
        List<AgentInfoDto> chosedAgentUsers = new ArrayList<>();
        AgentInfoDto agent = new AgentInfoDto();
        chosedAgentUsers.add(agent);
        scriptExecAuditDto.setChosedAgentUsers(chosedAgentUsers);

        // Mock deleteByTaskId抛出异常
        when(mockTaskIpsTemplateMapper.deleteByTaskId(anyLong()))
                .thenThrow(new RuntimeException("Delete error"));

        // Execute & Verify
        assertThrows(Exception.class, () ->
                updateChoseAgentIpsMethod.invoke(taskTemplateServiceUnderTest, scriptExecAuditDto));
    }


    @Test
    @DisplayName("测试更新审核模板任务 - 成功场景")
    void testUpdateAuditTemplateTask_Success() throws Exception {
        // 使用反射获取private方法
        Method updateChoseAgentIpsMethod = TaskTemplateServiceImpl.class.getDeclaredMethod(
                "updateChoseAgentIps",
                ScriptExecAuditDto.class
        );
        updateChoseAgentIpsMethod.setAccessible(true);

        // Setup
        ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();

        // 设置任务信息
        TaskDto taskInfo = new TaskDto();
        taskInfo.setId(1L);
        taskInfo.setTaskName("测试任务");
        scriptExecAuditDto.setTaskInfo(taskInfo);

        // 设置Agent列表
        List<AgentInfoDto> chosedAgentUsers = new ArrayList<>();
        AgentInfoDto agentInfoDto1 = new AgentInfoDto();
        agentInfoDto1.setAgentIp("***********");
        agentInfoDto1.setAgentPort(8080);
        chosedAgentUsers.add(agentInfoDto1);
        scriptExecAuditDto.setChosedAgentUsers(chosedAgentUsers);

        // Mock taskIpsTemplateMapper的行为
        when(mockTaskIpsTemplateMapper.deleteByTaskId(anyLong())).thenReturn(1);

        // Mock agentInfoService的行为
        List<AgentInfo> mockAgentInfos = new ArrayList<>();
        AgentInfo agentInfo = new AgentInfo();
        agentInfo.setId(1L);
        agentInfo.setAgentIp("***********");
        agentInfo.setAgentPort(8080);
        mockAgentInfos.add(agentInfo);
        when(mockAgentInfoService.selectAgentInfoByIpAndPort(anyList())).thenReturn(mockAgentInfos);

        // Mock batchData方法
        doNothing().when(taskTemplateServiceUnderTest).batchData(anyList(), any());

        // Execute
        updateChoseAgentIpsMethod.invoke(taskTemplateServiceUnderTest, scriptExecAuditDto);

        // Verify
        verify(mockTaskIpsTemplateMapper).deleteByTaskId(1L);
        verify(mockAgentInfoService).selectAgentInfoByIpAndPort(anyList());

        // 验证batchData的调用
        ArgumentCaptor<List<TaskIps>> taskIpsCaptor = ArgumentCaptor.forClass(List.class);
        verify(taskTemplateServiceUnderTest).batchData(taskIpsCaptor.capture(), any());

        // 验证传入batchData的参数
        List<TaskIps> capturedTaskIps = taskIpsCaptor.getValue();
        assertEquals(1, capturedTaskIps.size());
        TaskIps taskIps = capturedTaskIps.get(0);
        assertEquals(1L, taskIps.getScriptTaskId());
        assertEquals(1L, taskIps.getScriptAgentinfoId());
    }

    @Test
    @DisplayName("测试更新审核模板任务 - taskTemplateMapper异常场景")
    void testUpdateAuditTemplateTask_MapperException() {
        // Setup
        ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();

        // 设置任务信息
        TaskDto taskInfo = new TaskDto();
        taskInfo.setId(1L);
        scriptExecAuditDto.setTaskInfo(taskInfo);
        scriptExecAuditDto.setAuditUserId(100L);

        // Mock taskTemplateMapper抛出异常
        when(mockTaskTemplateMapper.updateById(any(Task.class)))
                .thenThrow(new RuntimeException("Database error"));

        // Execute & Verify
        Exception exception = assertThrows(Exception.class, () ->
                taskTemplateServiceUnderTest.updateAuditTemplateTask(scriptExecAuditDto));
        assertEquals("Database error",exception.getMessage());
    }

    @Test
    @DisplayName("测试更新审核模板任务 - 空任务信息场景")
    void testUpdateAuditTemplateTask_NullTaskInfo() {
        // Setup
        ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();

        // 设置空的任务信息
        scriptExecAuditDto.setTaskInfo(null);

        // Execute & Verify
        Exception exception = assertThrows(Exception.class, () ->
                taskTemplateServiceUnderTest.updateAuditTemplateTask(scriptExecAuditDto));
        assertNull(exception.getMessage());
    }

    @Test
    @DisplayName("测试获取资源组数据 - 成功场景")
    void testGetAllChoseGroup_Success() {
        // Setup
        Long taskId = 1L;

        // 准备模拟数据
        List<TaskGroups> mockTaskGroups = new ArrayList<>();
        TaskGroups group1 = new TaskGroups();
        group1.setSysmComputerGroupId(101L);
        group1.setCpname("Group1");

        TaskGroups group2 = new TaskGroups();
        group2.setSysmComputerGroupId(102L);
        group2.setCpname("Group2");

        mockTaskGroups.add(group1);
        mockTaskGroups.add(group2);

        // Mock taskGroupsTemplateMapper的行为
        when(mockTaskGroupsTemplateMapper.selectTaskGroupsByTaskId(taskId))
                .thenReturn(mockTaskGroups);

        // Execute
        List<SystemComputerGroupDto> result = taskTemplateServiceUnderTest.getAllChoseGroup(taskId);

        // Verify
        assertNotNull(result);
        assertEquals(2, result.size());

        // 验证第一个组
        SystemComputerGroupDto firstGroup = result.get(0);
        assertEquals(101L, firstGroup.getSysmComputerGroupId());
        assertEquals("Group1", firstGroup.getCpName());  // 修改为getCpName()

        // 验证第二个组
        SystemComputerGroupDto secondGroup = result.get(1);
        assertEquals(102L, secondGroup.getSysmComputerGroupId());
        assertEquals("Group2", secondGroup.getCpName());  // 修改为getCpName()

        // 验证mapper方法被调用
        verify(mockTaskGroupsTemplateMapper).selectTaskGroupsByTaskId(taskId);
    }

    @Test
    @DisplayName("测试获取资源组数据 - 空列表场景")
    void testGetAllChoseGroup_EmptyList() {
        // Setup
        Long taskId = 1L;

        // Mock返回空列表
        when(mockTaskGroupsTemplateMapper.selectTaskGroupsByTaskId(taskId))
                .thenReturn(new ArrayList<>());

        // Execute
        List<SystemComputerGroupDto> result = taskTemplateServiceUnderTest.getAllChoseGroup(taskId);

        // Verify
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(mockTaskGroupsTemplateMapper).selectTaskGroupsByTaskId(taskId);
    }

    @Test
    @DisplayName("测试获取资源组数据 - 数据库异常场景")
    void testGetAllChoseGroup_DatabaseException() {
        // Setup
        Long taskId = 1L;

        // Mock数据库异常
        when(mockTaskGroupsTemplateMapper.selectTaskGroupsByTaskId(taskId))
                .thenThrow(new RuntimeException("Database error"));

        // Execute & Verify
        assertThrows(RuntimeException.class, () ->
                taskTemplateServiceUnderTest.getAllChoseGroup(taskId));

        verify(mockTaskGroupsTemplateMapper).selectTaskGroupsByTaskId(taskId);
    }


    @Test
    @DisplayName("测试获取脚本模板详情 - 成功场景（有参数）")
    void testGetScriptTemplateDetail_Success_WithParams() throws ScriptException {
        // Setup
        ScriptInfoQueryDto queryDto = new ScriptInfoQueryDto();
        queryDto.setTaskId(1L);

        // 准备ScriptInfoDto模拟数据
        ScriptInfoDto mockScriptInfo = new ScriptInfoDto();
        ScriptVersionDto scriptVersionDto = new ScriptVersionDto();
        List<ParameterValidationDto> paramList = new ArrayList<>();

        ParameterValidationDto param1 = new ParameterValidationDto();
        param1.setParamOrder(1);
        param1.setParamDefaultValue("oldValue1");

        ParameterValidationDto param2 = new ParameterValidationDto();
        param2.setParamOrder(2);
        param2.setParamDefaultValue("oldValue2");

        paramList.add(param1);
        paramList.add(param2);
        scriptVersionDto.setParameterValidationDtoList(paramList);
        mockScriptInfo.setScriptVersionDto(scriptVersionDto);

        // 准备TaskParams模拟数据
        List<TaskParams> mockTaskParams = new ArrayList<>();
        TaskParams taskParam1 = new TaskParams();
        taskParam1.setOrder(1);
        taskParam1.setValue("newValue1");

        TaskParams taskParam2 = new TaskParams();
        taskParam2.setOrder(2);
        taskParam2.setValue("newValue2");

        mockTaskParams.add(taskParam1);
        mockTaskParams.add(taskParam2);

        // Mock依赖服务
        when(mockMyScriptService.getScriptDetail(queryDto)).thenReturn(mockScriptInfo);
        when(mockTaskParamsTemplateMapper.selectTaskParamsByTaskId(1L)).thenReturn(mockTaskParams);

        // Execute
        ScriptInfoDto result = taskTemplateServiceUnderTest.getScriptTemplateDetail(queryDto);

        // Verify
        assertNotNull(result);
        assertNotNull(result.getScriptVersionDto());
        assertNotNull(result.getScriptVersionDto().getParameterValidationDtoList());
        assertEquals(2, result.getScriptVersionDto().getParameterValidationDtoList().size());

        // 验证参数值是否被正确更新
        assertEquals("newValue1", result.getScriptVersionDto().getParameterValidationDtoList().get(0).getParamDefaultValue());
        assertEquals("newValue2", result.getScriptVersionDto().getParameterValidationDtoList().get(1).getParamDefaultValue());

        // 验证服务调用
        verify(mockMyScriptService).getScriptDetail(queryDto);
        verify(mockTaskParamsTemplateMapper).selectTaskParamsByTaskId(1L);
    }

    @Test
    @DisplayName("测试获取脚本模板详情 - 成功场景（无参数）")
    void testGetScriptTemplateDetail_Success_NoParams() throws ScriptException {
        // Setup
        ScriptInfoQueryDto queryDto = new ScriptInfoQueryDto();
        queryDto.setTaskId(1L);

        // 准备ScriptInfoDto模拟数据（空参数列表）
        ScriptInfoDto mockScriptInfo = new ScriptInfoDto();
        ScriptVersionDto scriptVersionDto = new ScriptVersionDto();
        scriptVersionDto.setParameterValidationDtoList(new ArrayList<>());
        mockScriptInfo.setScriptVersionDto(scriptVersionDto);

        // Mock依赖服务
        when(mockMyScriptService.getScriptDetail(queryDto)).thenReturn(mockScriptInfo);
        when(mockTaskParamsTemplateMapper.selectTaskParamsByTaskId(1L)).thenReturn(new ArrayList<>());

        // Execute
        ScriptInfoDto result = taskTemplateServiceUnderTest.getScriptTemplateDetail(queryDto);

        // Verify
        assertNotNull(result);
        assertNotNull(result.getScriptVersionDto());
        assertTrue(result.getScriptVersionDto().getParameterValidationDtoList().isEmpty());

        // 验证服务调用
        verify(mockMyScriptService).getScriptDetail(queryDto);
        verify(mockTaskParamsTemplateMapper).selectTaskParamsByTaskId(1L);
    }

    @Test
    @DisplayName("测试获取脚本模板详情 - ScriptService抛出异常")
    void testGetScriptTemplateDetail_ScriptServiceException() throws ScriptException {
        // Setup
        ScriptInfoQueryDto queryDto = new ScriptInfoQueryDto();
        queryDto.setTaskId(1L);

        // Mock ScriptService抛出异常
        when(mockMyScriptService.getScriptDetail(queryDto))
                .thenThrow(new ScriptException("Script service error"));

        // Execute & Verify
        ScriptException exception = assertThrows(ScriptException.class, () ->
                taskTemplateServiceUnderTest.getScriptTemplateDetail(queryDto));
        assertEquals("Script service error", exception.getMessage());

        // 验证服务调用
        verify(mockMyScriptService).getScriptDetail(queryDto);
        verify(mockTaskParamsTemplateMapper, never()).selectTaskParamsByTaskId(anyLong());
    }

    @Test
    @DisplayName("测试获取脚本模板详情 - 参数映射不完全匹配")
    void testGetScriptTemplateDetail_PartialParamMatch() throws ScriptException {
        // Setup
        ScriptInfoQueryDto queryDto = new ScriptInfoQueryDto();
        queryDto.setTaskId(1L);

        // 准备ScriptInfoDto模拟数据
        ScriptInfoDto mockScriptInfo = new ScriptInfoDto();
        ScriptVersionDto scriptVersionDto = new ScriptVersionDto();
        List<ParameterValidationDto> paramList = new ArrayList<>();

        ParameterValidationDto param = new ParameterValidationDto();
        param.setParamOrder(1);
        param.setParamDefaultValue("oldValue");
        paramList.add(param);

        scriptVersionDto.setParameterValidationDtoList(paramList);
        mockScriptInfo.setScriptVersionDto(scriptVersionDto);

        // 准备不匹配的TaskParams数据
        List<TaskParams> mockTaskParams = new ArrayList<>();
        TaskParams taskParam = new TaskParams();
        taskParam.setOrder(2); // 不同的order
        taskParam.setValue("newValue");
        mockTaskParams.add(taskParam);

        // Mock依赖服务
        when(mockMyScriptService.getScriptDetail(queryDto)).thenReturn(mockScriptInfo);
        when(mockTaskParamsTemplateMapper.selectTaskParamsByTaskId(1L)).thenReturn(mockTaskParams);

        // Execute
        ScriptInfoDto result = taskTemplateServiceUnderTest.getScriptTemplateDetail(queryDto);

        // Verify
        assertNotNull(result);
        assertEquals(1, result.getScriptVersionDto().getParameterValidationDtoList().size());
        assertEquals("oldValue", result.getScriptVersionDto().getParameterValidationDtoList().get(0).getParamDefaultValue());

        // 验证服务调用
        verify(mockMyScriptService).getScriptDetail(queryDto);
        verify(mockTaskParamsTemplateMapper).selectTaskParamsByTaskId(1L);
    }

    @Test
    @DisplayName("测试获取克隆任务列表 - 成功场景（有分类ID）")
    void testListCloneTask_Success_WithCategory() {
        // Setup
        TaskApplyQueryDto queryDto = new TaskApplyQueryDto();
        Integer pageNum = 1;
        Integer pageSize = 10;
        CurrentUser user = new CurrentUser();
        user.setOrgCode("TEST_ORG");

        // 创建Page对象而不是ArrayList
        Page<TaskCloneBean> mockTaskList = new Page<>(pageNum, pageSize);
        TaskCloneBean task1 = new TaskCloneBean();
        task1.setCategoryId(1L);
        TaskCloneBean task2 = new TaskCloneBean();
        task2.setCategoryId(2L);
        mockTaskList.add(task1);
        mockTaskList.add(task2);
        mockTaskList.setTotal(2);

        // Mock依赖服务
        doReturn(mockTaskList)
                .when(mockTaskTemplateMapper)
                .selectTaskTemplateList(any(), any(), any());
        when(mockCategoryService.getCategoryFullPath(1L))
                .thenReturn("分类1/子分类1");
        when(mockCategoryService.getCategoryFullPath(2L))
                .thenReturn("分类2/子分类2");

        // Execute
        PageInfo<TaskTemplateDto> result = taskTemplateServiceUnderTest.listCloneTask(
                queryDto, pageNum, pageSize, user);

        // Verify
        assertNotNull(result);
        assertEquals(2, result.getList().size());
        assertEquals("分类1/子分类1", result.getList().get(0).getScriptCategoryName());
        assertEquals("分类2/子分类2", result.getList().get(1).getScriptCategoryName());

        // 验证服务调用
        verify(mockTaskTemplateMapper).selectTaskTemplateList(any(), any(),any());
        verify(mockCategoryService).getCategoryFullPath(1L);
        verify(mockCategoryService).getCategoryFullPath(2L);
    }

    @Test
    @DisplayName("测试获取克隆任务列表 - 空列表")
    void testListCloneTask_EmptyList() {
        // Setup
        TaskApplyQueryDto queryDto = new TaskApplyQueryDto();
        Integer pageNum = 1;
        Integer pageSize = 10;
        CurrentUser user = new CurrentUser();
        user.setOrgCode("TEST_ORG");

        // 创建空的Page对象
        Page<TaskCloneBean> mockTaskList = new Page<>(pageNum, pageSize);
        mockTaskList.setTotal(0);

        // Mock依赖服务
        doReturn(mockTaskList).when(mockTaskTemplateMapper).selectTaskTemplateList(any(), any(), any());

        // Execute
        PageInfo<TaskTemplateDto> result = taskTemplateServiceUnderTest.listCloneTask(
                queryDto, pageNum, pageSize, user);

        // Verify
        assertNotNull(result);
        assertTrue(result.getList().isEmpty());

        // 验证服务调用
        verify(mockTaskTemplateMapper).selectTaskTemplateList(any(), any(), any());
        verify(mockCategoryService, never()).getCategoryFullPath(anyLong());
    }

    @Test
    @DisplayName("测试获取克隆任务列表 - 分类服务异常")
    void testListCloneTask_CategoryServiceException() {
        // Setup
        TaskApplyQueryDto queryDto = new TaskApplyQueryDto();
        Integer pageNum = 1;
        Integer pageSize = 10;
        CurrentUser user = new CurrentUser();
        user.setOrgCode("TEST_ORG");

        // 创建Page对象
        Page<TaskCloneBean> mockTaskList = new Page<>(pageNum, pageSize);
        TaskCloneBean task = new TaskCloneBean();
        task.setCategoryId(1L);
        mockTaskList.add(task);
        mockTaskList.setTotal(1);
        List<TaskCloneBean> taskCloneBeans = new ArrayList<>();
        taskCloneBeans.add(task);

        // Mock依赖服务
        when(mockTaskTemplateMapper.selectTaskTemplateList(any(), any(),any()))
                .thenReturn(taskCloneBeans);
        when(mockCategoryService.getCategoryFullPath(1L))
                .thenThrow(new RuntimeException("Category service error"));

        // Execute & Verify
        assertThrows(RuntimeException.class, () ->
                taskTemplateServiceUnderTest.listCloneTask(queryDto, pageNum, pageSize, user));

        // 验证服务调用
        verify(mockTaskTemplateMapper).selectTaskTemplateList(any(), any(),any());
        verify(mockCategoryService).getCategoryFullPath(1L);
    }

    @Test
    @DisplayName("测试getTaskParams方法 - 正常场景")
    void testGetTaskParams() throws Exception {
        // 使用反射获取私有方法
        Method getTaskParamsMethod = TaskTemplateServiceImpl.class.getDeclaredMethod(
                "getTaskParams",
                ScriptExecAuditDto.class,
                Long.class
        );
        getTaskParamsMethod.setAccessible(true);

        // 准备测试数据
        ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        List<ParameterDto> parameterDtos = new ArrayList<>();

        // 创建测试参数1
        ParameterDto param1 = new ParameterDto();
        param1.setParamCheckIid(1L);
        param1.setScriptParameterManagerId(100L);
        param1.setParamType("STRING");
        param1.setParamDefaultValue("testValue1");
        param1.setParamDesc("测试参数1");
        param1.setParamOrder(1);

        // 创建测试参数2
        ParameterDto param2 = new ParameterDto();
        param2.setParamCheckIid(2L);
        param2.setScriptParameterManagerId(200L);
        param2.setParamType("NUMBER");
        param2.setParamDefaultValue("123");
        param2.setParamDesc("测试参数2");
        param2.setParamOrder(2);

        parameterDtos.add(param1);
        parameterDtos.add(param2);
        scriptExecAuditDto.setParams(parameterDtos);

        Long taskId = 1000L;

        // 执行私有方法
        @SuppressWarnings("unchecked")
        List<TaskParams> result = (List<TaskParams>) getTaskParamsMethod.invoke(
                taskTemplateServiceUnderTest,
                scriptExecAuditDto,
                taskId
        );

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());

        // 验证第一个参数
        TaskParams taskParams1 = result.get(0);
        assertEquals(taskId, taskParams1.getScriptTaskId());
        assertEquals(param1.getParamCheckIid(), taskParams1.getScriptParameterCheckId());
        assertEquals(param1.getScriptParameterManagerId(), taskParams1.getScriptParameterManagerId());
        assertEquals(param1.getParamType(), taskParams1.getType());
        assertEquals(param1.getParamDefaultValue(), taskParams1.getValue());
        assertEquals(param1.getParamDesc(), taskParams1.getDesc());
        assertEquals(param1.getParamOrder(), taskParams1.getOrder());
        assertEquals(0, taskParams1.getStartType());

        // 验证第二个参数
        TaskParams taskParams2 = result.get(1);
        assertEquals(taskId, taskParams2.getScriptTaskId());
        assertEquals(param2.getParamCheckIid(), taskParams2.getScriptParameterCheckId());
        assertEquals(param2.getScriptParameterManagerId(), taskParams2.getScriptParameterManagerId());
        assertEquals(param2.getParamType(), taskParams2.getType());
        assertEquals(param2.getParamDefaultValue(), taskParams2.getValue());
        assertEquals(param2.getParamDesc(), taskParams2.getDesc());
        assertEquals(param2.getParamOrder(), taskParams2.getOrder());
        assertEquals(0, taskParams2.getStartType());
    }

    @Test
    @DisplayName("测试getTaskParams方法 - 空参数列表")
    void testGetTaskParams_EmptyParams() throws Exception {
        // 使用反射获取私有方法
        Method getTaskParamsMethod = TaskTemplateServiceImpl.class.getDeclaredMethod(
                "getTaskParams",
                ScriptExecAuditDto.class,
                Long.class
        );
        getTaskParamsMethod.setAccessible(true);

        // 准备测试数据
        ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        scriptExecAuditDto.setParams(new ArrayList<>());
        Long taskId = 1000L;

        // 执行私有方法
        @SuppressWarnings("unchecked")
        List<TaskParams> result = (List<TaskParams>) getTaskParamsMethod.invoke(
                taskTemplateServiceUnderTest,
                scriptExecAuditDto,
                taskId
        );

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("测试getTaskParams方法 - null参数列表")
    void testGetTaskParams_NullParams() throws Exception {
        // 使用反射获取私有方法
        Method getTaskParamsMethod = TaskTemplateServiceImpl.class.getDeclaredMethod(
                "getTaskParams",
                ScriptExecAuditDto.class,
                Long.class
        );
        getTaskParamsMethod.setAccessible(true);

        // 准备测试数据
        ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        List<ParameterDto> parameterDtosList = new ArrayList<>();
        ParameterDto parameterDto = new ParameterDto();
        parameterDto.setId(1L);
        parameterDto.setCreatorId(1L);
        parameterDto.setParamDesc("abc");
        parameterDto.setParamOrder(1);
        parameterDto.setParamType("STRING");
        parameterDto.setParamCheckIid(1L);
        parameterDto.setParamName("testValue1");
        parameterDto.setScriptParameterManagerId(1L);
        parameterDto.setSrcScriptUuid("uuid");
        parameterDtosList.add(parameterDto);
        scriptExecAuditDto.setParams(parameterDtosList);
        Long taskId = 1000L;

        // 执行私有方法
        @SuppressWarnings("unchecked")
        List<TaskParams> result = (List<TaskParams>) getTaskParamsMethod.invoke(
                taskTemplateServiceUnderTest,
                scriptExecAuditDto,
                taskId
        );

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    @Test
    @DisplayName("测试获取克隆任务列表 - 成功场景 - 角色权限")
    void testListCloneTask_Success_WithCategory_Role() {
        // Setup
        TaskApplyQueryDto queryDto = new TaskApplyQueryDto();
//        queryDto.setUserIdList(Arrays.asList(1L));
        Integer pageNum = 1;
        Integer pageSize = 10;
        CurrentUser user = new CurrentUser();
        user.setSupervisor(false);
        user.setOrgCode("TEST_ORG");

        when(mockMyScriptService.getRolePermission())
                .thenReturn(true);

        when(mockCategoryService.getShareTypeRoleIdsByLoginName(any()))
                .thenReturn(Arrays.asList(111111111L));

        when(myScriptServiceScripts.getiUserInfo()).thenReturn(userInfo);

        when(userInfo.queryPermissionUserInfoList(any())).thenReturn(new ArrayList<PermissionUserInfoApiDto>());

        // 创建Page对象而不是ArrayList
        Page<TaskCloneBean> mockTaskList = new Page<>(pageNum, pageSize);
        TaskCloneBean task1 = new TaskCloneBean();
        task1.setCategoryId(1L);
        TaskCloneBean task2 = new TaskCloneBean();
        task2.setCategoryId(2L);
        mockTaskList.add(task1);
        mockTaskList.add(task2);
        mockTaskList.setTotal(2);

        // Mock依赖服务
        doReturn(mockTaskList)
                .when(mockTaskTemplateMapper)
                .selectTaskTemplateList(any(), any(), any());
        when(mockCategoryService.getCategoryFullPath(1L))
                .thenReturn("分类1/子分类1");
        when(mockCategoryService.getCategoryFullPath(2L))
                .thenReturn("分类2/子分类2");

        // Execute
        PageInfo<TaskTemplateDto> result = taskTemplateServiceUnderTest.listCloneTask(
                queryDto, pageNum, pageSize, user);

        // Verify
        assertNotNull(result);
        assertEquals(2, result.getList().size());
        assertEquals("分类1/子分类1", result.getList().get(0).getScriptCategoryName());
        assertEquals("分类2/子分类2", result.getList().get(1).getScriptCategoryName());

        // 验证服务调用
        verify(mockTaskTemplateMapper).selectTaskTemplateList(any(), any(),any());
        verify(mockCategoryService).getCategoryFullPath(1L);
        verify(mockCategoryService).getCategoryFullPath(2L);
    }

    @Test
    @DisplayName("测试从任务申请创建克隆任务-重复场景")
    void testCreateCloneTaskFromTaskApply_Fail() throws ScriptException {
        // Setup
        ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        scriptExecAuditDto.setScriptInfoVersionId(1L);
        scriptExecAuditDto.setExecuser("testUser");

        // 设置TaskInfo，确保taskId不为空
        TaskDto taskInfo = new TaskDto();
        taskInfo.setId(1L);
        taskInfo.setTaskName("testTask");
        scriptExecAuditDto.setTaskInfo(taskInfo);

        // 设置附件列表
        List<AttachmentDto> attachments = new ArrayList<>();
        AttachmentDto attachment = new AttachmentDto();
        attachment.setId(1L);
        attachments.add(attachment);
        scriptExecAuditDto.setScriptTempAttachments(attachments);

        // 设置Agent信息
        List<AgentInfoDto> agentInfoDtos = new ArrayList<>();
        AgentInfoDto agentInfoDto = new AgentInfoDto();
        agentInfoDto.setAgentIp("***********");
        agentInfoDto.setAgentPort(8080);
        agentInfoDtos.add(agentInfoDto);
        scriptExecAuditDto.setChosedAgentUsers(agentInfoDtos);

        // 设置资源组信息
        List<TaskGroupsDto> resGroups = new ArrayList<>();
        TaskGroupsDto groupDto = new TaskGroupsDto();
        groupDto.setSysmComputerGroupId(1L);
        groupDto.setCpname("TestGroup");
        resGroups.add(groupDto);
        scriptExecAuditDto.setChosedResGroups(resGroups);


        CurrentUser user = new CurrentUser();
        user.setSupervisor(false);
        user.setOrgCode("TEST_ORG");

        List<TaskCloneBean> taskCloneBean = new ArrayList<>();
        TaskCloneBean taskCloneBean1 = new TaskCloneBean();
        taskCloneBean1.setIid(1L);
        taskCloneBean.add(taskCloneBean1);



        when(mockTaskTemplateMapper.selectTaskTemplateByTaskName(any()))
                .thenReturn(taskCloneBean);

        assertThrows(ScriptException.class, () ->
                taskTemplateServiceUnderTest.createCloneTaskFromTaskApply(scriptExecAuditDto, user)
        );

    }

    @ParameterizedTest
    @MethodSource("startCommonTaskServiceTestCases")
    @DisplayName("启动常用任务服务 - 参数化测试")
    void testStartCommonTask_Parameterized(
            String testCase,
            StartCommonTaskDto dto,
            TaskCloneBean taskCloneBean,
            List<AgentInfo> agentInfos,
            List<TaskGroups> taskGroups,
            List<TaskParams> taskParams,
            boolean hasAuditPermission,
            Class<? extends Exception> expectedException
    ) throws Exception {
        // Arrange
        CurrentUser user = new CurrentUser();
        user.setId(1L);
        user.setLoginName("testUser");
        user.setFullName("测试用户");

        // Mock taskTemplateMapper.selectTaskTemplateById
        if (taskCloneBean != null) {
            when(mockTaskTemplateMapper.selectTaskTemplateById(dto.getCommonTaskId()))
                    .thenReturn(taskCloneBean);
        } else {
            when(mockTaskTemplateMapper.selectTaskTemplateById(dto.getCommonTaskId()))
                    .thenReturn(null);
        }

        // 只在非异常场景下Mock其他方法
        if (expectedException == null && taskCloneBean != null) {
            // Mock taskParamsTemplateMapper.selectTaskParamsByTaskId
            when(mockTaskParamsTemplateMapper.selectTaskParamsByTaskId(dto.getCommonTaskId()))
                    .thenReturn(taskParams != null ? taskParams : new ArrayList<>());

            // Mock taskIpsTemplateMapper.selectAgentInfoByTaskId
            when(mockTaskIpsTemplateMapper.selectAgentInfoByTaskId(dto.getCommonTaskId()))
                    .thenReturn(agentInfos != null ? agentInfos : new ArrayList<>());

            // Mock taskGroupsTemplateMapper.selectTaskGroupsByTaskId
            when(mockTaskGroupsTemplateMapper.selectTaskGroupsByTaskId(dto.getCommonTaskId()))
                    .thenReturn(taskGroups != null ? taskGroups : new ArrayList<>());

            // Mock权限验证 - 只在需要审核且是风险脚本时才Mock
            if (dto.getAuditUserId() != null && taskCloneBean.getLevel() != 0) {
                List<UserInfoApiDto> permissionUsers = new ArrayList<>();
                if (hasAuditPermission) {
                    UserInfoApiDto permissionUser = new UserInfoApiDto();
                    permissionUser.setId(dto.getAuditUserId());
                    permissionUsers.add(permissionUser);
                }
                when(mockTaskApplyService.queryPermissionUserInfoList(any(), any()))
                        .thenReturn(permissionUsers);
            }

            // Mock execAuditTemplateTask (自引用) - 注意这里调用的是接口方法
            Map<String, Long> mockResult = new HashMap<>();
            mockResult.put("taskId", 123L);
            mockResult.put("auditId", 456L);
            when(mockTaskTemplateService.execAuditTemplateTask(any())).thenReturn(mockResult);
        }

        // Act & Assert
        if (expectedException != null) {
            assertThrows(expectedException, () ->
                    taskTemplateServiceUnderTest.startCommonTask(dto, user)
            );
        } else {
            ScriptTaskApplyResDto scriptTaskApplyResDto = taskTemplateServiceUnderTest.startCommonTask(dto, user);
            assertNotNull(scriptTaskApplyResDto);
            assertEquals(123L, scriptTaskApplyResDto.getTaskId());
            assertEquals(456L, scriptTaskApplyResDto.getAuditId());
        }

        // Verify关键方法调用 - 只验证一定会被调用的方法
        verify(mockTaskTemplateMapper).selectTaskTemplateById(dto.getCommonTaskId());
        if (taskCloneBean != null && expectedException == null) {
            verify(mockTaskParamsTemplateMapper).selectTaskParamsByTaskId(dto.getCommonTaskId());
            verify(mockTaskIpsTemplateMapper).selectAgentInfoByTaskId(dto.getCommonTaskId());

            // 只有在没有Agent信息时才会查询设备组信息
            if (agentInfos == null || agentInfos.isEmpty()) {
                verify(mockTaskGroupsTemplateMapper).selectTaskGroupsByTaskId(dto.getCommonTaskId());
            }
        }
    }

    static Stream<Arguments> startCommonTaskServiceTestCases() {
        // 测试用例1: 白名单脚本，无审核人，成功
        StartCommonTaskDto dto1 = new StartCommonTaskDto();
        dto1.setCommonTaskId(1L);
        dto1.setAuditUserId(null);

        TaskCloneBean taskCloneBean1 = new TaskCloneBean();
        taskCloneBean1.setIid(1L);
        taskCloneBean1.setLevel(0); // 白名单脚本
        taskCloneBean1.setCategoryId(1L); // 添加分类ID
        taskCloneBean1.setTaskName("测试任务1");
        taskCloneBean1.setScriptName("test_script1");
        taskCloneBean1.setScriptInfoVersionId(101L);
        taskCloneBean1.setEachNum(1);
        taskCloneBean1.setDriveMode(1);
        taskCloneBean1.setTimeout(300);
        taskCloneBean1.setExecuser("testUser");

        List<AgentInfo> agentInfos1 = new ArrayList<>();
        AgentInfo agentInfo1 = new AgentInfo();
        agentInfo1.setId(1L);
        agentInfo1.setAgentIp("***********00");
        agentInfo1.setAgentPort(8080);
        agentInfos1.add(agentInfo1);

        List<TaskParams> taskParams1 = new ArrayList<>();
        TaskParams taskParam1 = new TaskParams();
        taskParam1.setId(1L);
        taskParam1.setOrder(1);
        taskParam1.setValue("param1");
        taskParam1.setType("string");
        taskParam1.setDesc("测试参数1");
        taskParams1.add(taskParam1);

        // 测试用例2: 白名单脚本，有审核人，成功
        StartCommonTaskDto dto2 = new StartCommonTaskDto();
        dto2.setCommonTaskId(2L);
        dto2.setAuditUserId(100L);

        TaskCloneBean taskCloneBean2 = new TaskCloneBean();
        taskCloneBean2.setIid(2L);
        taskCloneBean2.setLevel(0); // 白名单脚本
        taskCloneBean2.setCategoryId(1L); // 添加分类ID
        taskCloneBean2.setTaskName("测试任务2");
        taskCloneBean2.setScriptInfoVersionId(102L);
        taskCloneBean2.setEachNum(1);
        taskCloneBean2.setDriveMode(1);
        taskCloneBean2.setTimeout(300);
        taskCloneBean2.setExecuser("testUser");

        // 测试用例3: 风险脚本，无审核人，抛出异常
        StartCommonTaskDto dto3 = new StartCommonTaskDto();
        dto3.setCommonTaskId(3L);
        dto3.setAuditUserId(null);

        TaskCloneBean taskCloneBean3 = new TaskCloneBean();
        taskCloneBean3.setIid(3L);
        taskCloneBean3.setLevel(1); // 风险脚本
        taskCloneBean3.setCategoryId(2L); // 添加分类ID
        taskCloneBean3.setTaskName("测试任务3");
        taskCloneBean3.setScriptInfoVersionId(103L);
        taskCloneBean3.setEachNum(1);
        taskCloneBean3.setDriveMode(1);
        taskCloneBean3.setTimeout(300);
        taskCloneBean3.setExecuser("testUser");

        // 测试用例4: 风险脚本，有审核人但权限不足，抛出异常
        StartCommonTaskDto dto4 = new StartCommonTaskDto();
        dto4.setCommonTaskId(4L);
        dto4.setAuditUserId(200L);

        TaskCloneBean taskCloneBean4 = new TaskCloneBean();
        taskCloneBean4.setIid(4L);
        taskCloneBean4.setLevel(1); // 风险脚本
        taskCloneBean4.setCategoryId(2L); // 添加分类ID
        taskCloneBean4.setTaskName("测试任务4");
        taskCloneBean4.setScriptInfoVersionId(104L);
        taskCloneBean4.setEachNum(1);
        taskCloneBean4.setDriveMode(1);
        taskCloneBean4.setTimeout(300);
        taskCloneBean4.setExecuser("testUser");

        // 测试用例5: 风险脚本，有审核人且权限充足，成功
        StartCommonTaskDto dto5 = new StartCommonTaskDto();
        dto5.setCommonTaskId(5L);
        dto5.setAuditUserId(300L);

        TaskCloneBean taskCloneBean5 = new TaskCloneBean();
        taskCloneBean5.setIid(5L);
        taskCloneBean5.setLevel(1); // 风险脚本
        taskCloneBean5.setCategoryId(2L); // 添加分类ID
        taskCloneBean5.setTaskName("测试任务5");
        taskCloneBean5.setScriptInfoVersionId(105L);
        taskCloneBean5.setEachNum(1);
        taskCloneBean5.setDriveMode(1);
        taskCloneBean5.setTimeout(300);
        taskCloneBean5.setExecuser("testUser");

        // 测试用例6: 常用任务不存在，抛出异常
        StartCommonTaskDto dto6 = new StartCommonTaskDto();
        dto6.setCommonTaskId(999L);

        // 测试用例7: 有设备组信息的任务
        StartCommonTaskDto dto7 = new StartCommonTaskDto();
        dto7.setCommonTaskId(7L);

        TaskCloneBean taskCloneBean7 = new TaskCloneBean();
        taskCloneBean7.setIid(7L);
        taskCloneBean7.setLevel(0);
        taskCloneBean7.setCategoryId(1L); // 添加分类ID
        taskCloneBean7.setTaskName("测试任务7");
        taskCloneBean7.setScriptInfoVersionId(107L);
        taskCloneBean7.setEachNum(1);
        taskCloneBean7.setDriveMode(1);
        taskCloneBean7.setTimeout(300);
        taskCloneBean7.setExecuser("testUser");

        List<TaskGroups> taskGroups7 = new ArrayList<>();
        TaskGroups taskGroup7 = new TaskGroups();
        taskGroup7.setId(1L);
        taskGroup7.setSysmComputerGroupId(100L);
        taskGroup7.setCpname("测试设备组");
        taskGroups7.add(taskGroup7);

        // 测试用例8: 既无Agent也无设备组，抛出异常
        StartCommonTaskDto dto8 = new StartCommonTaskDto();
        dto8.setCommonTaskId(8L);

        TaskCloneBean taskCloneBean8 = new TaskCloneBean();
        taskCloneBean8.setIid(8L);
        taskCloneBean8.setLevel(0);
        taskCloneBean8.setCategoryId(1L); // 添加分类ID
        taskCloneBean8.setTaskName("测试任务8");
        taskCloneBean8.setScriptInfoVersionId(108L);
        taskCloneBean8.setEachNum(1);
        taskCloneBean8.setDriveMode(1);
        taskCloneBean8.setTimeout(300);
        taskCloneBean8.setExecuser("testUser");

        return Stream.of(
                Arguments.of("白名单脚本_无审核人_成功", dto1, taskCloneBean1, agentInfos1, null, taskParams1, false, null),
                Arguments.of("白名单脚本_有审核人_成功", dto2, taskCloneBean2, agentInfos1, null, taskParams1, true, null),
                Arguments.of("风险脚本_无审核人_异常", dto3, taskCloneBean3, agentInfos1, null, taskParams1, false, ScriptException.class),
                Arguments.of("风险脚本_有审核人但权限不足_异常", dto4, taskCloneBean4, agentInfos1, null, taskParams1, false, ScriptException.class),
                Arguments.of("风险脚本_有审核人且权限充足_成功", dto5, taskCloneBean5, agentInfos1, null, taskParams1, true, null),
                Arguments.of("常用任务不存在_异常", dto6, null, null, null, null, false, ScriptException.class),
                Arguments.of("有设备组信息的任务_成功", dto7, taskCloneBean7, null, taskGroups7, taskParams1, false, null),
                Arguments.of("既无Agent也无设备组_异常", dto8, taskCloneBean8, null, null, taskParams1, false, ScriptException.class)
        );
    }

    @Test
    @DisplayName("测试getParameterDtos方法 - params不为null且不为空的情况")
    void testGetParameterDtos_WithValidParams() throws Exception {
        // 准备测试数据
        List<TaskParams> taskParamsList = Arrays.asList(
            createMockTaskParams(1L, "String", "defaultValue1", "desc1", 1),
            createMockTaskParams(2L, "Cipher", "defaultValue2", "desc2", 2)
        );

        StartCommonTaskDto startCommonTaskDto = new StartCommonTaskDto();
        List<ParameterValidationDto> params = Arrays.asList(
            createMockParameterValidationDto(1, "String", "testValue1", "testDesc1", 1),
            createMockParameterValidationDto(2, "Cipher", "testValue2", "testDesc2", 2)
        );
        startCommonTaskDto.setParams(params);

        // 使用反射调用私有方法
        Method getParameterDtosMethod = TaskTemplateServiceImpl.class.getDeclaredMethod(
            "getParameterDtos", List.class, StartCommonTaskDto.class);
        getParameterDtosMethod.setAccessible(true);

        // 执行测试
        @SuppressWarnings("unchecked")
        List<ParameterDto> result = (List<ParameterDto>) getParameterDtosMethod.invoke(
            null, taskParamsList, startCommonTaskDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());

        // 验证第一个参数
        ParameterDto firstParam = result.get(0);
        assertEquals(1, firstParam.getParamOrder());
        assertEquals("testValue1", firstParam.getParamDefaultValue());
        assertEquals("String", firstParam.getParamType());
        assertEquals("testDesc1", firstParam.getParamDesc());

        // 验证第二个参数
        ParameterDto secondParam = result.get(1);
        assertEquals(2, secondParam.getParamOrder());
        assertEquals("testValue2", secondParam.getParamDefaultValue());
        assertEquals("Cipher", secondParam.getParamType());
        assertEquals("testDesc2", secondParam.getParamDesc());
    }

    /**
     * 创建模拟的TaskParams对象
     */
    private TaskParams createMockTaskParams(Long id, String type, String value, String desc, Integer order) {
        TaskParams taskParams = new TaskParams();
        taskParams.setId(id);
        taskParams.setType(type);
        taskParams.setValue(value);
        taskParams.setDesc(desc);
        taskParams.setOrder(order);
        return taskParams;
    }

    /**
     * 创建模拟的ParameterValidationDto对象
     */
    private ParameterValidationDto createMockParameterValidationDto(Integer paramOrder, String paramType,
                                                                  String paramDefaultValue, String paramDesc, Integer order) {
        ParameterValidationDto dto = new ParameterValidationDto();
        dto.setParamOrder(paramOrder);
        dto.setParamType(paramType);
        dto.setParamDefaultValue(paramDefaultValue);
        dto.setParamDesc(paramDesc);
        return dto;
    }

}