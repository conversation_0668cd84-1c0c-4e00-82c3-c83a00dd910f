package com.ideal.script.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.script.model.dto.AgentInfoDto;
import com.ideal.script.model.dto.AgentInfoQueryDto;
import com.ideal.script.model.dto.TaskGroupsDto;
import com.ideal.script.model.dto.interaction.agent.SystemComputerGroupDto;
import com.ideal.script.model.dto.interaction.agent.SystemComputerGroupQueryDto;
import com.ideal.script.service.ITaskGroupsService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * TaskGroupsController单元测试类
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class TaskGroupsControllerTest {

    @Mock
    private ITaskGroupsService taskGroupsService;

    @InjectMocks
    private TaskGroupsController taskGroupsController;

    private TaskGroupsDto taskGroupsDto;
    private SystemComputerGroupDto systemComputerGroupDto;
    private SystemComputerGroupQueryDto systemComputerGroupQueryDto;
    private AgentInfoDto agentInfoDto;
    private AgentInfoQueryDto agentInfoQueryDto;
    private TableQueryDto<TaskGroupsDto> taskGroupsTableQueryDto;
    private TableQueryDto<SystemComputerGroupQueryDto> systemComputerGroupTableQueryDto;
    private TableQueryDto<AgentInfoQueryDto> agentInfoTableQueryDto;

    @BeforeEach
    void setUp() {
        // 初始化TaskGroupsDto测试数据
        taskGroupsDto = new TaskGroupsDto();
        taskGroupsDto.setId(1L);
        taskGroupsDto.setScriptTaskId(100L);
        taskGroupsDto.setSysmComputerGroupId(200L);
        taskGroupsDto.setCpname("测试资源组");
        taskGroupsDto.setCreateTime(new Timestamp(System.currentTimeMillis()));

        // 初始化SystemComputerGroupDto测试数据
        systemComputerGroupDto = new SystemComputerGroupDto();
        systemComputerGroupDto.setSysmComputerGroupId(200L);
        systemComputerGroupDto.setCpname("测试设备组");
        systemComputerGroupDto.setEnName("test_group");
        systemComputerGroupDto.setDescription("测试设备组描述");

        // 初始化SystemComputerGroupQueryDto测试数据
        systemComputerGroupQueryDto = new SystemComputerGroupQueryDto();
        systemComputerGroupQueryDto.setSysmComputerGroupId(200L);
        systemComputerGroupQueryDto.setCpName("测试设备组");

        // 初始化AgentInfoDto测试数据
        agentInfoDto = new AgentInfoDto();
        agentInfoDto.setId(1L);
        agentInfoDto.setAgentIp("*************");
        agentInfoDto.setAgentPort(8080);
        agentInfoDto.setAgentName("测试Agent");
        agentInfoDto.setAgentState(1);

        // 初始化AgentInfoQueryDto测试数据
        agentInfoQueryDto = new AgentInfoQueryDto();
        agentInfoQueryDto.setAgentIp("*************");
        agentInfoQueryDto.setAgentName("测试Agent");

        // 初始化TableQueryDto测试数据
        taskGroupsTableQueryDto = new TableQueryDto<>();
        taskGroupsTableQueryDto.setPageNum(1);
        taskGroupsTableQueryDto.setPageSize(10);
        taskGroupsTableQueryDto.setQueryParam(taskGroupsDto);

        systemComputerGroupTableQueryDto = new TableQueryDto<>();
        systemComputerGroupTableQueryDto.setPageNum(1);
        systemComputerGroupTableQueryDto.setPageSize(10);
        systemComputerGroupTableQueryDto.setQueryParam(systemComputerGroupQueryDto);

        agentInfoTableQueryDto = new TableQueryDto<>();
        agentInfoTableQueryDto.setPageNum(1);
        agentInfoTableQueryDto.setPageSize(10);
        agentInfoTableQueryDto.setQueryParam(agentInfoQueryDto);
    }

    @Test
    @DisplayName("查询任务与资源组关系列表-成功")
    void listTaskGroups_success() {
        // 创建分页结果
        PageInfo<TaskGroupsDto> pageInfo = new PageInfo<>();
        pageInfo.setList(Arrays.asList(taskGroupsDto));
        pageInfo.setTotal(1);
        pageInfo.setPageNum(1);
        pageInfo.setPageSize(10);

        // Mock service方法
        doReturn(pageInfo).when(taskGroupsService).selectTaskGroupsList(any(TaskGroupsDto.class), eq(1), eq(10));

        // 执行测试方法
        R<PageInfo<TaskGroupsDto>> result = taskGroupsController.listTaskGroups(taskGroupsTableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("list.success", result.getMessage());
        assertEquals(pageInfo, result.getData());
        assertEquals(1, result.getData().getTotal());
        assertEquals(1, result.getData().getList().size());
        assertEquals("测试资源组", result.getData().getList().get(0).getCpname());
        
        // 验证service方法被调用
        verify(taskGroupsService, times(1)).selectTaskGroupsList(any(TaskGroupsDto.class), eq(1), eq(10));
    }

    @Test
    @DisplayName("查询任务与资源组关系列表-空结果")
    void listTaskGroups_emptyResult() {
        // 创建空的分页结果
        PageInfo<TaskGroupsDto> emptyPageInfo = new PageInfo<>();
        emptyPageInfo.setList(Collections.emptyList());
        emptyPageInfo.setTotal(0);
        emptyPageInfo.setPageNum(1);
        emptyPageInfo.setPageSize(10);

        // Mock service方法
        doReturn(emptyPageInfo).when(taskGroupsService).selectTaskGroupsList(any(TaskGroupsDto.class), eq(1), eq(10));

        // 执行测试方法
        R<PageInfo<TaskGroupsDto>> result = taskGroupsController.listTaskGroups(taskGroupsTableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("list.success", result.getMessage());
        assertEquals(emptyPageInfo, result.getData());
        assertEquals(0, result.getData().getTotal());
        assertTrue(result.getData().getList().isEmpty());
        
        // 验证service方法被调用
        verify(taskGroupsService, times(1)).selectTaskGroupsList(any(TaskGroupsDto.class), eq(1), eq(10));
    }

    @Test
    @DisplayName("获取agent分组列表数据-成功")
    void queryAgentGroupPageList_success() {
        // 创建分页结果
        PageInfo<SystemComputerGroupDto> pageInfo = new PageInfo<>();
        pageInfo.setList(Arrays.asList(systemComputerGroupDto));
        pageInfo.setTotal(1);
        pageInfo.setPageNum(1);
        pageInfo.setPageSize(10);

        // Mock service方法
        doReturn(pageInfo).when(taskGroupsService).queryAgentGroupPageList(any(SystemComputerGroupQueryDto.class), eq(1), eq(10));

        // 执行测试方法
        R<PageInfo<SystemComputerGroupDto>> result = taskGroupsController.queryAgentGroupPageList(systemComputerGroupTableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("list.success", result.getMessage());
        assertEquals(pageInfo, result.getData());
        assertEquals(1, result.getData().getTotal());
        assertEquals(1, result.getData().getList().size());
        assertEquals("测试设备组", result.getData().getList().get(0).getCpName());
        
        // 验证service方法被调用
        verify(taskGroupsService, times(1)).queryAgentGroupPageList(any(SystemComputerGroupQueryDto.class), eq(1), eq(10));
    }

    @Test
    @DisplayName("获取agent分组列表数据-空结果")
    void queryAgentGroupPageList_emptyResult() {
        // 创建空的分页结果
        PageInfo<SystemComputerGroupDto> emptyPageInfo = new PageInfo<>();
        emptyPageInfo.setList(Collections.emptyList());
        emptyPageInfo.setTotal(0);
        emptyPageInfo.setPageNum(1);
        emptyPageInfo.setPageSize(10);

        // Mock service方法
        doReturn(emptyPageInfo).when(taskGroupsService).queryAgentGroupPageList(any(SystemComputerGroupQueryDto.class), eq(1), eq(10));

        // 执行测试方法
        R<PageInfo<SystemComputerGroupDto>> result = taskGroupsController.queryAgentGroupPageList(systemComputerGroupTableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("list.success", result.getMessage());
        assertEquals(emptyPageInfo, result.getData());
        assertEquals(0, result.getData().getTotal());
        assertTrue(result.getData().getList().isEmpty());
        
        // 验证service方法被调用
        verify(taskGroupsService, times(1)).queryAgentGroupPageList(any(SystemComputerGroupQueryDto.class), eq(1), eq(10));
    }

    @Test
    @DisplayName("查询某个agent分组下绑定的agent信息-成功")
    void queryAgentPageListByGroupId_success() {
        // 创建分页结果
        PageInfo<AgentInfoDto> pageInfo = new PageInfo<>();
        pageInfo.setList(Arrays.asList(agentInfoDto));
        pageInfo.setTotal(1);
        pageInfo.setPageNum(1);
        pageInfo.setPageSize(10);

        // Mock service方法
        doReturn(pageInfo).when(taskGroupsService).queryAgentPageListByGroupId(any(SystemComputerGroupQueryDto.class), eq(1), eq(10));

        // 执行测试方法
        R<PageInfo<AgentInfoDto>> result = taskGroupsController.queryAgentPageListByGroupId(systemComputerGroupTableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("list.success", result.getMessage());
        assertEquals(pageInfo, result.getData());
        assertEquals(1, result.getData().getTotal());
        assertEquals(1, result.getData().getList().size());
        assertEquals("*************", result.getData().getList().get(0).getAgentIp());
        assertEquals("测试Agent", result.getData().getList().get(0).getAgentName());
        
        // 验证service方法被调用
        verify(taskGroupsService, times(1)).queryAgentPageListByGroupId(any(SystemComputerGroupQueryDto.class), eq(1), eq(10));
    }

    @Test
    @DisplayName("查询某个agent分组下绑定的agent信息-空结果")
    void queryAgentPageListByGroupId_emptyResult() {
        // 创建空的分页结果
        PageInfo<AgentInfoDto> emptyPageInfo = new PageInfo<>();
        emptyPageInfo.setList(Collections.emptyList());
        emptyPageInfo.setTotal(0);
        emptyPageInfo.setPageNum(1);
        emptyPageInfo.setPageSize(10);

        // Mock service方法
        doReturn(emptyPageInfo).when(taskGroupsService).queryAgentPageListByGroupId(any(SystemComputerGroupQueryDto.class), eq(1), eq(10));

        // 执行测试方法
        R<PageInfo<AgentInfoDto>> result = taskGroupsController.queryAgentPageListByGroupId(systemComputerGroupTableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("list.success", result.getMessage());
        assertEquals(emptyPageInfo, result.getData());
        assertEquals(0, result.getData().getTotal());
        assertTrue(result.getData().getList().isEmpty());
        
        // 验证service方法被调用
        verify(taskGroupsService, times(1)).queryAgentPageListByGroupId(any(SystemComputerGroupQueryDto.class), eq(1), eq(10));
    }

    @Test
    @DisplayName("查询用户角色下所有agent信息-成功")
    void queryAgentInfoGroupRole_success() {
        // 创建分页结果
        PageInfo<AgentInfoDto> pageInfo = new PageInfo<>();
        pageInfo.setList(Arrays.asList(agentInfoDto));
        pageInfo.setTotal(1);
        pageInfo.setPageNum(1);
        pageInfo.setPageSize(10);

        // Mock service方法
        doReturn(pageInfo).when(taskGroupsService).queryAgentInfoGroupRole(any(AgentInfoQueryDto.class), eq(1), eq(10));

        // 执行测试方法
        R<PageInfo<AgentInfoDto>> result = taskGroupsController.queryAgentInfoGroupRole(agentInfoTableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("list.success", result.getMessage());
        assertEquals(pageInfo, result.getData());
        assertEquals(1, result.getData().getTotal());
        assertEquals(1, result.getData().getList().size());
        assertEquals("*************", result.getData().getList().get(0).getAgentIp());
        assertEquals("测试Agent", result.getData().getList().get(0).getAgentName());
        
        // 验证service方法被调用
        verify(taskGroupsService, times(1)).queryAgentInfoGroupRole(any(AgentInfoQueryDto.class), eq(1), eq(10));
    }

    @Test
    @DisplayName("查询用户角色下所有agent信息-空结果")
    void queryAgentInfoGroupRole_emptyResult() {
        // 创建空的分页结果
        PageInfo<AgentInfoDto> emptyPageInfo = new PageInfo<>();
        emptyPageInfo.setList(Collections.emptyList());
        emptyPageInfo.setTotal(0);
        emptyPageInfo.setPageNum(1);
        emptyPageInfo.setPageSize(10);

        // Mock service方法
        doReturn(emptyPageInfo).when(taskGroupsService).queryAgentInfoGroupRole(any(AgentInfoQueryDto.class), eq(1), eq(10));

        // 执行测试方法
        R<PageInfo<AgentInfoDto>> result = taskGroupsController.queryAgentInfoGroupRole(agentInfoTableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("list.success", result.getMessage());
        assertEquals(emptyPageInfo, result.getData());
        assertEquals(0, result.getData().getTotal());
        assertTrue(result.getData().getList().isEmpty());
        
        // 验证service方法被调用
        verify(taskGroupsService, times(1)).queryAgentInfoGroupRole(any(AgentInfoQueryDto.class), eq(1), eq(10));
    }

    @Test
    @DisplayName("所有方法-service抛出异常")
    void allMethods_serviceException() {
        // Mock service方法抛出异常
        doThrow(new RuntimeException("Database connection failed")).when(taskGroupsService)
                .selectTaskGroupsList(any(TaskGroupsDto.class), eq(1), eq(10));
        doThrow(new RuntimeException("Database connection failed")).when(taskGroupsService)
                .queryAgentGroupPageList(any(SystemComputerGroupQueryDto.class), eq(1), eq(10));
        doThrow(new RuntimeException("Database connection failed")).when(taskGroupsService)
                .queryAgentPageListByGroupId(any(SystemComputerGroupQueryDto.class), eq(1), eq(10));
        doThrow(new RuntimeException("Database connection failed")).when(taskGroupsService)
                .queryAgentInfoGroupRole(any(AgentInfoQueryDto.class), eq(1), eq(10));

        // 验证所有方法都会抛出异常
        assertThrows(RuntimeException.class, () -> {
            taskGroupsController.listTaskGroups(taskGroupsTableQueryDto);
        });
        
        assertThrows(RuntimeException.class, () -> {
            taskGroupsController.queryAgentGroupPageList(systemComputerGroupTableQueryDto);
        });
        
        assertThrows(RuntimeException.class, () -> {
            taskGroupsController.queryAgentPageListByGroupId(systemComputerGroupTableQueryDto);
        });
        
        assertThrows(RuntimeException.class, () -> {
            taskGroupsController.queryAgentInfoGroupRole(agentInfoTableQueryDto);
        });
    }
}
