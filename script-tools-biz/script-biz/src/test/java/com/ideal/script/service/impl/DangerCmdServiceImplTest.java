package com.ideal.script.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.DangerCmdMapper;
import com.ideal.script.model.dto.DangerCmdDto;
import com.ideal.script.model.entity.DangerCmd;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class DangerCmdServiceImplTest {

    @Mock
    private DangerCmdMapper mockDangerCmdMapper;

    private DangerCmdServiceImpl dangerCmdServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        dangerCmdServiceImplUnderTest = new DangerCmdServiceImpl(mockDangerCmdMapper);
    }

    @Test
    void testSelectDangerCmdById() {
        // Setup
        // Configure DangerCmdMapper.selectDangerCmdById(...).
        final DangerCmd dangerCmd = new DangerCmd();
        dangerCmd.setId(0L);
        dangerCmd.setScriptCmd("scriptCmd");
        dangerCmd.setWhiteCommand(0);
        dangerCmd.setScriptType("scriptType");
        dangerCmd.setScriptCmdLevel(0);
        when(mockDangerCmdMapper.selectDangerCmdById(0L)).thenReturn(dangerCmd);

        // Run the test
        final DangerCmdDto result = dangerCmdServiceImplUnderTest.selectDangerCmdById(0L);
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testSelectDangerCmdList() {
        // Setup
        final DangerCmdDto dangerCmdDto = new DangerCmdDto();
        dangerCmdDto.setId(0L);
        dangerCmdDto.setScriptCmd("scriptCmd");
        dangerCmdDto.setWhiteCommand(0);
        dangerCmdDto.setScriptType("scriptType");
        dangerCmdDto.setScriptCmdLevel(0);

        // Configure DangerCmdMapper.selectDangerCmdList(...).
        final DangerCmd dangerCmd = new DangerCmd();
        dangerCmd.setId(0L);
        dangerCmd.setScriptCmd("scriptCmd");
        dangerCmd.setWhiteCommand(0);
        dangerCmd.setScriptType("scriptType");
        dangerCmd.setScriptCmdLevel(0);
        Page<DangerCmd> page = new Page<>();
        page.add(dangerCmd);
        when(mockDangerCmdMapper.selectDangerCmdList(any(DangerCmd.class))).thenReturn(page);

        // Run the test
        final PageInfo<DangerCmdDto> result = dangerCmdServiceImplUnderTest.selectDangerCmdList(dangerCmdDto, 0, 0);
        assertNotNull(result);
        // Verify the results
    }


    @Test
    void testInsertDangerCmd() throws ScriptException {
        // Setup
        final DangerCmdDto dangerCmdDto = new DangerCmdDto();
        dangerCmdDto.setId(0L);
        dangerCmdDto.setScriptCmd("scriptCmd");
        dangerCmdDto.setWhiteCommand(0);
        dangerCmdDto.setScriptType("scriptType");
        dangerCmdDto.setScriptCmdLevel(0);

        List<DangerCmd> dangerCmdList = new ArrayList<>();
        DangerCmd dangerCmd  = new DangerCmd();
        dangerCmd.setScriptCmd("[0-9]+");
        dangerCmdList.add(dangerCmd);

        when(mockDangerCmdMapper.insertDangerCmd(any(DangerCmd.class))).thenReturn(0);
        when(mockDangerCmdMapper.selectDangerCmdList(any(DangerCmd.class))).thenReturn(dangerCmdList);

        // Run the test
        final int result = dangerCmdServiceImplUnderTest.insertDangerCmd(dangerCmdDto);

        // Verify the results
        assertThat(result).isZero();
    }


    @Test
    void testInsertDangerCmd_checkDangerCmd_exception() throws ScriptException {
        // Setup
        final DangerCmdDto dangerCmdDto = new DangerCmdDto();
        dangerCmdDto.setId(0L);
        dangerCmdDto.setScriptCmd("aaa(bbb");
        dangerCmdDto.setWhiteCommand(0);
        dangerCmdDto.setScriptType("scriptType");
        dangerCmdDto.setScriptCmdLevel(0);

        // Run the test
         assertThrows(ScriptException.class, ()->{
             dangerCmdServiceImplUnderTest.insertDangerCmd(dangerCmdDto);
         });
    }

    @Test
    void testInsertDangerCmd_checkDuplicateDangerCmd_exception() throws ScriptException {
        // Setup
        final DangerCmdDto dangerCmdDto = new DangerCmdDto();
        dangerCmdDto.setId(0L);
        dangerCmdDto.setScriptCmd("[0-9]+");
        dangerCmdDto.setWhiteCommand(0);
        dangerCmdDto.setScriptType("scriptType");
        dangerCmdDto.setScriptCmdLevel(0);

        List<DangerCmd> dangerCmdList = new ArrayList<>();
        DangerCmd dangerCmd  = new DangerCmd();
        dangerCmd.setScriptCmd("[0-9]+");
        dangerCmdList.add(dangerCmd);

        when(mockDangerCmdMapper.selectDangerCmdList(any(DangerCmd.class))).thenReturn(dangerCmdList);

        // Run the test
        assertThrows(ScriptException.class,()->{
            dangerCmdServiceImplUnderTest.insertDangerCmd(dangerCmdDto);
        });


    }

    @Test
    void testUpdateDangerCmd() throws ScriptException {
        // Setup
        final DangerCmdDto dangerCmdDto = new DangerCmdDto();
        dangerCmdDto.setId(0L);
        dangerCmdDto.setScriptCmd("scriptCmd");
        dangerCmdDto.setWhiteCommand(0);
        dangerCmdDto.setScriptType("scriptType");
        dangerCmdDto.setScriptCmdLevel(0);

        when(mockDangerCmdMapper.updateDangerCmd(any(DangerCmd.class))).thenReturn(0);

        // Run the test
        final int result = dangerCmdServiceImplUnderTest.updateDangerCmd(dangerCmdDto);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testDeleteDangerCmdByIds() {
        // Setup
        when(mockDangerCmdMapper.deleteDangerCmdByIds(any(Long[].class))).thenReturn(0);

        // Run the test
        final int result = dangerCmdServiceImplUnderTest.deleteDangerCmdByIds(new Long[]{0L});

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testDeleteDangerCmdById() {
        // Setup
        when(mockDangerCmdMapper.deleteDangerCmdById(0L)).thenReturn(0);

        // Run the test
        final int result = dangerCmdServiceImplUnderTest.deleteDangerCmdById(0L);

        // Verify the results
        assertThat(result).isZero();
    }


    @ParameterizedTest
    @ValueSource(strings = {"shell","python","powershell"})
    void convertScriptType(String type) throws Exception {
        DangerCmdDto dangerCmdDto = new DangerCmdDto();
        dangerCmdDto.setScriptType(type);
        dangerCmdServiceImplUnderTest.convertScriptType(dangerCmdDto);

        switch (type) {
            case "shell":
                assertThat(dangerCmdDto.getScriptType()).isEqualTo("sh");
                break;
            case "python":
                assertThat(dangerCmdDto.getScriptType()).isEqualTo("py");
                break;
            case "powershell":
                assertThat(dangerCmdDto.getScriptType()).isEqualTo("ps1");
                break;
            default:
                throw new Exception();
        }
    }
}
