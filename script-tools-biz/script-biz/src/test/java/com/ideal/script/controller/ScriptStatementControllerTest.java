package com.ideal.script.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.script.model.dto.StatementDto;
import com.ideal.script.service.IScriptStatementService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.servlet.http.HttpServletResponse;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * ScriptStatementController单元测试类
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class ScriptStatementControllerTest {

    @Mock
    private IScriptStatementService scriptStatementService;

    @Mock
    private HttpServletResponse response;

    @InjectMocks
    private ScriptStatementController scriptStatementController;

    private StatementDto statementDto;
    private TableQueryDto<StatementDto> tableQueryDto;
    private PageInfo<StatementDto> pageInfo;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        statementDto = new StatementDto();
        statementDto.setId(1L);
        statementDto.setInfoId(100L);
        statementDto.setScriptNameZh("测试脚本");
        statementDto.setScriptName("test_script");
        statementDto.setCategoryPath("/root/test");
        statementDto.setDefaultVersion("1.0.0");
        statementDto.setTaskCount(10);
        statementDto.setSuccessRate("95%");
        statementDto.setUnmodifyDay(30);
        statementDto.setConfirmState(1);
        statementDto.setCreatorName("测试用户");

        tableQueryDto = new TableQueryDto<>();
        tableQueryDto.setPageNum(1);
        tableQueryDto.setPageSize(10);
        tableQueryDto.setQueryParam(statementDto);

        pageInfo = new PageInfo<>();
        pageInfo.setList(Arrays.asList(statementDto));
        pageInfo.setTotal(1);
        pageInfo.setPageNum(1);
        pageInfo.setPageSize(10);
    }

    @Test
    @DisplayName("查询脚本报表列表-成功")
    void scriptStatementList_success() {
        // Mock service方法
        doReturn(pageInfo).when(scriptStatementService).selectScriptStatementPage(any(StatementDto.class), eq(1), eq(10));

        // 执行测试方法
        R<PageInfo<StatementDto>> result = scriptStatementController.scriptStatementList(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("list.success", result.getMessage());
        assertEquals(pageInfo, result.getData());
        assertEquals(1, result.getData().getTotal());
        assertEquals(1, result.getData().getList().size());
        assertEquals("测试脚本", result.getData().getList().get(0).getScriptNameZh());
        
        // 验证service方法被调用
        verify(scriptStatementService, times(1)).selectScriptStatementPage(any(StatementDto.class), eq(1), eq(10));
    }

    @Test
    @DisplayName("查询脚本报表列表-空结果")
    void scriptStatementList_emptyResult() {
        // 创建空的分页结果
        PageInfo<StatementDto> emptyPageInfo = new PageInfo<>();
        emptyPageInfo.setList(Collections.emptyList());
        emptyPageInfo.setTotal(0);
        emptyPageInfo.setPageNum(1);
        emptyPageInfo.setPageSize(10);

        // Mock service方法
        doReturn(emptyPageInfo).when(scriptStatementService).selectScriptStatementPage(any(StatementDto.class), eq(1), eq(10));

        // 执行测试方法
        R<PageInfo<StatementDto>> result = scriptStatementController.scriptStatementList(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("list.success", result.getMessage());
        assertEquals(emptyPageInfo, result.getData());
        assertEquals(0, result.getData().getTotal());
        assertTrue(result.getData().getList().isEmpty());
        
        // 验证service方法被调用
        verify(scriptStatementService, times(1)).selectScriptStatementPage(any(StatementDto.class), eq(1), eq(10));
    }

    @Test
    @DisplayName("查询脚本报表列表-空查询参数")
    void scriptStatementList_nullQueryParam() {
        // 设置空查询参数
        tableQueryDto.setQueryParam(null);

        // Mock service方法
        doReturn(pageInfo).when(scriptStatementService).selectScriptStatementPage(any(), eq(1), eq(10));

        // 执行测试方法
        R<PageInfo<StatementDto>> result = scriptStatementController.scriptStatementList(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("list.success", result.getMessage());
        assertEquals(pageInfo, result.getData());
        
        // 验证service方法被调用
        verify(scriptStatementService, times(1)).selectScriptStatementPage(any(), eq(1), eq(10));
    }

    @Test
    @DisplayName("导出Excel-成功")
    void exportExcel_success() {
        // 准备测试数据
        List<Long> ids = Arrays.asList(1L, 2L, 3L);

        // Mock service方法（void方法不需要返回值）
        doNothing().when(scriptStatementService).exportExcel(ids, response);

        // 执行测试方法
        assertDoesNotThrow(() -> {
            scriptStatementController.exportExcel(ids, response);
        });

        // 验证service方法被调用
        verify(scriptStatementService, times(1)).exportExcel(ids, response);
    }

    @Test
    @DisplayName("导出Excel-空ID列表")
    void exportExcel_emptyIds() {
        // 准备测试数据
        List<Long> emptyIds = Collections.emptyList();

        // Mock service方法
        doNothing().when(scriptStatementService).exportExcel(emptyIds, response);

        // 执行测试方法
        assertDoesNotThrow(() -> {
            scriptStatementController.exportExcel(emptyIds, response);
        });

        // 验证service方法被调用
        verify(scriptStatementService, times(1)).exportExcel(emptyIds, response);
    }

    @Test
    @DisplayName("导出Excel-service抛出异常")
    void exportExcel_serviceException() {
        // 准备测试数据
        List<Long> ids = Arrays.asList(1L, 2L, 3L);

        // Mock service方法抛出异常
        doThrow(new RuntimeException("Export failed")).when(scriptStatementService).exportExcel(ids, response);

        // 执行测试方法，应该不抛出异常（因为controller捕获了异常）
        assertDoesNotThrow(() -> {
            scriptStatementController.exportExcel(ids, response);
        });

        // 验证service方法被调用
        verify(scriptStatementService, times(1)).exportExcel(ids, response);
    }

    @Test
    @DisplayName("查询脚本报表列表-service抛出异常")
    void scriptStatementList_serviceException() {
        // Mock service方法抛出异常
        doThrow(new RuntimeException("Database connection failed")).when(scriptStatementService)
                .selectScriptStatementPage(any(StatementDto.class), eq(1), eq(10));

        // 执行测试方法并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            scriptStatementController.scriptStatementList(tableQueryDto);
        });

        // 验证异常信息
        assertEquals("Database connection failed", exception.getMessage());
        
        // 验证service方法被调用
        verify(scriptStatementService, times(1)).selectScriptStatementPage(any(StatementDto.class), eq(1), eq(10));
    }

    @Test
    @DisplayName("查询脚本报表列表-分页参数测试")
    void scriptStatementList_paginationTest() {
        // 设置不同的分页参数
        tableQueryDto.setPageNum(2);
        tableQueryDto.setPageSize(20);

        // 创建对应的分页结果
        PageInfo<StatementDto> customPageInfo = new PageInfo<>();
        customPageInfo.setList(Arrays.asList(statementDto));
        customPageInfo.setTotal(1);
        customPageInfo.setPageNum(2);
        customPageInfo.setPageSize(20);

        // Mock service方法
        doReturn(customPageInfo).when(scriptStatementService).selectScriptStatementPage(any(StatementDto.class), eq(2), eq(20));

        // 执行测试方法
        R<PageInfo<StatementDto>> result = scriptStatementController.scriptStatementList(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("list.success", result.getMessage());
        assertEquals(customPageInfo, result.getData());
        assertEquals(2, result.getData().getPageNum());
        assertEquals(20, result.getData().getPageSize());
        
        // 验证service方法被调用
        verify(scriptStatementService, times(1)).selectScriptStatementPage(any(StatementDto.class), eq(2), eq(20));
    }
}
