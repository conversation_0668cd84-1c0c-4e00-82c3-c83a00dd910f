package com.ideal.script.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ideal.script.dto.*;
import com.ideal.script.mapper.InfoMapper;
import com.ideal.script.mapper.MyScriptMapper;
import com.ideal.script.model.bean.ScriptInfoQueryBean;
import com.ideal.script.model.bean.TaskApplyBean;
import com.ideal.script.model.entity.Category;
import com.ideal.script.model.entity.Info;
import com.ideal.script.model.entity.Parameter;
import com.ideal.script.service.ICategoryService;
import com.ideal.script.service.IInfoVersionService;
import com.ideal.script.service.IInfoVersionTextService;
import com.ideal.script.service.IParameterService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import org.junit.jupiter.api.DisplayName;

@ExtendWith(MockitoExtension.class)
class InfoServiceImplTest {

    @Mock
    private InfoMapper mockInfoMapper;
    @Mock
    private IInfoVersionService mockInfoVersionService;
    @Mock
    private IInfoVersionTextService mockInfoVersionTextService;
    @Mock
    private ICategoryService mockCategoryService;
    @Mock
    private  IParameterService mockParameterService;
    @Mock
    private  MyScriptMapper mockMyScriptMapper;

    @InjectMocks
    private InfoServiceImpl infoServiceImplUnderTest;


    @Test
    void testSelectInfoById() {
        // Setup
        // Configure InfoMapper.selectInfoById(...).
        final Info info = new Info();
        info.setId(1L);
        info.setUniqueUuid("uniqueUuid");
        info.setScriptNameZh("scriptNameZh");
        info.setScriptName("scriptName");
        info.setScriptType("scriptType");
        when(mockInfoMapper.selectInfoById(1L)).thenReturn(info);

        // Run the test
        final ScriptInfoDto result = infoServiceImplUnderTest.selectInfoById(1L);
        assertNotNull(result);
        System.out.println(result);
        // Verify the results
        Mockito.verify(mockInfoMapper, times(1)).selectInfoById(1L);
    }

    @Test
    void testSelectInfoByUniqueUuid() {
        // Setup
        // Configure InfoMapper.selectInfoById(...).
        final Info info = new Info();
        info.setId(1L);
        info.setUniqueUuid("uniqueUuid");
        info.setScriptNameZh("scriptNameZh");
        info.setScriptName("scriptName");
        info.setScriptType("scriptType");
        when(mockInfoMapper.selectInfoByUniqueUuid("uniqueUuid")).thenReturn(info);

        // Run the test
        final ScriptInfoDto result = infoServiceImplUnderTest.selectInfoByUniqueUuid("uniqueUuid");
        assertNotNull(result);
        System.out.println(result);
        // Verify the results
        Mockito.verify(mockInfoMapper, times(1)).selectInfoByUniqueUuid("uniqueUuid");
    }

    @Test
    void testSelectInfoList() {
        // Setup
        final ScriptInfoDto infoDto = new ScriptInfoDto();
        infoDto.setId(1L);
        infoDto.setUniqueUuid("uniqueUuid");
        infoDto.setScriptNameZh("scriptNameZh");
        infoDto.setScriptName("scriptName");
        infoDto.setScriptType("scriptType");

        // Configure InfoMapper.selectInfoList(...).
        final Info info = new Info();
        info.setId(1L);
        info.setUniqueUuid("uniqueUuid");
        info.setScriptNameZh("scriptNameZh");
        info.setScriptName("scriptName");
        info.setScriptType("scriptType");
        Page<Info> page = new Page<>();
        page.add(info);
        when(mockInfoMapper.selectInfoList(any(Info.class))).thenReturn(page);

        // Run the test
        final PageInfo<ScriptInfoDto> result = infoServiceImplUnderTest.selectInfoList(infoDto, 0, 50);
        assertNotNull(result);
        System.out.println(result);
        // Verify the results
        Mockito.verify(mockInfoMapper, times(1)).selectInfoList(any(Info.class));
    }


    @Test
    void testSelectInfoListApi() {
        // Setup
        // Configure InfoMapper.selectInfoListApi(...).
        final Info info = new Info();
        info.setId(1L);
        info.setUniqueUuid("uniqueUuid");
        info.setScriptNameZh("scriptNameZh");
        info.setScriptName("scriptName");
        info.setScriptType("scriptType");
        Page<Info> page = new Page<>();
        page.add(info);
        when(mockInfoMapper.selectInfoListApi(1L, "scriptName", Collections.singletonList(1L))).thenReturn(page);

        // Run the test
        final List<ScriptInfoDto> result = infoServiceImplUnderTest.selectInfoListApi(1L, "scriptName", Collections.singletonList(1L));
        assertNotNull(result);
        System.out.println(result);
        // Verify the results
        Mockito.verify(mockInfoMapper, times(1)).selectInfoListApi(1L, "scriptName", Collections.singletonList(1L));
    }


    @Test
    void testInsertInfo() {
        // Setup
        final ScriptInfoDto infoDto = new ScriptInfoDto();
        infoDto.setId(1L);
        infoDto.setUniqueUuid("uniqueUuid");
        infoDto.setScriptNameZh("scriptNameZh");
        infoDto.setScriptName("scriptName");
        infoDto.setScriptType("scriptType");

        // Run the test
        infoServiceImplUnderTest.insertInfo(infoDto);

        // Verify the results
        verify(mockInfoMapper).insertInfo(any(Info.class));
    }

    @Test
    void testUpdateInfo() {
        // Setup
        final ScriptInfoDto infoDto = new ScriptInfoDto();
        infoDto.setId(1L);
        infoDto.setUniqueUuid("uniqueUuid");
        infoDto.setScriptNameZh("scriptNameZh");
        infoDto.setScriptName("scriptName");
        infoDto.setScriptType("scriptType");

        // Run the test
        infoServiceImplUnderTest.updateInfo(infoDto);

        // Verify the results
        verify(mockInfoMapper).updateInfo(any(Info.class));
    }

    @Test
    void testDeleteInfoByIds() {
        // Setup
        // Run the test
        infoServiceImplUnderTest.deleteInfoByIds(new Long[]{1L, 2L});

        // Verify the results
        verify(mockInfoMapper).deleteInfoByIds(any(Long[].class));
    }

    @Test
    void testDeleteInfoById() {
        // Setup
        when(mockInfoMapper.deleteInfoById(1L)).thenReturn(1);

        // Run the test
        final int result = infoServiceImplUnderTest.deleteInfoById(1L);

        // Verify the results
        assertThat(result).isEqualTo(1);
    }


    @Test
    void validScriptNameZhCountExist() {

        final boolean result = infoServiceImplUnderTest.validScriptNameZhCountExist("中文名");
        verify(mockInfoMapper).validScriptNameZhCountExist(anyString());
    }





    @Test
    void getCategoryTreeApi() {
        // 创建测试数据
        CategoryQueryDto categoryQueryDto = new CategoryQueryDto();
        categoryQueryDto.setQueryChildren(true);


        Category category = new Category();
        category.setLevel(1);

        List<Category> categoryList = new ArrayList<>();
        // 添加测试数据到列表中

        // 设置模拟行为
        when(mockCategoryService.getCategoryWithChildren(any(Category.class), any(Boolean.class))).thenReturn(categoryList);

        // 调用被测试的方法
        List<CategoryApiDto> result = infoServiceImplUnderTest.getCategoryTreeApi(categoryQueryDto);

        // 验证行为
        verify(mockCategoryService, times(1)).getCategoryWithChildren(any(Category.class), any(Boolean.class));

        // 验证返回值
        assertEquals(categoryList.size(), result.size());
        // 可以进一步验证返回的每个 CategoryApiDto 是否符合预期
    }

    @Test
    void getScriptInfoListApi() {
        // 创建测试数据
        ScriptInfoQueryDto scriptInfoQueryDto = new ScriptInfoQueryDto();
        // 添加适当的测试数据到 scriptInfoQueryDto 中

        List<TaskApplyBean> infoVersionList = new ArrayList<>();
        TaskApplyBean taskApplyBean = new TaskApplyBean();
        taskApplyBean.setSrcScriptUuid("SrcScriptUuid");
        taskApplyBean.setCategoryId(1L);
        taskApplyBean.setUniqueUuid("uniqueUuid");
        infoVersionList.add(taskApplyBean);
        // 添加适当的测试数据到 infoVersionList 中

        List<Parameter> parameterList =  new ArrayList<>();
        // 添加适当的测试数据到 scriptParamApiDtoList 中

        // 设置模拟行为
        when(mockInfoVersionService.getInfoVersionList(any(ScriptInfoQueryBean.class))).thenReturn(infoVersionList);
        when(mockParameterService.getParameterByUuid(anyString())).thenReturn(parameterList);

        // 调用被测试的方法
        List<ScriptInfoApiDto> result = infoServiceImplUnderTest.getScriptInfoListApi(scriptInfoQueryDto);

        // 验证行为
        verify(mockInfoVersionService, times(1)).getInfoVersionList(any(ScriptInfoQueryBean.class));
        verify(mockParameterService, times(infoVersionList.size())).getParameterByUuid(anyString());
    }


    @Test
    void getScriptInfoListApi_noCategoryId() {
        // 创建测试数据
        ScriptInfoQueryDto scriptInfoQueryDto = new ScriptInfoQueryDto();
        // 添加适当的测试数据到 scriptInfoQueryDto 中

        List<TaskApplyBean> infoVersionList = new ArrayList<>();
        TaskApplyBean taskApplyBean = new TaskApplyBean();
        taskApplyBean.setSrcScriptUuid("SrcScriptUuid");
        taskApplyBean.setUniqueUuid("uniqueUuid");
        infoVersionList.add(taskApplyBean);
        // 添加适当的测试数据到 infoVersionList 中


        List<Parameter> parameterList =  new ArrayList<>();
        // 添加适当的测试数据到 scriptParamApiDtoList 中

        // 设置模拟行为
        when(mockInfoVersionService.getInfoVersionList(any(ScriptInfoQueryBean.class))).thenReturn(infoVersionList);
        when(mockParameterService.getParameterByUuid(anyString())).thenReturn(parameterList);

        // 调用被测试的方法
        List<ScriptInfoApiDto> result = infoServiceImplUnderTest.getScriptInfoListApi(scriptInfoQueryDto);

        // 验证行为
        verify(mockInfoVersionService, times(1)).getInfoVersionList(any(ScriptInfoQueryBean.class));
        verify(mockParameterService, times(infoVersionList.size())).getParameterByUuid(anyString());
    }


    @Test
    void getScriptInfoPageListApi() {
        // 创建测试数据
        ScriptInfoQueryDto scriptInfoQueryDto = new ScriptInfoQueryDto();
        scriptInfoQueryDto.setPageNum(1);
        scriptInfoQueryDto.setPageSize(10);
        // 设置适当的测试数据到 scriptInfoQueryDto 中

        ScriptInfoQueryBean scriptInfoQueryBean = new ScriptInfoQueryBean();
        // 设置适当的模拟数据到 scriptInfoQueryBean 中

        List<TaskApplyBean> infoVersionList = new ArrayList<>();
        TaskApplyBean taskApplyBean = new TaskApplyBean();
        taskApplyBean.setScriptName("scripName");
        taskApplyBean.setScriptInfoId(1L);
        taskApplyBean.setSrcScriptUuid("scrUuid");
        taskApplyBean.setUniqueUuid("uniqueUuid");
        Page<TaskApplyBean> page  =  new Page();
        page.add(taskApplyBean);

        // 添加适当的测试数据到 infoVersionList 中

        PageInfo<ScriptInfoApiDto> pageInfo = new PageInfo<>();
        // 添加适当的测试数据到 pageInfo 中

        // 设置模拟行为
        when(mockInfoVersionService.getInfoVersionList(any(ScriptInfoQueryBean.class))).thenReturn(page);

        when(mockParameterService.getParameterByUuid(anyString())).thenReturn(new ArrayList<>());

        // 调用被测试的方法
        PageInfo<ScriptInfoApiDto> result = infoServiceImplUnderTest.getScriptInfoPageListApi(scriptInfoQueryDto);

        // 验证行为
        verify(mockCategoryService, times(infoVersionList.size())).getCategoryFullPath(anyLong());


        // 验证返回值
        // 进一步验证返回的结果是否符合预期

        //assertEquals(expectedResult, result);
    }

    @Test
    void getScriptInfoPageListApi_hasCategoryId() {
        // 创建测试数据
        ScriptInfoQueryDto scriptInfoQueryDto = new ScriptInfoQueryDto();
        scriptInfoQueryDto.setPageNum(1);
        scriptInfoQueryDto.setPageSize(10);
        // 设置适当的测试数据到 scriptInfoQueryDto 中

        ScriptInfoQueryBean scriptInfoQueryBean = new ScriptInfoQueryBean();
        // 设置适当的模拟数据到 scriptInfoQueryBean 中

        List<TaskApplyBean> infoVersionList = new ArrayList<>();
        TaskApplyBean taskApplyBean = new TaskApplyBean();
        taskApplyBean.setScriptName("scripName");
        taskApplyBean.setScriptInfoId(1L);
        taskApplyBean.setSrcScriptUuid("scrUuid");
        taskApplyBean.setUniqueUuid("uniqueUuid");
        taskApplyBean.setCategoryId(1L);
        Page<TaskApplyBean> page  =  new Page();
        page.add(taskApplyBean);

        // 添加适当的测试数据到 infoVersionList 中

        PageInfo<ScriptInfoApiDto> pageInfo = new PageInfo<>();
        // 添加适当的测试数据到 pageInfo 中

        // 设置模拟行为
        when(mockInfoVersionService.getInfoVersionList(any(ScriptInfoQueryBean.class))).thenReturn(page);

        when(mockParameterService.getParameterByUuid(anyString())).thenReturn(new ArrayList<>());

        // 调用被测试的方法
        PageInfo<ScriptInfoApiDto> result = infoServiceImplUnderTest.getScriptInfoPageListApi(scriptInfoQueryDto);

        // 验证行为
        assertNotNull(result);

    }

    @Test
    @DisplayName("测试获取脚本分类图标列表-正常流程")
    void testGetScriptCategoryIconList_Normal() {
        // Setup - 创建测试数据
        List<String> srcScriptUuids = Arrays.asList("uuid1", "uuid2", "uuid3");

        // Mock InfoMapper返回的脚本分类图标数据
        List<ScriptCategoryIconDto> mockScriptCategoryIconList = new ArrayList<>();

        ScriptCategoryIconDto dto1 = new ScriptCategoryIconDto();
        dto1.setScriptSrcUuid("uuid1");
        dto1.setCategoryPath("系统管理/用户管理");
        mockScriptCategoryIconList.add(dto1);

        ScriptCategoryIconDto dto2 = new ScriptCategoryIconDto();
        dto2.setScriptSrcUuid("uuid2");
        dto2.setCategoryPath("数据库/MySQL");
        mockScriptCategoryIconList.add(dto2);

        ScriptCategoryIconDto dto3 = new ScriptCategoryIconDto();
        dto3.setScriptSrcUuid("uuid3");
        dto3.setCategoryPath("系统管理/权限管理");
        mockScriptCategoryIconList.add(dto3);

        // Mock CategoryService返回的分类数据
        List<Category> mockCategoryList = new ArrayList<>();

        Category category1 = new Category();
        category1.setName("系统管理");
        category1.setIcon("system-icon");
        mockCategoryList.add(category1);

        Category category2 = new Category();
        category2.setName("数据库");
        category2.setIcon("database-icon");
        mockCategoryList.add(category2);

        // 设置Mock行为
        when(mockInfoMapper.getScriptCategoryIconList(srcScriptUuids)).thenReturn(mockScriptCategoryIconList);
        when(mockCategoryService.getFirstCategoryByCategoryNameList(Arrays.asList("系统管理", "数据库")))
                .thenReturn(mockCategoryList);

        // 执行测试
        List<ScriptCategoryIconDto> result = infoServiceImplUnderTest.getScriptCategoryIconList(srcScriptUuids);

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size());

        // 验证图标设置正确
        assertEquals("system-icon", result.get(0).getIcon());
        assertEquals("database-icon", result.get(1).getIcon());
        assertEquals("system-icon", result.get(2).getIcon());

        // 验证方法调用
        verify(mockInfoMapper, times(1)).getScriptCategoryIconList(srcScriptUuids);
        verify(mockCategoryService, times(1)).getFirstCategoryByCategoryNameList(Arrays.asList("系统管理", "数据库"));
    }

    @Test
    @DisplayName("测试获取脚本分类图标列表-空输入参数")
    void testGetScriptCategoryIconList_EmptyInput() {
        // Setup - 空输入参数
        List<String> srcScriptUuids = new ArrayList<>();

        // 执行测试
        List<ScriptCategoryIconDto> result = infoServiceImplUnderTest.getScriptCategoryIconList(srcScriptUuids);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证没有调用Mapper方法
        verify(mockInfoMapper, never()).getScriptCategoryIconList(any());
        verify(mockCategoryService, never()).getFirstCategoryByCategoryNameList(any());
    }

    @Test
    @DisplayName("测试获取脚本分类图标列表-null输入参数")
    void testGetScriptCategoryIconList_NullInput() {
        // Setup - null输入参数
        List<String> srcScriptUuids = null;

        // 执行测试
        List<ScriptCategoryIconDto> result = infoServiceImplUnderTest.getScriptCategoryIconList(srcScriptUuids);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证没有调用Mapper方法
        verify(mockInfoMapper, never()).getScriptCategoryIconList(any());
        verify(mockCategoryService, never()).getFirstCategoryByCategoryNameList(any());
    }

    @Test
    @DisplayName("测试获取脚本分类图标列表-Mapper返回空结果")
    void testGetScriptCategoryIconList_EmptyMapperResult() {
        // Setup - Mapper返回空结果
        List<String> srcScriptUuids = Arrays.asList("uuid1", "uuid2");
        List<ScriptCategoryIconDto> emptyList = new ArrayList<>();

        // 设置Mock行为
        when(mockInfoMapper.getScriptCategoryIconList(srcScriptUuids)).thenReturn(emptyList);

        // 执行测试
        List<ScriptCategoryIconDto> result = infoServiceImplUnderTest.getScriptCategoryIconList(srcScriptUuids);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证方法调用
        verify(mockInfoMapper, times(1)).getScriptCategoryIconList(srcScriptUuids);
        verify(mockCategoryService, never()).getFirstCategoryByCategoryNameList(any());
    }

    @Test
    @DisplayName("测试获取脚本分类图标列表-Mapper返回null结果")
    void testGetScriptCategoryIconList_NullMapperResult() {
        // Setup - Mapper返回null结果
        List<String> srcScriptUuids = Arrays.asList("uuid1", "uuid2");

        // 设置Mock行为
        when(mockInfoMapper.getScriptCategoryIconList(srcScriptUuids)).thenReturn(null);

        // 执行测试
        List<ScriptCategoryIconDto> result = infoServiceImplUnderTest.getScriptCategoryIconList(srcScriptUuids);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证方法调用
        verify(mockInfoMapper, times(1)).getScriptCategoryIconList(srcScriptUuids);
        verify(mockCategoryService, never()).getFirstCategoryByCategoryNameList(any());
    }

}
