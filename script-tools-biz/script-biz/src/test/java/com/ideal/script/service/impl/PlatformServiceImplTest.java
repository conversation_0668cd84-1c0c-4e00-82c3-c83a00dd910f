package com.ideal.script.service.impl;

import com.ideal.script.mapper.PlatformMapper;
import com.ideal.script.model.dto.PlatformDto;
import com.ideal.script.model.entity.Platform;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PlatformServiceImplTest {

    @Mock
    private PlatformMapper mockPlatformMapper;

    @InjectMocks
    private PlatformServiceImpl platformServiceImplUnderTest;


    @Test
    void testGetScriptPlatformCode() {
        // Setup
        // Configure PlatformMapper.selectPlatformList(...).
        final Platform platform = new Platform();
        platform.setId(1L);
        platform.setName("name");
        platform.setCodevalue("codevalue");
        final List<Platform> platforms = Collections.singletonList(platform);
        when(mockPlatformMapper.selectPlatformList()).thenReturn(platforms);

        // Run the test
        final List<PlatformDto> result = platformServiceImplUnderTest.getScriptPlatformCode();

        assertNotNull(result);
        System.out.println(result);
        // Verify the results
        Mockito.verify(mockPlatformMapper,times(1)).selectPlatformList();
    }
}
