package com.ideal.script.service.impl.resulthandler;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.TaskRuntimeStdoutMapper;
import com.ideal.script.model.dto.TaskInstanceDto;
import com.ideal.script.model.dto.TaskRuntimeDto;
import com.ideal.script.service.ITaskInstanceService;
import com.ideal.script.service.ITaskRuntimeService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;

import java.time.Duration;
import java.util.Collections;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ScriptResultHandlerServiceImplTest {

    @Mock
    private RedissonClient mockRedissonClient;
    @Mock
    private ITaskRuntimeService mockTaskRuntimeService;
    @Mock
    private ITaskInstanceService mockTaskInstanceService;
    @Mock
    private TaskRuntimeStdoutMapper taskRuntimeStdoutMapper;
    // 不使用@Mock，避免Java 21环境下的Mockito兼容性问题
    private ObjectMapper objectMapper;

    private ScriptResultHandlerServiceImpl scriptResultHandlerServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        // 使用真实的ObjectMapper实例，避免mock问题
        objectMapper = new ObjectMapper();
        scriptResultHandlerServiceImplUnderTest = new ScriptResultHandlerServiceImpl(mockRedissonClient,
                mockTaskRuntimeService, mockTaskInstanceService, objectMapper);
    }

    @ParameterizedTest
    @ValueSource(strings = {"agent-script-retry-","agent-script-kill"})
    void testHandleScriptExecuteResult(String bizId) throws Exception {
        // Setup
        final TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setAgentTaskId(0L);
        taskRuntimeDto.setId(0L);
        taskRuntimeDto.setTaskInstanceId(0L);
        taskRuntimeDto.setState(10);
        taskRuntimeDto.setBizId(bizId);

        when(mockTaskRuntimeService.selectTaskRuntimeById(any())).thenReturn(taskRuntimeDto);

        // 使用真实的JSON数据，避免mock JsonNode
        String jsonMessage = "{\"scriptId\":\"123456\",\"stdout\":\"dGVzdCBvdXRwdXQ=\",\"lastLine\":\"bGFzdCBsaW5l\",\"stderr\":\"\",\"transcoding\":\"Completed\",\"status\":\"20\"}";

        @SuppressWarnings("unchecked")
        RBucket<Object> mockRBucket = mock(RBucket.class);
        when(mockRedissonClient.getBucket(anyString())).thenReturn(mockRBucket);
        when(mockRBucket.setIfAbsent("value", Duration.ofMinutes(1))).thenReturn(true);

        TaskRuntimeDto taskRuntimeValDto = new TaskRuntimeDto();
        taskRuntimeValDto.setStartType(0);
        taskRuntimeValDto.setAgentPort(15000);
        taskRuntimeValDto.setAgentIp("127.0.0.1");
        taskRuntimeValDto.setScriptTaskId(1L);
        taskRuntimeValDto.setBizId(bizId);
        taskRuntimeValDto.setState(1);
        when(mockTaskRuntimeService.selectTaskRuntimeById(any())).thenReturn(taskRuntimeValDto);

        // Run the test - 使用真实的ObjectMapper处理JSON
        assertDoesNotThrow(() -> scriptResultHandlerServiceImplUnderTest.handleScriptExecuteResult(Collections.singletonList(jsonMessage)));
    }


    @Test
    void testHandleScriptExecuteResult_exception() throws Exception {
        // Setup
        String jsonMessage = "{\"scriptId\":\"123456\",\"stdout\":\"dGVzdCBvdXRwdXQ=\",\"lastLine\":\"bGFzdCBsaW5l\",\"stderr\":\"\",\"transcoding\":\"Completed\",\"status\":\"20\"}";

        // 让 selectTaskRuntimeById 返回一个 bizId 为空的对象以触发异常
        TaskRuntimeDto emptyBizIdDto = new TaskRuntimeDto();
        emptyBizIdDto.setBizId("");
        when(mockTaskRuntimeService.selectTaskRuntimeById(123456L)).thenReturn(emptyBizIdDto);

        // Run the test - 使用真实的ObjectMapper处理JSON
        assertThatThrownBy(() -> scriptResultHandlerServiceImplUnderTest.handleScriptExecuteResult(Collections.singletonList(jsonMessage)))
                .isInstanceOf(ScriptException.class);
    }


    @Test
    void testValidateScriptResultJson_ThrowsScriptException() {
        // Setup - 测试无效JSON
        // Run the test
        assertThatThrownBy(
                () -> scriptResultHandlerServiceImplUnderTest.validateScriptResultJson("invalid json"))
                .isInstanceOf(ScriptException.class);
    }

    @Test
    void testHandleScriptSendResult() throws Exception {
        // Setup - 使用真实的JSON数据
        String validJson = "{\"bizId\": \"123-456\", \"agentIp\": \"127.0.0.1\", \"agentPort\": 8080, \"taskId\": \"789012\", \"send\": true}";

        // Mock RedissonClient的getBucket方法
        @SuppressWarnings("unchecked")
        RBucket<Object> mockRBucket = mock(RBucket.class);
        when(mockRedissonClient.getBucket(anyString())).thenReturn(mockRBucket);
        when(mockRBucket.setIfAbsent("value", Duration.ofMinutes(1))).thenReturn(true);

        // Mock TaskInstanceService的getTaskInstanceByRuntimeId方法
        TaskInstanceDto taskInstanceDto = new TaskInstanceDto();
        taskInstanceDto.setStartType(0); // 设置为定时任务启动类型
        when(mockTaskInstanceService.getTaskInstanceByRuntimeId(anyLong())).thenReturn(taskInstanceDto);

        // Run the test - 使用真实的ObjectMapper处理JSON
        assertDoesNotThrow(() -> scriptResultHandlerServiceImplUnderTest.handleScriptSendResult(validJson));
    }

    @Test
    void testValidateScriptSendResultJson() throws Exception {
        // Setup - 使用真实的JSON数据
        String validJson = "{\"bizId\": \"123456\", \"agentIp\": \"127.0.0.1\", \"agentPort\": 8080, \"taskId\": \"789012\", \"send\": true}";

        // Run the test - 使用真实的ObjectMapper处理JSON
        assertDoesNotThrow(() -> scriptResultHandlerServiceImplUnderTest.validateScriptSendResultJson(validJson));
    }

    @Test
    void testValidateScriptSendResultJson_ThrowsScriptException() {
        // Setup - 缺少bizId的JSON
        String invalidJson = "{\"agentIp\": \"127.0.0.1\", \"agentPort\": 8080, \"taskId\": \"789012\", \"send\": true}";

        // Run the test
        assertThrows(ScriptException.class, () -> {
            scriptResultHandlerServiceImplUnderTest.validateScriptSendResultJson(invalidJson);
        });
    }

    @Test
    void testBizIdValid() throws Exception {
        // Setup
        // Run the test
        assertDoesNotThrow(() -> scriptResultHandlerServiceImplUnderTest.bizIdValid("bizId"));
    }

    @Test
    void testBizIdValid_ThrowsScriptException() {
        // Setup
        // Run the test
        assertThatThrownBy(() -> scriptResultHandlerServiceImplUnderTest.bizIdValid(""))
                .isInstanceOf(ScriptException.class);
    }

    @Test
    void testHandleScriptErrorResult() throws Exception {
        // Setup
        @SuppressWarnings("unchecked")
        RBucket<Object> mockRBucket = mock(RBucket.class);
        when(mockRedissonClient.getBucket(anyString())).thenReturn(mockRBucket);
        when(mockRBucket.setIfAbsent("value", Duration.ofMinutes(1))).thenReturn(true);

        // Run the test
        assertDoesNotThrow(() -> scriptResultHandlerServiceImplUnderTest.handleScriptErrorResult("message", "1-1", 0L));
    }

}
