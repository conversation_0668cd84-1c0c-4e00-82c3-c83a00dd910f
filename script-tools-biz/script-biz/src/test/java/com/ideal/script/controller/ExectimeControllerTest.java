package com.ideal.script.controller;

import com.ideal.common.dto.R;
import com.ideal.script.model.dto.ExectimeDto;
import com.ideal.script.service.IExectimeService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Timestamp;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * ExectimeController单元测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class ExectimeControllerTest {

    @Mock
    private IExectimeService exectimeService;

    @InjectMocks
    private ExectimeController exectimeController;

    private ExectimeDto exectimeDto;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        exectimeDto = new ExectimeDto();
        exectimeDto.setId(1L);
        exectimeDto.setSrcScriptUuid("test-uuid-123");
        exectimeDto.setSuccessTimes(100L);
        exectimeDto.setTotalTimes(120L);
        exectimeDto.setTaskCount(50L);
        exectimeDto.setSuccessRate("83.33%");
        exectimeDto.setCreateTime(new Timestamp(System.currentTimeMillis()));
    }

    @Test
    @DisplayName("测试查询脚本使用次数、成功数、总数、成功率 - 正常情况")
    void getTotalAndSuccessRate_success() {
        // 准备数据
        String srcScriptUuid = "test-uuid-123";

        // Mock方法
        when(exectimeService.getTotalAndSuccessRate(srcScriptUuid)).thenReturn(exectimeDto);

        // 执行测试
        R<ExectimeDto> result = exectimeController.getTotalAndSuccessRate(srcScriptUuid);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals(exectimeDto, result.getData());
        assertEquals(null, result.getMessage()); // R.ok(data)不设置message
        verify(exectimeService, times(1)).getTotalAndSuccessRate(srcScriptUuid);
    }

    @Test
    @DisplayName("测试查询脚本使用次数、成功数、总数、成功率 - 返回null")
    void getTotalAndSuccessRate_returnNull() {
        // 准备数据
        String srcScriptUuid = "non-existent-uuid";

        // Mock方法 - 返回null
        when(exectimeService.getTotalAndSuccessRate(srcScriptUuid)).thenReturn(null);

        // 执行测试
        R<ExectimeDto> result = exectimeController.getTotalAndSuccessRate(srcScriptUuid);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals(null, result.getData());
        assertEquals(null, result.getMessage()); // R.ok(data)不设置message
        verify(exectimeService, times(1)).getTotalAndSuccessRate(srcScriptUuid);
    }

    @Test
    @DisplayName("测试查询脚本使用次数、成功数、总数、成功率 - 异常情况")
    void getTotalAndSuccessRate_exception() {
        // 准备数据
        String srcScriptUuid = "error-uuid";
        String errorMessage = "数据库连接异常";

        // Mock方法抛出异常
        when(exectimeService.getTotalAndSuccessRate(srcScriptUuid))
                .thenThrow(new RuntimeException(errorMessage));

        // 执行测试
        R<ExectimeDto> result = exectimeController.getTotalAndSuccessRate(srcScriptUuid);

        // 验证结果
        assertNotNull(result);
        assertEquals("00000", result.getCode());
        assertEquals("操作失败", result.getMessage()); // R.fail()设置message为"操作失败"
        verify(exectimeService, times(1)).getTotalAndSuccessRate(srcScriptUuid);
    }

    @ParameterizedTest
    @ValueSource(strings = {"uuid-1", "uuid-2", "uuid-3", "", "test-script-uuid"})
    @DisplayName("测试查询脚本使用次数、成功数、总数、成功率 - 参数化测试")
    void getTotalAndSuccessRate_parameterized(String srcScriptUuid) {
        // Mock方法
        when(exectimeService.getTotalAndSuccessRate(anyString())).thenReturn(exectimeDto);

        // 执行测试
        R<ExectimeDto> result = exectimeController.getTotalAndSuccessRate(srcScriptUuid);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals(exectimeDto, result.getData());
        assertEquals(null, result.getMessage()); // R.ok(data)不设置message
        verify(exectimeService, times(1)).getTotalAndSuccessRate(srcScriptUuid);
    }

    @ParameterizedTest
    @MethodSource("provideExectimeDtoData")
    @DisplayName("测试查询脚本使用次数、成功数、总数、成功率 - 不同数据场景")
    void getTotalAndSuccessRate_differentData(String srcScriptUuid, ExectimeDto expectedDto) {
        // Mock方法
        when(exectimeService.getTotalAndSuccessRate(anyString())).thenReturn(expectedDto);

        // 执行测试
        R<ExectimeDto> result = exectimeController.getTotalAndSuccessRate(srcScriptUuid);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals(expectedDto, result.getData());
        assertEquals(null, result.getMessage()); // R.ok(data)不设置message
        verify(exectimeService, times(1)).getTotalAndSuccessRate(srcScriptUuid);
    }

    static Stream<Arguments> provideExectimeDtoData() {
        // 场景1：正常数据
        ExectimeDto dto1 = new ExectimeDto();
        dto1.setId(1L);
        dto1.setSrcScriptUuid("uuid-1");
        dto1.setSuccessTimes(80L);
        dto1.setTotalTimes(100L);
        dto1.setTaskCount(30L);
        dto1.setSuccessRate("80.00%");

        // 场景2：零成功率
        ExectimeDto dto2 = new ExectimeDto();
        dto2.setId(2L);
        dto2.setSrcScriptUuid("uuid-2");
        dto2.setSuccessTimes(0L);
        dto2.setTotalTimes(10L);
        dto2.setTaskCount(5L);
        dto2.setSuccessRate("0.00%");

        // 场景3：100%成功率
        ExectimeDto dto3 = new ExectimeDto();
        dto3.setId(3L);
        dto3.setSrcScriptUuid("uuid-3");
        dto3.setSuccessTimes(50L);
        dto3.setTotalTimes(50L);
        dto3.setTaskCount(20L);
        dto3.setSuccessRate("100.00%");

        // 场景4：新脚本无执行记录
        ExectimeDto dto4 = new ExectimeDto();
        dto4.setId(4L);
        dto4.setSrcScriptUuid("uuid-4");
        dto4.setSuccessTimes(0L);
        dto4.setTotalTimes(0L);
        dto4.setTaskCount(0L);
        dto4.setSuccessRate("0.00%");

        return Stream.of(
                Arguments.of("uuid-1", dto1),
                Arguments.of("uuid-2", dto2),
                Arguments.of("uuid-3", dto3),
                Arguments.of("uuid-4", dto4),
                Arguments.of("null-uuid", null)
        );
    }

    @Test
    @DisplayName("测试查询脚本使用次数、成功数、总数、成功率 - 空字符串参数")
    void getTotalAndSuccessRate_emptyString() {
        // 准备数据
        String srcScriptUuid = "";

        // Mock方法
        when(exectimeService.getTotalAndSuccessRate(srcScriptUuid)).thenReturn(null);

        // 执行测试
        R<ExectimeDto> result = exectimeController.getTotalAndSuccessRate(srcScriptUuid);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals(null, result.getData());
        assertEquals(null, result.getMessage()); // R.ok(data)不设置message
        verify(exectimeService, times(1)).getTotalAndSuccessRate(srcScriptUuid);
    }

    @Test
    @DisplayName("测试查询脚本使用次数、成功数、总数、成功率 - 特殊字符参数")
    void getTotalAndSuccessRate_specialCharacters() {
        // 准备数据
        String srcScriptUuid = "uuid-with-special-chars-@#$%";

        // Mock方法
        when(exectimeService.getTotalAndSuccessRate(srcScriptUuid)).thenReturn(exectimeDto);

        // 执行测试
        R<ExectimeDto> result = exectimeController.getTotalAndSuccessRate(srcScriptUuid);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals(exectimeDto, result.getData());
        assertEquals(null, result.getMessage()); // R.ok(data)不设置message
        verify(exectimeService, times(1)).getTotalAndSuccessRate(srcScriptUuid);
    }
}
