package com.ideal.script.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.InfoVersionMapper;
import com.ideal.script.model.bean.TaskAuthorityBean;
import com.ideal.script.model.dto.TaskAuthorityDto;
import com.ideal.script.model.entity.InfoVersion;
import com.ideal.script.service.ICategoryService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TaskAuthorityServiceImplTest {

    @Mock
    private InfoVersionMapper mockInfoVersionMapper;
    @Mock
    private ICategoryService mockCategoryService;

    private TaskAuthorityServiceImpl taskAuthorityServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        taskAuthorityServiceImplUnderTest = new TaskAuthorityServiceImpl(mockInfoVersionMapper, mockCategoryService);
    }

    @Test
    void testSelectTaskAuthorityList() {
        // Setup
        final TaskAuthorityDto taskAuthorityDto = new TaskAuthorityDto();
        taskAuthorityDto.setScriptInfoId(0L);
        taskAuthorityDto.setUniqueUuid("uniqueUuid");
        taskAuthorityDto.setScriptNameZh("scriptNameZh");
        taskAuthorityDto.setScriptInfoVersionId(0L);
        taskAuthorityDto.setUseState(0);

        // Configure InfoVersionMapper.selectTaskAuthorityList(...).
        final TaskAuthorityBean taskAuthorityBean = new TaskAuthorityBean();
        taskAuthorityBean.setScriptInfoId(0L);
        taskAuthorityBean.setUniqueUuid("uniqueUuid");
        taskAuthorityBean.setScriptNameZh("scriptNameZh");
        taskAuthorityBean.setCategoryId(0L);
        taskAuthorityBean.setScriptCategoryName("scriptCategoryName");
        Page<TaskAuthorityBean> page = new Page<>();
        page.add(taskAuthorityBean);
        when(mockInfoVersionMapper.selectTaskAuthorityList(any(TaskAuthorityBean.class)))
                .thenReturn(page);

        when(mockCategoryService.getCategoryFullPath(0L)).thenReturn("scriptCategoryName");

        // Run the test
        final PageInfo<TaskAuthorityDto> result = taskAuthorityServiceImplUnderTest.selectTaskAuthorityList(
                taskAuthorityDto, 0, 0);
        assertNotNull(result);
        // Verify the results
    }



    @Test
    void testSelectTaskAuthorityList_noCategoryId() {
        // Setup
        final TaskAuthorityDto taskAuthorityDto = new TaskAuthorityDto();
        taskAuthorityDto.setScriptInfoId(0L);
        taskAuthorityDto.setUniqueUuid("uniqueUuid");
        taskAuthorityDto.setScriptNameZh("scriptNameZh");
        taskAuthorityDto.setScriptInfoVersionId(0L);
        taskAuthorityDto.setUseState(0);

        // Configure InfoVersionMapper.selectTaskAuthorityList(...).
        final TaskAuthorityBean taskAuthorityBean = new TaskAuthorityBean();
        taskAuthorityBean.setScriptInfoId(0L);
        taskAuthorityBean.setUniqueUuid("uniqueUuid");
        taskAuthorityBean.setScriptNameZh("scriptNameZh");
        taskAuthorityBean.setScriptCategoryName("scriptCategoryName");
        Page<TaskAuthorityBean> page = new Page<>();
        page.add(taskAuthorityBean);
        when(mockInfoVersionMapper.selectTaskAuthorityList(any(TaskAuthorityBean.class)))
                .thenReturn(page);
        // Run the test
        final PageInfo<TaskAuthorityDto> result = taskAuthorityServiceImplUnderTest.selectTaskAuthorityList(
                taskAuthorityDto, 0, 0);
        assertNotNull(result);
        // Verify the results
    }


    @Test
    void testSelectTaskAuthorityList_InfoVersionMapperReturnsNoItems() {
        // Setup
        final TaskAuthorityDto taskAuthorityDto = new TaskAuthorityDto();
        taskAuthorityDto.setScriptInfoId(0L);
        taskAuthorityDto.setUniqueUuid("uniqueUuid");
        taskAuthorityDto.setScriptNameZh("scriptNameZh");
        taskAuthorityDto.setScriptInfoVersionId(0L);
        taskAuthorityDto.setUseState(0);

        when(mockInfoVersionMapper.selectTaskAuthorityList(any(TaskAuthorityBean.class)))
                .thenReturn(new Page<>());

        // Run the test
        final PageInfo<TaskAuthorityDto> result = taskAuthorityServiceImplUnderTest.selectTaskAuthorityList(
                taskAuthorityDto, 0, 0);
        assertNotNull(result);
        // Verify the results
    }

    @ParameterizedTest
    @ValueSource(ints = {0,1})
    void testUpdateUseState(int state) throws Exception {
        // Setup
        final TaskAuthorityDto taskAuthorityDto = new TaskAuthorityDto();
        taskAuthorityDto.setScriptInfoId(0L);
        taskAuthorityDto.setUniqueUuid("uniqueUuid");
        taskAuthorityDto.setScriptNameZh("scriptNameZh");
        taskAuthorityDto.setScriptInfoVersionId(0L);
        taskAuthorityDto.setUseState(state);


        //返回的版本信息
        InfoVersion infoVersion = new InfoVersion();
        infoVersion.setIsDefault(1);
        infoVersion.setInfoUniqueUuid("uniqueUuid");



        when(mockInfoVersionMapper.selectInfoVersionById(0L)).thenReturn(infoVersion);
        if(state == 0) {
            when(mockInfoVersionMapper.getLastVersionByUuid("uniqueUuid")).thenReturn(1L);
        }else{
            when(mockInfoVersionMapper.getLastVersionByUuid("uniqueUuid")).thenReturn(null);

        }

        when(mockInfoVersionMapper.updateInfoVersion(any(InfoVersion.class))).thenReturn(0);

        // Run the test
        final boolean result = taskAuthorityServiceImplUnderTest.updateUseState(taskAuthorityDto);

        // Verify the results
        assertThat(result).isFalse();
    }


    @Test
    void testUpdateUseState_exception() throws Exception {
        // Setup
        final TaskAuthorityDto taskAuthorityDto = new TaskAuthorityDto();
        taskAuthorityDto.setScriptInfoId(0L);
        taskAuthorityDto.setUniqueUuid("uniqueUuid");
        taskAuthorityDto.setScriptNameZh("scriptNameZh");
        taskAuthorityDto.setScriptInfoVersionId(0L);
        taskAuthorityDto.setUseState(0);

//        when(mockInfoVersionMapper.updateInfoVersion(any(InfoVersion.class))).thenReturn(0);
        when(mockInfoVersionMapper.updateInfoVersion(any(InfoVersion.class)))
                .thenThrow(new RuntimeException("Simulated exception"));


        // Run the test
        assertThrows(Exception.class,()->{
            taskAuthorityServiceImplUnderTest.updateUseState(taskAuthorityDto);
        });


    }

    @Test
    void checkExistRunTask() throws ScriptException {
        when(mockInfoVersionMapper.checkExistRunTask(any(Long.class))).thenReturn(1);
        final boolean result = taskAuthorityServiceImplUnderTest.checkExistRunTask(1L);
        Assertions.assertTrue(result);
    }


    @Test
    void checkExistRunTask_exception() {
        when(mockInfoVersionMapper.checkExistRunTask(any(Long.class))).thenThrow(new RuntimeException("error.check.unfinished.task.failed"));
        assertThrows(Exception.class,()->{
            taskAuthorityServiceImplUnderTest.checkExistRunTask(1L);
        });

    }

    @ParameterizedTest
    @ValueSource(ints = {0,1})
    void batchUpdateUseState(int state) throws ScriptException {
        // Arrange
        TaskAuthorityDto taskAuthorityDto = new TaskAuthorityDto();
        taskAuthorityDto.setScriptInfoVersionIds(new Long[]{1L, 2L, 3L});
        taskAuthorityDto.setUseState(state);

        //返回的版本信息
        InfoVersion infoVersion = new InfoVersion();
        infoVersion.setIsDefault(1);
        infoVersion.setInfoUniqueUuid("uniqueUuid");

        //默认版本信息
        InfoVersion infoVersion1 = new InfoVersion();
        infoVersion1.setId(1L);
        infoVersion1.setInfoUniqueUuid("defaultUuid");



        when(mockInfoVersionMapper.selectInfoVersionById(1L)).thenReturn(infoVersion);
        if(state == 0) {
            when(mockInfoVersionMapper.selectDefaultInfoVersionByUuid("uniqueUuid")).thenReturn(infoVersion1);

            when(mockInfoVersionMapper.getLastVersionByUuid("uniqueUuid")).thenReturn(1L);
        }else{
            when(mockInfoVersionMapper.getLastVersionByUuid("uniqueUuid")).thenReturn(null);
            when(mockInfoVersionMapper.getLastInfoVersionByIds("uniqueUuid",new Long[]{1L, 2L, 3L})).thenReturn(infoVersion1);

        }

        when(mockInfoVersionMapper.batchUpdateInfoVersion(any())).thenReturn(3);

        // Act
        boolean result = taskAuthorityServiceImplUnderTest.batchUpdateUseState(taskAuthorityDto);

        // Assert
        assertThat(result).isTrue();
        verify(mockInfoVersionMapper).batchUpdateInfoVersion(any());
    }

    @ParameterizedTest
    @ValueSource(ints = {0,1})
    void batchUpdateUseState_scriptException(Integer type) {
        // Arrange
        TaskAuthorityDto taskAuthorityDto = new TaskAuthorityDto();
        taskAuthorityDto.setScriptInfoVersionIds(new Long[]{});
        taskAuthorityDto.setUseState(type);
        // Act
        assertThrows(ScriptException.class,()->{
            taskAuthorityServiceImplUnderTest.batchUpdateUseState(taskAuthorityDto);
        });

    }


    @Test
    void batchUpdateUseState_Exception() {
        // Arrange
        TaskAuthorityDto taskAuthorityDto = new TaskAuthorityDto();
        taskAuthorityDto.setScriptInfoVersionIds(new Long[]{1L, 2L, 3L});
        taskAuthorityDto.setUseState(0);

        when(mockInfoVersionMapper.batchUpdateInfoVersion(any())).thenThrow(new RuntimeException("批量修改任务权限失败"));

        // Act
        assertThrows(ScriptException.class,()->{
            taskAuthorityServiceImplUnderTest.batchUpdateUseState(taskAuthorityDto);
        });
    }
}
