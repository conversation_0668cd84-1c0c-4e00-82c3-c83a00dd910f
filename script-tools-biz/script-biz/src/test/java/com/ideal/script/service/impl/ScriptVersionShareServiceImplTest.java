package com.ideal.script.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ideal.common.util.PageDataUtil;
import com.ideal.script.mapper.ScriptVersionShareMapper;
import com.ideal.script.model.dto.ScriptVersionShareDto;
import com.ideal.script.model.dto.interaction.agent.UserInfoDto;
import com.ideal.script.model.entity.ScriptVersionShare;
import com.ideal.script.service.impl.builders.MyScriptServiceScripts;
import com.ideal.script.service.impl.builders.MyScriptServiceScriptsBuilder;
import com.ideal.system.api.IOrgManagement;
import com.ideal.system.api.IRole;
import com.ideal.system.api.IUserInfo;
import com.ideal.system.dto.OrgManagementApiDto;
import com.ideal.system.dto.RoleApiDto;
import com.ideal.system.dto.UserInfoApiDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import java.util.function.Consumer;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class ScriptVersionShareServiceImplTest {

    @Mock
    private ScriptVersionShareMapper scriptVersionShareMapper;

    @Mock
    private IUserInfo iUserInfoApi;

    @Mock
    private IOrgManagement orgManagementApi;

    @InjectMocks
    private ScriptVersionShareServiceImpl scriptVersionShareService;

    @Mock
    private IRole iRole;
    @Mock
    private MyScriptServiceScriptsBuilder mockBuilder;
    @Mock
    private MyScriptServiceScripts scripts;

    private List<ScriptVersionShareDto> scriptVersionShareDtoList;
    private ScriptVersionShareDto scriptVersionShareDto;
    private List<ScriptVersionShare> scriptVersionShares;
    private List<UserInfoApiDto> userInfoApiDtoList;
    private OrgManagementApiDto orgManagementApiDto;

    @BeforeEach
    @MockitoSettings(strictness = Strictness.LENIENT)
    void setUp() throws NoSuchFieldException, IllegalAccessException {
        // 初始化测试数据
        scriptVersionShareDto = new ScriptVersionShareDto();
        scriptVersionShareDto.setIid(1L);
        scriptVersionShareDto.setScriptInfoId(100L);
        scriptVersionShareDto.setShareType((short) 0);
        scriptVersionShareDto.setShareObjectId("1001");
        scriptVersionShareDto.setCreatedTime(Timestamp.valueOf(LocalDateTime.now()));

        scriptVersionShareDtoList = new ArrayList<>();
        scriptVersionShareDtoList.add(scriptVersionShareDto);

        // 初始化实体数据
        ScriptVersionShare scriptVersionShare = new ScriptVersionShare();
        scriptVersionShare.setIid(1L);
        scriptVersionShare.setScriptInfoId(100L);
        scriptVersionShare.setShareType((short) 0);
        scriptVersionShare.setShareObjectId("1001");
        scriptVersionShare.setCreateTime(Timestamp.valueOf(LocalDateTime.now()));

        scriptVersionShares = new ArrayList<>();
        scriptVersionShares.add(scriptVersionShare);

        // 初始化用户数据
        UserInfoApiDto userInfoApiDto = new UserInfoApiDto();
        userInfoApiDto.setId(1001L);
        userInfoApiDto.setFullName("测试用户");
        userInfoApiDto.setLoginName("testuser");

        userInfoApiDtoList = new ArrayList<>();
        userInfoApiDtoList.add(userInfoApiDto);

        // 初始化部门数据
        orgManagementApiDto = new OrgManagementApiDto();
        orgManagementApiDto.setId(2001L);
        orgManagementApiDto.setName("测试部门");

        Field roleField = MyScriptServiceScriptsBuilder.class.getDeclaredField("iRole");
        roleField.setAccessible(true);
        roleField.set(mockBuilder, iRole);

        Field roleField1 = MyScriptServiceScripts.class.getDeclaredField("iRole");
        roleField1.setAccessible(true);
        roleField1.set(scripts, iRole);

    }

    @Test
    @DisplayName("测试插入脚本共享 - 无需过滤上级部门")
    void testInsertScriptVersionShare_NoFilter() {
        // 准备测试数据
        scriptVersionShareDto.setShareType((short) 0); // 用户共享类型

        // 由于Batch接口的batchData方法依赖Spring上下文，我们需要直接测试filterUpperDepartments方法
        // 然后验证mapper的调用
        when(scriptVersionShareMapper.insertScriptVersionShare(any(ScriptVersionShare.class))).thenReturn(1);

        // 使用反射调用私有方法
        List<ScriptVersionShareDto> filteredList = scriptVersionShareDtoList;
        try {
            java.lang.reflect.Method filterMethod = ScriptVersionShareServiceImpl.class.getDeclaredMethod("filterUpperDepartments", List.class);
            filterMethod.setAccessible(true);
            filteredList = (List<ScriptVersionShareDto>) filterMethod.invoke(scriptVersionShareService, scriptVersionShareDtoList);

            // 手动调用mapper方法，模拟batchData的行为
            for (ScriptVersionShareDto dto : filteredList) {
                ScriptVersionShare entity = new ScriptVersionShare();
                entity.setIid(dto.getIid());
                entity.setScriptInfoId(dto.getScriptInfoId());
                entity.setShareType(dto.getShareType());
                entity.setShareObjectId(dto.getShareObjectId());
                scriptVersionShareMapper.insertScriptVersionShare(entity);
            }
        } catch (Exception e) {
            fail("反射调用失败: " + e.getMessage());
        }

        // 验证结果
        verify(scriptVersionShareMapper, times(filteredList.size())).insertScriptVersionShare(any(ScriptVersionShare.class));
    }

    @Test
    @DisplayName("测试插入脚本共享 - 需要过滤上级部门")
    void testInsertScriptVersionShare_WithFilter() {
        // 准备测试数据
        ScriptVersionShareDto dto1 = new ScriptVersionShareDto();
        dto1.setIid(1L);
        dto1.setScriptInfoId(100L);
        dto1.setShareType((short) 1); // 部门共享类型
        dto1.setShareObjectId("100#200#300");

        ScriptVersionShareDto dto2 = new ScriptVersionShareDto();
        dto2.setIid(2L);
        dto2.setScriptInfoId(100L);
        dto2.setShareType((short) 1); // 部门共享类型
        dto2.setShareObjectId("100#200");

        List<ScriptVersionShareDto> dtoList = Arrays.asList(dto1, dto2);

        // 由于Batch接口的batchData方法依赖Spring上下文，我们需要直接测试filterUpperDepartments方法
        when(scriptVersionShareMapper.insertScriptVersionShare(any(ScriptVersionShare.class))).thenReturn(1);

        // 使用反射调用私有方法
        List<ScriptVersionShareDto> filteredList = Collections.emptyList();
        try {
            java.lang.reflect.Method filterMethod = ScriptVersionShareServiceImpl.class.getDeclaredMethod("filterUpperDepartments", List.class);
            filterMethod.setAccessible(true);
            filteredList = (List<ScriptVersionShareDto>) filterMethod.invoke(scriptVersionShareService, dtoList);

            // 手动调用mapper方法，模拟batchData的行为
            for (ScriptVersionShareDto dto : filteredList) {
                ScriptVersionShare entity = new ScriptVersionShare();
                entity.setIid(dto.getIid());
                entity.setScriptInfoId(dto.getScriptInfoId());
                entity.setShareType(dto.getShareType());
                entity.setShareObjectId(dto.getShareObjectId());
                scriptVersionShareMapper.insertScriptVersionShare(entity);
            }
        } catch (Exception e) {
            fail("反射调用失败: " + e.getMessage());
        }

        // 验证结果 - 应该只插入dto1，因为dto2的ID是dto1的前缀
        assertEquals(1, filteredList.size());
        verify(scriptVersionShareMapper, times(filteredList.size())).insertScriptVersionShare(any(ScriptVersionShare.class));
    }

    @Test
    @DisplayName("测试查询共享脚本数据 - 用户共享类型")
    void testSelectShareScriptData_UserShareType() {
        // 准备测试数据
        ScriptVersionShare share = new ScriptVersionShare();
        share.setIid(1L);
        share.setScriptInfoId(100L);
        share.setShareType((short) 0); // 用户共享类型
        share.setShareObjectId("1001");

        // 创建Page对象而不是普通List
        Page<ScriptVersionShare> sharePage = new Page<>();
        sharePage.add(share);

        // 模拟依赖
        when(scriptVersionShareMapper.selectShareScriptData(any(ScriptVersionShare.class))).thenReturn(sharePage);
        when(iUserInfoApi.getUserInfoList(anyList())).thenReturn(userInfoApiDtoList);

        // 使用MockedStatic模拟PageDataUtil.toDtoPage方法
        try (MockedStatic<PageDataUtil> mockedPageDataUtil = Mockito.mockStatic(PageDataUtil.class)) {
            // 创建预期的返回结果
            PageInfo<ScriptVersionShareDto> expectedPageInfo = new PageInfo<>();
            List<ScriptVersionShareDto> dtoList = new ArrayList<>();
            ScriptVersionShareDto dto = new ScriptVersionShareDto();
            dto.setIid(1L);
            dto.setScriptInfoId(100L);
            dto.setShareType((short) 0);
            dto.setShareObjectId("1001");
            dto.setShareObjectName("测试用户"); // 这是在service中设置的
            dtoList.add(dto);
            expectedPageInfo.setList(dtoList);

            // 模拟静态方法
            mockedPageDataUtil.when(() -> PageDataUtil.toDtoPage(any(List.class), any(Class.class))).thenReturn(expectedPageInfo);

            // 执行测试
            PageInfo<ScriptVersionShareDto> result = scriptVersionShareService.selectShareScriptData(scriptVersionShareDto, 1, 10);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.getList().size());
            verify(scriptVersionShareMapper).selectShareScriptData(any(ScriptVersionShare.class));
            verify(iUserInfoApi).getUserInfoList(anyList());
            mockedPageDataUtil.verify(() -> PageDataUtil.toDtoPage(any(List.class), any(Class.class)));
        }
    }

    @Test
    @DisplayName("测试查询共享脚本数据 - 部门共享类型")
    void testSelectShareScriptData_DepartmentShareType() {
        // 准备测试数据
        ScriptVersionShare share = new ScriptVersionShare();
        share.setIid(1L);
        share.setScriptInfoId(100L);
        share.setShareType((short) 1); // 部门共享类型
        share.setShareObjectId("100#200");

        // 创建Page对象而不是普通List
        Page<ScriptVersionShare> sharePage = new Page<>();
        sharePage.add(share);

        // 模拟依赖
        when(scriptVersionShareMapper.selectShareScriptData(any(ScriptVersionShare.class))).thenReturn(sharePage);
        when(orgManagementApi.selectOrgManagementById(anyLong())).thenReturn(orgManagementApiDto);

        // 使用MockedStatic模拟PageDataUtil.toDtoPage方法
        try (MockedStatic<PageDataUtil> mockedPageDataUtil = Mockito.mockStatic(PageDataUtil.class)) {
            // 创建预期的返回结果
            PageInfo<ScriptVersionShareDto> expectedPageInfo = new PageInfo<>();
            List<ScriptVersionShareDto> dtoList = new ArrayList<>();
            ScriptVersionShareDto dto = new ScriptVersionShareDto();
            dto.setIid(1L);
            dto.setScriptInfoId(100L);
            dto.setShareType((short) 1);
            dto.setShareObjectId("100#200");
            dto.setShareObjectName("测试部门"); // 这是在service中设置的
            dtoList.add(dto);
            expectedPageInfo.setList(dtoList);

            // 模拟静态方法
            mockedPageDataUtil.when(() -> PageDataUtil.toDtoPage(any(List.class), any(Class.class))).thenReturn(expectedPageInfo);

            // 执行测试
            PageInfo<ScriptVersionShareDto> result = scriptVersionShareService.selectShareScriptData(scriptVersionShareDto, 1, 10);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.getList().size());
            verify(scriptVersionShareMapper).selectShareScriptData(any(ScriptVersionShare.class));
            verify(orgManagementApi).selectOrgManagementById(anyLong());
            mockedPageDataUtil.verify(() -> PageDataUtil.toDtoPage(any(List.class), any(Class.class)));
        }
    }

    @Test
    @DisplayName("测试查询共享脚本数据 - 所有人共享类型")
    void testSelectShareScriptData_AllShareType() {
        // 准备测试数据
        ScriptVersionShare share = new ScriptVersionShare();
        share.setIid(1L);
        share.setScriptInfoId(100L);
        share.setShareType((short) 2); // 所有人共享类型
        share.setShareObjectId("-1");

        // 创建Page对象而不是普通List
        Page<ScriptVersionShare> sharePage = new Page<>();
        sharePage.add(share);

        // 模拟依赖
        when(scriptVersionShareMapper.selectShareScriptData(any(ScriptVersionShare.class))).thenReturn(sharePage);

        // 使用MockedStatic模拟PageDataUtil.toDtoPage方法
        try (MockedStatic<PageDataUtil> mockedPageDataUtil = Mockito.mockStatic(PageDataUtil.class)) {
            // 创建预期的返回结果
            PageInfo<ScriptVersionShareDto> expectedPageInfo = new PageInfo<>();
            List<ScriptVersionShareDto> dtoList = new ArrayList<>();
            ScriptVersionShareDto dto = new ScriptVersionShareDto();
            dto.setIid(1L);
            dto.setScriptInfoId(100L);
            dto.setShareType((short) 2);
            dto.setShareObjectId("-1");
            dto.setShareObjectName("所有人"); // 这是在service中设置的
            dtoList.add(dto);
            expectedPageInfo.setList(dtoList);

            // 模拟静态方法
            mockedPageDataUtil.when(() -> PageDataUtil.toDtoPage(any(List.class), any(Class.class))).thenReturn(expectedPageInfo);

            // 执行测试
            PageInfo<ScriptVersionShareDto> result = scriptVersionShareService.selectShareScriptData(scriptVersionShareDto, 1, 10);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.getList().size());
            assertEquals("所有人", result.getList().get(0).getShareObjectName());
            verify(scriptVersionShareMapper).selectShareScriptData(any(ScriptVersionShare.class));
            mockedPageDataUtil.verify(() -> PageDataUtil.toDtoPage(any(List.class), any(Class.class)));
        }
    }

    @Test
    @DisplayName("测试查询共享脚本数据 - 所有人共享类型")
    void testSelectShareScriptData_ShareTypeRole() {
        // 准备测试数据
        ScriptVersionShare share = new ScriptVersionShare();
        share.setIid(1L);
        share.setScriptInfoId(100L);
        share.setShareType((short) 3); // 角色共享类型
        share.setShareObjectId("1L");

        // 创建Page对象而不是普通List
        Page<ScriptVersionShare> sharePage = new Page<>();
        sharePage.add(share);

        // 模拟依赖
        when(scriptVersionShareMapper.selectShareScriptData(any(ScriptVersionShare.class))).thenReturn(sharePage);

        // 使用MockedStatic模拟PageDataUtil.toDtoPage方法
        try (MockedStatic<PageDataUtil> mockedPageDataUtil = Mockito.mockStatic(PageDataUtil.class)) {
            // 创建预期的返回结果
            PageInfo<ScriptVersionShareDto> expectedPageInfo = new PageInfo<>();
            List<ScriptVersionShareDto> dtoList = new ArrayList<>();
            ScriptVersionShareDto dto = new ScriptVersionShareDto();
            dto.setIid(1L);
            dto.setScriptInfoId(100L);
            dto.setShareType((short) 2);
            dto.setShareObjectId("1L");
            dto.setShareObjectName("测试角色"); // 这是在service中设置的
            dtoList.add(dto);
            expectedPageInfo.setList(dtoList);

            // 模拟静态方法
            mockedPageDataUtil.when(() -> PageDataUtil.toDtoPage(any(List.class), any(Class.class))).thenReturn(expectedPageInfo);

            when(scripts.getiRole()).thenReturn(iRole);

            PageInfo<RoleApiDto> dataPage = new PageInfo<>();
            List<RoleApiDto> dataList = new ArrayList<>();
            RoleApiDto roleApiDto = new RoleApiDto();
            roleApiDto.setId(1L);
            roleApiDto.setName("test_name");
            dataList.add(roleApiDto);
            dataPage.setList(dataList);

            when(iRole.selectRolePage(any(),any(),any())).thenReturn(dataPage);
            // 执行测试
            PageInfo<ScriptVersionShareDto> result = scriptVersionShareService.selectShareScriptData(scriptVersionShareDto, 1, 10);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.getList().size());
            assertEquals("测试角色", result.getList().get(0).getShareObjectName());
            verify(scriptVersionShareMapper).selectShareScriptData(any(ScriptVersionShare.class));
            mockedPageDataUtil.verify(() -> PageDataUtil.toDtoPage(any(List.class), any(Class.class)));
        }
    }

    static Stream<Arguments> getShareUserTestCases() {
        return Stream.of(
            // 有名称过滤条件
            Arguments.of(1L, 1, 10, "test", true),
            // 无名称过滤条件
            Arguments.of(1L, 1, 10, null, false)
        );
    }

    @ParameterizedTest
    @MethodSource("getShareUserTestCases")
    @DisplayName("测试获取共享用户 - 参数化测试")
    void testGetShareUser(Long userId, int pageNum, int pageSize, String fullName, boolean hasNameFilter) {
        // 准备测试数据
        ScriptVersionShareDto dto = new ScriptVersionShareDto();
        dto.setScriptInfoId(100L);
        if (hasNameFilter) {
            dto.setFullName(fullName);
        }

        List<Long> sharedUserIdList = Collections.singletonList(2001L);

        UserInfoApiDto user1 = new UserInfoApiDto();
        user1.setId(1001L);
        user1.setFullName("test user");

        UserInfoApiDto user2 = new UserInfoApiDto();
        user2.setId(2001L);
        user2.setFullName("another user");

        List<UserInfoApiDto> allUsers = Arrays.asList(user1, user2);

        // 模拟依赖
        when(iUserInfoApi.queryUserInfoListByOtherOrgManagement(userId)).thenReturn(allUsers);
        when(scriptVersionShareMapper.getSharedUser(dto.getScriptInfoId())).thenReturn(sharedUserIdList);

        // 执行测试
        PageInfo<UserInfoDto> result = scriptVersionShareService.getShareUser(userId, pageNum, pageSize, dto);

        // 验证结果
        assertNotNull(result);
        verify(iUserInfoApi).queryUserInfoListByOtherOrgManagement(userId);
        verify(scriptVersionShareMapper).getSharedUser(dto.getScriptInfoId());

        // 验证分页和过滤逻辑
        if (hasNameFilter) {
            // 如果有名称过滤，应该只返回包含该名称的用户
            for (UserInfoDto userDto : result.getList()) {
                assertTrue(userDto.getFullName().contains(fullName));
            }
        }
    }

    @Test
    @DisplayName("测试删除脚本共享")
    void testDeleteScriptVersionShareByIds() {
        // 准备测试数据
        Long[] ids = {1L, 2L, 3L};

        // 执行测试
        scriptVersionShareService.deleteScriptVersionShareByIds(ids);

        // 验证结果
        verify(scriptVersionShareMapper).deleteScriptVersionShareByIds(ids);
    }

    @Test
    @DisplayName("测试获取共享对象ID列表")
    void testGetObjectIdList() {
        // 准备测试数据
        Long scriptVersionId = 100L;
        Short shareType = 1;
        List<String> expectedIds = Arrays.asList("100#200", "100#300");

        // 模拟依赖
        when(scriptVersionShareMapper.getObjectIdList(scriptVersionId, shareType)).thenReturn(expectedIds);

        // 执行测试
        List<String> result = scriptVersionShareService.getObjectIdList(scriptVersionId, shareType);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedIds.size(), result.size());
        assertEquals(expectedIds, result);
        verify(scriptVersionShareMapper).getObjectIdList(scriptVersionId, shareType);
    }

    @Test
    @DisplayName("测试过滤上级部门 - 私有方法")
    void testFilterUpperDepartments() throws Exception {
        // 准备测试数据
        ScriptVersionShareDto dto1 = new ScriptVersionShareDto();
        dto1.setShareObjectId("100#200#300");

        ScriptVersionShareDto dto2 = new ScriptVersionShareDto();
        dto2.setShareObjectId("100#200");

        ScriptVersionShareDto dto3 = new ScriptVersionShareDto();
        dto3.setShareObjectId("100#400");

        List<ScriptVersionShareDto> inputList = Arrays.asList(dto1, dto2, dto3);

        // 使用反射调用私有方法
        java.lang.reflect.Method filterMethod = ScriptVersionShareServiceImpl.class.getDeclaredMethod("filterUpperDepartments", List.class);
        filterMethod.setAccessible(true);

        @SuppressWarnings("unchecked")
        List<ScriptVersionShareDto> result = (List<ScriptVersionShareDto>) filterMethod.invoke(scriptVersionShareService, inputList);

        // 验证结果
        assertNotNull(result);
        // 应该过滤掉dto2，因为它是dto1的前缀
        assertEquals(2, result.size());
        assertTrue(result.contains(dto1));
        assertFalse(result.contains(dto2));
        assertTrue(result.contains(dto3));
    }

    static Stream<Arguments> insertScriptVersionShareTestCases() {
        return Stream.of(
            // 用户共享类型 - 不需要过滤
            Arguments.of((short) 0, false),
            // 部门共享类型 - 需要过滤
            Arguments.of((short) 1, true),
            // 所有人共享类型 - 不需要过滤
            Arguments.of((short) 2, false)
        );
    }

    @ParameterizedTest
    @MethodSource("insertScriptVersionShareTestCases")
    @DisplayName("测试插入脚本共享 - 参数化测试")
    void testInsertScriptVersionShare(short shareType, boolean needFilter) {
        // 准备测试数据
        ScriptVersionShareDto dto1 = new ScriptVersionShareDto();
        dto1.setIid(1L);
        dto1.setScriptInfoId(100L);
        dto1.setShareType(shareType);
        dto1.setShareObjectId(shareType == 1 ? "100#200#300" : "1001");

        ScriptVersionShareDto dto2 = new ScriptVersionShareDto();
        dto2.setIid(2L);
        dto2.setScriptInfoId(100L);
        dto2.setShareType(shareType);
        dto2.setShareObjectId(shareType == 1 ? "100#200" : "1002");

        List<ScriptVersionShareDto> dtoList = Arrays.asList(dto1, dto2);

        // 创建一个新的spy对象，用于测试
        ScriptVersionShareServiceImpl serviceSpy = spy(scriptVersionShareService);

        // 模拟batchData方法，不实际执行
        doNothing().when(serviceSpy).batchData(anyList(), any(Consumer.class));

        // 执行测试
        serviceSpy.insertScriptVersionShare(dtoList);

        // 验证batchData方法被调用
        verify(serviceSpy).batchData(argThat(list -> {
            // 如果需要过滤，验证实体数量是否正确
            if (needFilter) {
                // 部门共享类型应该过滤掉上级部门，只剩下一个实体
                return list.size() == 1;
            } else {
                // 其他共享类型不需要过滤，应该有两个实体
                return list.size() == 2;
            }
        }), any(Consumer.class));
    }

    @Test
    @DisplayName("测试获取共享用户 - 手动分页")
    void testGetShareUser_ManualPaging() {
        // 准备测试数据
        Long userId = 1L;
        int pageNum = 1;
        int pageSize = 10;
        ScriptVersionShareDto queryDto = new ScriptVersionShareDto();
        queryDto.setScriptInfoId(100L);
        queryDto.setFullName(null); // 无名称过滤

        List<Long> sharedUserIds = Collections.singletonList(2001L);

        UserInfoApiDto user1 = new UserInfoApiDto();
        user1.setId(1001L);
        user1.setFullName("test user");

        UserInfoApiDto user2 = new UserInfoApiDto();
        user2.setId(2001L); // 这个ID在sharedUserIds中，应该被过滤掉
        user2.setFullName("another user");

        List<UserInfoApiDto> allUsers = Arrays.asList(user1, user2);

        // 模拟依赖
        when(iUserInfoApi.queryUserInfoListByOtherOrgManagement(userId)).thenReturn(allUsers);
        when(scriptVersionShareMapper.getSharedUser(queryDto.getScriptInfoId())).thenReturn(sharedUserIds);

        // 执行测试
        PageInfo<UserInfoDto> result = scriptVersionShareService.getShareUser(userId, pageNum, pageSize, queryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getList().size()); // 应该只有一个用户，因为另一个在sharedUserIds中
        assertEquals(1, result.getTotal()); // 总记录数应该是1
        assertEquals(pageNum, result.getPageNum()); // 页码应该是1
        assertEquals(pageSize, result.getPageSize()); // 每页大小应该是10

        verify(iUserInfoApi).queryUserInfoListByOtherOrgManagement(userId);
        verify(scriptVersionShareMapper).getSharedUser(queryDto.getScriptInfoId());
    }

    @Test
    @DisplayName("获取未被共享过的角色")
    void testGetNotShareRoles() {

        Integer pageNum = 1;
        Integer pageSize = 10;
        ScriptVersionShareDto share = new ScriptVersionShareDto();
        share.setIid(1L);
        share.setScriptInfoId(100L);
        share.setShareType((short) 3); // 角色共享类型
        share.setShareObjectId("1L");
        share.setShareObjectName("test");

        List<String> idList = new ArrayList<>();
        idList.add("1");
        // 模拟依赖
        when(scriptVersionShareMapper.getObjectIdList(any(),any())).thenReturn(idList);

        // 使用MockedStatic模拟PageDataUtil.toDtoPage方法
        try (MockedStatic<PageDataUtil> mockedPageDataUtil = Mockito.mockStatic(PageDataUtil.class)) {

            when(scripts.getiRole()).thenReturn(iRole);

            PageInfo<RoleApiDto> dataPage = new PageInfo<>();
            List<RoleApiDto> dataList = new ArrayList<>();
            RoleApiDto roleApiDto = new RoleApiDto();
            roleApiDto.setId(1L);
            roleApiDto.setName("test_name");
            dataList.add(roleApiDto);
            dataPage.setList(dataList);

            when(iRole.selectRolePage(any(),any(),any())).thenReturn(dataPage);
            // 执行测试
            PageInfo<RoleApiDto> result = scriptVersionShareService.getNotShareRoles(pageNum,pageSize,scriptVersionShareDto);

            // 验证结果
            assertNotNull(result);
        }
    }
}
