package com.ideal.script.controller;

import com.ideal.common.dto.R;
import com.ideal.script.model.dto.PlatformDto;
import com.ideal.script.service.IPlatformService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * PlatformController单元测试类
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class PlatformControllerTest {

    @Mock
    private IPlatformService platformService;

    @InjectMocks
    private PlatformController platformController;

    private PlatformDto platformDto1;
    private PlatformDto platformDto2;
    private List<PlatformDto> platformList;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        platformDto1 = new PlatformDto();
        platformDto1.setId(1L);
        platformDto1.setName("Linux");
        platformDto1.setCodevalue("linux");

        platformDto2 = new PlatformDto();
        platformDto2.setId(2L);
        platformDto2.setName("Windows");
        platformDto2.setCodevalue("windows");

        platformList = Arrays.asList(platformDto1, platformDto2);
    }

    @Test
    @DisplayName("获取脚本平台代码列表-成功")
    void getScriptPlatformCode_success() {
        // Mock service方法
        doReturn(platformList).when(platformService).getScriptPlatformCode();

        // 执行测试方法
        R<List<PlatformDto>> result = platformController.getScriptPlatformCode();

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("list.success", result.getMessage());
        assertEquals(platformList, result.getData());
        assertEquals(2, result.getData().size());
        assertEquals("Linux", result.getData().get(0).getName());
        assertEquals("Windows", result.getData().get(1).getName());
        
        // 验证service方法被调用
        verify(platformService, times(1)).getScriptPlatformCode();
    }

    @Test
    @DisplayName("获取脚本平台代码列表-空列表")
    void getScriptPlatformCode_emptyList() {
        // Mock service方法返回空列表
        doReturn(Collections.emptyList()).when(platformService).getScriptPlatformCode();

        // 执行测试方法
        R<List<PlatformDto>> result = platformController.getScriptPlatformCode();

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("list.success", result.getMessage());
        assertEquals(Collections.emptyList(), result.getData());
        assertTrue(result.getData().isEmpty());
        
        // 验证service方法被调用
        verify(platformService, times(1)).getScriptPlatformCode();
    }

    @Test
    @DisplayName("获取脚本平台代码列表-单个平台")
    void getScriptPlatformCode_singlePlatform() {
        // Mock service方法返回单个平台
        List<PlatformDto> singlePlatformList = Collections.singletonList(platformDto1);
        doReturn(singlePlatformList).when(platformService).getScriptPlatformCode();

        // 执行测试方法
        R<List<PlatformDto>> result = platformController.getScriptPlatformCode();

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("list.success", result.getMessage());
        assertEquals(singlePlatformList, result.getData());
        assertEquals(1, result.getData().size());
        assertEquals("Linux", result.getData().get(0).getName());
        assertEquals("linux", result.getData().get(0).getCodevalue());
        
        // 验证service方法被调用
        verify(platformService, times(1)).getScriptPlatformCode();
    }

    @Test
    @DisplayName("获取脚本平台代码列表-service抛出异常")
    void getScriptPlatformCode_serviceException() {
        // Mock service方法抛出异常
        doThrow(new RuntimeException("Database connection failed")).when(platformService).getScriptPlatformCode();

        // 执行测试方法并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            platformController.getScriptPlatformCode();
        });

        // 验证异常信息
        assertEquals("Database connection failed", exception.getMessage());
        
        // 验证service方法被调用
        verify(platformService, times(1)).getScriptPlatformCode();
    }
}
