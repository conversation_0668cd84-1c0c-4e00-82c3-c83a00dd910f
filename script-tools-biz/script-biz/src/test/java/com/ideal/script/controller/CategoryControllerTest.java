package com.ideal.script.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.common.util.spring.MessageUtil;
import com.ideal.sc.util.ValidationUtils;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.dto.CategoryDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.bean.CategoryOrgBean;
import com.ideal.script.model.bean.CategoryRoleBean;
import com.ideal.script.model.bean.CategoryUserBean;
import com.ideal.script.model.dto.CategoryOrgDto;
import com.ideal.script.model.dto.CategoryRoleDto;
import com.ideal.script.model.dto.CategoryUserDto;
import com.ideal.script.service.ICategoryService;
import com.ideal.system.dto.OrgManagementApiDto;
import com.ideal.system.dto.PermissionUserInfoApiDto;
import com.ideal.system.dto.RoleApiDto;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * CategoryController单元测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class CategoryControllerTest {
    
    @Mock
    private ICategoryService categoryService;
    
    @InjectMocks
    private CategoryController categoryController;
    
    private static MockedStatic<MessageUtil> mockedMessageUtil;
    private static MockedStatic<ValidationUtils> validationUtilsMock;
    
    private CategoryDto categoryDto;
    private CategoryOrgDto categoryOrgDto;
    private CategoryUserDto categoryUserDto;
    private TableQueryDto<CategoryDto> tableQueryDto;
    private OrgManagementApiDto orgManagementApiDto;

    private RoleApiDto roleApiDto;
    private CategoryRoleDto categoryRoleDto;
    
    @BeforeAll
    static void beforeAll() {
        // Mock 静态方法
        mockedMessageUtil = mockStatic(MessageUtil.class);
        mockedMessageUtil.when(() -> MessageUtil.message(anyString())).thenReturn("Mocked Message");
        
        validationUtilsMock = mockStatic(ValidationUtils.class);
        validationUtilsMock.when(() -> ValidationUtils.customFailResult(anyString(), anyString()))
                .thenAnswer(invocation -> R.fail(Constants.REPONSE_STATUS_FAIL_CODE, invocation.getArgument(0), invocation.getArgument(1)));
    }
    
    @AfterAll
    static void afterAll() {
        // 关闭静态方法的mock
        mockedMessageUtil.close();
        validationUtilsMock.close();
    }
    
    @BeforeEach
    void setUp() {
        // 初始化测试数据
        categoryDto = new CategoryDto();
        categoryDto.setId(1L);
        categoryDto.setName("测试分类");
        categoryDto.setLevel(1);
        
        tableQueryDto = new TableQueryDto<>();
        tableQueryDto.setQueryParam(categoryDto);
        tableQueryDto.setPageNum(1);
        tableQueryDto.setPageSize(10);
        
        categoryOrgDto = new CategoryOrgDto();
        categoryOrgDto.setCategoryId(1L);
        categoryOrgDto.setLevel(1);
        
        categoryUserDto = new CategoryUserDto();
        categoryUserDto.setCategoryId(1L);
        
        orgManagementApiDto = new OrgManagementApiDto();

        roleApiDto = new RoleApiDto();
        roleApiDto.setDataShare(1);

        categoryRoleDto = new CategoryRoleDto();
        categoryRoleDto.setCategoryId(1L);
    }
    
    @Test
    @DisplayName("测试查询分类列表")
    void listCategory_test() {
        // 准备数据
        PageInfo<CategoryDto> pageInfo = new PageInfo<>(Collections.singletonList(categoryDto));
        
        // Mock方法
        when(categoryService.selectCategoryList(any(CategoryDto.class), anyInt(), anyInt()))
                .thenReturn(pageInfo);
        
        // 执行测试
        R<PageInfo<CategoryDto>> result = categoryController.listCategory(tableQueryDto);
        
        // 验证结果
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        assertEquals(pageInfo, result.getData());
        verify(categoryService, times(1)).selectCategoryList(any(CategoryDto.class), anyInt(), anyInt());
    }
    
    @Test
    @DisplayName("测试获取分类列表不分页")
    void getCategoryList_test() {
        // 准备数据
        List<CategoryDto> categoryList = Collections.singletonList(categoryDto);
        
        // Mock方法
        when(categoryService.selectCategoryListNoPage(any(CategoryDto.class)))
                .thenReturn(categoryList);
        
        // 执行测试
        R<List<CategoryDto>> result = categoryController.getCategoryList(categoryDto);
        
        // 验证结果
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        assertEquals(categoryList, result.getData());
        verify(categoryService, times(1)).selectCategoryListNoPage(any(CategoryDto.class));
    }
    
    @ParameterizedTest
    @MethodSource("provideSaveCategoryTestData")
    @DisplayName("测试新增分类")
    void saveCategory_test(boolean throwException, String exceptionMessage, String expectedCode) throws ScriptException {
        // Mock方法
        if (throwException) {
            doThrow(new ScriptException(exceptionMessage)).when(categoryService).insertCategory(any(CategoryDto.class));
        } else {
            doReturn(1).when(categoryService).insertCategory(any(CategoryDto.class));
        }
        
        // 执行测试
        R<Object> result = categoryController.saveCategory(categoryDto);
        
        // 验证结果
        assertEquals(expectedCode, result.getCode());
        verify(categoryService, times(1)).insertCategory(any(CategoryDto.class));
    }
    
    static Stream<Arguments> provideSaveCategoryTestData() {
        return Stream.of(
                Arguments.of(false, null, Constants.REPONSE_STATUS_SUSSCESS_CODE),
                Arguments.of(true, "分类名称已存在", Constants.REPONSE_STATUS_FAIL_CODE)
        );
    }
    
    @ParameterizedTest
    @MethodSource("provideUpdateCategoryTestData")
    @DisplayName("测试修改分类")
    void updateCategory_test(boolean throwException, String exceptionMessage, String expectedCode) throws ScriptException {
        // Mock方法
        if (throwException) {
            doThrow(new ScriptException(exceptionMessage)).when(categoryService).updateCategory(any(CategoryDto.class));
        } else {
            doReturn(1).when(categoryService).updateCategory(any(CategoryDto.class));
        }
        
        // 执行测试
        R<Object> result = categoryController.updateCategory(categoryDto);
        
        // 验证结果
        assertEquals(expectedCode, result.getCode());
        verify(categoryService, times(1)).updateCategory(any(CategoryDto.class));
    }
    
    static Stream<Arguments> provideUpdateCategoryTestData() {
        return Stream.of(
                Arguments.of(false, null, Constants.REPONSE_STATUS_SUSSCESS_CODE),
                Arguments.of(true, "分类名称已存在", Constants.REPONSE_STATUS_FAIL_CODE)
        );
    }
    
    @ParameterizedTest
    @MethodSource("provideRemoveCategoryTestData")
    @DisplayName("测试删除分类")
    void removeCategory_test(boolean throwScriptException, boolean throwOtherException, String exceptionMessage, String expectedCode) throws ScriptException {
        // 准备数据
        Long[] ids = {1L, 2L};
        
        // Mock方法
        if (throwScriptException) {
            doThrow(new ScriptException(exceptionMessage)).when(categoryService).deleteCategoryByIds(any());
        } else if (throwOtherException) {
            doThrow(new RuntimeException(exceptionMessage)).when(categoryService).deleteCategoryByIds(any());
        } else {
            doReturn(1).when(categoryService).deleteCategoryByIds(any());
        }
        
        // 执行测试
        R<Object> result = categoryController.removeCategory(ids);
        
        // 验证结果
        assertEquals(expectedCode, result.getCode());
        verify(categoryService, times(1)).deleteCategoryByIds(any());
    }
    
    static Stream<Arguments> provideRemoveCategoryTestData() {
        return Stream.of(
                Arguments.of(false, false, null, Constants.REPONSE_STATUS_SUSSCESS_CODE),
                Arguments.of(true, false, "删除失败", Constants.REPONSE_STATUS_FAIL_CODE),
                Arguments.of(false, true, "删除失败", Constants.REPONSE_STATUS_FAIL_CODE)
        );
    }
    
    @Test
    @DisplayName("测试查询一级分类列表")
    void listFirstCategory_test() {
        // 准备数据
        List<CategoryDto> categoryList = Collections.singletonList(categoryDto);
        
        // Mock方法
        when(categoryService.listFirstCategory()).thenReturn(categoryList);
        
        // 执行测试
        R<List<CategoryDto>> result = categoryController.listFirstCategory();
        
        // 验证结果
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        assertEquals(categoryList, result.getData());
        verify(categoryService, times(1)).listFirstCategory();
    }
    
    @Test
    @DisplayName("测试查询多级分类列表")
    void listMultiCategory_test() {
        // 准备数据
        Long level = 1L;
        Long parentId = 2L;
        List<CategoryDto> categoryList = Collections.singletonList(categoryDto);
        
        // Mock方法
        when(categoryService.listMultiCategory(anyInt(), anyLong())).thenReturn(categoryList);
        
        // 执行测试
        R<List<CategoryDto>> result = categoryController.listMultiCategory(level, parentId);
        
        // 验证结果
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        assertEquals(categoryList, result.getData());
        verify(categoryService, times(1)).listMultiCategory(level.intValue(), parentId);
    }
    
    @Test
    @DisplayName("测试查询下一级分类列表")
    void listNextCategory_test() {
        // 准备数据
        long parentId = 1L;
        List<CategoryDto> categoryList = Collections.singletonList(categoryDto);
        
        // Mock方法
        when(categoryService.listNextCategory(anyLong())).thenReturn(categoryList);
        
        // 执行测试
        R<List<CategoryDto>> result = categoryController.listNextCategory(parentId);
        
        // 验证结果
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        assertEquals(categoryList, result.getData());
        verify(categoryService, times(1)).listNextCategory(parentId);
    }
    
    @Test
    @DisplayName("测试分类授权部门")
    void assignCategoryToOrg_test() {
        // Mock方法
        doNothing().when(categoryService).assignCategoryToOrg(any(CategoryOrgDto.class));
        
        // 执行测试
        R<Object> result = categoryController.assignCategoryToOrg(categoryOrgDto);
        
        // 验证结果
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        verify(categoryService, times(1)).assignCategoryToOrg(any(CategoryOrgDto.class));
    }
    
    @Test
    @DisplayName("测试查询分类的部门绑定信息")
    void getCategoryOrgRelations_test() {
        // 准备数据
        long categoryId = 1L;
        CategoryOrgBean categoryOrgBean = new CategoryOrgBean();
        categoryOrgBean.setCategoryId(categoryId);
        
        // Mock方法
        when(categoryService.getCategoryOrgRelations(anyLong())).thenReturn(categoryOrgBean);
        
        // 执行测试
        R<CategoryOrgDto> result = categoryController.getCategoryOrgRelations(categoryId);
        
        // 验证结果
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        verify(categoryService, times(1)).getCategoryOrgRelations(categoryId);
    }
    
    @ParameterizedTest
    @MethodSource("provideAssignCategoryToUserTestData")
    @DisplayName("测试分类授权用户")
    void assignCategoryToUser_test(boolean throwException, String exceptionMessage, String expectedCode) {
        // Mock方法
        try {
            if (throwException) {
                doThrow(new ScriptException(exceptionMessage)).when(categoryService).assignCategoryToUser(any(CategoryUserDto.class));
            } else {
                doNothing().when(categoryService).assignCategoryToUser(any(CategoryUserDto.class));
            }
            
            // 执行测试
            R<Object> result;
            if (throwException) {
                try {
                    result = categoryController.assignCategoryToUser(categoryUserDto);
                    // 如果没有抛出异常，但预期是失败的，说明控制器处理了异常并返回了失败结果
                    assertEquals(expectedCode, result.getCode());
                } catch (ScriptException e) {
                    // 如果抛出异常，我们验证异常信息是否与预期一致
                    assertEquals(exceptionMessage, e.getMessage());
                    // 既然抛出了异常，则无需验证返回值
                    return;
                }
            } else {
                result = categoryController.assignCategoryToUser(categoryUserDto);
                assertEquals(expectedCode, result.getCode());
            }
            
            // 验证Service方法被调用了一次
            verify(categoryService, times(1)).assignCategoryToUser(any(CategoryUserDto.class));
        } catch (Exception e) {
            // 捕获其他未预期的异常
            fail("Unexpected exception: " + e.getMessage());
        }
    }
    
    static Stream<Arguments> provideAssignCategoryToUserTestData() {
        return Stream.of(
                Arguments.of(false, null, Constants.REPONSE_STATUS_SUSSCESS_CODE),
                Arguments.of(true, "授权失败", Constants.REPONSE_STATUS_FAIL_CODE)
        );
    }
    
    @Test
    @DisplayName("测试查询分类的用户绑定信息")
    void getCategoryUserRelations_test() {
        // 准备数据
        long categoryId = 1L;
        CategoryUserBean categoryUserBean = new CategoryUserBean();
        categoryUserBean.setCategoryId(categoryId);
        
        // Mock方法
        when(categoryService.getCategoryUserRelations(anyLong())).thenReturn(categoryUserBean);
        
        // 执行测试
        R<CategoryUserDto> result = categoryController.getCategoryUserRelations(categoryId);
        
        // 验证结果
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        verify(categoryService, times(1)).getCategoryUserRelations(categoryId);
    }
    
    @Test
    @DisplayName("测试查询用户列表（分类绑定用户）")
    void queryPermissionUserInfoList_test() {
        // 准备数据
        CategoryUserBean categoryUserBean = new CategoryUserBean();
        categoryUserBean.setCategoryId(1L);
        List<PermissionUserInfoApiDto> userList = new ArrayList<>();
        
        // Mock方法
        when(categoryService.queryPermissionUserInfoList(any(CategoryUserBean.class))).thenReturn(userList);
        
        // 执行测试
        R<List<PermissionUserInfoApiDto>> result = categoryController.queryPermissionUserInfoList(categoryUserBean);
        
        // 验证结果
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        assertEquals(userList, result.getData());
        verify(categoryService, times(1)).queryPermissionUserInfoList(any(CategoryUserBean.class));
    }
    
    @Test
    @DisplayName("测试查询用户列表分页（分类绑定用户）")
    void queryPermissionUserInfoPage_test() {
        // 准备数据
        TableQueryDto<CategoryUserBean> tableQueryDto = new TableQueryDto<>();
        CategoryUserBean categoryUserBean = new CategoryUserBean();
        categoryUserBean.setCategoryId(1L);
        tableQueryDto.setQueryParam(categoryUserBean);
        tableQueryDto.setPageNum(1);
        tableQueryDto.setPageSize(10);
        PageInfo<PermissionUserInfoApiDto> pageInfo = new PageInfo<>(Collections.emptyList());
        
        // Mock方法
        when(categoryService.queryPermissionUserInfoPage(any(CategoryUserBean.class), anyInt(), anyInt())).thenReturn(pageInfo);
        
        // 执行测试
        R<PageInfo<PermissionUserInfoApiDto>> result = categoryController.queryPermissionUserInfoPage(tableQueryDto);
        
        // 验证结果
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        assertEquals(pageInfo, result.getData());
        verify(categoryService, times(1)).queryPermissionUserInfoPage(any(CategoryUserBean.class), anyInt(), anyInt());
    }
    
    @Test
    @DisplayName("测试查询部门树形结构")
    void selectOrgManagementTree_test() {
        // 准备数据
        List<OrgManagementApiDto> orgList = new ArrayList<>();
        
        // Mock方法
        when(categoryService.selectOrgManagementTree(any(OrgManagementApiDto.class))).thenReturn(orgList);
        
        // 执行测试
        R<List<OrgManagementApiDto>> result = categoryController.selectOrgManagementTree(orgManagementApiDto);
        
        // 验证结果
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        assertEquals(orgList, result.getData());
        verify(categoryService, times(1)).selectOrgManagementTree(any(OrgManagementApiDto.class));
    }
    
    @Test
    @DisplayName("测试获取未共享脚本的部门")
    void selectNotShareOrgManagementTree_test() {
        // 准备数据
        Long scriptVersionId = 1L;
        List<OrgManagementApiDto> orgList = new ArrayList<>();
        
        // Mock方法
        when(categoryService.selectNotShareOrgManagementTree(anyLong())).thenReturn(orgList);
        
        // 执行测试
        R<Object> result = categoryController.selectNotShareOrgManagementTree(scriptVersionId);
        
        // 验证结果
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        assertEquals(orgList, result.getData());
        verify(categoryService, times(1)).selectNotShareOrgManagementTree(scriptVersionId);
    }
    
    @Test
    @DisplayName("测试深度遍历构建分类树")
    void selectCategoryListDFS_test() {
        // 准备数据
        List<CategoryDto> categoryList = Collections.singletonList(categoryDto);
        
        // Mock方法
        when(categoryService.selectCategoryListDFS(any(CategoryDto.class))).thenReturn(categoryList);
        
        // 执行测试
        R<List<CategoryDto>> result = categoryController.selectCategoryListDFS(categoryDto);
        
        // 验证结果
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        assertEquals(categoryList, result.getData());
        verify(categoryService, times(1)).selectCategoryListDFS(any(CategoryDto.class));
    }
    
    @Test
    @DisplayName("测试获取分类全路径")
    void getCategoryFullPath_test() {
        // 准备数据
        Long categoryId = 1L;
        String fullPath = "一级分类/二级分类";
        
        // Mock方法
        when(categoryService.getCategoryFullPath(anyLong())).thenReturn(fullPath);
        
        // 执行测试
        R<String> result = categoryController.getCategoryFullPath(categoryId);
        
        // 验证结果
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        assertEquals(fullPath, result.getData());
        verify(categoryService, times(1)).getCategoryFullPath(categoryId);
    }

    @Test
    @DisplayName("测试获取分类可授权角色")
    void selectRoleManagementList_test(){
        // 准备数据
        TableQueryDto<RoleApiDto> tableQueryDto = new TableQueryDto<>();
        RoleApiDto roleApiDto1 = new RoleApiDto();
        roleApiDto1.setName("测试");
        roleApiDto1.setDataShare(1);
        tableQueryDto.setQueryParam(roleApiDto1);
        tableQueryDto.setPageNum(1);
        tableQueryDto.setPageSize(10);
        PageInfo<RoleApiDto> pageInfo = new PageInfo<>(Collections.emptyList());

        // Mock方法
        when(categoryService.selectRoleManagementList(any(RoleApiDto.class),any(Integer.class),any(Integer.class))).thenReturn(pageInfo);

        // 执行测试
        R<PageInfo<RoleApiDto>> result = categoryController.selectRoleManagementList(tableQueryDto);

        // 验证结果
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        assertEquals(pageInfo, result.getData());
        verify(categoryService, times(1)).selectRoleManagementList(any(RoleApiDto.class),anyInt(),anyInt());

    }

    @Test
    @DisplayName("测试分类授权角色")
    void assignCategoryToRole_test(){
        // Mock方法
        doNothing().when(categoryService).assignCategoryToRole(any(CategoryRoleDto.class));

        // 执行测试
        R<Object> result = categoryController.assignCategoryToRole(categoryRoleDto);

        // 验证结果
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        verify(categoryService, times(1)).assignCategoryToRole(any(CategoryRoleDto.class));

    }

    @Test
    @DisplayName("测试查询该分类的角色绑定信息")
    void getCategoryRoleRelations_test(){

        // Mock方法
        when(categoryService.getCategoryRoleRelations(any(Long.class))).thenReturn(categoryRoleDto);

        // 执行测试
        R<CategoryRoleDto> result = categoryController.getCategoryRoleRelations(1);

        // 验证结果
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        assertEquals(categoryRoleDto, result.getData());
        verify(categoryService, times(1)).getCategoryRoleRelations(any(Long.class));

    }

    @Test
    @DisplayName("测试根据角色获取用户列表（分类绑定用户）(分页)")
    void queryPermissionUserInfoPageByRole_test(){

        // 准备数据
        TableQueryDto<CategoryRoleDto> tableQueryDto = new TableQueryDto<>();
        CategoryRoleDto categoryRoleDto1 = new CategoryRoleDto();
        categoryRoleDto1.setCategoryId(1L);
        tableQueryDto.setQueryParam(categoryRoleDto1);
        tableQueryDto.setPageNum(1);
        tableQueryDto.setPageSize(10);
        PageInfo<PermissionUserInfoApiDto> pageInfo = new PageInfo<>(Collections.emptyList());

        // Mock方法
        when(categoryService.queryPermissionUserInfoPageByRole(any(CategoryRoleDto.class), anyInt(), anyInt())).thenReturn(pageInfo);

        // 执行测试
        R<PageInfo<PermissionUserInfoApiDto>> result = categoryController.queryPermissionUserInfoPageByRole(tableQueryDto);

        // 验证结果
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        assertEquals(pageInfo, result.getData());
        verify(categoryService, times(1)).queryPermissionUserInfoPageByRole(any(CategoryRoleDto.class), anyInt(), anyInt());

    }



} 