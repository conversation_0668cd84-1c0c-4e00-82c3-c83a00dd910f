package com.ideal.script.service.impl;

import com.ideal.approval.dto.DoubleCheckApiDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.exception.SystemException;
import com.ideal.script.model.dto.AuditRelationDto;
import com.ideal.script.service.IAuditRelationService;
import com.ideal.script.service.IMyScriptService;
import com.ideal.script.service.ITaskApplyService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Objects;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ScriptApprovalResultServiceImplTest {

    @Mock
    private IAuditRelationService mockAuditRelationService;
    @Mock
    private ITaskApplyService mockTaskApplyService;
    @Mock
    private IMyScriptService mockScriptService;

    private ScriptApprovalResultServiceImpl scriptApprovalResultServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        scriptApprovalResultServiceImplUnderTest = new ScriptApprovalResultServiceImpl(mockAuditRelationService,
                mockTaskApplyService, mockScriptService);
    }

    @ParameterizedTest
    @CsvSource({
            "1,toolbox",
            "2,toolbox",
            "3,toolbox",
            "1,script",
            "2,script",
            "3,script",
    })
    void testScriptCall(Integer id,String scriptSource) throws Exception {
        // Setup
        final DoubleCheckApiDto doubleCheckApiDto = new DoubleCheckApiDto();
        /*doubleCheckApiDto.setActionBy(0L);
        doubleCheckApiDto.setActionByUserName("actionByUserName");*/
        doubleCheckApiDto.setId(0L);
        doubleCheckApiDto.setServiceId(0L);
        doubleCheckApiDto.setTaskSubject("taskSubject");
        doubleCheckApiDto.setItemType(scriptSource);

        // Configure IAuditRelationService.selectAuditRelationByWorkItemId(...).
        final AuditRelationDto auditRelationDto = new AuditRelationDto();
        auditRelationDto.setInfoId(0L);
        auditRelationDto.setSrcScriptUuid("srcScriptUuid");
        auditRelationDto.setAuditUserId(0L);
        auditRelationDto.setId(1L);
        auditRelationDto.setAuditType(id);
        if(Objects.equals(scriptSource, "toolbox")){
            when(mockAuditRelationService.selectAuditRelationByWorkItemId(0L)).thenReturn(auditRelationDto);
        }else{
            when(mockAuditRelationService.selectAuditRelationById(0L)).thenReturn(auditRelationDto);
        }

        // Run the test
        scriptApprovalResultServiceImplUnderTest.scriptCall(doubleCheckApiDto);

        // Verify the results
        if(id.equals(1)) {
            verify(mockScriptService).doubleCheckScriptCallBack(any(DoubleCheckApiDto.class), eq(1));
        }else if(id.equals(2)){
            verify(mockScriptService).doubleCheckScriptCallBack(any(DoubleCheckApiDto.class), eq(2));
        }else{
            verify(mockTaskApplyService).doubleCheckCallBack(any(DoubleCheckApiDto.class));
        }
    }

    @Test
    void testScriptCall_IMyScriptServiceThrowsSystemException() throws Exception {
        // Setup
        final DoubleCheckApiDto doubleCheckApiDto = new DoubleCheckApiDto();
        /*doubleCheckApiDto.setActionBy(0L);
        doubleCheckApiDto.setActionByUserName("actionByUserName");*/
        doubleCheckApiDto.setId(0L);
        doubleCheckApiDto.setServiceId(0L);
        doubleCheckApiDto.setTaskSubject("taskSubject");
        doubleCheckApiDto.setItemType("toolbox");


        // Configure IAuditRelationService.selectAuditRelationByWorkItemId(...).
        final AuditRelationDto auditRelationDto = new AuditRelationDto();
        auditRelationDto.setInfoId(0L);
        auditRelationDto.setSrcScriptUuid("srcScriptUuid");
        auditRelationDto.setAuditUserId(0L);
        auditRelationDto.setId(0L);
        auditRelationDto.setAuditType(1);
        when(mockAuditRelationService.selectAuditRelationByWorkItemId(0L)).thenReturn(auditRelationDto);


        doThrow(SystemException.class).when(mockScriptService).doubleCheckScriptCallBack(any(DoubleCheckApiDto.class),
                eq(1));


        // Run the test
        assertThatThrownBy(() -> scriptApprovalResultServiceImplUnderTest.scriptCall(doubleCheckApiDto))
                .isInstanceOf(ScriptException.class);
    }

    @Test
    void testScriptCall_ITaskApplyServiceThrowsScriptException() throws Exception {
        // Setup
        final DoubleCheckApiDto doubleCheckApiDto = new DoubleCheckApiDto();
        /*doubleCheckApiDto.setActionBy(0L);
        doubleCheckApiDto.setActionByUserName("actionByUserName");*/
        doubleCheckApiDto.setId(0L);
        doubleCheckApiDto.setServiceId(0L);
        doubleCheckApiDto.setTaskSubject("taskSubject");
        doubleCheckApiDto.setItemType("toolbox");


        // Configure IAuditRelationService.selectAuditRelationByWorkItemId(...).
        final AuditRelationDto auditRelationDto = new AuditRelationDto();
        auditRelationDto.setInfoId(0L);
        auditRelationDto.setSrcScriptUuid("srcScriptUuid");
        auditRelationDto.setAuditUserId(0L);
        auditRelationDto.setId(0L);
        auditRelationDto.setAuditType(3);
        when(mockAuditRelationService.selectAuditRelationByWorkItemId(0L)).thenReturn(auditRelationDto);

        doThrow(ScriptException.class).when(mockTaskApplyService).doubleCheckCallBack(any(DoubleCheckApiDto.class));

        // Run the test
        assertThatThrownBy(() -> scriptApprovalResultServiceImplUnderTest.scriptCall(doubleCheckApiDto))
                .isInstanceOf(ScriptException.class);
    }

    @Test
    void testScriptCall_ITaskApplyServiceThrowsSystemException() throws Exception {
        // Setup
        final DoubleCheckApiDto doubleCheckApiDto = new DoubleCheckApiDto();
        /*doubleCheckApiDto.setActionBy(0L);
        doubleCheckApiDto.setActionByUserName("actionByUserName");*/
        doubleCheckApiDto.setId(0L);
        doubleCheckApiDto.setServiceId(0L);
        doubleCheckApiDto.setTaskSubject("taskSubject");
        doubleCheckApiDto.setItemType("toolbox");


        // Configure IAuditRelationService.selectAuditRelationByWorkItemId(...).
        final AuditRelationDto auditRelationDto = new AuditRelationDto();
        auditRelationDto.setInfoId(0L);
        auditRelationDto.setSrcScriptUuid("srcScriptUuid");
        auditRelationDto.setAuditUserId(0L);
        auditRelationDto.setId(0L);
        auditRelationDto.setAuditType(3);
        when(mockAuditRelationService.selectAuditRelationByWorkItemId(0L)).thenReturn(auditRelationDto);

        doThrow(SystemException.class).when(mockTaskApplyService).doubleCheckCallBack(any(DoubleCheckApiDto.class));

        // Run the test
        assertThatThrownBy(() -> scriptApprovalResultServiceImplUnderTest.scriptCall(doubleCheckApiDto))
                .isInstanceOf(ScriptException.class);
    }
}
