package com.ideal.script.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ideal.script.mapper.AuditRelationMapper;
import com.ideal.script.model.dto.AuditRelationDto;
import com.ideal.script.model.entity.AuditRelation;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AuditRelationServiceImplTest {

    @Mock
    private AuditRelationMapper mockAuditRelationMapper;

    private AuditRelationServiceImpl auditRelationServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        auditRelationServiceImplUnderTest = new AuditRelationServiceImpl(mockAuditRelationMapper);
    }

    @Test
    void testSelectAuditRelationById() {
        // Setup
        // Configure AuditRelationMapper.selectAuditRelationById(...).
        final AuditRelation auditRelation = new AuditRelation();
        auditRelation.setAuditUserId(0L);
        auditRelation.setApplyUserId(0L);
        auditRelation.setId(0L);
        auditRelation.setScriptTaskId(0L);
        auditRelation.setApprWorkitemId(0L);
        when(mockAuditRelationMapper.selectAuditRelationById(0L)).thenReturn(auditRelation);

        // Run the test
        final AuditRelationDto result = auditRelationServiceImplUnderTest.selectAuditRelationById(0L);
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testSelectAuditRelationList() {
        // Setup
        final AuditRelationDto auditRelationDto = new AuditRelationDto();
        auditRelationDto.setSrcScriptUuid("srcScriptUuid");
        auditRelationDto.setAuditUserId(0L);
        auditRelationDto.setApplyUserId(0L);
        auditRelationDto.setId(0L);
        auditRelationDto.setScriptTaskId(0L);

        // Configure AuditRelationMapper.selectAuditRelationList(...).
        final AuditRelation auditRelation = new AuditRelation();
        auditRelation.setAuditUserId(0L);
        auditRelation.setApplyUserId(0L);
        auditRelation.setId(0L);
        auditRelation.setScriptTaskId(0L);
        auditRelation.setApprWorkitemId(0L);
        Page<AuditRelation> page = new Page<>();
        page.add(auditRelation);
        when(mockAuditRelationMapper.selectAuditRelationList(any(AuditRelation.class))).thenReturn(page);

        // Run the test
        final PageInfo<AuditRelationDto> result = auditRelationServiceImplUnderTest.selectAuditRelationList(
                auditRelationDto, 0, 0);
        assertNotNull(result);
        // Verify the results
    }


    @Test
    void testInsertAuditRelation() {
        // Setup
        final AuditRelationDto auditRelationDto = new AuditRelationDto();
        auditRelationDto.setSrcScriptUuid("srcScriptUuid");
        auditRelationDto.setAuditUserId(0L);
        auditRelationDto.setApplyUserId(0L);
        auditRelationDto.setId(0L);
        auditRelationDto.setScriptTaskId(0L);

        when(mockAuditRelationMapper.insertAuditRelation(any(AuditRelation.class))).thenReturn(0);

        // Run the test
        final int result = auditRelationServiceImplUnderTest.insertAuditRelation(auditRelationDto);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testUpdateAuditRelation() {
        // Setup
        final AuditRelationDto auditRelationDto = new AuditRelationDto();
        auditRelationDto.setSrcScriptUuid("srcScriptUuid");
        auditRelationDto.setAuditUserId(0L);
        auditRelationDto.setApplyUserId(0L);
        auditRelationDto.setId(0L);
        auditRelationDto.setScriptTaskId(0L);

        when(mockAuditRelationMapper.updateAuditRelation(any(AuditRelation.class))).thenReturn(0);

        // Run the test
        final int result = auditRelationServiceImplUnderTest.updateAuditRelation(auditRelationDto);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testDeleteAuditRelationByIds() {
        // Setup
        when(mockAuditRelationMapper.deleteAuditRelationByIds(any(Long[].class))).thenReturn(0);

        // Run the test
        final int result = auditRelationServiceImplUnderTest.deleteAuditRelationByIds(new Long[]{0L});

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testDeleteAuditRelationById() {
        // Setup
        when(mockAuditRelationMapper.deleteAuditRelationById(0L)).thenReturn(0);

        // Run the test
        final int result = auditRelationServiceImplUnderTest.deleteAuditRelationById(0L);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void selectInfoIdAndSrcScriptUuidByAuditRelationId() {
        // 设置模拟对象的行为，当调用 selectInfoIdAndSrcScriptUuidByAuditRelationId 方法时返回一个预设的对象
        AuditRelation expected = new AuditRelation(); // 假设这是你预期的返回值
        when(mockAuditRelationMapper.selectInfoIdAndSrcScriptUuidByAuditRelationId(1L)).thenReturn(expected);
        // 调用被测试方法
        AuditRelationDto resultDto = auditRelationServiceImplUnderTest.selectInfoIdAndSrcScriptUuidByAuditRelationId(1L);
        assertNotNull(resultDto);
    }

    @Test
    void selectAuditRelationByWorkItemId() {
        AuditRelationDto auditRelationDto = auditRelationServiceImplUnderTest.selectAuditRelationByWorkItemId(1L);
        assertNotNull(auditRelationDto);
    }

    @Test
    void updateAuditRelationByWorkItemId() {
        AuditRelationDto auditRelationDto = new AuditRelationDto();
        auditRelationDto.setId(1L);
        auditRelationDto.setApprWorkitemId(2L);
        assertDoesNotThrow(() -> auditRelationServiceImplUnderTest.updateAuditRelationByWorkItemId(auditRelationDto));
    }
}
