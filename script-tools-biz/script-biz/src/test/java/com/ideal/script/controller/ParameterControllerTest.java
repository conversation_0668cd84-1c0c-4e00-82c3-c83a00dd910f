package com.ideal.script.controller;

import com.ideal.common.dto.R;
import com.ideal.common.util.BeanUtils;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.model.dto.TaskParamsDto;
import com.ideal.script.model.entity.Parameter;
import com.ideal.script.model.entity.TaskParams;
import com.ideal.script.service.IParameterService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * ParameterController单元测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class ParameterControllerTest {

    @Mock
    private IParameterService parameterService;

    @InjectMocks
    private ParameterController parameterController;

    private Parameter parameter;
    private TaskParams taskParams;
    private TaskParamsDto taskParamsDto;
    private List<Parameter> parameterList;
    private List<TaskParams> taskParamsList;
    private List<TaskParamsDto> taskParamsDtoList;

    @BeforeEach
    void setUp() {
        // 初始化Parameter测试数据
        parameter = new Parameter();
        parameter.setId(1L);
        parameter.setSrcScriptUuid("test-uuid-123");
        parameter.setParamName("testParam");
        parameter.setParamType("String");
        parameter.setParamDefaultValue("defaultValue");
        parameter.setParamDesc("测试参数");
        parameter.setParamOrder(1);
        parameter.setParamCheckIid(100L);
        parameter.setScriptParameterManagerId(200L);
        parameter.setCreatorId(10L);
        parameter.setCreatorName("testUser");
        parameter.setCreateTime(new Timestamp(System.currentTimeMillis()));

        parameterList = new ArrayList<>();
        parameterList.add(parameter);

        // 初始化TaskParams测试数据
        taskParams = new TaskParams();
        taskParams.setId(1L);
        taskParams.setScriptTaskId(100L);
        taskParams.setScriptParameterCheckId(200L);
        taskParams.setScriptParameterManagerId(300L);
        taskParams.setType("String");
        taskParams.setValue("testValue");
        taskParams.setDesc("测试任务参数");
        taskParams.setOrder(1);
        taskParams.setStartType(0);
        taskParams.setCreateTime(new Timestamp(System.currentTimeMillis()));

        taskParamsList = new ArrayList<>();
        taskParamsList.add(taskParams);

        // 初始化TaskParamsDto测试数据
        taskParamsDto = new TaskParamsDto();
        taskParamsDto.setId(1L);
        taskParamsDto.setScriptTaskId(100L);
        taskParamsDto.setScriptParameterCheckId(200L);
        taskParamsDto.setScriptParameterManagerId(300L);
        taskParamsDto.setType("String");
        taskParamsDto.setValue("testValue");
        taskParamsDto.setDesc("测试任务参数");
        taskParamsDto.setOrder(1);
        taskParamsDto.setStartType(0);
        taskParamsDto.setCreateTime(new Timestamp(System.currentTimeMillis()));

        taskParamsDtoList = new ArrayList<>();
        taskParamsDtoList.add(taskParamsDto);
    }

    @Test
    @DisplayName("测试根据版本UUID查询参数 - 正常情况")
    void getParameterByUuid_success() {
        // 准备数据
        String versionUuid = "test-uuid-123";

        // Mock方法
        when(parameterService.getParameterByUuid(versionUuid)).thenReturn(parameterList);

        // 执行测试
        R<Object> result = parameterController.getParameterByUuid(versionUuid);

        // 验证结果
        assertNotNull(result);
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        assertEquals(parameterList, result.getData());
        assertEquals(Constants.LIST_SUCCESS, result.getMessage());
        verify(parameterService, times(1)).getParameterByUuid(versionUuid);
    }

    @Test
    @DisplayName("测试根据版本UUID查询参数 - 返回空列表")
    void getParameterByUuid_emptyList() {
        // 准备数据
        String versionUuid = "empty-uuid";
        List<Parameter> emptyList = Collections.emptyList();

        // Mock方法
        when(parameterService.getParameterByUuid(versionUuid)).thenReturn(emptyList);

        // 执行测试
        R<Object> result = parameterController.getParameterByUuid(versionUuid);

        // 验证结果
        assertNotNull(result);
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        assertEquals(emptyList, result.getData());
        assertEquals(Constants.LIST_SUCCESS, result.getMessage());
        verify(parameterService, times(1)).getParameterByUuid(versionUuid);
    }

    @Test
    @DisplayName("测试根据版本UUID查询参数 - 异常情况")
    void getParameterByUuid_exception() {
        // 准备数据
        String versionUuid = "error-uuid";
        String errorMessage = "数据库连接异常";

        // Mock方法抛出异常
        when(parameterService.getParameterByUuid(versionUuid))
                .thenThrow(new RuntimeException(errorMessage));

        // 执行测试
        R<Object> result = parameterController.getParameterByUuid(versionUuid);

        // 验证结果
        assertNotNull(result);
        assertEquals(Constants.REPONSE_STATUS_FAIL_CODE, result.getCode());
        assertEquals(errorMessage, result.getMessage());
        verify(parameterService, times(1)).getParameterByUuid(versionUuid);
    }

    @ParameterizedTest
    @ValueSource(strings = {"uuid-1", "uuid-2", "", "null-uuid"})
    @DisplayName("测试根据版本UUID查询参数 - 参数化测试")
    void getParameterByUuid_parameterized(String versionUuid) {
        // Mock方法
        when(parameterService.getParameterByUuid(anyString())).thenReturn(parameterList);

        // 执行测试
        R<Object> result = parameterController.getParameterByUuid(versionUuid);

        // 验证结果
        assertNotNull(result);
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        assertEquals(parameterList, result.getData());
        assertEquals(Constants.LIST_SUCCESS, result.getMessage());
        verify(parameterService, times(1)).getParameterByUuid(versionUuid);
    }

    @Test
    @DisplayName("测试根据任务ID查询参数 - 正常情况")
    void getParameterByTaskId_success() {
        // 准备数据
        Long taskId = 100L;

        // Mock BeanUtils.copy
        try (MockedStatic<BeanUtils> mockedBeanUtils = mockStatic(BeanUtils.class)) {
            mockedBeanUtils.when(() -> BeanUtils.copy(taskParamsList, TaskParamsDto.class))
                    .thenReturn(taskParamsDtoList);

            // Mock service方法
            when(parameterService.getParameterByTaskId(taskId)).thenReturn(taskParamsList);

            // 执行测试
            R<Object> result = parameterController.getParameterByTaskId(taskId);

            // 验证结果
            assertNotNull(result);
            assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
            assertEquals(taskParamsDtoList, result.getData());
            assertEquals(Constants.LIST_SUCCESS, result.getMessage());
            verify(parameterService, times(1)).getParameterByTaskId(taskId);
            mockedBeanUtils.verify(() -> BeanUtils.copy(taskParamsList, TaskParamsDto.class), times(1));
        }
    }

    @Test
    @DisplayName("测试根据任务ID查询参数 - 返回空列表")
    void getParameterByTaskId_emptyList() {
        // 准备数据
        Long taskId = 999L;
        List<TaskParams> emptyTaskParamsList = Collections.emptyList();
        List<TaskParamsDto> emptyTaskParamsDtoList = Collections.emptyList();

        // Mock BeanUtils.copy
        try (MockedStatic<BeanUtils> mockedBeanUtils = mockStatic(BeanUtils.class)) {
            mockedBeanUtils.when(() -> BeanUtils.copy(emptyTaskParamsList, TaskParamsDto.class))
                    .thenReturn(emptyTaskParamsDtoList);

            // Mock service方法
            when(parameterService.getParameterByTaskId(taskId)).thenReturn(emptyTaskParamsList);

            // 执行测试
            R<Object> result = parameterController.getParameterByTaskId(taskId);

            // 验证结果
            assertNotNull(result);
            assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
            assertEquals(emptyTaskParamsDtoList, result.getData());
            assertEquals(Constants.LIST_SUCCESS, result.getMessage());
            verify(parameterService, times(1)).getParameterByTaskId(taskId);
            mockedBeanUtils.verify(() -> BeanUtils.copy(emptyTaskParamsList, TaskParamsDto.class), times(1));
        }
    }

    @Test
    @DisplayName("测试根据任务ID查询参数 - 异常情况")
    void getParameterByTaskId_exception() {
        // 准备数据
        Long taskId = 100L;
        String errorMessage = "服务异常";

        // Mock service方法抛出异常
        when(parameterService.getParameterByTaskId(taskId))
                .thenThrow(new RuntimeException(errorMessage));

        // 执行测试
        R<Object> result = parameterController.getParameterByTaskId(taskId);

        // 验证结果
        assertNotNull(result);
        assertEquals(Constants.REPONSE_STATUS_FAIL_CODE, result.getCode());
        assertEquals(errorMessage, result.getMessage());
        verify(parameterService, times(1)).getParameterByTaskId(taskId);
    }

    @ParameterizedTest
    @MethodSource("provideTaskIds")
    @DisplayName("测试根据任务ID查询参数 - 参数化测试")
    void getParameterByTaskId_parameterized(Long taskId, List<TaskParams> expectedTaskParams, List<TaskParamsDto> expectedDtos) {
        // Mock BeanUtils.copy
        try (MockedStatic<BeanUtils> mockedBeanUtils = mockStatic(BeanUtils.class)) {
            mockedBeanUtils.when(() -> BeanUtils.copy(any(List.class), eq(TaskParamsDto.class)))
                    .thenReturn(expectedDtos);

            // Mock service方法
            when(parameterService.getParameterByTaskId(anyLong())).thenReturn(expectedTaskParams);

            // 执行测试
            R<Object> result = parameterController.getParameterByTaskId(taskId);

            // 验证结果
            assertNotNull(result);
            assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
            assertEquals(expectedDtos, result.getData());
            assertEquals(Constants.LIST_SUCCESS, result.getMessage());
            verify(parameterService, times(1)).getParameterByTaskId(taskId);
        }
    }

    static Stream<Arguments> provideTaskIds() {
        TaskParams params1 = new TaskParams();
        params1.setId(1L);
        params1.setScriptTaskId(100L);
        params1.setValue("value1");

        TaskParamsDto dto1 = new TaskParamsDto();
        dto1.setId(1L);
        dto1.setScriptTaskId(100L);
        dto1.setValue("value1");

        List<TaskParams> list1 = Collections.singletonList(params1);
        List<TaskParamsDto> dtoList1 = Collections.singletonList(dto1);

        return Stream.of(
                Arguments.of(100L, list1, dtoList1),
                Arguments.of(200L, Collections.emptyList(), Collections.emptyList()),
                Arguments.of(0L, Collections.emptyList(), Collections.emptyList()),
                Arguments.of(-1L, Collections.emptyList(), Collections.emptyList())
        );
    }
}
