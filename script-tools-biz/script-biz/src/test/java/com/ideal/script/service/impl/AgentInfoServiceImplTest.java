package com.ideal.script.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.AgentInfoMapper;
import com.ideal.script.model.bean.TaskBindAgentInfoBean;
import com.ideal.script.model.dto.AgentInfoDto;
import com.ideal.script.model.dto.QueryAgentInfoDto;
import com.ideal.script.model.entity.AgentInfo;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AgentInfoServiceImplTest {

    @Mock
    private AgentInfoMapper mockAgentInfoMapper;
    @InjectMocks
    private AgentInfoServiceImpl agentInfoServiceImplUnderTest;


    @Test
    void testSelectAgentInfoById() {
        // Setup
        final AgentInfoDto expectedResult = new AgentInfoDto();
        expectedResult.setId(0L);
        expectedResult.setSysmAgentInfoId(0L);
        expectedResult.setAgentIp("agentIp");
        expectedResult.setAgentName("agentName");
        expectedResult.setAgentPort(0);

        // Configure AgentInfoMapper.selectAgentInfoById(...).
        final AgentInfo agentInfo = new AgentInfo();
        agentInfo.setId(0L);
        agentInfo.setSysmAgentInfoId(0L);
        agentInfo.setAgentIp("agentIp");
        agentInfo.setAgentName("agentName");
        agentInfo.setAgentPort(0);
        when(mockAgentInfoMapper.selectAgentInfoById(0L)).thenReturn(agentInfo);

        // Run the test
        final AgentInfoDto result = agentInfoServiceImplUnderTest.selectAgentInfoById(0L);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testSelectAgentInfoList() {
        // Setup
        final AgentInfoDto agentInfoDto = new AgentInfoDto();
        agentInfoDto.setId(0L);
        agentInfoDto.setSysmAgentInfoId(0L);
        agentInfoDto.setAgentIp("agentIp");
        agentInfoDto.setAgentName("agentName");
        agentInfoDto.setAgentPort(0);

        // Configure AgentInfoMapper.selectAgentInfoList(...).
        final AgentInfo agentInfo = new AgentInfo();
        agentInfo.setId(0L);
        agentInfo.setSysmAgentInfoId(0L);
        agentInfo.setAgentIp("agentIp");
        agentInfo.setAgentName("agentName");
        agentInfo.setAgentPort(0);
        Page<AgentInfo> page = new Page<>();
        page.add(agentInfo);
        when(mockAgentInfoMapper.selectAgentInfoList(any(AgentInfo.class))).thenReturn(page);

        // Run the test
        final PageInfo<AgentInfoDto> result = agentInfoServiceImplUnderTest.selectAgentInfoList(agentInfoDto, 0, 0);
        // Verify the results
        assertNotNull(result);
    }

    @Test
    void testInsertAgentInfo() {
        // Setup
        final AgentInfoDto agentInfoDto = new AgentInfoDto();
        agentInfoDto.setId(0L);
        agentInfoDto.setSysmAgentInfoId(0L);
        agentInfoDto.setAgentIp("agentIp");
        agentInfoDto.setAgentName("agentName");
        agentInfoDto.setAgentPort(0);

        when(mockAgentInfoMapper.insertAgentInfo(any(AgentInfo.class))).thenReturn(0);

        // Run the test
        final int result = agentInfoServiceImplUnderTest.insertAgentInfo(agentInfoDto);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testUpdateAgentInfo() {
        // Setup
        final AgentInfoDto agentInfoDto = new AgentInfoDto();
        agentInfoDto.setId(0L);
        agentInfoDto.setSysmAgentInfoId(0L);
        agentInfoDto.setAgentIp("agentIp");
        agentInfoDto.setAgentName("agentName");
        agentInfoDto.setAgentPort(0);

        when(mockAgentInfoMapper.updateAgentInfo(any(AgentInfo.class))).thenReturn(0);

        // Run the test
        final int result = agentInfoServiceImplUnderTest.updateAgentInfo(agentInfoDto);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testDeleteAgentInfoByIds() {
        // Setup
        when(mockAgentInfoMapper.deleteAgentInfoByIds(any(Long[].class))).thenReturn(0);

        // Run the test
        final int result = agentInfoServiceImplUnderTest.deleteAgentInfoByIds(new Long[]{0L});

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testDeleteAgentInfoById() {
        // Setup
        when(mockAgentInfoMapper.deleteAgentInfoById(0L)).thenReturn(0);

        // Run the test
        final int result = agentInfoServiceImplUnderTest.deleteAgentInfoById(0L);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testGetTaskBindAgentInfo() {
        // Setup
        final QueryAgentInfoDto queryAgentInfoDto = new QueryAgentInfoDto();
        queryAgentInfoDto.setAgentIp("agentIp");
        queryAgentInfoDto.setAgentName("agentName");
        queryAgentInfoDto.setScriptTaskId(0L);

        // Configure AgentInfoMapper.getTaskBindAgentInfo(...).
        final TaskBindAgentInfoBean agentInfo = new TaskBindAgentInfoBean();
        agentInfo.setId(0L);
        agentInfo.setSysmAgentInfoId(0L);
        agentInfo.setAgentIp("agentIp");
        agentInfo.setAgentName("agentName");
        agentInfo.setAgentPort(0);

        Page<TaskBindAgentInfoBean> page = new Page<>();
        page.add(agentInfo);
        when(mockAgentInfoMapper.getTaskBindAgentInfo(any(AgentInfo.class), eq(0L),eq(null))).thenReturn(page);

        // Run the test
        final PageInfo<AgentInfoDto> result = agentInfoServiceImplUnderTest.getTaskBindAgentInfo(queryAgentInfoDto, 0,
                0);
        assertNotNull(result);
        // Verify the results
    }



    @Test
    void testCheckAgentInfoExists() {
        // Setup
        when(mockAgentInfoMapper.checkAgentInfoExists("agentIp", 0L)).thenReturn(false);

        // Run the test
        final boolean result = agentInfoServiceImplUnderTest.checkAgentInfoExists("agentIp", 0L);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    void testCheckAgentInfoExists_AgentInfoMapperReturnsTrue() {
        // Setup
        when(mockAgentInfoMapper.checkAgentInfoExists("agentIp", 0L)).thenReturn(true);

        // Run the test
        final boolean result = agentInfoServiceImplUnderTest.checkAgentInfoExists("agentIp", 0L);

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    void testSelectAgentInfoByIpAndPort() throws Exception {
        // Setup
        final AgentInfoDto agentInfoDto = new AgentInfoDto();
        agentInfoDto.setId(0L);
        agentInfoDto.setSysmAgentInfoId(0L);
        agentInfoDto.setAgentIp("agentIp");
        agentInfoDto.setAgentName("agentName");
        agentInfoDto.setAgentPort(0);

        // Configure AgentInfoMapper.selectAgentInfoList(...).
        final AgentInfo agentInfo = new AgentInfo();
        agentInfo.setId(0L);
        agentInfo.setSysmAgentInfoId(0L);
        agentInfo.setAgentIp("agentIp");
        agentInfo.setAgentName("agentName");
        agentInfo.setAgentPort(0);
        final List<AgentInfo> agentInfos = Collections.singletonList(agentInfo);
        when(mockAgentInfoMapper.selectAgentInfoList(any(AgentInfo.class))).thenReturn(agentInfos);

        // Run the test
        final AgentInfo result = agentInfoServiceImplUnderTest.selectAgentInfoByIpAndPort(agentInfoDto);
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testSelectAgentInfoByIpAndPort_AgentInfoMapperReturnsNull() {
        // Setup
        final AgentInfoDto agentInfoDto = new AgentInfoDto();
        agentInfoDto.setId(0L);
        agentInfoDto.setSysmAgentInfoId(0L);
        agentInfoDto.setAgentIp("agentIp");
        agentInfoDto.setAgentName("agentName");
        agentInfoDto.setAgentPort(0);

        when(mockAgentInfoMapper.selectAgentInfoList(any(AgentInfo.class))).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> agentInfoServiceImplUnderTest.selectAgentInfoByIpAndPort(agentInfoDto))
                .isInstanceOf(ScriptException.class);
    }

    @Test
    void testSelectAgentInfoByIpAndPort_AgentInfoMapperReturnsNoItems() {
        // Setup
        final AgentInfoDto agentInfoDto = new AgentInfoDto();
        agentInfoDto.setId(0L);
        agentInfoDto.setSysmAgentInfoId(0L);
        agentInfoDto.setAgentIp("agentIp");
        agentInfoDto.setAgentName("agentName");
        agentInfoDto.setAgentPort(0);

        when(mockAgentInfoMapper.selectAgentInfoList(any(AgentInfo.class))).thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> agentInfoServiceImplUnderTest.selectAgentInfoByIpAndPort(agentInfoDto))
                .isInstanceOf(ScriptException.class);
    }

    @Test
    void testSelectAgentInfoByServiceId() {
        // Setup
        final AgentInfoDto agentInfoDto = new AgentInfoDto();
        agentInfoDto.setId(0L);
        agentInfoDto.setSysmAgentInfoId(0L);
        agentInfoDto.setAgentIp("agentIp");
        agentInfoDto.setAgentName("agentName");
        agentInfoDto.setAgentPort(0);
        final List<AgentInfoDto> expectedResult = Collections.singletonList(agentInfoDto);

        // Configure AgentInfoMapper.selectAgentInfoByServiceId(...).
        final AgentInfo agentInfo = new AgentInfo();
        agentInfo.setId(0L);
        agentInfo.setSysmAgentInfoId(0L);
        agentInfo.setAgentIp("agentIp");
        agentInfo.setAgentName("agentName");
        agentInfo.setAgentPort(0);
        final List<AgentInfo> agentInfos = Collections.singletonList(agentInfo);
        when(mockAgentInfoMapper.selectAgentInfoByServiceId(0L,0L)).thenReturn(agentInfos);

        // Run the test
        final List<AgentInfoDto> result = agentInfoServiceImplUnderTest.selectAgentInfoByServiceId(0L,0L);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testSelectAgentInfoByServiceId_AgentInfoMapperReturnsNoItems() {
        // Setup
        when(mockAgentInfoMapper.selectAgentInfoByServiceId(0L,0L)).thenReturn(Collections.emptyList());

        // Run the test
        final List<AgentInfoDto> result = agentInfoServiceImplUnderTest.selectAgentInfoByServiceId(0L,0L);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void getTaskBindAgentInfo() {
        // 模拟输入参数
        QueryAgentInfoDto queryAgentInfoDto = new QueryAgentInfoDto();
        queryAgentInfoDto.setAgentIp("127.0.0.1");
        queryAgentInfoDto.setAgentName("TestAgent");
        queryAgentInfoDto.setScriptTaskId(873784789L);
        Integer pageNum = 1;
        Integer pageSize = 10;

        // 模拟数据库返回结果
//        List<TaskBindAgentInfoBean> agentInfoListFromDb = new ArrayList<>();
//        agentInfoListFromDb.add(new TaskBindAgentInfoBean());

        Page<TaskBindAgentInfoBean> page =  new Page<>();
        TaskBindAgentInfoBean taskBindAgentInfoBean =  new TaskBindAgentInfoBean();
        page.add(taskBindAgentInfoBean);
        when(mockAgentInfoMapper.getTaskBindAgentInfo(any(AgentInfo.class), any(Long.class),eq(null) ))
                .thenReturn(page);

        // 调用被测试方法
        PageInfo<AgentInfoDto> pageInfo = agentInfoServiceImplUnderTest.getTaskBindAgentInfo(queryAgentInfoDto, pageNum, pageSize);
        // 验证返回结果
        //AgentInfoDto agentInfoDto = new AgentInfoDto();会报空指针异常，具体原因不知道
        // 验证是否调用了相应的方法
        verify(mockAgentInfoMapper, times(1)).getTaskBindAgentInfo(any(AgentInfo.class), anyLong(), any());
    }

    @Test
    void getTaskAllAgentInfo() {
        // 准备测试数据
        QueryAgentInfoDto queryAgentInfoDto = new QueryAgentInfoDto();
        queryAgentInfoDto.setAgentIp("127.0.0.1");
        queryAgentInfoDto.setAgentName("Test Agent");
        queryAgentInfoDto.setScriptTaskId(236784267L);
        Integer pageNum = 1;
        Integer pageSize = 10;
        Page<TaskBindAgentInfoBean> page = new Page<>();
        // 添加测试数据到列表中
        // 模拟 agentInfoMapper 的行为
        when(mockAgentInfoMapper.getTaskBindAgentInfo(any(AgentInfo.class), anyLong(), eq("all")))
                .thenReturn(page);

        // 调用方法
        PageInfo<AgentInfoDto> result = agentInfoServiceImplUnderTest.getTaskAllAgentInfo(queryAgentInfoDto, pageNum, pageSize);

        // 验证返回结果
        assertNotNull(result);
        assertEquals(0, result.getSize()); // 假设返回的列表为空
        // 可以根据具体情况进一步验证其他字段和分页信息
    }
}
