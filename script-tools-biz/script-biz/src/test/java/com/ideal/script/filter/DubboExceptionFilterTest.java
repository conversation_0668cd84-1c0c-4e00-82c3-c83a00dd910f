package com.ideal.script.filter;

import com.ideal.common.util.spring.SpringUtil;
import com.ideal.sc.validation.BeanValidator;
import com.ideal.script.exception.ScriptException;
import org.apache.dubbo.common.logger.ErrorTypeAwareLogger;
import org.apache.dubbo.rpc.Invocation;
import org.apache.dubbo.rpc.Invoker;
import org.apache.dubbo.rpc.Result;
import org.apache.dubbo.rpc.RpcContext;
import org.apache.dubbo.rpc.service.GenericService;
import org.apache.dubbo.rpc.support.RpcUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.Path;
import java.util.HashSet;
import java.util.Set;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * DubboExceptionFilter 单元测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class DubboExceptionFilterTest {

    @Mock
    private Invoker<Object> mockInvoker;

    @Mock
    private Invocation mockInvocation;

    @Mock
    private Result mockResult;

    @Mock
    private BeanValidator mockBeanValidator;

    @Mock
    private ErrorTypeAwareLogger mockLogger;

    @Mock
    private RpcContext mockRpcContext;

    private DubboExceptionFilter dubboExceptionFilterUnderTest;

    @BeforeEach
    void setUp() {
        dubboExceptionFilterUnderTest = new DubboExceptionFilter();
    }

    @Test
    @DisplayName("测试处理ConstraintViolationException异常")
    void testOnResponse_ConstraintViolationException() {
        // Setup
        final ConstraintViolationException constraintViolationException = mock(ConstraintViolationException.class);
        final Set<ConstraintViolation<?>> constraintViolations = new HashSet<>();
        final ConstraintViolation<?> constraintViolation = mock(ConstraintViolation.class);
        final Path path = mock(Path.class);
        constraintViolations.add(constraintViolation);

        when(mockResult.hasException()).thenReturn(true);
        when(mockResult.getException()).thenReturn(constraintViolationException);
        when(mockInvoker.getInterface()).thenReturn(Object.class);
        when(mockInvocation.getMethodName()).thenReturn("testMethod");
        when(constraintViolationException.getConstraintViolations()).thenReturn(constraintViolations);

        try (MockedStatic<SpringUtil> mockedSpringUtil = mockStatic(SpringUtil.class)) {
            mockedSpringUtil.when(() -> SpringUtil.getBean(BeanValidator.class)).thenReturn(mockBeanValidator);
            doReturn("validation error message").when(mockBeanValidator).extractMessages(any());

            // Run the test
            dubboExceptionFilterUnderTest.onResponse(mockResult, mockInvoker, mockInvocation);

            // Verify the results
            verify(mockResult).setException(any(ScriptException.class));
        }
    }

    @Test
    @DisplayName("测试处理其他异常类型")
    void testOnResponse_OtherException() {
        // Setup
        final RuntimeException runtimeException = new RuntimeException("test exception");
        when(mockResult.hasException()).thenReturn(true);
        when(mockResult.getException()).thenReturn(runtimeException);
        when(mockInvoker.getInterface()).thenReturn(Object.class);

        // Run the test
        dubboExceptionFilterUnderTest.onResponse(mockResult, mockInvoker, mockInvocation);

        // Verify the results - 应该调用父类方法
        // 由于父类方法是protected，我们无法直接验证，但可以确认没有抛出异常
    }

    @Test
    @DisplayName("测试没有异常的情况")
    void testOnResponse_NoException() {
        // Setup
        when(mockResult.hasException()).thenReturn(false);

        // Run the test
        dubboExceptionFilterUnderTest.onResponse(mockResult, mockInvoker, mockInvocation);

        // Verify the results - 应该不执行任何异常处理逻辑
    }

    @Test
    @DisplayName("测试空约束违规集合的情况")
    void testOnResponse_EmptyConstraintViolations() {
        // Setup
        final ConstraintViolationException constraintViolationException = mock(ConstraintViolationException.class);
        final Set<ConstraintViolation<?>> constraintViolations = new HashSet<>();

        when(mockResult.hasException()).thenReturn(true);
        when(mockResult.getException()).thenReturn(constraintViolationException);
        when(mockInvoker.getInterface()).thenReturn(Object.class);
        when(mockInvocation.getMethodName()).thenReturn("testMethod");
        when(constraintViolationException.getConstraintViolations()).thenReturn(constraintViolations);

        try (MockedStatic<SpringUtil> mockedSpringUtil = mockStatic(SpringUtil.class)) {
            mockedSpringUtil.when(() -> SpringUtil.getBean(BeanValidator.class)).thenReturn(mockBeanValidator);
            doReturn("").when(mockBeanValidator).extractMessages(any());

            // Run the test
            dubboExceptionFilterUnderTest.onResponse(mockResult, mockInvoker, mockInvocation);

            // Verify the results
            verify(mockResult).setException(any(ScriptException.class));
        }
    }

    @Test
    @DisplayName("测试catch异常的情况")
    void testOnResponse_CatchException() {
        // Setup
        final ConstraintViolationException constraintViolationException = mock(ConstraintViolationException.class);
        final Set<ConstraintViolation<?>> constraintViolations = new HashSet<>();
        final ConstraintViolation<?> constraintViolation = mock(ConstraintViolation.class);
        constraintViolations.add(constraintViolation);

        when(mockResult.hasException()).thenReturn(true);
        when(mockResult.getException()).thenReturn(constraintViolationException);
        when(mockInvoker.getInterface()).thenReturn(Object.class);
        when(constraintViolationException.getConstraintViolations()).thenReturn(constraintViolations);

        try (MockedStatic<SpringUtil> mockedSpringUtil = mockStatic(SpringUtil.class);
             MockedStatic<RpcContext> mockedRpcContext = mockStatic(RpcContext.class);
             MockedStatic<RpcUtils> mockedRpcUtils = mockStatic(RpcUtils.class)) {
            
            // 模拟SpringUtil.getBean抛出异常，触发catch块
            mockedSpringUtil.when(() -> SpringUtil.getBean(BeanValidator.class)).thenThrow(new RuntimeException("BeanValidator error"));
            mockedRpcContext.when(RpcContext::getServiceContext).thenReturn(mock(org.apache.dubbo.rpc.RpcServiceContext.class));
            mockedRpcUtils.when(() -> RpcUtils.getMethodName(any())).thenReturn("testMethod");

            // Run the test
            dubboExceptionFilterUnderTest.onResponse(mockResult, mockInvoker, mockInvocation);

            // Verify the results - 应该捕获异常并记录日志，但不抛出异常
            // 由于catch块只是记录日志，我们无法直接验证，但可以确认没有抛出异常
        }
    }

}