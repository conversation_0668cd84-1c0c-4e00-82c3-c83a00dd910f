package com.ideal.script.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ideal.script.common.util.BatchDataUtil;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.TaskParamsMapper;
import com.ideal.script.model.bean.TaskHisParamsBean;
import com.ideal.script.model.dto.*;
import com.ideal.script.model.entity.TaskParams;
import com.ideal.script.service.IParameterCheckService;
import com.ideal.script.service.IParameterService;
import org.apache.ibatis.session.SqlSession;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TaskParamsServiceImplTest {

    @Mock
    private BatchDataUtil mockBatchDataUtil;
    @Mock
    private TaskParamsMapper mockTaskParamsMapper;
    @Mock
    private IParameterService mockParameterService;
    @Mock
    private IParameterCheckService parameterCheckService;

    private TaskParamsServiceImpl taskParamsServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        taskParamsServiceImplUnderTest = new TaskParamsServiceImpl(mockBatchDataUtil, mockTaskParamsMapper,
                mockParameterService,parameterCheckService);
    }

    @Test
    void testSelectTaskParamsById() {
        // Setup
        // Configure TaskParamsMapper.selectTaskParamsById(...).
        final TaskParams taskParams = new TaskParams();
        taskParams.setId(0L);
        taskParams.setScriptTaskId(0L);
        taskParams.setScriptParameterCheckId(0L);
        taskParams.setScriptParameterManagerId(0L);
        taskParams.setType("type");
        when(mockTaskParamsMapper.selectTaskParamsById(0L)).thenReturn(taskParams);

        // Run the test
        final TaskParamsDto result = taskParamsServiceImplUnderTest.selectTaskParamsById(0L);
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testSelectTaskParamsList() {
        // Setup
        final TaskParamsDto taskParamsDto = new TaskParamsDto();
        taskParamsDto.setId(0L);
        taskParamsDto.setScriptTaskId(0L);
        taskParamsDto.setScriptParameterCheckId(0L);
        taskParamsDto.setScriptParameterManagerId(0L);
        taskParamsDto.setType("paramType");
        taskParamsDto.setValue("paramDefaultValue");
        taskParamsDto.setDesc("desc");
        taskParamsDto.setOrder(0);
        taskParamsDto.setStartType(0);

        // Configure TaskParamsMapper.selectTaskParamsList(...).
        final TaskParams taskParams1 = new TaskParams();
        taskParams1.setId(0L);
        taskParams1.setScriptTaskId(0L);
        taskParams1.setScriptParameterCheckId(0L);
        taskParams1.setScriptParameterManagerId(0L);
        taskParams1.setType("type");
        Page<TaskParams> page = new Page<>();
        page.add(taskParams1);
        when(mockTaskParamsMapper.selectTaskParamsList(any(TaskParams.class))).thenReturn(page);

        // Run the test
        final PageInfo<TaskParamsDto> result = taskParamsServiceImplUnderTest.selectTaskParamsList(taskParamsDto, 0, 0);
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testInsertTaskParams() {
        // Setup
        final TaskParamsDto taskParamsDto = new TaskParamsDto();
        taskParamsDto.setId(0L);
        taskParamsDto.setScriptTaskId(0L);
        taskParamsDto.setScriptParameterCheckId(0L);
        taskParamsDto.setScriptParameterManagerId(0L);
        taskParamsDto.setType("paramType");
        taskParamsDto.setValue("paramDefaultValue");
        taskParamsDto.setDesc("desc");
        taskParamsDto.setOrder(0);
        taskParamsDto.setStartType(0);

        when(mockTaskParamsMapper.insertTaskParams(any(TaskParams.class))).thenReturn(0);

        // Run the test
        final int result = taskParamsServiceImplUnderTest.insertTaskParams(taskParamsDto);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testUpdateTaskParams() {
        // Setup
        final TaskParamsDto taskParamsDto = new TaskParamsDto();
        taskParamsDto.setId(0L);
        taskParamsDto.setScriptTaskId(0L);
        taskParamsDto.setScriptParameterCheckId(0L);
        taskParamsDto.setScriptParameterManagerId(0L);
        taskParamsDto.setType("paramType");
        taskParamsDto.setValue("paramDefaultValue");
        taskParamsDto.setDesc("desc");
        taskParamsDto.setOrder(0);
        taskParamsDto.setStartType(0);

        when(mockTaskParamsMapper.updateTaskParams(any(TaskParams.class))).thenReturn(0);

        // Run the test
        final int result = taskParamsServiceImplUnderTest.updateTaskParams(taskParamsDto);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testDeleteTaskParamsByIds() {
        // Setup
        when(mockTaskParamsMapper.deleteTaskParamsByIds(any(Long[].class))).thenReturn(0);

        // Run the test
        final int result = taskParamsServiceImplUnderTest.deleteTaskParamsByIds(new Long[]{0L});

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testDeleteTaskParamsById() {
        // Setup
        when(mockTaskParamsMapper.deleteTaskParamsById(0L)).thenReturn(0);

        // Run the test
        final int result = taskParamsServiceImplUnderTest.deleteTaskParamsById(0L);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testSelectHisParam() {
        // Setup
        // Configure TaskParamsMapper.selectHisParam(...).
        final TaskHisParamsBean taskHisParamsBean = new TaskHisParamsBean();
        taskHisParamsBean.setSrcScriptUuid("srcScriptUuid");
        taskHisParamsBean.setTaskParamsId(0L);
        taskHisParamsBean.setType("type");
        taskHisParamsBean.setValue("value");
        taskHisParamsBean.setDesc("desc");
        final List<TaskHisParamsBean> taskHisParamsBeans = Arrays.asList(taskHisParamsBean);
        when(mockTaskParamsMapper.selectHisParam("srcScriptUuid")).thenReturn(taskHisParamsBeans);

        // Run the test
        final List<TaskHisParamsDto> result = taskParamsServiceImplUnderTest.selectHisParam("srcScriptUuid");
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testSelectHisParam_TaskParamsMapperReturnsNoItems() {
        // Setup
        when(mockTaskParamsMapper.selectHisParam("srcScriptUuid")).thenReturn(Collections.emptyList());

        // Run the test
        final List<TaskHisParamsDto> result = taskParamsServiceImplUnderTest.selectHisParam("srcScriptUuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testSelectTaskParamsByServiceId() {
        // Setup
        // Configure TaskParamsMapper.selectTaskParamsByServiceId(...).
        final TaskParams taskParams1 = new TaskParams();
        taskParams1.setId(0L);
        taskParams1.setScriptTaskId(0L);
        taskParams1.setScriptParameterCheckId(0L);
        taskParams1.setScriptParameterManagerId(0L);
        taskParams1.setType("type");
        final List<TaskParams> taskParams = Arrays.asList(taskParams1);
        when(mockTaskParamsMapper.selectTaskParamsByServiceId(0L,0L)).thenReturn(taskParams);

        // Run the test
        final List<TaskParamsDto> result = taskParamsServiceImplUnderTest.selectTaskParamsByServiceId(0L,0L);
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testSelectTaskParamsByServiceId_TaskParamsMapperReturnsNoItems() {
        // Setup
        when(mockTaskParamsMapper.selectTaskParamsByServiceId(0L,0L)).thenReturn(Collections.emptyList());

        // Run the test
        final List<TaskParamsDto> result = taskParamsServiceImplUnderTest.selectTaskParamsByServiceId(0L,0L);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @ParameterizedTest
    @NullSource
    @ValueSource(longs = {1L})
    void testSaveTaskParams(Long id) throws Exception {
        // Setup
        final ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        final ParameterDto parameterDto = new ParameterDto();
        parameterDto.setId(id);
        parameterDto.setParamType("paramType");
        parameterDto.setParamDefaultValue("paramDefaultValue");
        parameterDto.setParamDesc("desc");
        parameterDto.setParamOrder(0);
        parameterDto.setParamCheckIid(0L);
        parameterDto.setScriptParameterManagerId(0L);
        scriptExecAuditDto.setParams(Collections.singletonList(parameterDto));

        final TaskDto taskInfo = new TaskDto();
        taskInfo.setScriptTaskSource(0);
        taskInfo.setStartType(0);
        taskInfo.setReadyToExecute(0);
        taskInfo.setId(0L);
        taskInfo.setSrcScriptUuid("srcScriptUuid");

        final SqlSession sqlSession = mock(SqlSession.class);

        // Configure IParameterService.selectParameterById(...).
        final ParameterDto parameterDto1 = new ParameterDto();
        parameterDto1.setId(0L);
        parameterDto1.setParamType("paramType");
        parameterDto1.setParamDefaultValue("paramDefaultValue");
        parameterDto1.setParamDesc("desc");
        parameterDto1.setParamOrder(0);
        parameterDto1.setParamCheckIid(0L);
        parameterDto1.setScriptParameterManagerId(0L);
        if(id != null) {
            when(mockParameterService.selectParameterById(1L)).thenReturn(parameterDto1);
        }

        // Run the test
        taskParamsServiceImplUnderTest.saveTaskParams(scriptExecAuditDto, taskInfo, sqlSession);

        // Verify the results
        if(id != null){
            verify(mockParameterService).selectParameterById(1L);
        }

    }

    @Test
    void validateParameterList() throws ScriptException {
        // 创建模拟的参数DTO列表
        ParameterDto parameter1 = new ParameterDto();
        parameter1.setParamDefaultValue("value1");
        parameter1.setParamCheckIid(1L);

        ParameterDto parameter2 = new ParameterDto();
        parameter2.setParamDefaultValue("value2");
        parameter2.setParamCheckIid(2L);

        List<ParameterDto> parameterDtoList = Arrays.asList(parameter1, parameter2);

        // 创建模拟的校验规则DTO
        ParameterCheckDto checkRule1 = new ParameterCheckDto();
        checkRule1.setId(1L);
        checkRule1.setCheckRule("rule1");

        ParameterCheckDto checkRule2 = new ParameterCheckDto();
        checkRule2.setId(2L);
        checkRule2.setCheckRule("rule2");

        // 设置模拟行为
        when(parameterCheckService.selectParameterCheckById(1L)).thenReturn(checkRule1);
        when(parameterCheckService.selectParameterCheckById(2L)).thenReturn(checkRule2);

        // 调用被测试的方法
        String result = taskParamsServiceImplUnderTest.validateParameterList(parameterDtoList);

        // 验证结果
        assertEquals("参数 value1 校验未通过，规则 rule1, 参数 value2 校验未通过，规则 rule2", result);
        // 验证是否调用了正确的方法
        verify(parameterCheckService, times(1)).selectParameterCheckById(1L);
        verify(parameterCheckService, times(1)).selectParameterCheckById(2L);
    }



    @Test
    void validateParameterList_validateParam_exception() throws ScriptException {
        // 创建模拟的参数DTO列表
        ParameterDto parameter1 = new ParameterDto();
        parameter1.setParamDefaultValue("value1");
        parameter1.setParamCheckIid(1L);


        List<ParameterDto> parameterDtoList = Collections.singletonList(parameter1);

        // 创建模拟的校验规则DTO
        ParameterCheckDto checkRule1 = new ParameterCheckDto();
        checkRule1.setId(1L);
        checkRule1.setCheckRule("(");
        // 设置模拟行为
        when(parameterCheckService.selectParameterCheckById(1L)).thenReturn(checkRule1);

        // 调用被测试的方法
        assertThrows(ScriptException.class,()->{
            taskParamsServiceImplUnderTest.validateParameterList(parameterDtoList);
        });

    }



    @Test
    void validateParameterList_WithInvalidParameters() throws ScriptException {
        // 创建模拟的参数DTO列表
        ParameterDto parameter1 = new ParameterDto();
        parameter1.setParamDefaultValue("value1");
        parameter1.setParamCheckIid(1L);

        ParameterDto parameter2 = new ParameterDto();
        parameter2.setParamDefaultValue("value2");
        parameter2.setParamCheckIid(2L);

        List<ParameterDto> parameterDtoList = Arrays.asList(parameter1, parameter2);

        // 创建模拟的校验规则DTO
        ParameterCheckDto checkRule1 = new ParameterCheckDto();
        checkRule1.setId(1L);
        checkRule1.setCheckRule("value1");

        ParameterCheckDto checkRule2 = new ParameterCheckDto();
        checkRule2.setId(2L);
        checkRule2.setCheckRule("value2");

        // 设置模拟行为
        when(parameterCheckService.selectParameterCheckById(1L)).thenReturn(checkRule1);
        when(parameterCheckService.selectParameterCheckById(2L)).thenReturn(checkRule2);

        // 调用被测试的方法
        String result = taskParamsServiceImplUnderTest.validateParameterList(parameterDtoList);

        // 验证结果
        assertNull(result);
        // 验证是否调用了正确的方法
        verify(parameterCheckService, times(1)).selectParameterCheckById(1L);
        verify(parameterCheckService, times(1)).selectParameterCheckById(2L);
    }
}
