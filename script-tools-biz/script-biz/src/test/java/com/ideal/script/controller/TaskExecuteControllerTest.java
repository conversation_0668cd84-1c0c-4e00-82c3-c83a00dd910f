package com.ideal.script.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.common.util.spring.MessageUtil;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.ScriptTestExecutionDto;
import com.ideal.script.model.dto.TaskExecuteDto;
import com.ideal.script.model.dto.TaskExecuteQueryDto;
import com.ideal.script.model.dto.TaskStartDto;
import com.ideal.script.service.ITaskExecuteService;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.RedisTemplate;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TaskExecuteControllerTest {

    static MockedStatic<MessageUtil> mockedMessageUtil;

    @InjectMocks
    private TaskExecuteController taskExecuteController;

    @Mock
    private ITaskExecuteService taskExecuteService;

    @Mock
    private RedisTemplate<String, Object> redisTemplate;




    @BeforeAll
    static void setUp() {
        // Mock the static method MessageUtil.message
        mockedMessageUtil = mockStatic(MessageUtil.class);
        mockedMessageUtil.when(() -> MessageUtil.message(anyString())).thenReturn("Mocked Message");
    }
    @AfterAll
    static void tearDown() {
        // 在所有测试方法执行后，关闭 Mock
        mockedMessageUtil.close();
    }

    @Test
    @DisplayName("Test listTaskReadyToExecute")
    void testListTaskReadyToExecute() {
        TableQueryDto<TaskExecuteQueryDto> tableQueryDto = new TableQueryDto<>();
        when(taskExecuteService.selectTaskReadyToExecuteList(any(), anyInt(), anyInt(), any()))
                .thenReturn(new PageInfo<>(Collections.emptyList()));

        R<PageInfo<TaskExecuteDto>> response = taskExecuteController.listTaskReadyToExecute(tableQueryDto);

        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, response.getCode());
        verify(taskExecuteService, times(1)).selectTaskReadyToExecuteList(any(), anyInt(), anyInt(), any());
    }

    @Test
    @DisplayName("Test listTimeTasks")
    void testListTimeTasks() {
        TableQueryDto<TaskExecuteQueryDto> tableQueryDto = new TableQueryDto<>();
        when(taskExecuteService.selectTimeTasks(any(), anyInt(), anyInt(), any()))
                .thenReturn(new PageInfo<>(Collections.emptyList()));

        R<PageInfo<TaskExecuteDto>> response = taskExecuteController.listTimeTasks(tableQueryDto);

        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, response.getCode());
        verify(taskExecuteService, times(1)).selectTimeTasks(any(), anyInt(), anyInt(), any());
    }

    @Test
    @DisplayName("Test timeTaskSwitch")
    void testTimeTaskSwitch() throws ScriptException {
        long scriptTaskId = 1L;
        int state = 1;
        R<Object> response = taskExecuteController.timeTaskSwitch(scriptTaskId, state);

        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, response.getCode());
        verify(taskExecuteService, times(1)).timeTaskSwitch(scriptTaskId, state);
    }

    @Test
    @DisplayName("Test timeTaskKill")
    void testTimeTaskKill() throws ScriptException {
        long scriptTaskId = 1L;
        R<Object> response = taskExecuteController.timeTaskKill(scriptTaskId);

        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, response.getCode());
        verify(taskExecuteService, times(1)).timeTaskKill(scriptTaskId);
    }

    @Test
    @DisplayName("Test updateTimeTaskCron")
    void testUpdateTimeTaskCron() throws ScriptException {
        TaskExecuteDto taskExecuteDto = new TaskExecuteDto();
        R<Object> response = taskExecuteController.updateTimeTaskCron(taskExecuteDto);

        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, response.getCode());
        verify(taskExecuteService, times(1)).updateTimeTaskCron(taskExecuteDto);
    }

    @Test
    @DisplayName("Test cancelTask")
    void testCancelTask() throws ScriptException {
        long scriptTaskId = 1L;
        R<Object> response = taskExecuteController.cancelTask(scriptTaskId);

        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, response.getCode());
        verify(taskExecuteService, times(1)).cancelTask(scriptTaskId);
    }

    @Test
    @DisplayName("Test listRunningScriptTasks")
    void testListRunningScriptTasks() {
        TableQueryDto<TaskExecuteQueryDto> tableQueryDto = new TableQueryDto<>();
        when(taskExecuteService.listRunningScriptTasks(any(), anyInt(), anyInt(), any()))
                .thenReturn(new PageInfo<>(Collections.emptyList()));

        R<PageInfo<TaskExecuteDto>> response = taskExecuteController.listRunningScriptTasks(tableQueryDto);

        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, response.getCode());
        verify(taskExecuteService, times(1)).listRunningScriptTasks(any(), anyInt(), anyInt(), any());
    }

    @Test
    @DisplayName("Test listCompleteScriptTasks")
    void testListCompleteScriptTasks() {
        TableQueryDto<TaskExecuteQueryDto> tableQueryDto = new TableQueryDto<>();
        when(taskExecuteService.listCompleteScriptTasks(any(), anyInt(), anyInt(), any()))
                .thenReturn(new PageInfo<>(Collections.emptyList()));

        R<PageInfo<TaskExecuteDto>> response = taskExecuteController.listCompleteScriptTasks(tableQueryDto);

        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, response.getCode());
        verify(taskExecuteService, times(1)).listCompleteScriptTasks(any(), anyInt(), anyInt(), any());
    }

    @Test
    @DisplayName("Test scriptTaskStart")
    void testScriptTaskStart() throws ScriptException {
        TaskStartDto taskStartDto = new TaskStartDto();
        when(taskExecuteService.scriptTaskStartFormApply(any(), any())).thenReturn(1L);

        R<Object> response = taskExecuteController.scriptTaskStart(taskStartDto);

        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, response.getCode());
        verify(taskExecuteService, times(1)).scriptTaskStartFormApply(any(), any());
    }

    @Test
    @DisplayName("Test getRealTimeOutPutMessage")
    void testGetRealTimeOutPutMessage() {
        Long taskRuntimeId = 1L;
        when(taskExecuteService.getRealTimeOutPutMessage(taskRuntimeId)).thenReturn("Output");

        R<Object> response = taskExecuteController.getRealTimeOutPutMessage(taskRuntimeId);

        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, response.getCode());
        verify(taskExecuteService, times(1)).getRealTimeOutPutMessage(taskRuntimeId);
    }

    @Test
    @DisplayName("Test scriptTestExecution")
    void testScriptTestExecution() throws ScriptException {
        ScriptTestExecutionDto testExecutionDto = new ScriptTestExecutionDto();
        when(taskExecuteService.scriptTestExecution(any(), any())).thenReturn(1L);

        R<Object> response = taskExecuteController.scriptTestExecution(testExecutionDto);

        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, response.getCode());
        verify(taskExecuteService, times(1)).scriptTestExecution(any(), any());
    }

    @Test
    @DisplayName("Test exportExcel")
    void testExportExcel() {
        HttpServletResponse response = mock(HttpServletResponse.class);
        doNothing().when(taskExecuteService).exportExcel(anyList(), any());

        taskExecuteController.exportExcel(Collections.singletonList(1L), response);

        verify(taskExecuteService, times(1)).exportExcel(anyList(), any());
    }

    @Test
    @DisplayName("Test retryScriptServiceShell - 正常情况")
    void testRetryScriptServiceShell() throws ScriptException {
        Long id = 1L;
        Long taskInstanceId = 2L;
        doNothing().when(taskExecuteService).retryScriptServiceShell(eq(id), eq(taskInstanceId), any());

        R<Object> response = taskExecuteController.retryScriptServiceShell(id, taskInstanceId);

        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, response.getCode());
        verify(taskExecuteService, times(1)).retryScriptServiceShell(eq(id), eq(taskInstanceId), any());
    }

    @Test
    @DisplayName("Test retryScriptServiceShell - 异常情况")
    void testRetryScriptServiceShellException() throws ScriptException {
        Long id = 1L;
        Long taskInstanceId = 2L;
        doThrow(new ScriptException("error")).when(taskExecuteService).retryScriptServiceShell(eq(id), eq(taskInstanceId), any());
        R<Object> response = taskExecuteController.retryScriptServiceShell(id, taskInstanceId);
        assertEquals(Constants.REPONSE_STATUS_FAIL_CODE, response.getCode());
    }

    @Test
    @DisplayName("Test batchRetryScriptServiceShell - 正常情况")
    void testBatchRetryScriptServiceShell() throws ScriptException {
        Long[] ids = {1L, 2L};
        Long taskInstanceId = 3L;
        doNothing().when(taskExecuteService).retryScriptServiceShell(anyLong(), eq(taskInstanceId), any());

        R<Object> response = taskExecuteController.batchRetryScriptServiceShell(ids, taskInstanceId);

        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, response.getCode());
        verify(taskExecuteService, times(2)).retryScriptServiceShell(anyLong(), eq(taskInstanceId), any());
    }

    @Test
    @DisplayName("Test scriptShellKill - 正常情况")
    void testScriptShellKill() throws ScriptException {
        Long taskInstanceId = 1L;
        Long[] runTimeIds = {2L, 3L};
        doNothing().when(taskExecuteService).scriptShellKillByRunTimeIds(taskInstanceId, runTimeIds);

        R<Object> response = taskExecuteController.scriptShellKill(taskInstanceId, runTimeIds);

        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, response.getCode());
        verify(taskExecuteService, times(1)).scriptShellKillByRunTimeIds(taskInstanceId, runTimeIds);
    }

    @Test
    @DisplayName("Test skipScriptShell - 正常情况")
    void testSkipScriptShell() throws ScriptException {
        Long taskInstanceId = 1L;
        Long[] runTimeIds = {2L, 3L};
        doNothing().when(taskExecuteService).skipScriptShell(taskInstanceId, runTimeIds);

        R<Object> response = taskExecuteController.skipScriptShell(taskInstanceId, runTimeIds);

        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, response.getCode());
        verify(taskExecuteService, times(1)).skipScriptShell(taskInstanceId, runTimeIds);
    }

    @Test
    @DisplayName("Test stopTask - 正常情况")
    void testStopTask() throws ScriptException {
        Long[] longs = {1L};

        doNothing().when(taskExecuteService).stopTask(longs);

        R<Object> response = taskExecuteController.stopTask(longs);

        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, response.getCode());
        verify(taskExecuteService, times(1)).stopTask(longs);
    }

    @Test
    @DisplayName("Test exportAgentHisExcel - 正常情况")
    void testExportAgentHisExcel() throws Exception {
        HttpServletResponse response = mock(HttpServletResponse.class);
        List<Long> taskInstanceIds = Collections.singletonList(1L);
        doNothing().when(taskExecuteService).exportAgentHisExcel(taskInstanceIds, response);

        taskExecuteController.exportAgentHisExcel(taskInstanceIds, response);

        verify(taskExecuteService, times(1)).exportAgentHisExcel(taskInstanceIds, response);
    }

    @Test
    @DisplayName("Test timeTaskSwitch - 异常情况")
    void testTimeTaskSwitchException() throws ScriptException {
        long scriptTaskId = 1L;
        int state = 1;
        doThrow(new ScriptException("error")).when(taskExecuteService).timeTaskSwitch(scriptTaskId, state);
        
        R<Object> response = taskExecuteController.timeTaskSwitch(scriptTaskId, state);
        
        assertEquals(Constants.REPONSE_STATUS_FAIL_CODE, response.getCode());
    }

    @Test
    @DisplayName("Test timeTaskKill - 异常情况")
    void testTimeTaskKillException() throws ScriptException {
        long scriptTaskId = 1L;
        doThrow(new ScriptException("error")).when(taskExecuteService).timeTaskKill(scriptTaskId);
        
        R<Object> response = taskExecuteController.timeTaskKill(scriptTaskId);
        
        assertEquals(Constants.REPONSE_STATUS_FAIL_CODE, response.getCode());
    }

    @Test
    @DisplayName("Test updateTimeTaskCron - 异常情况")
    void testUpdateTimeTaskCronException() throws ScriptException {
        TaskExecuteDto taskExecuteDto = new TaskExecuteDto();
        doThrow(new ScriptException("error")).when(taskExecuteService).updateTimeTaskCron(taskExecuteDto);
        
        R<Object> response = taskExecuteController.updateTimeTaskCron(taskExecuteDto);
        
        assertEquals(Constants.REPONSE_STATUS_FAIL_CODE, response.getCode());
    }

    @Test
    @DisplayName("Test scriptTaskStart - 异常情况")
    void testScriptTaskStartException() throws ScriptException {
        TaskStartDto taskStartDto = new TaskStartDto();
        doThrow(new ScriptException("error")).when(taskExecuteService).scriptTaskStartFormApply(any(), any());
        
        R<Object> response = taskExecuteController.scriptTaskStart(taskStartDto);
        
        assertEquals(Constants.REPONSE_STATUS_FAIL_CODE, response.getCode());
    }


    @Test
    @DisplayName("Test scriptTestExecution - 异常情况")
    void testScriptTestExecutionException() throws ScriptException {
        ScriptTestExecutionDto testExecutionDto = new ScriptTestExecutionDto();
        doThrow(new ScriptException("error")).when(taskExecuteService).scriptTestExecution(any(), any());

        R<Object> response = taskExecuteController.scriptTestExecution(testExecutionDto);

        assertEquals(Constants.REPONSE_STATUS_FAIL_CODE, response.getCode());
    }



    @Test
    @DisplayName("Test batchRetryScriptServiceShell - 异常情况")
    void testBatchRetryScriptServiceShellException() throws ScriptException {
        Long[] ids = {1L, 2L};
        Long taskInstanceId = 3L;
        doThrow(new ScriptException("error")).when(taskExecuteService).retryScriptServiceShell(anyLong(), eq(taskInstanceId), any());

        R<Object> response = taskExecuteController.batchRetryScriptServiceShell(ids, taskInstanceId);

        assertEquals(Constants.REPONSE_STATUS_FAIL_CODE, response.getCode());
    }

    @Test
    @DisplayName("Test scriptShellKill - 异常情况")
    void testScriptShellKillException() throws ScriptException {
        Long taskInstanceId = 1L;
        Long[] runTimeIds = {2L, 3L};
        doThrow(new ScriptException("error")).when(taskExecuteService).scriptShellKillByRunTimeIds(taskInstanceId, runTimeIds);

        R<Object> response = taskExecuteController.scriptShellKill(taskInstanceId, runTimeIds);

        assertEquals(Constants.REPONSE_STATUS_FAIL_CODE, response.getCode());
    }

    @Test
    @DisplayName("Test skipScriptShell - 异常情况")
    void testSkipScriptShellException() throws ScriptException {
        Long taskInstanceId = 1L;
        Long[] runTimeIds = {2L, 3L};
        doThrow(new ScriptException("error")).when(taskExecuteService).skipScriptShell(taskInstanceId, runTimeIds);

        R<Object> response = taskExecuteController.skipScriptShell(taskInstanceId, runTimeIds);

        assertEquals(Constants.REPONSE_STATUS_FAIL_CODE, response.getCode());
    }

    @Test
    @DisplayName("Test exportAgentHisExcel - 异常情况")
    void testExportAgentHisExcelException() throws IOException {
        HttpServletResponse response = mock(HttpServletResponse.class);
        List<Long> taskInstanceIds = Collections.singletonList(1L);
        doThrow(new RuntimeException("error")).when(taskExecuteService).exportAgentHisExcel(taskInstanceIds, response);

        taskExecuteController.exportAgentHisExcel(taskInstanceIds, response);

        verify(taskExecuteService, times(1)).exportAgentHisExcel(taskInstanceIds, response);
    }

    @Test
    @DisplayName("Test cancelTask - 异常情况")
    void testCancelTaskException() throws ScriptException {
        long scriptTaskId = 1L;
        doThrow(new ScriptException("error")).when(taskExecuteService).cancelTask(scriptTaskId);
        
        R<Object> response = taskExecuteController.cancelTask(scriptTaskId);
        
        assertEquals(Constants.REPONSE_STATUS_FAIL_CODE, response.getCode());
    }

    @Test
    @DisplayName("Test checkBefore - 正常情况")
    void testCheckBefore() {
        long taskId = 1L;
        when(redisTemplate.hasKey(anyString())).thenReturn(true);

        R<?> response = taskExecuteController.checkBefore(taskId);

        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, response.getCode());
        assertTrue((Boolean) response.getData());
    }

}
