package com.ideal.script.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ideal.script.mapper.VariablePublishMapper;
import com.ideal.script.model.dto.VarAndFuncForEditDto;
import com.ideal.script.model.dto.VariablePublishDto;
import com.ideal.script.model.entity.VarAndFuncForEdit;
import com.ideal.script.model.entity.VariablePublish;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class VariablePublishServiceImplTest {

    @Mock
    private VariablePublishMapper mockVariablePublishMapper;

    @InjectMocks
    private VariablePublishServiceImpl variablePublishServiceImplUnderTest;


    @Test
    void testSelectVariablePublishById() {
        // Setup
        // Configure VariablePublishMapper.selectVariablePublishById(...).
        final VariablePublish variablePublish = new VariablePublish();
        variablePublish.setKeyword("keyword");
        variablePublish.setBindState("bindState");
        variablePublish.setId(1L);
        variablePublish.setScriptVariableClassId(1L);
        variablePublish.setName("name");
        when(mockVariablePublishMapper.selectVariablePublishById(1L)).thenReturn(variablePublish);

        // Run the test
        final VariablePublishDto result = variablePublishServiceImplUnderTest.selectVariablePublishById(1L);

        assertNotNull(result);
        System.out.println(result);
        // Verify the results
        Mockito.verify(mockVariablePublishMapper,times(1)).selectVariablePublishById(1L);
    }

    @Test
    void testSelectVariablePublishList() {
        // Setup
        final VariablePublishDto variablePublishDto = new VariablePublishDto();
        variablePublishDto.setId(1L);
        variablePublishDto.setScriptVariableClassId(1L);
        variablePublishDto.setName("name");
        variablePublishDto.setType(1L);
        variablePublishDto.setValue("value");

        // Configure VariablePublishMapper.selectVariablePublishList(...).
        final VariablePublish variablePublish = new VariablePublish();
        variablePublish.setKeyword("keyword");
        variablePublish.setBindState("bindState");
        variablePublish.setId(1L);
        variablePublish.setScriptVariableClassId(1L);
        variablePublish.setName("name");
        Page<VariablePublish> page = new Page<>();
        page.add(variablePublish);
        when(mockVariablePublishMapper.selectVariablePublishList(any(VariablePublish.class)))
                .thenReturn(page);

        // Run the test
        final PageInfo<VariablePublishDto> result = variablePublishServiceImplUnderTest.selectVariablePublishList(
                variablePublishDto, 1, 50);

        assertNotNull(result);
        System.out.println(result);
        // Verify the results
        Mockito.verify(mockVariablePublishMapper,times(1)).selectVariablePublishList(any(VariablePublish.class));
    }


    @Test
    void testInsertVariableP() {
        // Setup
        final VariablePublishDto variablePublishDto = new VariablePublishDto();
        variablePublishDto.setId(1L);
        variablePublishDto.setScriptVariableClassId(1L);
        variablePublishDto.setName("name");
        variablePublishDto.setType(1L);
        variablePublishDto.setValue("value");

        // Run the test
        variablePublishServiceImplUnderTest.insertVariableP(variablePublishDto);

        // Verify the results
        verify(mockVariablePublishMapper).insertVariableP(any(VariablePublish.class));
    }

    @Test
    void testUpdateVariableP() {
        // Setup
        final VariablePublishDto variablePublishDto = new VariablePublishDto();
        variablePublishDto.setId(1L);
        variablePublishDto.setScriptVariableClassId(1L);
        variablePublishDto.setName("name");
        variablePublishDto.setType(1L);
        variablePublishDto.setValue("value");

        // Run the test
        variablePublishServiceImplUnderTest.updateVariableP(variablePublishDto);

        // Verify the results
        verify(mockVariablePublishMapper).updateVariableP(any(VariablePublish.class));
    }

    @Test
    void testDeleteVariablePublishByIds() {
        // Setup
        // Run the test
        variablePublishServiceImplUnderTest.deleteVariablePublishByIds(new Long[]{1L,2L});

        // Verify the results
        verify(mockVariablePublishMapper).deleteVariablePublishByIds(any(Long[].class));
    }

    @Test
    void testDeleteVariablePublishById() {
        // Setup
        when(mockVariablePublishMapper.deleteVariablePublishById(1L)).thenReturn(1);

        // Run the test
        final int result = variablePublishServiceImplUnderTest.deleteVariablePublishById(1L);

        // Verify the results
        assertThat(result).isEqualTo(1);
    }

    @Test
    void testSelectVariablePublishListForEdit() {
        // Setup
        final VarAndFuncForEditDto varAndFuncForEditDto = new VarAndFuncForEditDto();
        varAndFuncForEditDto.setBindState(1);
        varAndFuncForEditDto.setBindIds(new Long[]{1L});
        varAndFuncForEditDto.setKeyword("keyword");

        // Configure VariablePublishMapper.selectVariablePublishListForEdit(...).
        final VariablePublish variablePublish = new VariablePublish();
        variablePublish.setKeyword("keyword");
        variablePublish.setBindState("bindState");
        variablePublish.setId(1L);
        variablePublish.setScriptVariableClassId(1L);
        variablePublish.setName("name");
        Page<VariablePublish> page = new Page<>();
        page.add(variablePublish);
        when(mockVariablePublishMapper.selectVariablePublishListForEdit(any(VarAndFuncForEdit.class)))
                .thenReturn(page);

        // Run the test
        final PageInfo<VariablePublishDto> result = variablePublishServiceImplUnderTest.selectVariablePublishListForEdit(
                varAndFuncForEditDto, 1, 50);

        assertNotNull(result);
        System.out.println(result);
        // Verify the results
        Mockito.verify(mockVariablePublishMapper,times(1)).selectVariablePublishListForEdit(any(VarAndFuncForEdit.class));
    }

}
