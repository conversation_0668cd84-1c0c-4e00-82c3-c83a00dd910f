package com.ideal.script.aop;

import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.mapper.TaskMapper;
import com.ideal.script.model.dto.TaskStartDto;
import com.ideal.script.model.entity.Task;
import com.ideal.script.service.IScriptTaskStatePublisher;
import org.aspectj.lang.JoinPoint;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * ScriptTaskStartOrStopAspect的单元测试类
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class ScriptTaskStartOrStopAspectTest {

    @Mock
    private IScriptTaskStatePublisher scriptTaskStatePublisher;

    @Mock
    private TaskMapper taskMapper;

    @Mock
    private JoinPoint joinPoint;

    @InjectMocks
    private ScriptTaskStartOrStopAspect scriptTaskStartOrStopAspect;

    private TaskStartDto taskStartDto;
    private Task task;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        taskStartDto = new TaskStartDto();
        taskStartDto.setTaskName("测试任务");
        taskStartDto.setPublishDesc("测试描述");
        taskStartDto.setStartType(0);
        taskStartDto.setScriptTaskSource(Enums.TaskSource.TASK_APPLICATION.getValue());
        taskStartDto.setFirstBatch(true);
        taskStartDto.setRetry(false);
        taskStartDto.setIscriptTaskInstanceId(1001L);

        task = new Task();
        task.setTaskName("测试任务");
        task.setPublishDesc("测试描述");
    }

    @Test
    @DisplayName("测试构造函数 - 验证依赖注入和日志输出")
    void testConstructor() {
        // 由于Logger是静态final字段，在类加载时就已经初始化，
        // 我们无法直接mock静态Logger实例，但可以验证构造函数正常执行

        // 验证@InjectMocks注解的对象被正确注入
        assertNotNull(scriptTaskStartOrStopAspect);

        // 验证构造函数能正常执行，不抛出异常
        ScriptTaskStartOrStopAspect aspect = new ScriptTaskStartOrStopAspect(scriptTaskStatePublisher, taskMapper);
        assertNotNull(aspect);

        // 验证依赖对象被正确设置（通过反射验证私有字段）
        try {
            java.lang.reflect.Field publisherField = ScriptTaskStartOrStopAspect.class.getDeclaredField("scriptTaskStatePublisher");
            publisherField.setAccessible(true);
            assertSame(scriptTaskStatePublisher, publisherField.get(aspect));

            java.lang.reflect.Field mapperField = ScriptTaskStartOrStopAspect.class.getDeclaredField("taskMapper");
            mapperField.setAccessible(true);
            assertSame(taskMapper, mapperField.get(aspect));
        } catch (Exception e) {
            fail("反射访问字段失败: " + e.getMessage());
        }
    }

    /**
     * 提供afterScriptTaskStart方法的测试参数
     */
    static Stream<Object[]> provideAfterScriptTaskStartTestCases() {
        return Stream.of(
            // 参数为空的情况
            new Object[]{"参数为空", new Object[0], null, false},
            // 参数长度为0的情况
            new Object[]{"参数长度为0", new Object[0], null, false},
            // 参数不是TaskStartDto类型的情况
            new Object[]{"参数不是TaskStartDto类型", new Object[]{"非TaskStartDto对象"}, null, false},
            // startType不等于0的情况
            new Object[]{"startType不等于0", createTaskStartDtoArray(1, 1, true, false), 1001L, false},
            // scriptTaskSource等于SCRIPT_TESTING的情况
            new Object[]{"scriptTaskSource等于SCRIPT_TESTING", createTaskStartDtoArray(0, 2, true, false), 1001L, false},
            // firstBatch为false的情况
            new Object[]{"firstBatch为false", createTaskStartDtoArray(0, 1, false, false), 1001L, false},
            // retry为true的情况
            new Object[]{"retry为true", createTaskStartDtoArray(0, 1, true, true), 1001L, false},
            // 所有条件都满足的情况
            new Object[]{"所有条件都满足", createTaskStartDtoArray(0, 1, true, false), 1001L, true}
        );
    }

    /**
     * 创建TaskStartDto数组的辅助方法
     */
    static Object[] createTaskStartDtoArray(Integer startType, Integer scriptTaskSource, boolean firstBatch, Boolean retry) {
        TaskStartDto dto = new TaskStartDto();
        dto.setTaskName("测试任务");
        dto.setPublishDesc("测试描述");
        dto.setStartType(startType);
        dto.setScriptTaskSource(scriptTaskSource);
        dto.setFirstBatch(firstBatch);
        dto.setRetry(retry);
        return new Object[]{dto};
    }

    @ParameterizedTest
    @MethodSource("provideAfterScriptTaskStartTestCases")
    @DisplayName("测试afterScriptTaskStart方法的所有分支条件")
    void testAfterScriptTaskStart(String testCase, Object[] args, Long taskInstanceId, boolean shouldPublish) {
        // 准备Mock对象
        when(joinPoint.getArgs()).thenReturn(args);

        // 执行测试方法
        scriptTaskStartOrStopAspect.afterScriptTaskStart(joinPoint, taskInstanceId);

        // 验证结果
        if (shouldPublish) {
            // 验证publish方法被调用
            verify(scriptTaskStatePublisher).publish(any(TaskStartDto.class), eq(Enums.TaskInstanceStatus.RUNNING));
        } else {
            // 验证publish方法没有被调用
            verify(scriptTaskStatePublisher, never()).publish(any(TaskStartDto.class), any(Enums.TaskInstanceStatus.class));
        }
    }

    @Test
    @DisplayName("测试afterScriptTaskStop方法 - 参数为空或长度为0")
    void testAfterScriptTaskStop_EmptyArgs() {
        // 测试参数为空的情况
        when(joinPoint.getArgs()).thenReturn(new Object[0]);

        scriptTaskStartOrStopAspect.afterScriptTaskStop(joinPoint);

        // 验证taskMapper和scriptTaskStatePublisher都没有被调用
        verify(taskMapper, never()).selectTaskNameAndPublishDescByTaskInstanceId(anyLong());
        verify(scriptTaskStatePublisher, never()).publish(any(TaskStartDto.class), any(Enums.TaskInstanceStatus.class));
    }

    @Test
    @DisplayName("测试afterScriptTaskStop方法 - 参数不是Long类型")
    void testAfterScriptTaskStop_NonLongArg() {
        // 测试参数不是Long类型的情况
        when(joinPoint.getArgs()).thenReturn(new Object[]{"非Long类型参数"});

        scriptTaskStartOrStopAspect.afterScriptTaskStop(joinPoint);

        // 验证taskMapper和scriptTaskStatePublisher都没有被调用
        verify(taskMapper, never()).selectTaskNameAndPublishDescByTaskInstanceId(anyLong());
        verify(scriptTaskStatePublisher, never()).publish(any(TaskStartDto.class), any(Enums.TaskInstanceStatus.class));
    }

    @Test
    @DisplayName("测试afterScriptTaskStop方法 - 正常流程")
    void testAfterScriptTaskStop_NormalFlow() {
        // 准备测试数据
        Long taskInstanceId = 1001L;
        when(joinPoint.getArgs()).thenReturn(new Object[]{new Long[]{taskInstanceId}});

        // Mock taskMapper返回的Task对象
        Task mockTask = new Task();
        mockTask.setTaskName("测试任务");
        mockTask.setPublishDesc("测试描述");
        //脚本服务化来源
        mockTask.setStartType(0);
        //任务申请
        mockTask.setScriptTaskSource(1);
        doReturn(mockTask).when(taskMapper).selectTaskNameAndPublishDescByTaskInstanceId(taskInstanceId);

        // 执行测试方法
        scriptTaskStartOrStopAspect.afterScriptTaskStop(joinPoint);

        // 验证taskMapper被调用
        verify(taskMapper).selectTaskNameAndPublishDescByTaskInstanceId(taskInstanceId);

        // 验证scriptTaskStatePublisher.publish被调用，参数为正确的TaskStartDto和TERMINATED状态
        verify(scriptTaskStatePublisher).publish(argThat(dto ->
            "测试任务".equals(dto.getTaskName()) &&
            "测试描述".equals(dto.getPublishDesc()) &&
            taskInstanceId.equals(dto.getIscriptTaskInstanceId())
        ), eq(Enums.TaskInstanceStatus.TERMINATED));
    }
}
