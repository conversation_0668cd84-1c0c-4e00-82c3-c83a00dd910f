package com.ideal.script.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.sc.util.ValidationUtils;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.DangerCmdDto;
import com.ideal.script.service.IDangerCmdService;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * DangerCmdController单元测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class DangerCmdControllerTest {

    @Mock
    private IDangerCmdService dangerCmdService;

    @InjectMocks
    private DangerCmdController dangerCmdController;

    private static MockedStatic<ValidationUtils> validationUtilsMock;

    private TableQueryDto<DangerCmdDto> tableQueryDto;
    private DangerCmdDto dangerCmdDto;
    private PageInfo<DangerCmdDto> pageInfo;

    @BeforeAll
    static void setUpStatic() {
        // Mock ValidationUtils静态方法
        validationUtilsMock = mockStatic(ValidationUtils.class);
        validationUtilsMock.when(() -> ValidationUtils.customFailResult(anyString(), anyString()))
                .thenAnswer(invocation -> R.fail(ValidationUtils.RESPONSE_VALIDATE_CODE, 
                            invocation.getArgument(0), invocation.getArgument(1)));
    }

    @AfterAll
    static void tearDownStatic() {
        if (validationUtilsMock != null) {
            validationUtilsMock.close();
        }
    }

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        dangerCmdDto = new DangerCmdDto();
        dangerCmdDto.setId(1L);
        dangerCmdDto.setScriptCmd("rm -rf /");
        dangerCmdDto.setWhiteCommand(0);
        dangerCmdDto.setScriptType("sh");
        dangerCmdDto.setScriptCmdLevel(1);
        dangerCmdDto.setScriptCmdRemark("危险删除命令");
        dangerCmdDto.setHecktype(1);
        dangerCmdDto.setCreatorId(1L);
        dangerCmdDto.setCreatorName("测试用户");
        dangerCmdDto.setUpdatorId(1L);
        dangerCmdDto.setUpdatorName("测试用户");
        dangerCmdDto.setCreateTime(new Timestamp(System.currentTimeMillis()));
        dangerCmdDto.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        dangerCmdDto.setScriptLabel("高危,删除");
        dangerCmdDto.setCategoryId(1L);
        dangerCmdDto.setDangerCmdBindCategory(true);

        tableQueryDto = new TableQueryDto<>();
        tableQueryDto.setQueryParam(dangerCmdDto);
        tableQueryDto.setPageNum(1);
        tableQueryDto.setPageSize(10);

        List<DangerCmdDto> dangerCmdList = new ArrayList<>();
        dangerCmdList.add(dangerCmdDto);
        pageInfo = new PageInfo<>(dangerCmdList);
        pageInfo.setTotal(1);
        pageInfo.setPages(1);
    }

    @Test
    @DisplayName("测试查询关键命令列表 - 正常情况")
    void listDangerCmd_success() {
        // Mock方法
        when(dangerCmdService.selectDangerCmdList(any(DangerCmdDto.class), anyInt(), anyInt()))
                .thenReturn(pageInfo);

        // 执行测试
        R<PageInfo<DangerCmdDto>> result = dangerCmdController.listDangerCmd(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals(pageInfo, result.getData());
        assertEquals("list.success", result.getMessage());
        verify(dangerCmdService, times(1)).selectDangerCmdList(dangerCmdDto, 1, 10);
    }

    @Test
    @DisplayName("测试查询关键命令列表 - 返回空列表")
    void listDangerCmd_emptyList() {
        // 准备空列表数据
        PageInfo<DangerCmdDto> emptyPageInfo = new PageInfo<>(Collections.emptyList());
        emptyPageInfo.setTotal(0);
        emptyPageInfo.setPages(0);

        // Mock方法
        when(dangerCmdService.selectDangerCmdList(any(DangerCmdDto.class), anyInt(), anyInt()))
                .thenReturn(emptyPageInfo);

        // 执行测试
        R<PageInfo<DangerCmdDto>> result = dangerCmdController.listDangerCmd(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals(emptyPageInfo, result.getData());
        assertEquals("list.success", result.getMessage());
        assertEquals(0, result.getData().getTotal());
        verify(dangerCmdService, times(1)).selectDangerCmdList(dangerCmdDto, 1, 10);
    }

    @Test
    @DisplayName("测试查询关键命令列表 - null查询参数")
    void listDangerCmd_nullQueryParam() {
        // 准备null查询参数
        TableQueryDto<DangerCmdDto> nullParamTableQuery = new TableQueryDto<>();
        nullParamTableQuery.setQueryParam(null);
        nullParamTableQuery.setPageNum(1);
        nullParamTableQuery.setPageSize(10);

        // Mock方法
        when(dangerCmdService.selectDangerCmdList(any(), anyInt(), anyInt())).thenReturn(pageInfo);

        // 执行测试
        R<PageInfo<DangerCmdDto>> result = dangerCmdController.listDangerCmd(nullParamTableQuery);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals(pageInfo, result.getData());
        assertEquals("list.success", result.getMessage());
        verify(dangerCmdService, times(1)).selectDangerCmdList(null, 1, 10);
    }

    @ParameterizedTest
    @ValueSource(ints = {1, 2, 5, 10, 20})
    @DisplayName("测试查询关键命令列表 - 不同页码大小")
    void listDangerCmd_differentPageSizes(int pageSize) {
        // 准备数据
        tableQueryDto.setPageSize(pageSize);

        // Mock方法
        when(dangerCmdService.selectDangerCmdList(any(DangerCmdDto.class), anyInt(), anyInt()))
                .thenReturn(pageInfo);

        // 执行测试
        R<PageInfo<DangerCmdDto>> result = dangerCmdController.listDangerCmd(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals(pageInfo, result.getData());
        assertEquals("list.success", result.getMessage());
        verify(dangerCmdService, times(1)).selectDangerCmdList(dangerCmdDto, 1, pageSize);
    }

    @Test
    @DisplayName("测试新增关键命令 - 正常情况")
    void saveDangerCmd_success() throws Exception {
        // Mock方法
        when(dangerCmdService.insertDangerCmd(any(DangerCmdDto.class))).thenReturn(1);

        // 执行测试
        R<Object> result = dangerCmdController.saveDangerCmd(dangerCmdDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("", result.getData());
        assertEquals("save.success", result.getMessage());
        verify(dangerCmdService, times(1)).insertDangerCmd(dangerCmdDto);
    }

    @Test
    @DisplayName("测试新增关键命令 - ScriptException异常")
    void saveDangerCmd_scriptException() throws Exception {
        // Mock方法抛出ScriptException
        when(dangerCmdService.insertDangerCmd(any(DangerCmdDto.class)))
                .thenThrow(new ScriptException("duplicate.dangerCmd"));

        // 执行测试
        R<Object> result = dangerCmdController.saveDangerCmd(dangerCmdDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ValidationUtils.RESPONSE_VALIDATE_CODE, result.getCode());
        verify(dangerCmdService, times(1)).insertDangerCmd(dangerCmdDto);
        // 验证ValidationUtils.customFailResult被调用
        validationUtilsMock.verify(() -> ValidationUtils.customFailResult("scriptCmd", "duplicate.dangerCmd"));
    }

    @Test
    @DisplayName("测试修改关键命令 - 正常情况")
    void updateDangerCmd_success() throws Exception {
        // Mock方法
        when(dangerCmdService.updateDangerCmd(any(DangerCmdDto.class))).thenReturn(1);

        // 执行测试
        R<Object> result = dangerCmdController.updateDangerCmd(dangerCmdDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("", result.getData());
        assertEquals("update.success", result.getMessage());
        verify(dangerCmdService, times(1)).updateDangerCmd(dangerCmdDto);
    }

    @Test
    @DisplayName("测试修改关键命令 - 通用Exception异常")
    void updateDangerCmd_exception() throws Exception {
        // Mock方法抛出Exception
        when(dangerCmdService.updateDangerCmd(any(DangerCmdDto.class)))
                .thenThrow(new RuntimeException("数据库连接异常"));

        // 执行测试
        R<Object> result = dangerCmdController.updateDangerCmd(dangerCmdDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ValidationUtils.RESPONSE_VALIDATE_CODE, result.getCode());
        verify(dangerCmdService, times(1)).updateDangerCmd(dangerCmdDto);
        // 验证ValidationUtils.customFailResult被调用
        validationUtilsMock.verify(() -> ValidationUtils.customFailResult("scriptCmd", "数据库连接异常"));
    }

    @Test
    @DisplayName("测试删除关键命令 - 正常情况")
    void removeDangerCmd_success() {
        // 准备测试数据
        Long[] ids = {1L, 2L, 3L};

        // Mock方法
        when(dangerCmdService.deleteDangerCmdByIds(any(Long[].class))).thenReturn(3);

        // 执行测试
        R<Object> result = dangerCmdController.removeDangerCmd(ids);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("remove.success", result.getMessage());
        assertEquals(null, result.getData());
        verify(dangerCmdService, times(1)).deleteDangerCmdByIds(ids);
    }

    @ParameterizedTest
    @MethodSource("provideDangerCmdDtoData")
    @DisplayName("测试新增关键命令 - 不同数据场景")
    void saveDangerCmd_differentData(DangerCmdDto testDto, String description) throws Exception {
        // Mock方法
        when(dangerCmdService.insertDangerCmd(any(DangerCmdDto.class))).thenReturn(1);

        // 执行测试
        R<Object> result = dangerCmdController.saveDangerCmd(testDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("", result.getData());
        assertEquals("save.success", result.getMessage());
        verify(dangerCmdService, times(1)).insertDangerCmd(testDto);
    }

    static Stream<Arguments> provideDangerCmdDtoData() {
        // 场景1：Shell脚本高危命令
        DangerCmdDto dto1 = new DangerCmdDto();
        dto1.setScriptCmd("rm -rf /");
        dto1.setScriptType("sh");
        dto1.setScriptCmdLevel(1);
        dto1.setScriptCmdRemark("删除根目录");

        // 场景2：Python脚本提醒命令
        DangerCmdDto dto2 = new DangerCmdDto();
        dto2.setScriptCmd("os.system('format c:')");
        dto2.setScriptType("py");
        dto2.setScriptCmdLevel(0);
        dto2.setScriptCmdRemark("格式化C盘");

        // 场景3：PowerShell脚本屏蔽命令
        DangerCmdDto dto3 = new DangerCmdDto();
        dto3.setScriptCmd("Remove-Item -Recurse -Force C:\\");
        dto3.setScriptType("ps1");
        dto3.setScriptCmdLevel(1);
        dto3.setScriptCmdRemark("删除C盘所有文件");

        // 场景4：白名单命令
        DangerCmdDto dto4 = new DangerCmdDto();
        dto4.setScriptCmd("ls -la");
        dto4.setScriptType("sh");
        dto4.setScriptCmdLevel(0);
        dto4.setWhiteCommand(1);
        dto4.setScriptCmdRemark("列出文件详情");

        return Stream.of(
                Arguments.of(dto1, "Shell脚本高危命令"),
                Arguments.of(dto2, "Python脚本提醒命令"),
                Arguments.of(dto3, "PowerShell脚本屏蔽命令"),
                Arguments.of(dto4, "白名单命令")
        );
    }
}
