package com.ideal.script.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.ParameterCheckDto;
import com.ideal.script.service.IParameterCheckService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class ParameterCheckControllerTest {

    @Mock
    private IParameterCheckService mockParameterCheckService;

    private ParameterCheckController parameterCheckControllerUnderTest;

    @BeforeEach
    void setUp() {
        parameterCheckControllerUnderTest = new ParameterCheckController(mockParameterCheckService);
    }

    @Test
    @DisplayName("测试保存参数校验-成功")
    void testSaveParameterCheck() throws Exception {
        // Setup
        final ParameterCheckDto parameterCheckDto = new ParameterCheckDto();
        parameterCheckDto.setId(0L);
        parameterCheckDto.setRuleName("ruleName");
        parameterCheckDto.setCheckRule("checkRule");
        parameterCheckDto.setRuleDes("ruleDes");
        parameterCheckDto.setCreatorId(0L);

        // Run the test
        final R<Object> result = parameterCheckControllerUnderTest.saveParameterCheck(parameterCheckDto);

        // Verify the results
        verify(mockParameterCheckService).insertParameterCheck(any(ParameterCheckDto.class));
    }

    @Test
    @DisplayName("测试保存参数校验-异常")
    void testSaveParameterCheck_IParameterCheckServiceThrowsScriptException() throws Exception {
        // Setup
        final ParameterCheckDto parameterCheckDto = new ParameterCheckDto();
        parameterCheckDto.setId(0L);
        parameterCheckDto.setRuleName("ruleName");
        parameterCheckDto.setCheckRule("checkRule");
        parameterCheckDto.setRuleDes("ruleDes");
        parameterCheckDto.setCreatorId(0L);

        doThrow(new ScriptException("Error")).when(mockParameterCheckService).insertParameterCheck(
                any(ParameterCheckDto.class));

        try {
            // Run the test
            parameterCheckControllerUnderTest.saveParameterCheck(parameterCheckDto);
        } catch (Exception e) {
            // 异常被捕获并处理，这里不需要额外断言
        }

        // Verify service was called
        verify(mockParameterCheckService).insertParameterCheck(any(ParameterCheckDto.class));
    }

    @Test
    @DisplayName("测试更新参数校验-成功")
    void testUpdateParameterCheck() throws Exception {
        // Setup
        final ParameterCheckDto parameterCheckDto = new ParameterCheckDto();
        parameterCheckDto.setId(0L);
        parameterCheckDto.setRuleName("ruleName");
        parameterCheckDto.setCheckRule("checkRule");
        parameterCheckDto.setRuleDes("ruleDes");
        parameterCheckDto.setCreatorId(0L);

        // Run the test
        final R<Object> result = parameterCheckControllerUnderTest.updateParameterCheck(parameterCheckDto);

        // Verify the results
        verify(mockParameterCheckService).updateParameterCheck(any(ParameterCheckDto.class));
    }

    @Test
    @DisplayName("测试更新参数校验-异常")
    void testUpdateParameterCheck_IParameterCheckServiceThrowsScriptException() throws Exception {
        // Setup
        final ParameterCheckDto parameterCheckDto = new ParameterCheckDto();
        parameterCheckDto.setId(0L);
        parameterCheckDto.setRuleName("ruleName");
        parameterCheckDto.setCheckRule("checkRule");
        parameterCheckDto.setRuleDes("ruleDes");
        parameterCheckDto.setCreatorId(0L);

        doThrow(new ScriptException("Error")).when(mockParameterCheckService).updateParameterCheck(
                any(ParameterCheckDto.class));

        try {
            // Run the test
            parameterCheckControllerUnderTest.updateParameterCheck(parameterCheckDto);
        } catch (Exception e) {
            // 异常被捕获并处理，这里不需要额外断言
        }

        // Verify service was called
        verify(mockParameterCheckService).updateParameterCheck(any(ParameterCheckDto.class));
    }

    @Test
    @DisplayName("测试查询参数校验列表-有数据")
    void testListParameterCheck() {
        // Setup
        final TableQueryDto<ParameterCheckDto> tableQueryDTO = new TableQueryDto<>();
        tableQueryDTO.setPageNum(0);
        tableQueryDTO.setPageSize(10);
        final ParameterCheckDto parameterCheckDto = new ParameterCheckDto();
        parameterCheckDto.setId(0L);
        parameterCheckDto.setRuleName("ruleName");
        tableQueryDTO.setQueryParam(parameterCheckDto);

        // Configure IParameterCheckService.selectParameterCheckList(...).
        final ParameterCheckDto parameterCheckDto1 = new ParameterCheckDto();
        parameterCheckDto1.setId(0L);
        parameterCheckDto1.setRuleName("ruleName");
        parameterCheckDto1.setCheckRule("checkRule");
        parameterCheckDto1.setRuleDes("ruleDes");
        parameterCheckDto1.setCreatorId(0L);
        final PageInfo<ParameterCheckDto> parameterCheckDtoPageInfo = new PageInfo<>(Arrays.asList(parameterCheckDto1),
                0);
        doReturn(parameterCheckDtoPageInfo).when(mockParameterCheckService).selectParameterCheckList(any(ParameterCheckDto.class), anyInt(), anyInt());

        // Run the test
        final R<PageInfo<ParameterCheckDto>> result = parameterCheckControllerUnderTest.listParameterCheck(
                tableQueryDTO);

        // Verify the results
    }

    @Test
    @DisplayName("测试查询参数校验列表-空数据")
    void testListParameterCheck_IParameterCheckServiceReturnsNoItem() {
        // Setup
        final TableQueryDto<ParameterCheckDto> tableQueryDTO = new TableQueryDto<>();
        tableQueryDTO.setPageNum(0);
        tableQueryDTO.setPageSize(10);
        final ParameterCheckDto parameterCheckDto = new ParameterCheckDto();
        parameterCheckDto.setId(0L);
        parameterCheckDto.setRuleName("ruleName");
        tableQueryDTO.setQueryParam(parameterCheckDto);

        doReturn(PageInfo.emptyPageInfo()).when(mockParameterCheckService).selectParameterCheckList(any(ParameterCheckDto.class), anyInt(), anyInt());

        // Run the test
        final R<PageInfo<ParameterCheckDto>> result = parameterCheckControllerUnderTest.listParameterCheck(
                tableQueryDTO);

        // Verify the results
    }

    @Test
    @DisplayName("测试删除参数校验")
    void testRemoveParameterCheck() {
        // Setup
        // Run the test
        final R<Object> result = parameterCheckControllerUnderTest.removeParameterCheck(new Long[]{0L});

        // Verify the results
        verify(mockParameterCheckService).deleteParameterCheckByIds(any(Long[].class));
    }
}
