package com.ideal.script.service.impl;

import com.ideal.script.model.dto.AgentInfoDto;
import com.ideal.script.model.entity.AgentInfo;
import com.ideal.script.service.IAgentInfoService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * AsyncAgentQueryService 单元测试
 *
 */
@ExtendWith(MockitoExtension.class)
class AsyncAgentQueryServiceTest {

    @Mock
    private IAgentInfoService mockAgentInfoService;

    @InjectMocks
    private AsyncAgentQueryService asyncAgentQueryService;

    private List<AgentInfoDto> testAgentDtos;
    private List<AgentInfo> testExistingAgents;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        AgentInfoDto dto1 = new AgentInfoDto();
        dto1.setAgentIp("***********");
        dto1.setAgentPort(8080);
        dto1.setExecUserName("user1");

        AgentInfoDto dto2 = new AgentInfoDto();
        dto2.setAgentIp("***********");
        dto2.setAgentPort(8080);
        dto2.setExecUserName("user2");

        testAgentDtos = Arrays.asList(dto1, dto2);

        // 准备已存在的Agent数据
        AgentInfo existingAgent = new AgentInfo();
        existingAgent.setId(100L);
        existingAgent.setAgentIp("***********");
        existingAgent.setAgentPort(8080);

        testExistingAgents = Collections.singletonList(existingAgent);
    }

    @Test
    @DisplayName("测试异步查询成功场景")
    void testQueryAgentsBatchAsync_Success() throws ExecutionException, InterruptedException {
        // Mock: 批量查询返回已存在的agent
        when(mockAgentInfoService.selectAgentInfoByIpAndPort(any(List.class)))
                .thenReturn(testExistingAgents);

        // 执行异步查询
        CompletableFuture<List<AgentInfo>> future =
                asyncAgentQueryService.queryAgentsBatchAsync(testAgentDtos);

        // 等待结果
        List<AgentInfo> result = future.get();

        // 验证结果
        assertNotNull(result);
        assertEquals(testExistingAgents, result);
        assertEquals(1, result.size());
    }

    @Test
    @DisplayName("测试异步查询异常场景")
    void testQueryAgentsBatchAsync_Exception() {
        // Mock: 批量查询抛出异常
        when(mockAgentInfoService.selectAgentInfoByIpAndPort(any(List.class)))
                .thenThrow(new RuntimeException("Database connection failed"));

        // 执行异步查询
        CompletableFuture<List<AgentInfo>> future =
                asyncAgentQueryService.queryAgentsBatchAsync(testAgentDtos);

        // 验证异常会被传播
        ExecutionException exception = assertThrows(ExecutionException.class, future::get);

        // 验证异常原因
        assertInstanceOf(RuntimeException.class, exception.getCause());
        assertTrue(exception.getCause().getMessage().contains("Database connection failed"));
    }

    @Test
    @DisplayName("测试空Agent列表场景")
    void testQueryAgentsBatchAsync_EmptyList() throws ExecutionException, InterruptedException {
        // 准备空列表
        List<AgentInfoDto> emptyList = Collections.emptyList();

        // Mock: 批量查询返回空列表
        when(mockAgentInfoService.selectAgentInfoByIpAndPort(any(List.class)))
                .thenReturn(Collections.emptyList());

        // 执行异步查询
        CompletableFuture<List<AgentInfo>> future =
                asyncAgentQueryService.queryAgentsBatchAsync(emptyList);

        // 等待结果
        List<AgentInfo> result = future.get();

        // 验证结果
        assertNotNull(result);
        assertEquals(Collections.emptyList(), result);
        assertTrue(result.isEmpty());
    }


}
