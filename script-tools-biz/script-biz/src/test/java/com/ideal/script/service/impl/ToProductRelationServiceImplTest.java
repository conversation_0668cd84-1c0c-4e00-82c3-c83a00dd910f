package com.ideal.script.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ideal.script.mapper.ToProductRelationMapper;
import com.ideal.script.model.dto.ToProductRelationDto;
import com.ideal.script.model.dto.ToProductRelationQueryDto;
import com.ideal.script.model.entity.ToProductRelationEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ToProductRelationServiceImplTest {

    @Mock
    private ToProductRelationMapper mockToProductRelationMapper;

    private ToProductRelationServiceImpl toProductRelationServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        toProductRelationServiceImplUnderTest = new ToProductRelationServiceImpl(mockToProductRelationMapper);
    }

    @Test
    void testSelectToProductRelationById() {
        // Setup
        // Configure ToProductRelationMapper.selectToProductRelationById(...).
        final ToProductRelationEntity toProductRelationEntity = new ToProductRelationEntity();
        toProductRelationEntity.setId(0L);
        toProductRelationEntity.setScriptToproductId(0L);
        toProductRelationEntity.setSrcScriptUuid("srcScriptUuid");
        toProductRelationEntity.setCreateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockToProductRelationMapper.selectToProductRelationById(0L)).thenReturn(toProductRelationEntity);

        // Run the test
        final ToProductRelationDto result = toProductRelationServiceImplUnderTest.selectToProductRelationById(0L);
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testSelectToProductRelationList() {
        // Setup
        final ToProductRelationQueryDto toProductRelationQueryDto = new ToProductRelationQueryDto();
        toProductRelationQueryDto.setId(0L);
        toProductRelationQueryDto.setScriptToproductId(0L);
        toProductRelationQueryDto.setSrcScriptUuid("srcScriptUuid");
        toProductRelationQueryDto.setCreateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));

        // Configure ToProductRelationMapper.selectToProductRelationList(...).
        final ToProductRelationEntity toProductRelationEntity = new ToProductRelationEntity();
        toProductRelationEntity.setId(0L);
        toProductRelationEntity.setScriptToproductId(0L);
        toProductRelationEntity.setSrcScriptUuid("srcScriptUuid");
        toProductRelationEntity.setCreateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        Page<ToProductRelationEntity> page =new Page<>();
        page.add(toProductRelationEntity);
        when(mockToProductRelationMapper.selectToProductRelationList(any(ToProductRelationEntity.class)))
                .thenReturn(page);

        // Run the test
        final PageInfo<ToProductRelationDto> result = toProductRelationServiceImplUnderTest.selectToProductRelationList(
                toProductRelationQueryDto, 0, 0);
        assertNotNull(result);
        // Verify the results
    }


    @Test
    void testInsertToProductRelation() {
        // Setup
        final ToProductRelationDto toProductRelationDto = new ToProductRelationDto();
        toProductRelationDto.setId(0L);
        toProductRelationDto.setScriptToproductId(0L);
        toProductRelationDto.setSrcScriptUuid("srcScriptUuid");
        toProductRelationDto.setCreateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));

        when(mockToProductRelationMapper.insertToProductRelation(any(ToProductRelationEntity.class))).thenReturn(0);

        // Run the test
        final int result = toProductRelationServiceImplUnderTest.insertToProductRelation(toProductRelationDto);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testUpdateToProductRelation() {
        // Setup
        final ToProductRelationDto toProductRelationDto = new ToProductRelationDto();
        toProductRelationDto.setId(0L);
        toProductRelationDto.setScriptToproductId(0L);
        toProductRelationDto.setSrcScriptUuid("srcScriptUuid");
        toProductRelationDto.setCreateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));

        when(mockToProductRelationMapper.updateToProductRelation(any(ToProductRelationEntity.class))).thenReturn(0);

        // Run the test
        final int result = toProductRelationServiceImplUnderTest.updateToProductRelation(toProductRelationDto);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testDeleteToProductRelationByIds() {
        // Setup
        when(mockToProductRelationMapper.deleteToProductRelationByIds(any(Long[].class))).thenReturn(0);

        // Run the test
        final int result = toProductRelationServiceImplUnderTest.deleteToProductRelationByIds(new Long[]{0L});

        // Verify the results
        assertThat(result).isZero();
    }
}
