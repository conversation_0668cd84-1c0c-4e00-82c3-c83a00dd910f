package com.ideal.script.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ideal.script.mapper.ExectimeMapper;
import com.ideal.script.model.dto.ExectimeDto;
import com.ideal.script.model.entity.Exectime;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ExectimeServiceImplTest {

    @Mock
    private ExectimeMapper mockExectimeMapper;

    private ExectimeServiceImpl exectimeServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        exectimeServiceImplUnderTest = new ExectimeServiceImpl(mockExectimeMapper);
    }

    @Test
    void testSelectExectimeById() {
        // Setup
        // Configure ExectimeMapper.selectExectimeById(...).
        final Exectime exectime = new Exectime();
        exectime.setSuccessRate("successRate");
        exectime.setId(0L);
        exectime.setSuccessTimes(0L);
        exectime.setTotalTimes(0L);
        exectime.setSrcScriptUuid("srcScriptUuid");
        when(mockExectimeMapper.selectExectimeById(0L)).thenReturn(exectime);

        // Run the test
        final ExectimeDto result = exectimeServiceImplUnderTest.selectExectimeById(0L);
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testSelectExectimeList() {
        // Setup
        final ExectimeDto exectimeDto = new ExectimeDto();
        exectimeDto.setSuccessRate("successRate");
        exectimeDto.setId(0L);
        exectimeDto.setSuccessTimes(0L);
        exectimeDto.setTotalTimes(0L);
        exectimeDto.setSrcScriptUuid("srcScriptUuid");

        // Configure ExectimeMapper.selectExectimeList(...).
        final Exectime exectime = new Exectime();
        exectime.setSuccessRate("successRate");
        exectime.setId(0L);
        exectime.setSuccessTimes(0L);
        exectime.setTotalTimes(0L);
        exectime.setSrcScriptUuid("srcScriptUuid");
        Page<Exectime> page = new Page<>();
        page.add(exectime);
        when(mockExectimeMapper.selectExectimeList(any(Exectime.class))).thenReturn(page);

        // Run the test
        final PageInfo<ExectimeDto> result = exectimeServiceImplUnderTest.selectExectimeList(exectimeDto, 0, 0);
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testInsertExectime() {
        // Setup
        final ExectimeDto exectimeDto = new ExectimeDto();
        exectimeDto.setSuccessRate("successRate");
        exectimeDto.setId(0L);
        exectimeDto.setSuccessTimes(0L);
        exectimeDto.setTotalTimes(0L);
        exectimeDto.setSrcScriptUuid("srcScriptUuid");

        when(mockExectimeMapper.insertExectime(any(Exectime.class))).thenReturn(0);

        // Run the test
        final int result = exectimeServiceImplUnderTest.insertExectime(exectimeDto);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testUpdateExectime() {
        // Setup
        final ExectimeDto exectimeDto = new ExectimeDto();
        exectimeDto.setSuccessRate("successRate");
        exectimeDto.setId(0L);
        exectimeDto.setSuccessTimes(0L);
        exectimeDto.setTotalTimes(0L);
        exectimeDto.setSrcScriptUuid("srcScriptUuid");

        when(mockExectimeMapper.updateExectime(any(Exectime.class))).thenReturn(0);

        // Run the test
        final int result = exectimeServiceImplUnderTest.updateExectime(exectimeDto);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testDeleteExectimeByIds() {
        // Setup
        when(mockExectimeMapper.deleteExectimeByIds(any(Long[].class))).thenReturn(0);

        // Run the test
        final int result = exectimeServiceImplUnderTest.deleteExectimeByIds(new Long[]{0L});

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testDeleteExectimeById() {
        // Setup
        when(mockExectimeMapper.deleteExectimeById(0L)).thenReturn(0);

        // Run the test
        final int result = exectimeServiceImplUnderTest.deleteExectimeById(0L);

        // Verify the results
        assertThat(result).isZero();
    }

    @ParameterizedTest
    @ValueSource(ints = {10, 20})
    void testUpdateScriptExecTime(Integer state) {
        // Setup
        // Run the test
        exectimeServiceImplUnderTest.updateScriptExecTime(state, "srcScriptUuid", 0);

        // Verify the results
        verify(mockExectimeMapper).updateScriptExectime(anyInt(), eq("srcScriptUuid"));
    }

    @Test
    void testGetTotalAndSuccessRate() {
        // Setup
        // Configure ExectimeMapper.getTotalAndSuccessRate(...).
        final Exectime exectime = new Exectime();
        exectime.setSuccessRate("successRate");
        exectime.setId(0L);
        exectime.setSuccessTimes(0L);
        exectime.setTotalTimes(0L);
        exectime.setSrcScriptUuid("srcScriptUuid");
        when(mockExectimeMapper.getTotalAndSuccessRate("srcScriptUuid")).thenReturn(exectime);

        // Run the test
        final ExectimeDto result = exectimeServiceImplUnderTest.getTotalAndSuccessRate("srcScriptUuid");
        assertNotNull(result);
        // Verify the results
    }
}
