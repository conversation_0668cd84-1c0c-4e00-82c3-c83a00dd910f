package com.ideal.script.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ideal.script.dto.*;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.ParameterCheckMapper;
import com.ideal.script.mapper.ParameterMapper;
import com.ideal.script.model.bean.ParameterValidationBean;
import com.ideal.script.model.dto.*;
import com.ideal.script.model.entity.InfoVersion;
import com.ideal.script.model.entity.InfoVersionText;
import com.ideal.script.model.entity.Parameter;
import com.ideal.script.model.entity.ParameterCheck;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ParameterServiceImplTest {

    @Mock
    private ParameterMapper mockParameterMapper;
    @Mock
    private ParameterCheckMapper mockParameterCheckMapper;

    @InjectMocks
    private ParameterServiceImpl parameterServiceImplUnderTest;


    @Test
    void testSelectParameterById() {
        // Setup
        // Configure ParameterMapper.selectParameterById(...).
        final Parameter parameter = new Parameter();
        parameter.setId(1L);
        parameter.setSrcScriptUuid("srcScriptUuid");
        parameter.setParamType("paramType");
        parameter.setParamDefaultValue("paramDefaultValue");
        parameter.setParamDesc("paramDesc");
        when(mockParameterMapper.selectParameterById(1L)).thenReturn(parameter);

        // Run the test
        final ParameterDto result = parameterServiceImplUnderTest.selectParameterById(1L);

        assertNotNull(result);
        System.out.println(result);
        // Verify the results
        Mockito.verify(mockParameterMapper, times(1)).selectParameterById(1L);
    }

    @Test
    void testSelectParameterList1() {
        // Setup
        final ParameterDto parameterDto = new ParameterDto();
        parameterDto.setId(1L);
        parameterDto.setSrcScriptUuid("srcScriptUuid");
        parameterDto.setParamType("paramType");
        parameterDto.setParamDefaultValue("paramDefaultValue");
        parameterDto.setParamDesc("paramDesc");

        // Configure ParameterMapper.selectParameterList(...).
        final Parameter parameter = new Parameter();
        parameter.setId(1L);
        parameter.setSrcScriptUuid("srcScriptUuid");
        parameter.setParamType("paramType");
        parameter.setParamDefaultValue("paramDefaultValue");
        parameter.setParamDesc("paramDesc");
        Page<Parameter> page = new Page<>();
        page.add(parameter);
        when(mockParameterMapper.selectParameterList(any(Parameter.class))).thenReturn(page);

        // Run the test
        final PageInfo<ParameterDto> result = parameterServiceImplUnderTest.selectParameterList(parameterDto, 1, 50);

        assertNotNull(result);
        System.out.println(result);
        // Verify the results
        Mockito.verify(mockParameterMapper, times(1)).selectParameterList(any(Parameter.class));
    }


    @Test
    void testInsertParameter() {
        // Setup
        final ParameterDto parameterDto = new ParameterDto();
        parameterDto.setId(1L);
        parameterDto.setSrcScriptUuid("srcScriptUuid");
        parameterDto.setParamType("paramType");
        parameterDto.setParamDefaultValue("paramDefaultValue");
        parameterDto.setParamDesc("paramDesc");

        // Run the test
        parameterServiceImplUnderTest.insertParameter(parameterDto);

        // Verify the results
        verify(mockParameterMapper).insertParameter(any(Parameter.class));
    }

    @Test
    void testUpdateParameter() {
        // Setup
        final ParameterDto parameterDto = new ParameterDto();
        parameterDto.setId(1L);
        parameterDto.setSrcScriptUuid("srcScriptUuid");
        parameterDto.setParamType("paramType");
        parameterDto.setParamDefaultValue("paramDefaultValue");
        parameterDto.setParamDesc("paramDesc");

        // Run the test
        parameterServiceImplUnderTest.updateParameter(parameterDto);

        // Verify the results
        verify(mockParameterMapper).updateParameter(any(Parameter.class));
    }

    @Test
    void testDeleteParameterByIds() {
        // Setup
        // Run the test
        parameterServiceImplUnderTest.deleteParameterByIds(new Long[]{1L, 2L});

        // Verify the results
        verify(mockParameterMapper).deleteParameterByIds(any(Long[].class));
    }

    @Test
    void testDeleteParameterById() {
        // Setup
        when(mockParameterMapper.deleteParameterById(1L)).thenReturn(1);

        // Run the test
        final int result = parameterServiceImplUnderTest.deleteParameterById(1L);

        // Verify the results
        assertThat(result).isEqualTo(1);
    }

    @Test
    void testSelectParameterList2() {
        // Setup
        // Configure ParameterMapper.selectParameterListByScriptId(...).
        final Parameter parameter = new Parameter();
        parameter.setId(1L);
        parameter.setSrcScriptUuid("srcScriptUuid");
        parameter.setParamType("paramType");
        parameter.setParamDefaultValue("paramDefaultValue");
        parameter.setParamDesc("paramDesc");
        Page<Parameter> page = new Page<>();
        page.add(parameter);
        when(mockParameterMapper.selectParameterListByScriptId(1L)).thenReturn(page);

        // Run the test
        final List<ParameterDto> result = parameterServiceImplUnderTest.selectParameterList(1L);

        assertNotNull(result);
        System.out.println(result);
        // Verify the results
        Mockito.verify(mockParameterMapper, times(1)).selectParameterListByScriptId(1L);
    }

    @Test
    void testCreateParameters() throws ScriptException {
        // Setup
        final ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        final ScriptVersionDto scriptVersionDto = new ScriptVersionDto();
        scriptVersionDto.setExpectType(0);
        scriptVersionDto.setExpectLastline("expectLastline");
        final ParameterValidationDto parameterValidationDto = new ParameterValidationDto();
        parameterValidationDto.setParamName("aaa");


        List<ParameterValidationDto> parameterValidationDtoList = new ArrayList<>();
        parameterValidationDtoList.add(parameterValidationDto);
        scriptVersionDto.setParameterValidationDtoList(parameterValidationDtoList);
        scriptInfoDto.setScriptVersionDto(scriptVersionDto);
        scriptVersionDto.setSrcScriptUuid("srcScriptUuid");
        scriptInfoDto.setScriptVersionDto(scriptVersionDto);

        // Run the test
        parameterServiceImplUnderTest.createParameters(scriptInfoDto);

        // Verify the results
        verify(mockParameterMapper).insertParameter(any(Parameter.class));
    }

    @Test
    void testGetParameterValidationDtos() {
        // Setup
        final InfoVersionText infoVersionText = new InfoVersionText();
        infoVersionText.setId(1L);
        infoVersionText.setSrcScriptUuid("srcScriptUuid");
        infoVersionText.setContent("content");
        infoVersionText.setCreatorId(1L);
        infoVersionText.setCreatorName("creatorName");

        // Configure ParameterMapper.selectParameterValidationList(...).
        final ParameterValidationBean parameterValidationBean = new ParameterValidationBean();
        parameterValidationBean.setId(1L);
        parameterValidationBean.setSrcScriptUuid("srcScriptUuid");
        parameterValidationBean.setParamType("paramType");
        parameterValidationBean.setParamDefaultValue("paramDefaultValue");
        parameterValidationBean.setParamDesc("paramDesc");
        final List<ParameterValidationBean> parameterValidationBeans = Collections.singletonList(parameterValidationBean);
        when(mockParameterMapper.selectParameterValidationList(any(Parameter.class)))
                .thenReturn(parameterValidationBeans);

        // Run the test
        final List<ParameterValidationDto> result = parameterServiceImplUnderTest.getParameterValidationDtos(
                new InfoVersion());
        assertNotNull(result);
        // Verify the results
        verify(mockParameterMapper).selectParameterValidationList(any(Parameter.class));
    }

    @Test
    void getParameterByUuid() {

        final List<Parameter> result = parameterServiceImplUnderTest.getParameterByUuid(anyString());
        assertNotNull(result);

    }

    @Test
    void validateParameter() {
        ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        ScriptVersionDto scriptVersionDto = new ScriptVersionDto();
        // 创建一个 ParameterValidationDto 对象
        ParameterValidationDto parameterValidationDto1 = new ParameterValidationDto();
        parameterValidationDto1.setParamCheckIid(1L);
        parameterValidationDto1.setParamDefaultValue("111");

        ParameterValidationDto parameterValidationDto = new ParameterValidationDto();
        parameterValidationDto.setParamCheckIid(1L);
        parameterValidationDto.setParamDefaultValue("aaaa");

        List<ParameterValidationDto> parameterValidationDtoList = new ArrayList<>();
        parameterValidationDtoList.add(parameterValidationDto);
        parameterValidationDtoList.add(parameterValidationDto1);

        scriptVersionDto.setParameterValidationDtoList(parameterValidationDtoList);
        scriptInfoDto.setScriptVersionDto(scriptVersionDto);

        ParameterCheck parameterCheck = new ParameterCheck();
        parameterCheck.setCheckRule("hhh");
        when(mockParameterCheckMapper.selectParameterCheckById(1L)).thenReturn(parameterCheck);
        final ParameterValidationResultDto result = parameterServiceImplUnderTest.validateParameter(scriptInfoDto);

        System.out.println(result);
        assertNotNull(result);
    }
    @Test
    void validateParameter_success() {
        ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        ScriptVersionDto scriptVersionDto = new ScriptVersionDto();

        // 创建一个 ParameterValidationDto 对象
        ParameterValidationDto parameterValidationDto1 = new ParameterValidationDto();
        parameterValidationDto1.setParamCheckIid(1L);
        parameterValidationDto1.setParamDefaultValue("hhh");

        ParameterValidationDto parameterValidationDto = new ParameterValidationDto();
        parameterValidationDto.setParamDefaultValue("aaaa");

        List<ParameterValidationDto> parameterValidationDtoList = new ArrayList<>();
        parameterValidationDtoList.add(parameterValidationDto);
        parameterValidationDtoList.add(parameterValidationDto1);

        scriptVersionDto.setParameterValidationDtoList(parameterValidationDtoList);
        scriptInfoDto.setScriptVersionDto(scriptVersionDto);

        ParameterCheck parameterCheck = new ParameterCheck();
        parameterCheck.setCheckRule("hhh");
        when(mockParameterCheckMapper.selectParameterCheckById(1L)).thenReturn(parameterCheck);
        final ParameterValidationResultDto result = parameterServiceImplUnderTest.validateParameter(scriptInfoDto);

        System.out.println(result);
        assertNotNull(result);
    }

    @Test
    void validateParameter_noDate() {
        ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        ScriptVersionDto scriptVersionDto = new ScriptVersionDto();
        scriptVersionDto.setParameterValidationDtoList(null);
        scriptInfoDto.setScriptVersionDto(scriptVersionDto);

        final ParameterValidationResultDto result = parameterServiceImplUnderTest.validateParameter(scriptInfoDto);

        System.out.println(result);
        assertNotNull(result);
    }


    @Test
    void validateParameter_noRules() {
        ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        ScriptVersionDto scriptVersionDto = new ScriptVersionDto();

        List<ParameterValidationDto> parameterValidationDtoList = new ArrayList<>();
        ParameterValidationDto parameterValidationDto = new ParameterValidationDto();
        parameterValidationDto.setParamName("name");
        parameterValidationDto.setId(1L);
        parameterValidationDto.setParamOrder(1);
        parameterValidationDto.setParamCheckIid(1L);
        parameterValidationDtoList.add(parameterValidationDto);

        scriptVersionDto.setParameterValidationDtoList(parameterValidationDtoList);
        scriptInfoDto.setScriptVersionDto(scriptVersionDto);

        final ParameterValidationResultDto result = parameterServiceImplUnderTest.validateParameter(scriptInfoDto);

        System.out.println(result);
        assertNotNull(result);
    }
}
