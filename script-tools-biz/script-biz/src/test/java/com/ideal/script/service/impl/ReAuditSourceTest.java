package com.ideal.script.service.impl;

import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.*;
import com.ideal.script.service.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SetOperations;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ReAuditSourceTest {

    @Mock
    private IAuditRelationService mockAuditRelationService;
    @Mock
    private ITaskIpsService mockTaskIpsService;
    @Mock
    private ITaskGroupsService mockTaskGroupsService;
    @Mock
    private ITaskParamsService mockTaskParamsService;
    @Mock
    private ITaskAttachmentService mockTaskAttachmentService;
    @Mock
    private RedisTemplate<String, Object> mockRedisTemplate;
    @Mock
    private ITaskService mockITaskService;

    private ReAuditSource reAuditSourceUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        reAuditSourceUnderTest = new ReAuditSource(mockAuditRelationService, mockTaskIpsService, mockTaskGroupsService,
                mockTaskParamsService, mockTaskAttachmentService, mockRedisTemplate, mockITaskService);
    }

    @Test
    void testPreHandle() {
        // Setup
        final ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        final TaskDto taskInfo = new TaskDto();
        taskInfo.setId(0L);
        taskInfo.setTaskScheduler(0);
        taskInfo.setTaskTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        taskInfo.setTaskCron("taskCron");
        scriptExecAuditDto.setTaskInfo(taskInfo);
        scriptExecAuditDto.setAuditUser("auditUser");
        scriptExecAuditDto.setAuditUserId(0L);
        final AgentInfoDto agentInfoDto = new AgentInfoDto();
        agentInfoDto.setTaskIpsId(0L);
        scriptExecAuditDto.setChosedAgentUsers(Collections.singletonList(agentInfoDto));
        final TaskGroupsDto taskGroupsDto = new TaskGroupsDto();
        taskGroupsDto.setScriptTaskId(0L);
        scriptExecAuditDto.setChosedResGroups(Collections.singletonList(taskGroupsDto));

        // Run the test
        reAuditSourceUnderTest.preHandle(scriptExecAuditDto);

        // Verify the results
        verify(mockTaskIpsService).deleteTaskIpsByTaskId(0L);
        verify(mockTaskGroupsService).deleteTaskGroupsByTaskId(0L);
        verify(mockTaskParamsService).deleteTaskParamsByTaskId(0L);
    }

    @Test
    void testGetRelationId() throws Exception {
        // Setup
        final ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        final TaskDto taskInfo = new TaskDto();
        taskInfo.setId(0L);
        taskInfo.setTaskScheduler(0);
        taskInfo.setTaskTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        taskInfo.setTaskCron("taskCron");
        scriptExecAuditDto.setTaskInfo(taskInfo);
        scriptExecAuditDto.setAuditUser("auditUser");
        scriptExecAuditDto.setAuditUserId(0L);
        final AgentInfoDto agentInfoDto = new AgentInfoDto();
        agentInfoDto.setTaskIpsId(0L);
        scriptExecAuditDto.setChosedAgentUsers(Collections.singletonList(agentInfoDto));
        final TaskGroupsDto taskGroupsDto = new TaskGroupsDto();
        taskGroupsDto.setScriptTaskId(0L);
        scriptExecAuditDto.setChosedResGroups(Collections.singletonList(taskGroupsDto));

        final TaskDto taskInfo1 = new TaskDto();
        taskInfo1.setScriptTaskSource(0);
        taskInfo1.setId(0L);
        taskInfo1.setTaskScheduler(0);
        taskInfo1.setTaskTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        taskInfo1.setTaskCron("taskCron");

        // Configure IAuditRelationService.selectAuditRelationByTaskId(...).
        final AuditRelationDto auditRelationDto = new AuditRelationDto();
        auditRelationDto.setWorkOrderNumber("workOrderNumber");
        auditRelationDto.setAuditUserId(0L);
        auditRelationDto.setId(0L);
        auditRelationDto.setState(0);
        auditRelationDto.setAuditUser("auditUser");
        when(mockAuditRelationService.selectAuditRelationByTaskId(0L)).thenReturn(auditRelationDto);

        // Run the test
        final Long result = reAuditSourceUnderTest.getRelationId(scriptExecAuditDto, taskInfo1, "srcScriptUuid");

        // Verify the results
        verify(mockAuditRelationService).updateAuditRelation(any(AuditRelationDto.class));
    }

    @Test
    void testGetRelationId_IAuditRelationServiceUpdateAuditRelationThrowsScriptException() throws Exception {
        // Setup
        final ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        final TaskDto taskInfo = new TaskDto();
        taskInfo.setId(0L);
        taskInfo.setTaskScheduler(0);
        taskInfo.setTaskTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        taskInfo.setTaskCron("taskCron");
        scriptExecAuditDto.setTaskInfo(taskInfo);
        scriptExecAuditDto.setAuditUser("auditUser");
        scriptExecAuditDto.setAuditUserId(0L);
        final AgentInfoDto agentInfoDto = new AgentInfoDto();
        agentInfoDto.setTaskIpsId(0L);
        scriptExecAuditDto.setChosedAgentUsers(Collections.singletonList(agentInfoDto));
        final TaskGroupsDto taskGroupsDto = new TaskGroupsDto();
        taskGroupsDto.setScriptTaskId(0L);
        scriptExecAuditDto.setChosedResGroups(Collections.singletonList(taskGroupsDto));

        final TaskDto taskInfo1 = new TaskDto();
        taskInfo1.setScriptTaskSource(0);
        taskInfo1.setId(0L);
        taskInfo1.setTaskScheduler(0);
        taskInfo1.setTaskTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        taskInfo1.setTaskCron("taskCron");

        // Configure IAuditRelationService.selectAuditRelationByTaskId(...).
        final AuditRelationDto auditRelationDto = new AuditRelationDto();
        auditRelationDto.setWorkOrderNumber("workOrderNumber");
        auditRelationDto.setAuditUserId(0L);
        auditRelationDto.setId(0L);
        auditRelationDto.setState(0);
        auditRelationDto.setAuditUser("auditUser");
        when(mockAuditRelationService.selectAuditRelationByTaskId(0L)).thenReturn(auditRelationDto);

        when(mockAuditRelationService.updateAuditRelation(any(AuditRelationDto.class)))
                .thenThrow(ScriptException.class);

        // Run the test
        assertThatThrownBy(() -> reAuditSourceUnderTest.getRelationId(scriptExecAuditDto, taskInfo1,
                "srcScriptUuid")).isInstanceOf(ScriptException.class);
    }

    @Test
    void testBindTaskId() {
        // Setup
        final ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        final TaskDto taskInfo = new TaskDto();
        taskInfo.setId(0L);
        taskInfo.setTaskScheduler(0);
        taskInfo.setTaskTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        taskInfo.setTaskCron("taskCron");
        scriptExecAuditDto.setTaskInfo(taskInfo);
        scriptExecAuditDto.setAuditUser("auditUser");
        scriptExecAuditDto.setAuditUserId(0L);
        final AgentInfoDto agentInfoDto = new AgentInfoDto();
        agentInfoDto.setTaskIpsId(0L);
        scriptExecAuditDto.setChosedAgentUsers(Collections.singletonList(agentInfoDto));
        final TaskGroupsDto taskGroupsDto = new TaskGroupsDto();
        taskGroupsDto.setScriptTaskId(0L);
        scriptExecAuditDto.setChosedResGroups(Collections.singletonList(taskGroupsDto));

        // Run the test
        reAuditSourceUnderTest.bindTaskId(scriptExecAuditDto);

        // Verify the results
        assertEquals(0L,taskGroupsDto.getScriptTaskId());
    }

    @Test
    void testIsInWhiteList() {
        assertThat(reAuditSourceUnderTest.isInWhiteList(new ScriptExecAuditDto())).isFalse();
    }

    @Test
    void testSaveOrUpdateTask() throws Exception {
        // Setup
        final ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        final TaskDto taskInfo = new TaskDto();
        taskInfo.setId(0L);
        taskInfo.setTaskScheduler(0);
        taskInfo.setTaskTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        taskInfo.setTaskCron("taskCron");
        scriptExecAuditDto.setTaskInfo(taskInfo);
        scriptExecAuditDto.setAuditUser("auditUser");
        scriptExecAuditDto.setAuditUserId(0L);
        final AgentInfoDto agentInfoDto = new AgentInfoDto();
        agentInfoDto.setTaskIpsId(0L);
        scriptExecAuditDto.setChosedAgentUsers(Collections.singletonList(agentInfoDto));
        final TaskGroupsDto taskGroupsDto = new TaskGroupsDto();
        taskGroupsDto.setScriptTaskId(0L);
        scriptExecAuditDto.setChosedResGroups(Collections.singletonList(taskGroupsDto));

        final TaskDto taskDto1 = new TaskDto();
        taskDto1.setScriptTaskSource(0);
        taskDto1.setId(0L);
        taskDto1.setTaskScheduler(0);
        taskDto1.setTaskTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        taskDto1.setTaskCron("taskCron");

        // Run the test
        reAuditSourceUnderTest.saveOrUpdateTask(scriptExecAuditDto, taskDto1);

        // Verify the results
        verify(mockITaskService).updateTask(any(TaskDto.class));
    }

    @Test
    void testPreHandleAttachment() {
        // Setup
        when(mockTaskAttachmentService.getIdsByTaskId(0L)).thenReturn(Collections.singletonList(0L));
        when(mockRedisTemplate.opsForSet()).thenReturn(null);
        SetOperations setOperation = mock(SetOperations.class);
        when(mockRedisTemplate.opsForSet()).thenReturn(setOperation);

        // Run the test
        reAuditSourceUnderTest.preHandleAttachment(0L);

        // Verify the results
        verify(mockTaskAttachmentService).updateTaskIdEmptyByTaskId(0L);
    }

    @Test
    void testPreHandleAttachment_ITaskAttachmentServiceGetIdsByTaskIdReturnsNoItems() {
        // Setup
        when(mockTaskAttachmentService.getIdsByTaskId(0L)).thenReturn(Collections.emptyList());

        // Run the test
        reAuditSourceUnderTest.preHandleAttachment(0L);

        // Verify the results
        verify(mockTaskAttachmentService).getIdsByTaskId(0L);
    }
}
