package com.ideal.script.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ideal.script.config.ScriptBusinessConfig;
import com.ideal.script.mapper.TaskMapper;
import com.ideal.script.service.impl.builders.MyScriptServiceScripts;
import com.ideal.system.common.component.model.CurrentUser;
import com.ideal.script.model.bean.TaskExecuteBean;
import com.ideal.script.model.dto.TaskDto;
import com.ideal.script.model.dto.TaskQueryDto;
import com.ideal.script.model.entity.Task;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TaskServiceImplTest {

    @Mock
    private TaskMapper mockTaskMapper;

    @Mock
    private MyScriptServiceScripts myScriptServiceScripts;

    @Mock
    private ScriptBusinessConfig scriptBusinessConfig;

    private TaskServiceImpl taskServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        taskServiceImplUnderTest = new TaskServiceImpl(myScriptServiceScripts, mockTaskMapper);
    }

    private void setupScriptBusinessConfig() {
        when(myScriptServiceScripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);
        when(scriptBusinessConfig.isSelectSelfDataFlag()).thenReturn(false);
    }

    @Test
    void testSelectTaskById() {
        // Setup
        // Configure TaskMapper.selectTaskById(...).
        final Task task = new Task();
        task.setScriptTaskSource(0);
        task.setReadyToExecute(0);
        task.setId(0L);
        task.setSrcScriptUuid("srcScriptUuid");
        task.setTaskName("taskName");
        when(mockTaskMapper.selectTaskById(0L)).thenReturn(task);

        // Run the test
        final TaskDto result = taskServiceImplUnderTest.selectTaskById(0L);
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testSelectTaskByServiceId() {
        // Setup
        // Configure TaskMapper.selectTaskByServiceId(...).
        final Task task = new Task();
        task.setScriptTaskSource(0);
        task.setReadyToExecute(0);
        task.setId(0L);
        task.setSrcScriptUuid("srcScriptUuid");
        task.setTaskName("taskName");
        when(mockTaskMapper.selectTaskByServiceId(0L, null)).thenReturn(task);

        // Run the test
        final TaskDto result = taskServiceImplUnderTest.selectTaskByServiceId(0L, null);
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testSelectTaskList() {
        // Setup
        final TaskDto taskDto = new TaskDto();
        taskDto.setScriptTaskSource(0);
        taskDto.setStartType(0);
        taskDto.setReadyToExecute(0);
        taskDto.setId(0L);
        taskDto.setSrcScriptUuid("srcScriptUuid");

        // Configure TaskMapper.selectTaskList(...).
        final Task task = new Task();
        task.setScriptTaskSource(0);
        task.setReadyToExecute(0);
        task.setId(0L);
        task.setSrcScriptUuid("srcScriptUuid");
        task.setTaskName("taskName");
        Page<Task> page = new Page<>();
        page.add(task);
        when(mockTaskMapper.selectTaskList(any(Task.class))).thenReturn(page);

        // Run the test
        final PageInfo<TaskDto> result = taskServiceImplUnderTest.selectTaskList(taskDto, 0, 0);
        assertNotNull(result);
        // Verify the results
    }


    @Test
    void testInsertTask() {
        // Setup
        final TaskDto taskDto = new TaskDto();
        taskDto.setScriptTaskSource(0);
        taskDto.setStartType(0);
        taskDto.setReadyToExecute(0);
        taskDto.setId(0L);
        taskDto.setSrcScriptUuid("srcScriptUuid");

        when(mockTaskMapper.insertTask(any(Task.class))).thenReturn(0);

        // Run the test
        final int result = taskServiceImplUnderTest.insertTask(taskDto);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testUpdateTask() {
        // Setup
        final TaskDto taskDto = new TaskDto();
        taskDto.setScriptTaskSource(0);
        taskDto.setStartType(0);
        taskDto.setReadyToExecute(0);
        taskDto.setId(0L);
        taskDto.setSrcScriptUuid("srcScriptUuid");

        when(mockTaskMapper.updateTask(any(Task.class))).thenReturn(0);

        // Run the test
        final int result = taskServiceImplUnderTest.updateTask(taskDto);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testDeleteTaskByIds() {
        // Setup
        when(mockTaskMapper.deleteTaskByIds(any(Long[].class))).thenReturn(0);

        // Run the test
        final int result = taskServiceImplUnderTest.deleteTaskByIds(new Long[]{0L});

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testDeleteTaskById() {
        // Setup
        when(mockTaskMapper.deleteTaskById(0L)).thenReturn(0);

        // Run the test
        final int result = taskServiceImplUnderTest.deleteTaskById(0L);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testSelectTaskReadyToExecuteList() {
        // Setup
        final TaskExecuteBean taskExecuteBean = new TaskExecuteBean();
        taskExecuteBean.setScriptTaskInstanceId(0L);
        taskExecuteBean.setRunAgentCount(0);
        taskExecuteBean.setScriptTaskInstanceState(0);
        taskExecuteBean.setScriptCategoryName("scriptCategoryName");
        taskExecuteBean.setSrcScriptUuid("srcScriptUuid");

        final CurrentUser currentUser = new CurrentUser();
        currentUser.setId(0L);
        currentUser.setLoginName("loginName");

        // Configure TaskMapper.selectTaskReadyToExecuteList(...).
        final TaskExecuteBean taskExecuteBean1 = new TaskExecuteBean();
        taskExecuteBean1.setScriptTaskInstanceId(0L);
        taskExecuteBean1.setRunAgentCount(0);
        taskExecuteBean1.setScriptTaskInstanceState(0);
        taskExecuteBean1.setScriptCategoryName("scriptCategoryName");
        taskExecuteBean1.setSrcScriptUuid("srcScriptUuid");
        final List<TaskExecuteBean> taskExecuteBeans = Collections.singletonList(taskExecuteBean1);

        setupScriptBusinessConfig();

        // Run the test
        final List<TaskExecuteBean> result = taskServiceImplUnderTest.selectTaskReadyToExecuteList(taskExecuteBean,
                currentUser);
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testSelectTaskReadyToExecuteList_TaskMapperReturnsNoItems() {
        // Setup
        final TaskExecuteBean taskExecuteBean = new TaskExecuteBean();
        taskExecuteBean.setScriptTaskInstanceId(0L);
        taskExecuteBean.setRunAgentCount(0);
        taskExecuteBean.setScriptTaskInstanceState(0);
        taskExecuteBean.setScriptCategoryName("scriptCategoryName");
        taskExecuteBean.setSrcScriptUuid("srcScriptUuid");

        final CurrentUser currentUser = new CurrentUser();
        currentUser.setId(0L);
        currentUser.setLoginName("loginName");

        setupScriptBusinessConfig();
        // Run the test
        final List<TaskExecuteBean> result = taskServiceImplUnderTest.selectTaskReadyToExecuteList(taskExecuteBean,
                currentUser);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testSelectRunningScriptTasks() {
        // Setup
        final TaskExecuteBean taskExecuteBean = new TaskExecuteBean();
        taskExecuteBean.setScriptTaskInstanceId(0L);
        taskExecuteBean.setRunAgentCount(0);
        taskExecuteBean.setScriptTaskInstanceState(0);
        taskExecuteBean.setScriptCategoryName("scriptCategoryName");
        taskExecuteBean.setSrcScriptUuid("srcScriptUuid");

        final CurrentUser currentUser = new CurrentUser();
        currentUser.setId(0L);
        currentUser.setLoginName("loginName");

        // Configure TaskMapper.selectRunningScriptTasks(...).
        final TaskExecuteBean taskExecuteBean1 = new TaskExecuteBean();
        taskExecuteBean1.setScriptTaskInstanceId(0L);
        taskExecuteBean1.setRunAgentCount(0);
        taskExecuteBean1.setScriptTaskInstanceState(0);
        taskExecuteBean1.setScriptCategoryName("scriptCategoryName");
        taskExecuteBean1.setSrcScriptUuid("srcScriptUuid");
        final List<TaskExecuteBean> taskExecuteBeans = Collections.singletonList(taskExecuteBean1);

        setupScriptBusinessConfig();
        // Run the test
        final List<TaskExecuteBean> result = taskServiceImplUnderTest.selectRunningScriptTasks(taskExecuteBean,
                currentUser);
        assertNotNull(result);
        // Verify the results
    }



    @Test
    void testSelectRunningScriptTasks_scriptTest() {
        // Setup
        final TaskExecuteBean taskExecuteBean = new TaskExecuteBean();
        taskExecuteBean.setScriptTaskInstanceId(0L);
        taskExecuteBean.setRunAgentCount(0);
        taskExecuteBean.setScriptTaskInstanceState(0);
        taskExecuteBean.setScriptCategoryName("scriptCategoryName");
        taskExecuteBean.setSrcScriptUuid("srcScriptUuid");
        taskExecuteBean.setScriptTaskSource(2);

        final CurrentUser currentUser = new CurrentUser();
        currentUser.setId(0L);
        currentUser.setLoginName("loginName");

        // Configure TaskMapper.selectRunningScriptTasks(...).
        final TaskExecuteBean taskExecuteBean1 = new TaskExecuteBean();
        taskExecuteBean1.setScriptTaskInstanceId(0L);
        taskExecuteBean1.setRunAgentCount(0);
        taskExecuteBean1.setScriptTaskInstanceState(0);
        taskExecuteBean1.setScriptCategoryName("scriptCategoryName");
        taskExecuteBean1.setSrcScriptUuid("srcScriptUuid");
        final List<TaskExecuteBean> taskExecuteBeans = Collections.singletonList(taskExecuteBean1);
        setupScriptBusinessConfig();
        // Run the test
        final List<TaskExecuteBean> result = taskServiceImplUnderTest.selectRunningScriptTasks(taskExecuteBean,
                currentUser);
        assertNotNull(result);
        // Verify the results
    }





    @Test
    void testSelectRunningScriptTasks_TaskMapperReturnsNoItems() {
        // Setup
        final TaskExecuteBean taskExecuteBean = new TaskExecuteBean();
        taskExecuteBean.setScriptTaskInstanceId(0L);
        taskExecuteBean.setRunAgentCount(0);
        taskExecuteBean.setScriptTaskInstanceState(0);
        taskExecuteBean.setScriptCategoryName("scriptCategoryName");
        taskExecuteBean.setSrcScriptUuid("srcScriptUuid");

        final CurrentUser currentUser = new CurrentUser();
        currentUser.setId(0L);
        currentUser.setLoginName("loginName");
        setupScriptBusinessConfig();
        // Run the test
        final List<TaskExecuteBean> result = taskServiceImplUnderTest.selectRunningScriptTasks(taskExecuteBean,
                currentUser);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testSelectCompleteScriptTasks() {
        // Setup
        final TaskExecuteBean taskExecuteBean = new TaskExecuteBean();
        taskExecuteBean.setScriptTaskInstanceId(0L);
        taskExecuteBean.setRunAgentCount(0);
        taskExecuteBean.setScriptTaskInstanceState(0);
        taskExecuteBean.setScriptCategoryName("scriptCategoryName");
        taskExecuteBean.setSrcScriptUuid("srcScriptUuid");

        final CurrentUser currentUser = new CurrentUser();
        currentUser.setId(0L);
        currentUser.setLoginName("loginName");

        // Configure TaskMapper.selectCompleteScriptTasks(...).
        final TaskExecuteBean taskExecuteBean1 = new TaskExecuteBean();
        taskExecuteBean1.setScriptTaskInstanceId(0L);
        taskExecuteBean1.setRunAgentCount(0);
        taskExecuteBean1.setScriptTaskInstanceState(0);
        taskExecuteBean1.setScriptCategoryName("scriptCategoryName");
        taskExecuteBean1.setSrcScriptUuid("srcScriptUuid");
        final List<TaskExecuteBean> taskExecuteBeans = Collections.singletonList(taskExecuteBean1);
        setupScriptBusinessConfig();
        // Run the test
        final List<TaskExecuteBean> result = taskServiceImplUnderTest.selectCompleteScriptTasks(taskExecuteBean,
                currentUser);
        assertNotNull(result);
        // Verify the results
    }


    @Test
    void testSelectCompleteScriptTasks_scriptTest() {
        // Setup
        final TaskExecuteBean taskExecuteBean = new TaskExecuteBean();
        taskExecuteBean.setScriptTaskInstanceId(0L);
        taskExecuteBean.setRunAgentCount(0);
        taskExecuteBean.setScriptTaskInstanceState(0);
        taskExecuteBean.setScriptCategoryName("scriptCategoryName");
        taskExecuteBean.setSrcScriptUuid("srcScriptUuid");
        taskExecuteBean.setScriptTaskSource(2);

        final CurrentUser currentUser = new CurrentUser();
        currentUser.setId(0L);
        currentUser.setLoginName("loginName");

        // Configure TaskMapper.selectCompleteScriptTasks(...).
        final TaskExecuteBean taskExecuteBean1 = new TaskExecuteBean();
        taskExecuteBean1.setScriptTaskInstanceId(0L);
        taskExecuteBean1.setRunAgentCount(0);
        taskExecuteBean1.setScriptTaskInstanceState(0);
        taskExecuteBean1.setScriptCategoryName("scriptCategoryName");
        taskExecuteBean1.setSrcScriptUuid("srcScriptUuid");
        final List<TaskExecuteBean> taskExecuteBeans = Collections.singletonList(taskExecuteBean1);
        setupScriptBusinessConfig();
        // Run the test
        final List<TaskExecuteBean> result = taskServiceImplUnderTest.selectCompleteScriptTasks(taskExecuteBean,
                currentUser);
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testSelectCompleteScriptTasks_TaskMapperReturnsNoItems() {
        // Setup
        final TaskExecuteBean taskExecuteBean = new TaskExecuteBean();
        taskExecuteBean.setScriptTaskInstanceId(0L);
        taskExecuteBean.setRunAgentCount(0);
        taskExecuteBean.setScriptTaskInstanceState(0);
        taskExecuteBean.setScriptCategoryName("scriptCategoryName");
        taskExecuteBean.setSrcScriptUuid("srcScriptUuid");

        final CurrentUser currentUser = new CurrentUser();
        currentUser.setId(0L);
        currentUser.setLoginName("loginName");

        setupScriptBusinessConfig();
        // Run the test
        final List<TaskExecuteBean> result = taskServiceImplUnderTest.selectCompleteScriptTasks(taskExecuteBean,
                currentUser);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testListTaskHis() {
        // Setup
        final TaskQueryDto queryParam = new TaskQueryDto();
        queryParam.setId(0L);
        queryParam.setSrcScriptUuid("srcScriptUuid");
        queryParam.setTaskName("taskName");
        queryParam.setExecUser("execUser");
        queryParam.setTaskScheduler(0);

        // Configure TaskMapper.selectTaskList(...).
        final Task task = new Task();
        task.setScriptTaskSource(0);
        task.setReadyToExecute(0);
        task.setId(0L);
        task.setSrcScriptUuid("srcScriptUuid");
        task.setTaskName("taskName");
        Page<Task> page =new Page<>();
        page.add(task);
        when(mockTaskMapper.selectTaskList(any(Task.class))).thenReturn(page);

        // Run the test
        final PageInfo<TaskDto> result = taskServiceImplUnderTest.listTaskHis(queryParam, 0, 0);
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void selectTimeTaskList() {
        // Setup
        final TaskExecuteBean taskExecuteBean = new TaskExecuteBean();
        taskExecuteBean.setScriptTaskInstanceId(0L);
        taskExecuteBean.setRunAgentCount(0);
        taskExecuteBean.setScriptTaskInstanceState(0);
        taskExecuteBean.setScriptCategoryName("scriptCategoryName");
        taskExecuteBean.setSrcScriptUuid("srcScriptUuid");

        final CurrentUser currentUser = new CurrentUser();
        currentUser.setId(0L);
        currentUser.setLoginName("loginName");

        // Run the test
        final List<TaskExecuteBean> result = taskServiceImplUnderTest.selectTimeTaskList(taskExecuteBean,
                currentUser);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
