package com.ideal.script.service.consumer;

import com.ideal.script.exception.ScriptException;
import com.ideal.script.service.resulthandler.IScriptResultHandlerService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * ScriptSendResultHandler单元测试类
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class ScriptSendResultHandlerTest {

    @Mock
    private IScriptResultHandlerService scriptResultHandlerService;

    @InjectMocks
    private ScriptSendResultHandler scriptSendResultHandler;

    private String testStringMessage;
    private Map<String, Object> testObjectMessage;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testStringMessage = "{\"taskId\":123,\"status\":\"SUCCESS\",\"result\":\"脚本发送成功\"}";
        
        testObjectMessage = new HashMap<>();
        testObjectMessage.put("taskId", 456);
        testObjectMessage.put("status", "FAILED");
        testObjectMessage.put("result", "脚本发送失败");
    }

    @Test
    @DisplayName("处理String类型消息_正常情况")
    void testNotice_WithStringMessage_Success() throws ScriptException {
        // Setup
        doNothing().when(scriptResultHandlerService).handleScriptSendResult(anyString());

        // Run the test
        scriptSendResultHandler.notice(testStringMessage);

        // Verify the results
        verify(scriptResultHandlerService).handleScriptSendResult(testStringMessage);
    }

    @Test
    @DisplayName("处理String类型消息_服务抛出异常_异常被捕获")
    void testNotice_WithStringMessage_ServiceException() throws ScriptException {
        // Setup
        doThrow(new ScriptException("处理脚本发送结果异常"))
                .when(scriptResultHandlerService).handleScriptSendResult(anyString());

        // Run the test - 异常被捕获，不会抛出
        scriptSendResultHandler.notice(testStringMessage);

        // Verify the results
        verify(scriptResultHandlerService).handleScriptSendResult(testStringMessage);
    }

    @Test
    @DisplayName("处理非String类型消息_转换为JSON字符串_正常情况")
    void testNotice_WithObjectMessage_Success() throws ScriptException {
        // Setup
        String expectedJsonString = "{\"taskId\":456,\"status\":\"FAILED\",\"result\":\"脚本发送失败\"}";
        doNothing().when(scriptResultHandlerService).handleScriptSendResult(anyString());

        try (MockedStatic<com.alibaba.fastjson2.JSON> mockedJSON = mockStatic(com.alibaba.fastjson2.JSON.class)) {
            mockedJSON.when(() -> com.alibaba.fastjson2.JSON.toJSONString(testObjectMessage))
                    .thenReturn(expectedJsonString);

            // Run the test
            scriptSendResultHandler.notice(testObjectMessage);

            // Verify the results
            verify(scriptResultHandlerService).handleScriptSendResult(expectedJsonString);
            mockedJSON.verify(() -> com.alibaba.fastjson2.JSON.toJSONString(testObjectMessage));
        }
    }

    @Test
    @DisplayName("处理非String类型消息_服务层异常_异常被捕获")
    void testNotice_WithObjectMessage_ServiceException() throws ScriptException {
        // Setup
        String jsonString = "{\"converted\":\"json\"}";
        doThrow(new ScriptException("服务处理异常"))
                .when(scriptResultHandlerService).handleScriptSendResult(anyString());

        try (MockedStatic<com.alibaba.fastjson2.JSON> mockedJSON = mockStatic(com.alibaba.fastjson2.JSON.class)) {
            mockedJSON.when(() -> com.alibaba.fastjson2.JSON.toJSONString(testObjectMessage))
                    .thenReturn(jsonString);

            // Run the test - 异常被捕获
            scriptSendResultHandler.notice(testObjectMessage);

            // Verify the results
            verify(scriptResultHandlerService).handleScriptSendResult(jsonString);
        }
    }

    @Test
    @DisplayName("处理非String类型消息_服务抛出ScriptException_异常被捕获")
    void testNotice_WithObjectMessage_ServiceScriptException() throws ScriptException {
        // Setup
        String jsonString = "{\"test\":\"data\"}";
        doThrow(new ScriptException("服务处理异常"))
                .when(scriptResultHandlerService).handleScriptSendResult(anyString());

        try (MockedStatic<com.alibaba.fastjson2.JSON> mockedJSON = mockStatic(com.alibaba.fastjson2.JSON.class)) {
            mockedJSON.when(() -> com.alibaba.fastjson2.JSON.toJSONString(testObjectMessage))
                    .thenReturn(jsonString);

            // Run the test - 异常被捕获
            scriptSendResultHandler.notice(testObjectMessage);

            // Verify the results
            verify(scriptResultHandlerService).handleScriptSendResult(jsonString);
        }
    }

    @Test
    @DisplayName("处理null消息_按非String类型处理")
    void testNotice_WithNullMessage_HandledAsNonString() throws ScriptException {
        // Setup
        String nullJsonString = "null";
        doNothing().when(scriptResultHandlerService).handleScriptSendResult(anyString());

        try (MockedStatic<com.alibaba.fastjson2.JSON> mockedJSON = mockStatic(com.alibaba.fastjson2.JSON.class)) {
            mockedJSON.when(() -> com.alibaba.fastjson2.JSON.toJSONString(null))
                    .thenReturn(nullJsonString);

            // Run the test
            scriptSendResultHandler.notice(null);

            // Verify the results
            verify(scriptResultHandlerService).handleScriptSendResult(nullJsonString);
        }
    }

    @Test
    @DisplayName("处理空字符串消息_正常情况")
    void testNotice_WithEmptyString_Success() throws ScriptException {
        // Setup
        String emptyMessage = "";
        doNothing().when(scriptResultHandlerService).handleScriptSendResult(anyString());

        // Run the test
        scriptSendResultHandler.notice(emptyMessage);

        // Verify the results
        verify(scriptResultHandlerService).handleScriptSendResult(emptyMessage);
    }

    @Test
    @DisplayName("处理复杂对象消息_正常情况")
    void testNotice_WithComplexObject_Success() throws ScriptException {
        // Setup
        Map<String, Object> complexObject = new HashMap<>();
        complexObject.put("taskId", 789L);
        complexObject.put("status", "PROCESSING");
        Map<String, Object> details = new HashMap<>();
        details.put("step", 1);
        details.put("total", 5);
        complexObject.put("details", details);
        complexObject.put("timestamp", System.currentTimeMillis());
        
        String expectedJson = "{\"complex\":\"json\"}";
        doNothing().when(scriptResultHandlerService).handleScriptSendResult(anyString());

        try (MockedStatic<com.alibaba.fastjson2.JSON> mockedJSON = mockStatic(com.alibaba.fastjson2.JSON.class)) {
            mockedJSON.when(() -> com.alibaba.fastjson2.JSON.toJSONString(complexObject))
                    .thenReturn(expectedJson);

            // Run the test
            scriptSendResultHandler.notice(complexObject);

            // Verify the results
            verify(scriptResultHandlerService).handleScriptSendResult(expectedJson);
        }
    }

    @Test
    @DisplayName("处理Integer类型消息_转换为JSON字符串")
    void testNotice_WithIntegerMessage_ConvertToJson() throws ScriptException {
        // Setup
        Integer integerMessage = 12345;
        String expectedJson = "12345";
        doNothing().when(scriptResultHandlerService).handleScriptSendResult(anyString());

        try (MockedStatic<com.alibaba.fastjson2.JSON> mockedJSON = mockStatic(com.alibaba.fastjson2.JSON.class)) {
            mockedJSON.when(() -> com.alibaba.fastjson2.JSON.toJSONString(integerMessage))
                    .thenReturn(expectedJson);

            // Run the test
            scriptSendResultHandler.notice(integerMessage);

            // Verify the results
            verify(scriptResultHandlerService).handleScriptSendResult(expectedJson);
        }
    }

    @Test
    @DisplayName("处理Boolean类型消息_转换为JSON字符串")
    void testNotice_WithBooleanMessage_ConvertToJson() throws ScriptException {
        // Setup
        Boolean booleanMessage = true;
        String expectedJson = "true";
        doNothing().when(scriptResultHandlerService).handleScriptSendResult(anyString());

        try (MockedStatic<com.alibaba.fastjson2.JSON> mockedJSON = mockStatic(com.alibaba.fastjson2.JSON.class)) {
            mockedJSON.when(() -> com.alibaba.fastjson2.JSON.toJSONString(booleanMessage))
                    .thenReturn(expectedJson);

            // Run the test
            scriptSendResultHandler.notice(booleanMessage);

            // Verify the results
            verify(scriptResultHandlerService).handleScriptSendResult(expectedJson);
        }
    }

    @Test
    @DisplayName("处理包含中文的String消息_正常情况")
    void testNotice_WithChineseStringMessage_Success() throws ScriptException {
        // Setup
        String chineseMessage = "{\"任务ID\":123,\"状态\":\"成功\",\"结果\":\"脚本执行完成\"}";
        doNothing().when(scriptResultHandlerService).handleScriptSendResult(anyString());

        // Run the test
        scriptSendResultHandler.notice(chineseMessage);

        // Verify the results
        verify(scriptResultHandlerService).handleScriptSendResult(chineseMessage);
    }

    @Test
    @DisplayName("处理长字符串消息_正常情况")
    void testNotice_WithLongStringMessage_Success() throws ScriptException {
        // Setup
        StringBuilder longMessageBuilder = new StringBuilder("{\"data\":\"");
        for (int i = 0; i < 1000; i++) {
            longMessageBuilder.append("长文本数据").append(i);
        }
        longMessageBuilder.append("\"}");
        String longMessage = longMessageBuilder.toString();
        
        doNothing().when(scriptResultHandlerService).handleScriptSendResult(anyString());

        // Run the test
        scriptSendResultHandler.notice(longMessage);

        // Verify the results
        verify(scriptResultHandlerService).handleScriptSendResult(longMessage);
    }
}
