package com.ideal.script.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.TaskRuntimeDto;
import com.ideal.script.service.ITaskRuntimeService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * TaskRuntimeController单元测试类
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class TaskRuntimeControllerTest {

    @Mock
    private ITaskRuntimeService taskRuntimeService;

    @InjectMocks
    private TaskRuntimeController taskRuntimeController;

    private TaskRuntimeDto taskRuntimeDto;
    private TableQueryDto<TaskRuntimeDto> tableQueryDto;
    private PageInfo<TaskRuntimeDto> pageInfo;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setId(1L);
        taskRuntimeDto.setScriptTaskId(100L);
        taskRuntimeDto.setTaskInstanceId(200L);
        taskRuntimeDto.setSrcScriptUuid("test-uuid-123");
        taskRuntimeDto.setState(20); // 完成状态
        taskRuntimeDto.setScriptName("测试脚本");
        taskRuntimeDto.setAgentIp("*************");
        taskRuntimeDto.setAgentPort(8080);
        taskRuntimeDto.setExecUser("testuser");
        taskRuntimeDto.setProviderIp("*************");
        taskRuntimeDto.setProviderPort(9090);
        taskRuntimeDto.setStartTime(new Timestamp(System.currentTimeMillis() - 60000));
        taskRuntimeDto.setEndTime(new Timestamp(System.currentTimeMillis()));
        taskRuntimeDto.setElapsedTime(60L);
        taskRuntimeDto.setExpectLastline("success");
        taskRuntimeDto.setExpectType(1);
        taskRuntimeDto.setTimeout(0);
        taskRuntimeDto.setTimeoutValue(300L);
        taskRuntimeDto.setStartType(0);
        taskRuntimeDto.setCreateTime(new Timestamp(System.currentTimeMillis()));
        taskRuntimeDto.setAgentTaskId(500L);
        taskRuntimeDto.setBizId("biz-123");
        taskRuntimeDto.setScriptTaskIpsId(600L);

        tableQueryDto = new TableQueryDto<>();
        tableQueryDto.setPageNum(1);
        tableQueryDto.setPageSize(10);
        tableQueryDto.setQueryParam(taskRuntimeDto);

        pageInfo = new PageInfo<>();
        pageInfo.setList(Arrays.asList(taskRuntimeDto));
        pageInfo.setTotal(1);
        pageInfo.setPageNum(1);
        pageInfo.setPageSize(10);
    }

    @Test
    @DisplayName("查询agent运行实例列表-成功")
    void listTaskRuntime_success() {
        // Mock service方法
        doReturn(pageInfo).when(taskRuntimeService).selectTaskRuntimeList(any(TaskRuntimeDto.class), eq(1), eq(10));

        // 执行测试方法
        R<PageInfo<TaskRuntimeDto>> result = taskRuntimeController.listTaskRuntime(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("list.success", result.getMessage());
        assertEquals(pageInfo, result.getData());
        assertEquals(1, result.getData().getTotal());
        assertEquals(1, result.getData().getList().size());
        assertEquals("测试脚本", result.getData().getList().get(0).getScriptName());
        assertEquals("*************", result.getData().getList().get(0).getAgentIp());
        
        // 验证service方法被调用
        verify(taskRuntimeService, times(1)).selectTaskRuntimeList(any(TaskRuntimeDto.class), eq(1), eq(10));
    }

    @Test
    @DisplayName("查询agent运行实例列表-空结果")
    void listTaskRuntime_emptyResult() {
        // 创建空的分页结果
        PageInfo<TaskRuntimeDto> emptyPageInfo = new PageInfo<>();
        emptyPageInfo.setList(Collections.emptyList());
        emptyPageInfo.setTotal(0);
        emptyPageInfo.setPageNum(1);
        emptyPageInfo.setPageSize(10);

        // Mock service方法
        doReturn(emptyPageInfo).when(taskRuntimeService).selectTaskRuntimeList(any(TaskRuntimeDto.class), eq(1), eq(10));

        // 执行测试方法
        R<PageInfo<TaskRuntimeDto>> result = taskRuntimeController.listTaskRuntime(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("list.success", result.getMessage());
        assertEquals(emptyPageInfo, result.getData());
        assertEquals(0, result.getData().getTotal());
        assertTrue(result.getData().getList().isEmpty());
        
        // 验证service方法被调用
        verify(taskRuntimeService, times(1)).selectTaskRuntimeList(any(TaskRuntimeDto.class), eq(1), eq(10));
    }

    @Test
    @DisplayName("查询agent运行实例列表-空查询参数")
    void listTaskRuntime_nullQueryParam() {
        // 设置空查询参数
        tableQueryDto.setQueryParam(null);

        // Mock service方法
        doReturn(pageInfo).when(taskRuntimeService).selectTaskRuntimeList(any(), eq(1), eq(10));

        // 执行测试方法
        R<PageInfo<TaskRuntimeDto>> result = taskRuntimeController.listTaskRuntime(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("list.success", result.getMessage());
        assertEquals(pageInfo, result.getData());
        
        // 验证service方法被调用
        verify(taskRuntimeService, times(1)).selectTaskRuntimeList(any(), eq(1), eq(10));
    }

    @Test
    @DisplayName("查询agent运行实例列表-分页参数测试")
    void listTaskRuntime_paginationTest() {
        // 设置不同的分页参数
        tableQueryDto.setPageNum(2);
        tableQueryDto.setPageSize(20);

        // 创建对应的分页结果
        PageInfo<TaskRuntimeDto> customPageInfo = new PageInfo<>();
        customPageInfo.setList(Arrays.asList(taskRuntimeDto));
        customPageInfo.setTotal(1);
        customPageInfo.setPageNum(2);
        customPageInfo.setPageSize(20);

        // Mock service方法
        doReturn(customPageInfo).when(taskRuntimeService).selectTaskRuntimeList(any(TaskRuntimeDto.class), eq(2), eq(20));

        // 执行测试方法
        R<PageInfo<TaskRuntimeDto>> result = taskRuntimeController.listTaskRuntime(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("list.success", result.getMessage());
        assertEquals(customPageInfo, result.getData());
        assertEquals(2, result.getData().getPageNum());
        assertEquals(20, result.getData().getPageSize());
        
        // 验证service方法被调用
        verify(taskRuntimeService, times(1)).selectTaskRuntimeList(any(TaskRuntimeDto.class), eq(2), eq(20));
    }

    @Test
    @DisplayName("获取agent运行结果-成功")
    void getOutPutMessage_success() throws ScriptException {
        // 准备测试数据
        Long id = 1L;
        String expectedOutput = "脚本执行成功\n输出结果：Hello World";

        // Mock service方法
        doReturn(expectedOutput).when(taskRuntimeService).getOutPutMessage(id);

        // 执行测试方法
        R<Object> result = taskRuntimeController.getOutPutMessage(id);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("get.out.put.message.success", result.getMessage());
        assertEquals(expectedOutput, result.getData());
        
        // 验证service方法被调用
        verify(taskRuntimeService, times(1)).getOutPutMessage(id);
    }

    @Test
    @DisplayName("获取agent运行结果-空输出")
    void getOutPutMessage_emptyOutput() throws ScriptException {
        // 准备测试数据
        Long id = 1L;
        String emptyOutput = "";

        // Mock service方法
        doReturn(emptyOutput).when(taskRuntimeService).getOutPutMessage(id);

        // 执行测试方法
        R<Object> result = taskRuntimeController.getOutPutMessage(id);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("get.out.put.message.success", result.getMessage());
        assertEquals(emptyOutput, result.getData());
        
        // 验证service方法被调用
        verify(taskRuntimeService, times(1)).getOutPutMessage(id);
    }

    @Test
    @DisplayName("获取agent运行结果-service抛出异常")
    void getOutPutMessage_serviceException() throws ScriptException {
        // 准备测试数据
        Long id = 1L;

        // Mock service方法抛出异常
        doThrow(new ScriptException("Database connection failed")).when(taskRuntimeService).getOutPutMessage(id);

        // 执行测试方法
        R<Object> result = taskRuntimeController.getOutPutMessage(id);

        // 验证结果
        assertNotNull(result);
        assertEquals("10602", result.getCode());
        assertEquals("", result.getData());
        assertEquals("get.out.put.message.fail", result.getMessage());
        
        // 验证service方法被调用
        verify(taskRuntimeService, times(1)).getOutPutMessage(id);
    }

    @Test
    @DisplayName("获取agent运行结果-运行时异常")
    void getOutPutMessage_runtimeException() throws ScriptException {
        // 准备测试数据
        Long id = 1L;

        // Mock service方法抛出运行时异常
        doThrow(new RuntimeException("Unexpected error")).when(taskRuntimeService).getOutPutMessage(id);

        // 执行测试方法
        R<Object> result = taskRuntimeController.getOutPutMessage(id);

        // 验证结果
        assertNotNull(result);
        assertEquals("10602", result.getCode());
        assertEquals("", result.getData());
        assertEquals("get.out.put.message.fail", result.getMessage());
        
        // 验证service方法被调用
        verify(taskRuntimeService, times(1)).getOutPutMessage(id);
    }

    @Test
    @DisplayName("获取agent运行结果-null ID")
    void getOutPutMessage_nullId() throws ScriptException {
        // 准备测试数据
        Long id = null;

        // Mock service方法
        doReturn("").when(taskRuntimeService).getOutPutMessage(id);

        // 执行测试方法
        R<Object> result = taskRuntimeController.getOutPutMessage(id);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("get.out.put.message.success", result.getMessage());
        assertEquals("", result.getData());
        
        // 验证service方法被调用
        verify(taskRuntimeService, times(1)).getOutPutMessage(id);
    }

    @Test
    @DisplayName("查询agent运行实例列表-service抛出异常")
    void listTaskRuntime_serviceException() {
        // Mock service方法抛出异常
        doThrow(new RuntimeException("Database connection failed")).when(taskRuntimeService)
                .selectTaskRuntimeList(any(TaskRuntimeDto.class), eq(1), eq(10));

        // 执行测试方法并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            taskRuntimeController.listTaskRuntime(tableQueryDto);
        });

        // 验证异常信息
        assertEquals("Database connection failed", exception.getMessage());
        
        // 验证service方法被调用
        verify(taskRuntimeService, times(1)).selectTaskRuntimeList(any(TaskRuntimeDto.class), eq(1), eq(10));
    }
}
