package com.ideal.script.service.consumer;

import com.ideal.script.exception.ScriptException;
import com.ideal.script.service.resulthandler.IScriptResultHandlerService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

/**
 * ScriptExecuteResultHandler单元测试类
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class ScriptExecuteResultHandlerTest {

    @Mock
    private IScriptResultHandlerService scriptResultHandlerService;

    @InjectMocks
    private ScriptExecuteResultHandler scriptExecuteResultHandler;

    private List<String> testMessageList;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testMessageList = Arrays.asList(
                "脚本执行结果1",
                "脚本执行结果2",
                "脚本执行结果3"
        );
    }

    @Test
    @DisplayName("castList方法_转换List<String>_正常情况")
    void testCastList_WithStringList_Success() {
        // Setup
        List<Object> objectList = Arrays.asList("item1", "item2", "item3");

        // Run the test
        List<String> result = scriptExecuteResultHandler.castList(objectList, String.class);

        // Verify the results
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("item1", result.get(0));
        assertEquals("item2", result.get(1));
        assertEquals("item3", result.get(2));
    }

    @Test
    @DisplayName("castList方法_转换空List_返回空列表")
    void testCastList_WithEmptyList_ReturnsEmptyList() {
        // Setup
        List<Object> emptyList = Collections.emptyList();

        // Run the test
        List<String> result = scriptExecuteResultHandler.castList(emptyList, String.class);

        // Verify the results
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("castList方法_非List对象_返回空列表")
    void testCastList_WithNonListObject_ReturnsEmptyList() {
        // Setup
        String nonListObject = "not a list";

        // Run the test
        List<String> result = scriptExecuteResultHandler.castList(nonListObject, String.class);

        // Verify the results
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("castList方法_null对象_返回空列表")
    void testCastList_WithNullObject_ReturnsEmptyList() {
        // Run the test
        List<String> result = scriptExecuteResultHandler.castList(null, String.class);

        // Verify the results
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("notice方法_处理List<String>消息_正常情况")
    void testNotice_WithStringList_Success() throws ScriptException {
        // Setup
        doNothing().when(scriptResultHandlerService).handleScriptExecuteResult(anyList());

        // Run the test
        scriptExecuteResultHandler.notice(testMessageList);

        // Verify the results
        verify(scriptResultHandlerService).handleScriptExecuteResult(testMessageList);
    }

    @Test
    @DisplayName("notice方法_处理空List消息_正常情况")
    void testNotice_WithEmptyList_Success() throws ScriptException {
        // Setup
        List<String> emptyList = Collections.emptyList();
        doNothing().when(scriptResultHandlerService).handleScriptExecuteResult(anyList());

        // Run the test
        scriptExecuteResultHandler.notice(emptyList);

        // Verify the results
        verify(scriptResultHandlerService).handleScriptExecuteResult(emptyList);
    }

    @Test
    @DisplayName("notice方法_服务抛出异常_异常被捕获")
    void testNotice_ServiceThrowsException_ExceptionCaught() throws ScriptException {
        // Setup
        doThrow(new ScriptException("处理脚本执行结果异常"))
                .when(scriptResultHandlerService).handleScriptExecuteResult(anyList());

        // Run the test - 异常被捕获，不会抛出
        scriptExecuteResultHandler.notice(testMessageList);

        // Verify the results
        verify(scriptResultHandlerService).handleScriptExecuteResult(testMessageList);
    }

    @Test
    @DisplayName("notice方法_处理非List消息_转换为空列表")
    void testNotice_WithNonListMessage_ConvertsToEmptyList() throws ScriptException {
        // Setup
        String nonListMessage = "not a list message";
        doNothing().when(scriptResultHandlerService).handleScriptExecuteResult(anyList());

        // Run the test
        scriptExecuteResultHandler.notice(nonListMessage);

        // Verify the results - 应该传递空列表
        verify(scriptResultHandlerService).handleScriptExecuteResult(Collections.emptyList());
    }

    @Test
    @DisplayName("notice方法_处理null消息_转换为空列表")
    void testNotice_WithNullMessage_ConvertsToEmptyList() throws ScriptException {
        // Setup
        doNothing().when(scriptResultHandlerService).handleScriptExecuteResult(anyList());

        // Run the test
        scriptExecuteResultHandler.notice(null);

        // Verify the results - 应该传递空列表
        verify(scriptResultHandlerService).handleScriptExecuteResult(Collections.emptyList());
    }

    @Test
    @DisplayName("notice方法_处理混合类型List_类型转换异常被捕获")
    void testNotice_WithMixedTypeList_ClassCastExceptionCaught() throws ScriptException {
        // Setup
        List<Object> mixedList = Arrays.asList("string", 123, true);

        // Run the test - 类型转换异常被捕获
        scriptExecuteResultHandler.notice(mixedList);

        // Verify the results - 由于类型转换异常，可能不会调用服务方法
        // 这取决于castList方法的具体实现
        verify(scriptResultHandlerService, atMost(1)).handleScriptExecuteResult(anyList());
    }

    @Test
    @DisplayName("notice方法_处理包含null元素的List_处理null元素")
    void testNotice_WithListContainingNulls_HandlesNullElements() throws ScriptException {
        // Setup
        List<Object> listWithNulls = Arrays.asList("item1", null, "item3");
        doNothing().when(scriptResultHandlerService).handleScriptExecuteResult(anyList());

        // Run the test - null元素可能导致类型转换异常
        scriptExecuteResultHandler.notice(listWithNulls);

        // Verify the results
        verify(scriptResultHandlerService, atMost(1)).handleScriptExecuteResult(anyList());
    }

    @Test
    @DisplayName("castList方法_转换Integer List_正常情况")
    void testCastList_WithIntegerList_Success() {
        // Setup
        List<Object> integerList = Arrays.asList(1, 2, 3);

        // Run the test
        List<Integer> result = scriptExecuteResultHandler.castList(integerList, Integer.class);

        // Verify the results
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals(Integer.valueOf(1), result.get(0));
        assertEquals(Integer.valueOf(2), result.get(1));
        assertEquals(Integer.valueOf(3), result.get(2));
    }

    @Test
    @DisplayName("notice方法_完整流程验证_包含日志记录")
    void testNotice_CompleteFlow_WithLogging() throws ScriptException {
        // Setup
        List<String> complexMessageList = Arrays.asList(
                "任务开始执行",
                "执行步骤1完成",
                "执行步骤2完成",
                "任务执行完成"
        );
        doNothing().when(scriptResultHandlerService).handleScriptExecuteResult(anyList());

        // Run the test
        scriptExecuteResultHandler.notice(complexMessageList);

        // Verify the results
        verify(scriptResultHandlerService).handleScriptExecuteResult(complexMessageList);
    }
}
