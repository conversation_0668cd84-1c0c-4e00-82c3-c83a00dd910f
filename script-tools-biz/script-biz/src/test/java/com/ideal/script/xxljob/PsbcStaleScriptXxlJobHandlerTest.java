package com.ideal.script.xxljob;

import com.ideal.script.config.PsbcProperties;
import com.ideal.script.mapper.ExectimeMapper;
import com.ideal.script.mapper.InfoMapper;
import com.ideal.script.mapper.InfoVersionMapper;
import com.ideal.script.mapper.StaleScriptMapper;
import com.ideal.script.model.entity.Exectime;
import com.ideal.script.model.entity.Info;
import com.ideal.script.model.entity.InfoVersion;
import com.ideal.script.model.entity.Stale;
import com.ideal.script.service.impl.MyScriptServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PsbcStaleScriptXxlJobHandlerTest {

    @Mock
    private StaleScriptMapper mockStaleScriptMapper;
    @Mock
    private InfoVersionMapper mockInfoVersionMapper;
    @Mock
    private MyScriptServiceImpl mockScriptService;
    @Mock
    private InfoMapper mockInfoMapper;
    @Mock
    private ExectimeMapper mockExectimeMapper;
    @Mock
    private PsbcProperties mockPsbcProperties;

    private PsbcStaleScriptXxlJobHandler psbcStaleScriptXxlJobHandlerUnderTest;

    @BeforeEach
    void setUp() {
        psbcStaleScriptXxlJobHandlerUnderTest = new PsbcStaleScriptXxlJobHandler(mockStaleScriptMapper,
                mockInfoVersionMapper, mockScriptService, mockInfoMapper, mockExectimeMapper, mockPsbcProperties);
    }

    @Test
    @DisplayName("测试长时间未修改脚本处理-存在过期脚本且stale数据存在")
    void testStaleScriptHandler() {
        // Setup
        when(mockPsbcProperties.getStaleLimit()).thenReturn(0);

        // Configure StaleScriptMapper.getData(...).
        final InfoVersion infoVersion = new InfoVersion();
        infoVersion.setId(0L);
        infoVersion.setInfoUniqueUuid("infoUniqueUuid");
        infoVersion.setSrcScriptUuid("srcScriptUuid");
        infoVersion.setVersion("defaultVersion");
        infoVersion.setCreatorId(0L);
        infoVersion.setCreatorName("creatorName");
        infoVersion.setUpdateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        final List<InfoVersion> infoVersions = Arrays.asList(infoVersion);
        when(mockStaleScriptMapper.getData()).thenReturn(infoVersions);

        // Configure StaleScriptMapper.getStaleData(...).
        final Stale stale = new Stale();
        stale.setId(0L);
        stale.setInfoId(0L);
        stale.setInfoVersionId(0L);
        stale.setScriptNameZh("scriptNameZh");
        stale.setScriptName("scriptName");
        stale.setCategoryPath("categoryPath");
        stale.setDefaultVersion("defaultVersion");
        stale.setTaskCount(0);
        stale.setSuccessRate("successRate");
        stale.setUnmodifyDay(1);
        stale.setConfirmState(0);
        stale.setInfoUpdatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        stale.setCreatorId(0L);
        stale.setCreatorName("creatorName");
        when(mockStaleScriptMapper.getStaleData(0L)).thenReturn(stale);

        // Configure InfoVersionMapper.selectInfoVersionBysrcScriptUuid(...).
        final InfoVersion infoVersion1 = new InfoVersion();
        infoVersion1.setId(0L);
        infoVersion1.setInfoUniqueUuid("infoUniqueUuid");
        infoVersion1.setSrcScriptUuid("srcScriptUuid");
        infoVersion1.setVersion("defaultVersion");
        infoVersion1.setCreatorId(0L);
        infoVersion1.setCreatorName("creatorName");
        infoVersion1.setUpdateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockInfoVersionMapper.selectInfoVersionBysrcScriptUuid("srcScriptUuid")).thenReturn(infoVersion1);
        
        // 设置mockScriptService.toAlarm方法的行为
        doNothing().when(mockScriptService).toAlarm(anyString(), anyLong());

        // Run the test
        psbcStaleScriptXxlJobHandlerUnderTest.staleScriptHandler();

        // 验证方法未被调用
        verify(mockStaleScriptMapper, never()).deleteStaleByInfoId(anyLong());
        
        // 验证更新方法被调用，并检查参数
        ArgumentCaptor<Integer> dayCaptor = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Long> idCaptor = ArgumentCaptor.forClass(Long.class);
        verify(mockStaleScriptMapper).updateStaleData(dayCaptor.capture(), idCaptor.capture());
        assertEquals(0L, idCaptor.getValue());
        
        // 验证告警方法被调用
        verify(mockScriptService).toAlarm(eq("srcScriptUuid"), eq(0L));
    }

    @Test
    @DisplayName("测试长时间未修改脚本处理-不存在过期脚本数据")
    void testStaleScriptHandler_StaleScriptMapperGetDataReturnsNoItems() {
        // Setup
        when(mockPsbcProperties.getStaleLimit()).thenReturn(0);
        when(mockStaleScriptMapper.getData()).thenReturn(Collections.emptyList());
        
        // 移除不必要的存根，只保留必要的mock
        // doNothing().when(mockScriptService).toAlarm(anyString(), anyLong());

        // Run the test
        psbcStaleScriptXxlJobHandlerUnderTest.staleScriptHandler();

        // 验证告警方法未被调用
        verify(mockScriptService, never()).toAlarm(anyString(), anyLong());
    }

    @Test
    @DisplayName("测试长时间未修改脚本处理-存在过期脚本但stale数据不存在")
    void testStaleScriptHandler_StaleScriptMapperGetStaleDataReturnsNull() {
        // Setup
        when(mockPsbcProperties.getStaleLimit()).thenReturn(0);

        // Configure StaleScriptMapper.getData(...).
        final InfoVersion infoVersion = new InfoVersion();
        infoVersion.setId(0L);
        infoVersion.setInfoUniqueUuid("infoUniqueUuid");
        infoVersion.setSrcScriptUuid("srcScriptUuid");
        infoVersion.setVersion("defaultVersion");
        infoVersion.setCreatorId(0L);
        infoVersion.setCreatorName("creatorName");
        infoVersion.setUpdateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        final List<InfoVersion> infoVersions = Arrays.asList(infoVersion);
        when(mockStaleScriptMapper.getData()).thenReturn(infoVersions);

        when(mockStaleScriptMapper.getStaleData(0L)).thenReturn(null);

        // Configure InfoVersionMapper.selectInfoVersionBysrcScriptUuid(...).
        final InfoVersion infoVersion1 = new InfoVersion();
        infoVersion1.setId(0L);
        infoVersion1.setInfoUniqueUuid("infoUniqueUuid");
        infoVersion1.setSrcScriptUuid("srcScriptUuid");
        infoVersion1.setVersion("defaultVersion");
        infoVersion1.setCreatorId(0L);
        infoVersion1.setCreatorName("creatorName");
        infoVersion1.setUpdateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockInfoVersionMapper.selectInfoVersionBysrcScriptUuid("srcScriptUuid")).thenReturn(infoVersion1);

        // Configure InfoMapper.selectInfoByUniqueUuid(...).
        final Info info = new Info();
        info.setCategoryPath("categoryPath");
        info.setScriptSource(0);
        info.setId(0L);
        info.setScriptNameZh("scriptNameZh");
        info.setScriptName("scriptName");
        when(mockInfoMapper.selectInfoByUniqueUuid("infoUniqueUuid")).thenReturn(info);

        // Configure ExectimeMapper.getTotalAndSuccessRate(...).
        final Exectime exectime = new Exectime();
        exectime.setSuccessRate("successRate");
        exectime.setId(0L);
        exectime.setSuccessTimes(0L);
        exectime.setTotalTimes(0L);
        exectime.setSrcScriptUuid("srcScriptUuid");
        when(mockExectimeMapper.getTotalAndSuccessRate("srcScriptUuid")).thenReturn(exectime);
        
        // 设置mockScriptService.buildCategoryPath和toAlarm方法的行为
        doNothing().when(mockScriptService).buildCategoryPath(any(Info.class));
        doNothing().when(mockScriptService).toAlarm(anyString(), anyLong());

        // Run the test
        psbcStaleScriptXxlJobHandlerUnderTest.staleScriptHandler();

        // 验证方法调用
        verify(mockScriptService).buildCategoryPath(any(Info.class));
        
        // 验证插入方法被调用
        ArgumentCaptor<Stale> staleCaptor = ArgumentCaptor.forClass(Stale.class);
        verify(mockStaleScriptMapper).insertStaleData(staleCaptor.capture());
        assertEquals("scriptName", staleCaptor.getValue().getScriptName());
        
        // 验证告警方法被调用
        verify(mockScriptService).toAlarm(eq("srcScriptUuid"), eq(0L));
    }

    @Test
    @DisplayName("测试长时间未修改脚本处理-执行时间数据为空")
    void testStaleScriptHandler_ExectimeMapperReturnsNull() {
        // Setup
        when(mockPsbcProperties.getStaleLimit()).thenReturn(0);

        // Configure StaleScriptMapper.getData(...).
        final InfoVersion infoVersion = new InfoVersion();
        infoVersion.setId(0L);
        infoVersion.setInfoUniqueUuid("infoUniqueUuid");
        infoVersion.setSrcScriptUuid("srcScriptUuid");
        infoVersion.setVersion("defaultVersion");
        infoVersion.setCreatorId(0L);
        infoVersion.setCreatorName("creatorName");
        infoVersion.setUpdateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        final List<InfoVersion> infoVersions = Arrays.asList(infoVersion);
        when(mockStaleScriptMapper.getData()).thenReturn(infoVersions);

        when(mockStaleScriptMapper.getStaleData(0L)).thenReturn(null);

        // Configure InfoVersionMapper.selectInfoVersionBysrcScriptUuid(...).
        final InfoVersion infoVersion1 = new InfoVersion();
        infoVersion1.setId(0L);
        infoVersion1.setInfoUniqueUuid("infoUniqueUuid");
        infoVersion1.setSrcScriptUuid("srcScriptUuid");
        infoVersion1.setVersion("defaultVersion");
        infoVersion1.setCreatorId(0L);
        infoVersion1.setCreatorName("creatorName");
        infoVersion1.setUpdateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockInfoVersionMapper.selectInfoVersionBysrcScriptUuid("srcScriptUuid")).thenReturn(infoVersion1);

        // Configure InfoMapper.selectInfoByUniqueUuid(...).
        final Info info = new Info();
        info.setCategoryPath("categoryPath");
        info.setScriptSource(0);
        info.setId(0L);
        info.setScriptNameZh("scriptNameZh");
        info.setScriptName("scriptName");
        when(mockInfoMapper.selectInfoByUniqueUuid("infoUniqueUuid")).thenReturn(info);

        when(mockExectimeMapper.getTotalAndSuccessRate("srcScriptUuid")).thenReturn(null);
        
        // 设置mockScriptService.buildCategoryPath和toAlarm方法的行为
        doNothing().when(mockScriptService).buildCategoryPath(any(Info.class));
        doNothing().when(mockScriptService).toAlarm(anyString(), anyLong());

        // Run the test
        psbcStaleScriptXxlJobHandlerUnderTest.staleScriptHandler();

        // 验证方法调用
        verify(mockScriptService).buildCategoryPath(any(Info.class));
        
        // 验证插入方法被调用
        ArgumentCaptor<Stale> staleCaptor = ArgumentCaptor.forClass(Stale.class);
        verify(mockStaleScriptMapper).insertStaleData(staleCaptor.capture());
        assertEquals("0%", staleCaptor.getValue().getSuccessRate());
        assertEquals(0, staleCaptor.getValue().getTaskCount());
        
        // 验证告警方法被调用
        verify(mockScriptService).toAlarm(eq("srcScriptUuid"), eq(0L));
    }
}
