package com.ideal.script.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.script.model.dto.VarAndFuncForEditDto;
import com.ideal.script.model.dto.VariablePublishDto;
import com.ideal.script.service.IVariablePublishService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * VariablePublishController单元测试类
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class VariablePublishControllerTest {

    @Mock
    private IVariablePublishService variablePublishService;

    @InjectMocks
    private VariablePublishController variablePublishController;

    private VarAndFuncForEditDto varAndFuncForEditDto;
    private VariablePublishDto variablePublishDto;
    private TableQueryDto<VarAndFuncForEditDto> tableQueryDto;
    private PageInfo<VariablePublishDto> pageInfo;

    @BeforeEach
    void setUp() {
        // 初始化VarAndFuncForEditDto测试数据
        varAndFuncForEditDto = new VarAndFuncForEditDto();
        varAndFuncForEditDto.setBindState(1);
        varAndFuncForEditDto.setBindIds(new Long[]{1L, 2L, 3L});
        varAndFuncForEditDto.setKeyword("test");

        // 初始化VariablePublishDto测试数据
        variablePublishDto = new VariablePublishDto();
        variablePublishDto.setId(1L);
        variablePublishDto.setScriptVariableClassId(100L);
        variablePublishDto.setName("testVariable");
        variablePublishDto.setType(1L);
        variablePublishDto.setValue("testValue");
        variablePublishDto.setDesc("测试变量描述");
        variablePublishDto.setAttribute(1L);
        variablePublishDto.setGlobal(1L);
        variablePublishDto.setStatus(2L);
        variablePublishDto.setUserglobal(1L);
        variablePublishDto.setMd5("test-md5-hash");
        variablePublishDto.setCreatorId(10L);
        variablePublishDto.setCreatorName("测试创建人");
        variablePublishDto.setUpdatorId(20L);
        variablePublishDto.setUpdatorName("测试修改人");
        variablePublishDto.setCreateTime(new Timestamp(System.currentTimeMillis()));
        variablePublishDto.setUpdateTime(new Timestamp(System.currentTimeMillis()));

        // 初始化TableQueryDto测试数据
        tableQueryDto = new TableQueryDto<>();
        tableQueryDto.setPageNum(1);
        tableQueryDto.setPageSize(10);
        tableQueryDto.setQueryParam(varAndFuncForEditDto);

        // 初始化PageInfo测试数据
        pageInfo = new PageInfo<>();
        pageInfo.setList(Arrays.asList(variablePublishDto));
        pageInfo.setTotal(1);
        pageInfo.setPageNum(1);
        pageInfo.setPageSize(10);
    }

    @Test
    @DisplayName("查询变量发布列表用于编辑-成功")
    void listVariablePublishForEdit_success() {
        // Mock service方法
        doReturn(pageInfo).when(variablePublishService).selectVariablePublishListForEdit(any(VarAndFuncForEditDto.class), eq(1), eq(10));

        // 执行测试方法
        R<Object> result = variablePublishController.listVariablePublishForEdit(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("list.success", result.getMessage());
        assertEquals(pageInfo, result.getData());
        
        // 验证返回的数据内容
        PageInfo<VariablePublishDto> resultPageInfo = (PageInfo<VariablePublishDto>) result.getData();
        assertEquals(1, resultPageInfo.getTotal());
        assertEquals(1, resultPageInfo.getList().size());
        assertEquals("testVariable", resultPageInfo.getList().get(0).getName());
        assertEquals("testValue", resultPageInfo.getList().get(0).getValue());
        assertEquals("测试变量描述", resultPageInfo.getList().get(0).getDesc());
        
        // 验证service方法被调用
        verify(variablePublishService, times(1)).selectVariablePublishListForEdit(any(VarAndFuncForEditDto.class), eq(1), eq(10));
    }

    @Test
    @DisplayName("查询变量发布列表用于编辑-空结果")
    void listVariablePublishForEdit_emptyResult() {
        // 创建空的分页结果
        PageInfo<VariablePublishDto> emptyPageInfo = new PageInfo<>();
        emptyPageInfo.setList(Collections.emptyList());
        emptyPageInfo.setTotal(0);
        emptyPageInfo.setPageNum(1);
        emptyPageInfo.setPageSize(10);

        // Mock service方法
        doReturn(emptyPageInfo).when(variablePublishService).selectVariablePublishListForEdit(any(VarAndFuncForEditDto.class), eq(1), eq(10));

        // 执行测试方法
        R<Object> result = variablePublishController.listVariablePublishForEdit(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("list.success", result.getMessage());
        assertEquals(emptyPageInfo, result.getData());
        
        // 验证返回的数据内容
        PageInfo<VariablePublishDto> resultPageInfo = (PageInfo<VariablePublishDto>) result.getData();
        assertEquals(0, resultPageInfo.getTotal());
        assertTrue(resultPageInfo.getList().isEmpty());
        
        // 验证service方法被调用
        verify(variablePublishService, times(1)).selectVariablePublishListForEdit(any(VarAndFuncForEditDto.class), eq(1), eq(10));
    }

    @Test
    @DisplayName("查询变量发布列表用于编辑-空查询参数")
    void listVariablePublishForEdit_nullQueryParam() {
        // 设置空查询参数
        tableQueryDto.setQueryParam(null);

        // Mock service方法
        doReturn(pageInfo).when(variablePublishService).selectVariablePublishListForEdit(any(), eq(1), eq(10));

        // 执行测试方法
        R<Object> result = variablePublishController.listVariablePublishForEdit(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("list.success", result.getMessage());
        assertEquals(pageInfo, result.getData());
        
        // 验证service方法被调用
        verify(variablePublishService, times(1)).selectVariablePublishListForEdit(any(), eq(1), eq(10));
    }

    @Test
    @DisplayName("查询变量发布列表用于编辑-分页参数测试")
    void listVariablePublishForEdit_paginationTest() {
        // 设置不同的分页参数
        tableQueryDto.setPageNum(2);
        tableQueryDto.setPageSize(20);

        // 创建对应的分页结果
        PageInfo<VariablePublishDto> customPageInfo = new PageInfo<>();
        customPageInfo.setList(Arrays.asList(variablePublishDto));
        customPageInfo.setTotal(1);
        customPageInfo.setPageNum(2);
        customPageInfo.setPageSize(20);

        // Mock service方法
        doReturn(customPageInfo).when(variablePublishService).selectVariablePublishListForEdit(any(VarAndFuncForEditDto.class), eq(2), eq(20));

        // 执行测试方法
        R<Object> result = variablePublishController.listVariablePublishForEdit(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("list.success", result.getMessage());
        assertEquals(customPageInfo, result.getData());
        
        // 验证返回的数据内容
        PageInfo<VariablePublishDto> resultPageInfo = (PageInfo<VariablePublishDto>) result.getData();
        assertEquals(2, resultPageInfo.getPageNum());
        assertEquals(20, resultPageInfo.getPageSize());
        
        // 验证service方法被调用
        verify(variablePublishService, times(1)).selectVariablePublishListForEdit(any(VarAndFuncForEditDto.class), eq(2), eq(20));
    }

    @Test
    @DisplayName("查询变量发布列表用于编辑-多条记录")
    void listVariablePublishForEdit_multipleRecords() {
        // 创建多条记录
        VariablePublishDto variablePublishDto2 = new VariablePublishDto();
        variablePublishDto2.setId(2L);
        variablePublishDto2.setName("testVariable2");
        variablePublishDto2.setValue("testValue2");
        variablePublishDto2.setDesc("测试变量描述2");
        variablePublishDto2.setAttribute(2L);
        variablePublishDto2.setStatus(1L);

        // 创建包含多条记录的分页结果
        PageInfo<VariablePublishDto> multiPageInfo = new PageInfo<>();
        multiPageInfo.setList(Arrays.asList(variablePublishDto, variablePublishDto2));
        multiPageInfo.setTotal(2);
        multiPageInfo.setPageNum(1);
        multiPageInfo.setPageSize(10);

        // Mock service方法
        doReturn(multiPageInfo).when(variablePublishService).selectVariablePublishListForEdit(any(VarAndFuncForEditDto.class), eq(1), eq(10));

        // 执行测试方法
        R<Object> result = variablePublishController.listVariablePublishForEdit(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("list.success", result.getMessage());
        assertEquals(multiPageInfo, result.getData());
        
        // 验证返回的数据内容
        PageInfo<VariablePublishDto> resultPageInfo = (PageInfo<VariablePublishDto>) result.getData();
        assertEquals(2, resultPageInfo.getTotal());
        assertEquals(2, resultPageInfo.getList().size());
        assertEquals("testVariable", resultPageInfo.getList().get(0).getName());
        assertEquals("testVariable2", resultPageInfo.getList().get(1).getName());
        
        // 验证service方法被调用
        verify(variablePublishService, times(1)).selectVariablePublishListForEdit(any(VarAndFuncForEditDto.class), eq(1), eq(10));
    }

    @Test
    @DisplayName("查询变量发布列表用于编辑-绑定状态过滤")
    void listVariablePublishForEdit_bindStateFilter() {
        // 设置特定的绑定状态
        varAndFuncForEditDto.setBindState(2); // 未绑定状态
        varAndFuncForEditDto.setBindIds(new Long[]{4L, 5L, 6L});
        varAndFuncForEditDto.setKeyword("filter");

        // Mock service方法
        doReturn(pageInfo).when(variablePublishService).selectVariablePublishListForEdit(any(VarAndFuncForEditDto.class), eq(1), eq(10));

        // 执行测试方法
        R<Object> result = variablePublishController.listVariablePublishForEdit(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("list.success", result.getMessage());
        assertEquals(pageInfo, result.getData());
        
        // 验证service方法被调用
        verify(variablePublishService, times(1)).selectVariablePublishListForEdit(any(VarAndFuncForEditDto.class), eq(1), eq(10));
    }

    @Test
    @DisplayName("查询变量发布列表用于编辑-关键字搜索")
    void listVariablePublishForEdit_keywordSearch() {
        // 设置关键字搜索
        varAndFuncForEditDto.setKeyword("variable");
        varAndFuncForEditDto.setBindState(0);
        varAndFuncForEditDto.setBindIds(null);

        // Mock service方法
        doReturn(pageInfo).when(variablePublishService).selectVariablePublishListForEdit(any(VarAndFuncForEditDto.class), eq(1), eq(10));

        // 执行测试方法
        R<Object> result = variablePublishController.listVariablePublishForEdit(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("list.success", result.getMessage());
        assertEquals(pageInfo, result.getData());
        
        // 验证service方法被调用
        verify(variablePublishService, times(1)).selectVariablePublishListForEdit(any(VarAndFuncForEditDto.class), eq(1), eq(10));
    }

    @Test
    @DisplayName("查询变量发布列表用于编辑-service抛出异常")
    void listVariablePublishForEdit_serviceException() {
        // Mock service方法抛出异常
        doThrow(new RuntimeException("Database connection failed")).when(variablePublishService)
                .selectVariablePublishListForEdit(any(VarAndFuncForEditDto.class), eq(1), eq(10));

        // 执行测试方法并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            variablePublishController.listVariablePublishForEdit(tableQueryDto);
        });

        // 验证异常信息
        assertEquals("Database connection failed", exception.getMessage());
        
        // 验证service方法被调用
        verify(variablePublishService, times(1)).selectVariablePublishListForEdit(any(VarAndFuncForEditDto.class), eq(1), eq(10));
    }

    @Test
    @DisplayName("查询变量发布列表用于编辑-验证变量属性")
    void listVariablePublishForEdit_variableAttributes() {
        // 创建具有不同属性的变量
        VariablePublishDto builtinVariable = new VariablePublishDto();
        builtinVariable.setId(3L);
        builtinVariable.setName("builtinVar");
        builtinVariable.setAttribute(2L); // 内置变量
        builtinVariable.setGlobal(1L); // 全域有效
        builtinVariable.setStatus(2L); // 已发布

        PageInfo<VariablePublishDto> attributePageInfo = new PageInfo<>();
        attributePageInfo.setList(Arrays.asList(builtinVariable));
        attributePageInfo.setTotal(1);
        attributePageInfo.setPageNum(1);
        attributePageInfo.setPageSize(10);

        // Mock service方法
        doReturn(attributePageInfo).when(variablePublishService).selectVariablePublishListForEdit(any(VarAndFuncForEditDto.class), eq(1), eq(10));

        // 执行测试方法
        R<Object> result = variablePublishController.listVariablePublishForEdit(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("list.success", result.getMessage());
        
        // 验证返回的数据内容
        PageInfo<VariablePublishDto> resultPageInfo = (PageInfo<VariablePublishDto>) result.getData();
        assertEquals(1, resultPageInfo.getTotal());
        VariablePublishDto resultVariable = resultPageInfo.getList().get(0);
        assertEquals("builtinVar", resultVariable.getName());
        assertEquals(Long.valueOf(2L), resultVariable.getAttribute()); // 内置变量
        assertEquals(Long.valueOf(1L), resultVariable.getGlobal()); // 全域有效
        assertEquals(Long.valueOf(2L), resultVariable.getStatus()); // 已发布
        
        // 验证service方法被调用
        verify(variablePublishService, times(1)).selectVariablePublishListForEdit(any(VarAndFuncForEditDto.class), eq(1), eq(10));
    }
}
