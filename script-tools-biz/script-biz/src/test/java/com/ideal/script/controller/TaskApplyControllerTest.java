package com.ideal.script.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.common.util.spring.MessageUtil;
import com.ideal.common.util.spring.SpringUtil;
import com.ideal.sc.CustomerProperty;
import com.ideal.sc.util.CurrentUserUtil;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.ScriptAuditDetailDto;
import com.ideal.script.model.dto.ScriptExecAuditDto;
import com.ideal.script.model.dto.TaskApplyDto;
import com.ideal.script.model.dto.TaskApplyQueryDto;
import com.ideal.script.model.dto.TaskAttachmentDto;
import com.ideal.script.model.dto.TaskDto;
import com.ideal.script.model.dto.interaction.agent.UserInfoDto;
import com.ideal.script.service.AuditSource;
import com.ideal.script.service.ITaskApplyService;
import com.ideal.system.common.component.model.CurrentUser;
import com.ideal.system.dto.ServicePermissionApiQueryDto;
import com.ideal.system.dto.UserInfoApiDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TaskApplyControllerTest {

    @Mock
    private ITaskApplyService mockTaskApplyService;
    @Mock
    private AuditSource mockTaskApplySource;
    @Mock
    private AuditSource mockReAuditSource;
    @Mock
    private CustomerProperty mockCustomerProperty;

    private TaskApplyController taskApplyControllerUnderTest;
    private CurrentUser currentUser;

    @BeforeEach
    void setUp() throws Exception {
        taskApplyControllerUnderTest = new TaskApplyController(mockTaskApplyService, mockTaskApplySource,
                mockReAuditSource);
        currentUser = new CurrentUser();
        currentUser.setId(1L);
        currentUser.setLoginName("admin");
        currentUser.setFullName("超级管理员");
        currentUser.setOrgCode(null);
        currentUser.setOrgId(null);
        currentUser.setSupervisor(true);
    }

    @Test
    @DisplayName("测试任务申请列表")
    void testListTaskApply() {
        // Setup
        final TableQueryDto<TaskApplyQueryDto> tableQueryDTO = new TableQueryDto<>();
        tableQueryDTO.setPageNum(0);
        tableQueryDTO.setPageSize(0);
        final TaskApplyQueryDto taskApplyQueryDto = new TaskApplyQueryDto();
        taskApplyQueryDto.setDutyApply(false);
        taskApplyQueryDto.setKeyword("keyword");
        tableQueryDTO.setQueryParam(taskApplyQueryDto);

        // Configure ITaskApplyService.selectTaskApplyList(...).
        final TaskApplyDto taskApplyDto = new TaskApplyDto();
        taskApplyDto.setScriptInfoId(0L);
        taskApplyDto.setUniqueUuid("uniqueUuid");
        taskApplyDto.setScriptNameZh("scriptNameZh");
        taskApplyDto.setScriptName("scriptName");
        taskApplyDto.setScriptType("scriptType");
        final PageInfo<TaskApplyDto> taskApplyDtoPageInfo = new PageInfo<>(Arrays.asList(taskApplyDto), 0);
        
        try (MockedStatic<CurrentUserUtil> mockedCurrentUserUtil = mockStatic(CurrentUserUtil.class)) {
            mockedCurrentUserUtil.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);
            
            doReturn(taskApplyDtoPageInfo).when(mockTaskApplyService).selectTaskApplyList(
                any(TaskApplyQueryDto.class), anyInt(), anyInt(), any(CurrentUser.class));

            // Run the test
            final R<PageInfo<TaskApplyDto>> result = taskApplyControllerUnderTest.listTaskApply(tableQueryDTO);

            // Verify the results
            verify(mockTaskApplyService).selectTaskApplyList(any(TaskApplyQueryDto.class), anyInt(), anyInt(), any(CurrentUser.class));
        }
    }

    @Test
    @DisplayName("测试任务申请列表-返回空结果")
    void testListTaskApply_ITaskApplyServiceReturnsNoItem() {
        // Setup
        final TableQueryDto<TaskApplyQueryDto> tableQueryDTO = new TableQueryDto<>();
        tableQueryDTO.setPageNum(0);
        tableQueryDTO.setPageSize(0);
        final TaskApplyQueryDto taskApplyQueryDto = new TaskApplyQueryDto();
        taskApplyQueryDto.setDutyApply(false);
        taskApplyQueryDto.setKeyword("keyword");
        tableQueryDTO.setQueryParam(taskApplyQueryDto);

        try (MockedStatic<CurrentUserUtil> mockedCurrentUserUtil = mockStatic(CurrentUserUtil.class)) {
            mockedCurrentUserUtil.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);
            
            doReturn(PageInfo.emptyPageInfo()).when(mockTaskApplyService).selectTaskApplyList(
                any(TaskApplyQueryDto.class), anyInt(), anyInt(), any(CurrentUser.class));

            // Run the test
            final R<PageInfo<TaskApplyDto>> result = taskApplyControllerUnderTest.listTaskApply(tableQueryDTO);

            // Verify the results
            verify(mockTaskApplyService).selectTaskApplyList(any(TaskApplyQueryDto.class), anyInt(), anyInt(), any(CurrentUser.class));
        }
    }

    @Test
    @DisplayName("测试根据权限码查询用户信息列表")
    void testQueryUserInfoListByPermissionCode() {
        // Setup
        // Configure ITaskApplyService.queryUserInfoListByPermissionCode(...).
        final UserInfoDto userInfoDto = new UserInfoDto();
        userInfoDto.setId(0L);
        userInfoDto.setLoginName("loginName");
        userInfoDto.setEmail("email");
        userInfoDto.setPhone("phone");
        userInfoDto.setFullName("fullName");
        final List<UserInfoDto> userInfoDtos = Arrays.asList(userInfoDto);
        when(mockTaskApplyService.queryUserInfoListByPermissionCode("permissionCode")).thenReturn(userInfoDtos);

        // Run the test
        final R<List<UserInfoDto>> result = taskApplyControllerUnderTest.queryUserInfoListByPermissionCode(
                "permissionCode");

        // Verify the results
        verify(mockTaskApplyService).queryUserInfoListByPermissionCode("permissionCode");
    }

    @Test
    @DisplayName("测试根据权限码查询用户信息列表-返回空结果")
    void testQueryUserInfoListByPermissionCode_ITaskApplyServiceReturnsNoItems() {
        // Setup
        when(mockTaskApplyService.queryUserInfoListByPermissionCode("permissionCode"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final R<List<UserInfoDto>> result = taskApplyControllerUnderTest.queryUserInfoListByPermissionCode(
                "permissionCode");

        // Verify the results
        verify(mockTaskApplyService).queryUserInfoListByPermissionCode("permissionCode");
    }

    @Test
    @DisplayName("测试查询权限用户信息列表")
    void testQueryPermissionUserInfoList() {
        // Setup
        final ServicePermissionApiQueryDto servicePermissionApiQueryDto = new ServicePermissionApiQueryDto();
        servicePermissionApiQueryDto.setId(0L);
        servicePermissionApiQueryDto.setPermissionCodes(Arrays.asList("value"));
        servicePermissionApiQueryDto.setName("name");
        servicePermissionApiQueryDto.setOrgManagementIds(Arrays.asList(0L));
        servicePermissionApiQueryDto.setUserLevel(0);

        // Configure ITaskApplyService.queryPermissionUserInfoList(...).
        final UserInfoApiDto userInfoApiDto = new UserInfoApiDto();
        userInfoApiDto.setAccess("access");
        userInfoApiDto.setId(0L);
        userInfoApiDto.setLoginName("loginName");
        userInfoApiDto.setEmail("email");
        userInfoApiDto.setFullName("fullName");
        final List<UserInfoApiDto> userInfoApiDtos = Arrays.asList(userInfoApiDto);
        when(mockTaskApplyService.queryPermissionUserInfoList(any(ServicePermissionApiQueryDto.class),
                eq(0L))).thenReturn(userInfoApiDtos);

        // Run the test
        final R<List<UserInfoApiDto>> result = taskApplyControllerUnderTest.queryPermissionUserInfoList(
                servicePermissionApiQueryDto, 0L);

        // Verify the results
        verify(mockTaskApplyService).queryPermissionUserInfoList(any(ServicePermissionApiQueryDto.class), eq(0L));
    }

    @Test
    @DisplayName("测试查询权限用户信息列表-返回空结果")
    void testQueryPermissionUserInfoList_ITaskApplyServiceReturnsNoItems() {
        // Setup
        final ServicePermissionApiQueryDto servicePermissionApiQueryDto = new ServicePermissionApiQueryDto();
        servicePermissionApiQueryDto.setId(0L);
        servicePermissionApiQueryDto.setPermissionCodes(Arrays.asList("value"));
        servicePermissionApiQueryDto.setName("name");
        servicePermissionApiQueryDto.setOrgManagementIds(Arrays.asList(0L));
        servicePermissionApiQueryDto.setUserLevel(0);

        when(mockTaskApplyService.queryPermissionUserInfoList(any(ServicePermissionApiQueryDto.class),
                eq(0L))).thenReturn(Collections.emptyList());

        // Run the test
        final R<List<UserInfoApiDto>> result = taskApplyControllerUnderTest.queryPermissionUserInfoList(
                servicePermissionApiQueryDto, 0L);

        // Verify the results
        verify(mockTaskApplyService).queryPermissionUserInfoList(any(ServicePermissionApiQueryDto.class), eq(0L));
    }

    @Test
    @DisplayName("测试查询部门用户信息列表")
    void testQueryDepartmentUserInfoList() {
        // Setup
        // Configure ITaskApplyService.queryDepartmentUserInfoList(...).
        final UserInfoApiDto userInfoApiDto = new UserInfoApiDto();
        userInfoApiDto.setAccess("access");
        userInfoApiDto.setId(0L);
        userInfoApiDto.setLoginName("loginName");
        userInfoApiDto.setEmail("email");
        userInfoApiDto.setFullName("fullName");
        final List<UserInfoApiDto> userInfoApiDtos = Arrays.asList(userInfoApiDto);
        when(mockTaskApplyService.queryDepartmentUserInfoList(0L)).thenReturn(userInfoApiDtos);

        // Run the test
        final R<List<UserInfoApiDto>> result = taskApplyControllerUnderTest.queryDepartmentUserInfoList(0L);

        // Verify the results
        verify(mockTaskApplyService).queryDepartmentUserInfoList(0L);
    }

    @Test
    @DisplayName("测试查询部门用户信息列表-返回空结果")
    void testQueryDepartmentUserInfoList_ITaskApplyServiceReturnsNoItems() {
        // Setup
        when(mockTaskApplyService.queryDepartmentUserInfoList(0L)).thenReturn(Collections.emptyList());

        // Run the test
        final R<List<UserInfoApiDto>> result = taskApplyControllerUnderTest.queryDepartmentUserInfoList(0L);

        // Verify the results
        verify(mockTaskApplyService).queryDepartmentUserInfoList(0L);
    }

    @Test
    @DisplayName("测试脚本执行审核")
    void testScriptExecAuditing() throws Exception {
        // Setup
        final ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        scriptExecAuditDto.setTelephone("telephone");
        scriptExecAuditDto.setInvokeId("invokeId");
        final TaskDto taskInfo = new TaskDto();
        taskInfo.setId(0L);
        scriptExecAuditDto.setTaskInfo(taskInfo);

        try (MockedStatic<CurrentUserUtil> mockedCurrentUserUtil = mockStatic(CurrentUserUtil.class)) {
            mockedCurrentUserUtil.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);
            Map<String, Long> res = new HashMap<>();
            res.put("taskId", 2L);
            res.put("auditId", 2L);
            when(mockTaskApplyService.scriptExecAuditingForWeb(any(ScriptExecAuditDto.class), any(CurrentUser.class),
                    any(AuditSource.class))).thenReturn(res);

            // Run the test
            final R<Object> result = taskApplyControllerUnderTest.scriptExecAuditing(scriptExecAuditDto);

            // Verify the results
            verify(mockTaskApplyService).scriptExecAuditingForWeb(any(ScriptExecAuditDto.class), any(CurrentUser.class), any(AuditSource.class));
        }
    }

    @Test
    @DisplayName("测试脚本执行审核-抛出ScriptException异常")
    void testScriptExecAuditing_ITaskApplyServiceThrowsScriptException() throws Exception {
        // Setup
        final ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        scriptExecAuditDto.setTelephone("telephone");
        scriptExecAuditDto.setInvokeId("invokeId");
        final TaskDto taskInfo = new TaskDto();
        taskInfo.setId(0L);
        scriptExecAuditDto.setTaskInfo(taskInfo);

        try (MockedStatic<CurrentUserUtil> mockedCurrentUserUtil = mockStatic(CurrentUserUtil.class)) {
            mockedCurrentUserUtil.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);
            
            when(mockTaskApplyService.scriptExecAuditingForWeb(any(ScriptExecAuditDto.class), any(CurrentUser.class),
                    any(AuditSource.class))).thenThrow(new ScriptException("测试异常"));

            // Run the test
            assertThatThrownBy(() -> taskApplyControllerUnderTest.scriptExecAuditing(scriptExecAuditDto))
                    .isInstanceOf(ScriptException.class);
        }
    }

    @Test
    @DisplayName("测试获取审核人自身信息")
    void testGetAuditorUserSelf() {
        // Setup
        // Configure ITaskApplyService.getUserByUserId(...).
        final UserInfoDto userInfoDto = new UserInfoDto();
        userInfoDto.setId(0L);
        userInfoDto.setLoginName("loginName");
        userInfoDto.setEmail("email");
        userInfoDto.setPhone("phone");
        userInfoDto.setFullName("fullName");
        final List<UserInfoDto> userInfoDtos = Arrays.asList(userInfoDto);
        
        try (MockedStatic<CurrentUserUtil> mockedCurrentUserUtil = mockStatic(CurrentUserUtil.class)) {
            mockedCurrentUserUtil.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);
            
            doReturn(userInfoDtos).when(mockTaskApplyService).getUserByUserId(anyLong());

            // Run the test
            final R<UserInfoDto> result = taskApplyControllerUnderTest.getAuditorUserSelf();

            // Verify the results
            verify(mockTaskApplyService).getUserByUserId(1L);
        }
    }

    @Test
    @DisplayName("测试获取审核人自身信息-返回空结果")
    void testGetAuditorUserSelf_ITaskApplyServiceReturnsNoItems() {
        try (MockedStatic<CurrentUserUtil> mockedCurrentUserUtil = mockStatic(CurrentUserUtil.class)) {
            mockedCurrentUserUtil.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);
            
            doReturn(Collections.emptyList()).when(mockTaskApplyService).getUserByUserId(anyLong());

            // Run the test
            assertThatThrownBy(() -> taskApplyControllerUnderTest.getAuditorUserSelf())
                    .isInstanceOf(IndexOutOfBoundsException.class);

            // Verify the results
            verify(mockTaskApplyService).getUserByUserId(1L);
        }
    }

    @Test
    @DisplayName("测试获取审核详情")
    void testGetAuditDetail() throws Exception {
        // Setup
        // Configure ITaskApplyService.getAuditDetail(...).
        final ScriptAuditDetailDto scriptAuditDetailDto = new ScriptAuditDetailDto();
        final TaskAttachmentDto taskAttachmentDto = new TaskAttachmentDto();
        taskAttachmentDto.setId(0L);
        taskAttachmentDto.setScriptTaskId(0L);
        taskAttachmentDto.setName("name");
        taskAttachmentDto.setSize(0L);
        scriptAuditDetailDto.setTaskAttachmentDtoList(Arrays.asList(taskAttachmentDto));
        when(mockTaskApplyService.getAuditDetail(0L, 0L)).thenReturn(scriptAuditDetailDto);

        // Run the test
        final R<ScriptAuditDetailDto> result = taskApplyControllerUnderTest.getAuditDetail(0L, 0L);

        // Verify the results
        verify(mockTaskApplyService).getAuditDetail(0L, 0L);
    }

    @Test
    @DisplayName("测试获取审核详情-抛出ScriptException异常")
    void testGetAuditDetail_ITaskApplyServiceThrowsScriptException() throws Exception {
        // Setup
        when(mockTaskApplyService.getAuditDetail(0L, 0L)).thenThrow(new ScriptException("测试异常"));

        // Run the test
        assertThatThrownBy(() -> taskApplyControllerUnderTest.getAuditDetail(0L, 0L))
                .isInstanceOf(ScriptException.class);
    }

    @Test
    @DisplayName("测试重新审核")
    void testReAudit1() throws Exception {
        // Setup
        final ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        scriptExecAuditDto.setTelephone("telephone");
        scriptExecAuditDto.setInvokeId("invokeId");
        final TaskDto taskInfo = new TaskDto();
        taskInfo.setId(0L);
        scriptExecAuditDto.setTaskInfo(taskInfo);

        try (MockedStatic<CurrentUserUtil> mockedCurrentUserUtil = mockStatic(CurrentUserUtil.class);
             MockedStatic<MessageUtil> mockedMessageUtil = mockStatic(MessageUtil.class)) {
            mockedCurrentUserUtil.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);
            mockedMessageUtil.when(() -> MessageUtil.message(anyString())).thenReturn("成功消息");
            
            when(mockTaskApplyService.scriptExecAuditing(any(ScriptExecAuditDto.class), any(CurrentUser.class),
                    any(AuditSource.class))).thenReturn(0L);

            // Run the test
            final R<?> result = taskApplyControllerUnderTest.reAudit(scriptExecAuditDto, 0L);

            // Verify the results
            verify(mockTaskApplyService).scriptExecAuditing(any(ScriptExecAuditDto.class), any(CurrentUser.class), any(AuditSource.class));
        }
    }

    @Test
    @DisplayName("测试重新审核-抛出ScriptException异常")
    void testReAudit1_ITaskApplyServiceThrowsScriptException() throws Exception {
        // Setup
        final ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        scriptExecAuditDto.setTelephone("telephone");
        scriptExecAuditDto.setInvokeId("invokeId");
        final TaskDto taskInfo = new TaskDto();
        taskInfo.setId(0L);
        scriptExecAuditDto.setTaskInfo(taskInfo);

        try (MockedStatic<CurrentUserUtil> mockedCurrentUserUtil = mockStatic(CurrentUserUtil.class)) {
            mockedCurrentUserUtil.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);
            
            when(mockTaskApplyService.scriptExecAuditing(any(ScriptExecAuditDto.class), any(CurrentUser.class),
                    any(AuditSource.class))).thenThrow(new ScriptException("测试异常"));

            // Run the test
            assertThatThrownBy(() -> taskApplyControllerUnderTest.reAudit(scriptExecAuditDto, 0L))
                    .isInstanceOf(ScriptException.class);
        }
    }

    @Test
    @DisplayName("测试重新审核2")
    void testReAudit2() {
        // Setup
        when(mockTaskApplyService.getScriptInfoByAuditRelationId(0L)).thenReturn(0L);

        // Run the test
        final R<?> result = taskApplyControllerUnderTest.reAudit(0L);

        // Verify the results
        verify(mockTaskApplyService).getScriptInfoByAuditRelationId(0L);
    }

    @Test
    @DisplayName("测试删除附件")
    void testDeleteAttachment() {
        // Setup
        // Run the test
        final R<?> result = taskApplyControllerUnderTest.deleteAttachment(0L);

        // Verify the results
        verify(mockTaskApplyService).deleteAttachment(0L);
    }

    @Test
    @DisplayName("测试导入服务器Excel")
    void testImportServerExcel() throws Exception {
        // Setup
        final MultipartFile file = new MockMultipartFile("name", "content".getBytes());
        
        try (MockedStatic<MessageUtil> mockedMessageUtil = mockStatic(MessageUtil.class)) {
            mockedMessageUtil.when(() -> MessageUtil.message(anyString())).thenReturn("成功消息");
            
            when(mockTaskApplyService.importServerExcel(any(MultipartFile.class))).thenReturn(new HashMap<>());

            // Run the test
            final R<?> result = taskApplyControllerUnderTest.importServerExcel(file);

            // Verify the results
            verify(mockTaskApplyService).importServerExcel(file);
        }
    }

    @Test
    @DisplayName("测试导入服务器Excel-抛出IOException异常")
    void testImportServerExcel_ITaskApplyServiceThrowsIOException() throws Exception {
        // Setup
        final MultipartFile file = new MockMultipartFile("name", "content".getBytes());
        
        try (MockedStatic<MessageUtil> mockedMessageUtil = mockStatic(MessageUtil.class)) {
            mockedMessageUtil.when(() -> MessageUtil.message(anyString())).thenReturn("失败消息");
            
            when(mockTaskApplyService.importServerExcel(any(MultipartFile.class))).thenThrow(new IOException());

            // Run the test
            final R<?> result = taskApplyControllerUnderTest.importServerExcel(file);

            // Verify the results
            verify(mockTaskApplyService).importServerExcel(file);
        }
    }

    @Test
    @DisplayName("测试导入服务器Excel-抛出ScriptException异常")
    void testImportServerExcel_ITaskApplyServiceThrowsScriptException() throws Exception {
        // Setup
        final MultipartFile file = new MockMultipartFile("name", "content".getBytes());
        
        try (MockedStatic<MessageUtil> mockedMessageUtil = mockStatic(MessageUtil.class)) {
            mockedMessageUtil.when(() -> MessageUtil.message(anyString())).thenReturn("失败消息");
            
            when(mockTaskApplyService.importServerExcel(any(MultipartFile.class))).thenThrow(new ScriptException("测试异常"));

            // Run the test
            final R<?> result = taskApplyControllerUnderTest.importServerExcel(file);

            // Verify the results
            verify(mockTaskApplyService).importServerExcel(file);
        }
    }

    @Test
    @DisplayName("测试下载导入模板")
    void testDownloadImportTemplate() {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Run the test
        taskApplyControllerUnderTest.downloadImportTemplate(response);

        // Verify the results
        verify(mockTaskApplyService).downloadImportTemplate(any(HttpServletResponse.class));
    }

    @Test
    @DisplayName("测试获取来源")
    void testGetSource() {
        // Setup
        try (MockedStatic<SpringUtil> mockedSpringUtil = mockStatic(SpringUtil.class)) {
            mockedSpringUtil.when(() -> SpringUtil.getBean(CustomerProperty.class)).thenReturn(mockCustomerProperty);
            when(mockCustomerProperty.getName()).thenReturn("测试客户");
            
            // Run the test
            final R<String> result = taskApplyControllerUnderTest.getSource();

            // Verify the results
            verify(mockCustomerProperty).getName();
        }
    }
}
