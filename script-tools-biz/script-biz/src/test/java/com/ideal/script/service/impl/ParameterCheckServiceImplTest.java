package com.ideal.script.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.ParameterCheckMapper;
import com.ideal.script.model.dto.ParameterCheckDto;
import com.ideal.script.model.entity.ParameterCheck;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ParameterCheckServiceImplTest {

    @Mock
    private ParameterCheckMapper mockParameterCheckMapper;
    @InjectMocks
    private ParameterCheckServiceImpl parameterCheckServiceImplUnderTest;


    @Test
    void testInsertParameterCheck() throws ScriptException {
        // Setup
        final ParameterCheckDto parameterCheckDto = new ParameterCheckDto();
        parameterCheckDto.setId(1L);
        parameterCheckDto.setRuleName("ruleName");
        parameterCheckDto.setCheckRule("checkRule");
        parameterCheckDto.setRuleDes("ruleDes");
        parameterCheckDto.setCreatorId(1L);

        // Run the test
        parameterCheckServiceImplUnderTest.insertParameterCheck(parameterCheckDto);

        // Verify the results
        verify(mockParameterCheckMapper).insertParameterCheck(any(ParameterCheck.class));
    }


    @Test
    void testInsertParameterCheck_checkDuplicateName() throws ScriptException {
        // Setup
        final ParameterCheckDto parameterCheckDto = new ParameterCheckDto();
        parameterCheckDto.setId(1L);
        parameterCheckDto.setRuleName("ruleName");
        parameterCheckDto.setCheckRule("checkRule");
        parameterCheckDto.setRuleDes("ruleDes");
        parameterCheckDto.setCreatorId(1L);


        List<ParameterCheck> parameterCheckList = new ArrayList<>();
        ParameterCheck parameterCheck = new ParameterCheck();
        parameterCheck.setId(2L);
        parameterCheckList.add(parameterCheck);
        // Run the test
        when(mockParameterCheckMapper.selectParameterCheckByName("ruleName")).thenReturn(parameterCheckList);

        assertThrows(ScriptException.class,()->{
            parameterCheckServiceImplUnderTest.insertParameterCheck(parameterCheckDto);
        });
    }

    @Test
    void testInsertParameterCheck_checkDuplicateName_MultipleData() throws ScriptException {
        // Setup
        final ParameterCheckDto parameterCheckDto = new ParameterCheckDto();
        parameterCheckDto.setId(1L);
        parameterCheckDto.setRuleName("ruleName");
        parameterCheckDto.setCheckRule("checkRule");
        parameterCheckDto.setRuleDes("ruleDes");
        parameterCheckDto.setCreatorId(1L);


        List<ParameterCheck> parameterCheckList = new ArrayList<>();
        ParameterCheck parameterCheck = new ParameterCheck();
        parameterCheck.setId(2L);

        ParameterCheck parameterCheck1 = new ParameterCheck();
        parameterCheck1.setId(3L);

        parameterCheckList.add(parameterCheck);
        parameterCheckList.add(parameterCheck1);
        // Run the test
        when(mockParameterCheckMapper.selectParameterCheckByName("ruleName")).thenReturn(parameterCheckList);

        assertThrows(ScriptException.class,()->{
            parameterCheckServiceImplUnderTest.insertParameterCheck(parameterCheckDto);
        });
    }

    @Test
    void testUpdateParameterCheck() throws ScriptException {
        // Setup
        final ParameterCheckDto parameterCheckDto = new ParameterCheckDto();
        parameterCheckDto.setId(1L);
        parameterCheckDto.setRuleName("ruleName");
        parameterCheckDto.setCheckRule("checkRule");
        parameterCheckDto.setRuleDes("ruleDes");
        parameterCheckDto.setCreatorId(1L);

        // Run the test
        parameterCheckServiceImplUnderTest.updateParameterCheck(parameterCheckDto);

        // Verify the results
        verify(mockParameterCheckMapper).updateParameterCheck(any(ParameterCheck.class));
    }

    @Test
    void testSelectParameterCheckList() {
        // Setup
        final ParameterCheckDto parameterCheckDto = new ParameterCheckDto();
        parameterCheckDto.setId(1L);
        parameterCheckDto.setRuleName("ruleName");
        parameterCheckDto.setCheckRule("checkRule");
        parameterCheckDto.setRuleDes("ruleDes");
        parameterCheckDto.setCreatorId(1L);

        // Configure ParameterCheckMapper.selectParameterCheckList(...).
        final ParameterCheck parameterCheck = new ParameterCheck();
        parameterCheck.setId(1L);
        parameterCheck.setRuleName("ruleName");
        parameterCheck.setCheckRule("checkRule");
        parameterCheck.setRuleDes("ruleDes");
        parameterCheck.setCreatorId(1L);
        Page<ParameterCheck> page = new Page<>();
        page.add(parameterCheck);
        when(mockParameterCheckMapper.selectParameterCheckList(any(ParameterCheck.class))).thenReturn(page);

        // Run the test
        final PageInfo<ParameterCheckDto> result = parameterCheckServiceImplUnderTest.selectParameterCheckList(
                parameterCheckDto, 1, 50);

        assertNotNull(result);
        System.out.println(result);
        // Verify the results
        Mockito.verify(mockParameterCheckMapper,times(1)).selectParameterCheckList(any(ParameterCheck.class));
    }


    @Test
    void testDeleteParameterCheckByIds() {
        // Setup
        // Run the test
        parameterCheckServiceImplUnderTest.deleteParameterCheckByIds(new Long[]{1L,2L});

        // Verify the results
        verify(mockParameterCheckMapper).deleteParameterCheckByIds(any(Long[].class));
    }

    @Test
    void selectParameterCheckById() {
     ParameterCheck parameterCheck =  new ParameterCheck();

     when(mockParameterCheckMapper.selectParameterCheckById(anyLong())).thenReturn(parameterCheck);

     final ParameterCheckDto result = parameterCheckServiceImplUnderTest.selectParameterCheckById(anyLong());

     assertNotNull(result);

    }

    @Test
    void selectParameterCheckByName() {
        List<ParameterCheck> parameterCheckList = new ArrayList<>();
        ParameterCheck parameterCheck = new ParameterCheck();
        parameterCheck.setCheckRule("^[A-Za-z]+$");
        parameterCheck.setId(1L);
        parameterCheckList.add(parameterCheck);
        when(mockParameterCheckMapper.selectParameterCheckByName(anyString())).thenReturn(parameterCheckList);

        final ParameterCheckDto result = parameterCheckServiceImplUnderTest.selectParameterCheckByName("ruleName");
        assertNotNull(result);
    }

    @Test
    void validParamterCheckExist() {
        when(mockParameterCheckMapper.validParamterCheckExist(anyString())).thenReturn(true);
        final boolean result = parameterCheckServiceImplUnderTest.validParamterCheckExist("ruleName");
        assertTrue(result);
    }
}
