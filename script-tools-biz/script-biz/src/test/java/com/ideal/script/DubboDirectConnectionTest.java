package com.ideal.script;

import com.alibaba.fastjson2.JSON;
import com.ideal.script.api.IScriptInfo;
import com.ideal.script.api.IScriptTask;
import com.ideal.script.dto.ScriptTaskApplyApiDto;
import com.ideal.script.exception.ScriptException;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.apache.dubbo.config.bootstrap.DubboBootstrap;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;

/**
 * Dubbo直连测试类任务启动接口
 */
public class DubboDirectConnectionTest {

    private static final Logger log = LoggerFactory.getLogger(DubboDirectConnectionTest.class);
    private static IScriptTask iScriptTask;
    private static IScriptInfo iScriptInfo;
    private static  DubboBootstrap bootstrap = null;
    // 直连地址测试地址
    private static final String DUBBO_ADDRESS = "dubbo://************:21270";

    @BeforeEach
    public void setUp() {
        
        // 创建应用配置
        ApplicationConfig application = new ApplicationConfig();
        application.setName("dubbo-direct-test");
        application.setSerializeCheckStatus("DISABLE");
        // 创建注册中心配置（设置为N/A表示不使用注册中心）
        RegistryConfig registry = new RegistryConfig();
        registry.setAddress("N/A");

        // 创建服务引用配置
        ReferenceConfig<IScriptTask> reference = new ReferenceConfig<>();
        reference.setInterface(IScriptTask.class);
        // 设置直连地址
        reference.setUrl(DUBBO_ADDRESS);
        // 设置超时时间
        reference.setTimeout(30000);
        // 设置重试次数
        reference.setRetries(0);
        // 设置版本和分组（根据服务提供者的配置）
        reference.setVersion("1.0.0");
        reference.setGroup("script");

        ReferenceConfig<IScriptInfo> reference2 = new ReferenceConfig<>();
        reference2.setInterface(IScriptInfo.class);
        // 设置直连地址
        reference2.setUrl(DUBBO_ADDRESS);
        // 设置超时时间
        reference2.setTimeout(30000);
        // 设置重试次数
        reference2.setRetries(0);
        // 设置版本和分组（根据服务提供者的配置）
        reference2.setVersion("1.0.0");
        reference2.setGroup("script");



        // 使用DubboBootstrap启动
        bootstrap = DubboBootstrap.newInstance();
        bootstrap.application(application)
                .registry(registry)
                .references(Arrays.asList(reference,reference2))
                .start();

        // 获取远程服务代理
        iScriptTask = reference.get();
        iScriptInfo = reference2.get();
    }

    @AfterEach
    public void tearDown() {
        if (bootstrap != null) {
            bootstrap.stop();
        }
    }


    @Test
    @Disabled
    @DisplayName("测试IScriptTask接口")
    public void testIScriptTask() {
        log.info("测试脚本任务启动...");
        String json = "{\n" +
                "    \"scriptUuid\": \"18ada039-fe7c-4b0e-8795-eea13b8c431f\",\n" +
                "    \"taskName\": \"参数测试123123\",\n" +
                "    \"chosedAgentUsers\": [\n" +
                "        {\n" +
                "            \"agentIp\": \"*********\",\n" +
                "            \"agentPort\": 15005\n" +
                "        }\n" +
                "    ],\n" +
                "    \"params\": [\n" +
                "        {\n" +
                "            \"paramType\": \"String\",\n" +
                "            \"paramDefaultValue\": \"ccxcx\",\n" +
                "            \"paramOrder\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"paramType\": \"String\",\n" +
                "            \"paramDefaultValue\": \"zzzzzz\",\n" +
                "            \"paramOrder\": 2\n" +
                "        }\n" +
                "    ],\n" +
                "\t\"execuser\":\"\",\n" +
                "\t\"startType\":0,\n" +
                "    \"resGroupFlag\": false\n" +
                "}";
        ScriptTaskApplyApiDto scriptTaskApplyApiDto = JSON.parseObject(json, ScriptTaskApplyApiDto.class);
        try {
            iScriptTask.scriptTaskApply(scriptTaskApplyApiDto);
        } catch (ScriptException e) {
            log.error("error:",e);
        }

    }

    @Test
    @Disabled
    @DisplayName("测试IScriptInfo接口")
    public void testIScriptInfo() throws ScriptException {
        iScriptInfo.exportScriptProduction(null);
    }
}
