package com.ideal.script.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ideal.script.mapper.TaskInstanceMapper;
import com.ideal.script.model.dto.TaskInstanceDto;
import com.ideal.script.model.dto.TaskRuntimeDto;
import com.ideal.script.model.entity.TaskInstance;
import com.ideal.script.service.interact.ToolsApiAct;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TaskInstanceServiceImplTest {

    @Mock
    private TaskInstanceMapper mockTaskInstanceMapper;
    @Mock
    private ToolsApiAct toolsApiAct;

    private TaskInstanceServiceImpl taskInstanceServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        taskInstanceServiceImplUnderTest = new TaskInstanceServiceImpl(mockTaskInstanceMapper,toolsApiAct);
    }

    @Test
    void testSelectTaskInstanceById() {
        // Setup
        // Configure TaskInstanceMapper.selectTaskInstanceById(...).
        final TaskInstance taskInstance = new TaskInstance();
        taskInstance.setId(0L);
        taskInstance.setScriptTaskId(0L);
        taskInstance.setSrcScriptUuid("srcScriptUuid");
        taskInstance.setEndTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        taskInstance.setServerNum(0);
        when(mockTaskInstanceMapper.selectTaskInstanceById(0L)).thenReturn(taskInstance);

        // Run the test
        final TaskInstanceDto result = taskInstanceServiceImplUnderTest.selectTaskInstanceById(0L);
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testSelectTaskInstanceList() {
        // Setup
        final TaskInstanceDto taskInstanceDto = new TaskInstanceDto();
        taskInstanceDto.setId(0L);
        taskInstanceDto.setScriptTaskId(0L);
        taskInstanceDto.setSrcScriptUuid("srcScriptUuid");
        taskInstanceDto.setEndTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        taskInstanceDto.setServerNum(0);

        // Configure TaskInstanceMapper.selectTaskInstanceList(...).
        final TaskInstance taskInstance = new TaskInstance();
        taskInstance.setId(0L);
        taskInstance.setScriptTaskId(0L);
        taskInstance.setSrcScriptUuid("srcScriptUuid");
        taskInstance.setEndTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        taskInstance.setServerNum(0);
        Page<TaskInstance> page = new Page<>();
        page.add(taskInstance);
        when(mockTaskInstanceMapper.selectTaskInstanceList(any(TaskInstance.class))).thenReturn(page);

        // Run the test
        final PageInfo<TaskInstanceDto> result = taskInstanceServiceImplUnderTest.selectTaskInstanceList(
                taskInstanceDto, 0, 0);
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testInsertTaskInstance() {
        // Setup
        final TaskInstanceDto taskInstanceDto = new TaskInstanceDto();
        taskInstanceDto.setId(0L);
        taskInstanceDto.setScriptTaskId(0L);
        taskInstanceDto.setSrcScriptUuid("srcScriptUuid");
        taskInstanceDto.setEndTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        taskInstanceDto.setServerNum(0);

        when(mockTaskInstanceMapper.insertTaskInstance(any(TaskInstance.class))).thenReturn(0);

        // Run the test
        final int result = taskInstanceServiceImplUnderTest.insertTaskInstance(taskInstanceDto);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testUpdateTaskInstance() {
        // Setup
        final TaskInstanceDto taskInstanceDto = new TaskInstanceDto();
        taskInstanceDto.setId(0L);
        taskInstanceDto.setScriptTaskId(0L);
        taskInstanceDto.setSrcScriptUuid("srcScriptUuid");
        taskInstanceDto.setEndTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        taskInstanceDto.setServerNum(0);

        when(mockTaskInstanceMapper.updateTaskInstance(any(TaskInstance.class))).thenReturn(0);

        // Run the test
        final int result = taskInstanceServiceImplUnderTest.updateTaskInstance(taskInstanceDto);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testDeleteTaskInstanceByIds() {
        // Setup
        when(mockTaskInstanceMapper.deleteTaskInstanceByIds(any(Long[].class))).thenReturn(0);

        // Run the test
        final int result = taskInstanceServiceImplUnderTest.deleteTaskInstanceByIds(new Long[]{0L});

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testDeleteTaskInstanceById() {
        // Setup
        when(mockTaskInstanceMapper.deleteTaskInstanceById(0L)).thenReturn(0);

        // Run the test
        final int result = taskInstanceServiceImplUnderTest.deleteTaskInstanceById(0L);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testUpdateServerNum() {
        // Setup
        // Run the test
        taskInstanceServiceImplUnderTest.updateServerNum(0L, Collections.singletonList(0));

        // Verify the results
        verify(mockTaskInstanceMapper).updateServerNum(0L, Collections.singletonList(0));
    }


    @Test
    void testUpdateState() {
        // Setup
        // Run the test
        taskInstanceServiceImplUnderTest.updateState(0, 0L,new int[]{0});

        // Verify the results
        verify(mockTaskInstanceMapper).updateState(0, 0L, new int[]{0});
    }

    @Test
    void testUpdateRunNum() {
        // Setup
        // Run the test
        taskInstanceServiceImplUnderTest.updateRunNum(0L);

        // Verify the results
        verify(mockTaskInstanceMapper).updateRunNum(0L);
    }

    @Test
    void testUpdateEndTime() {
        // Setup
        // Run the test
        taskInstanceServiceImplUnderTest.updateEndTime(0L, Arrays.asList(0));

        // Verify the results
        verify(mockTaskInstanceMapper).updateEndTime(0L, Arrays.asList(0));
    }

    @Test
    void testUpdateTaskInstanceState() {
        // Setup
        final TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setScriptTaskIpsId(0L);
        taskRuntimeDto.setAgentTaskId(0L);
        taskRuntimeDto.setId(0L);
        taskRuntimeDto.setScriptTaskId(0L);
        taskRuntimeDto.setTaskInstanceId(0L);

        // Configure TaskInstanceMapper.selectTaskInstanceById(...).
        final TaskInstance taskInstance = new TaskInstance();
        taskInstance.setId(0L);
        taskInstance.setScriptTaskId(0L);
        taskInstance.setSrcScriptUuid("srcScriptUuid");
        taskInstance.setEndTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        taskInstance.setServerNum(0);
        when(mockTaskInstanceMapper.selectTaskInstanceById(0L)).thenReturn(taskInstance);

        Map<String, Object> counts = new HashMap<>();
        counts.put("istate", com.ideal.script.common.constant.enums.Enums.TaskRuntimeState.RUNNING.getValue());
        counts.put("counts", 1L);
        List<Map<String,Object>> objects = new ArrayList<>();
        objects.add(counts);
        when(mockTaskInstanceMapper.getStatusSummary(anyLong())).thenReturn(objects);

        // Run the test
        taskInstanceServiceImplUnderTest.updateTaskInstanceState(taskInstance.getId());

        // Verify the results
        verify(mockTaskInstanceMapper).updateState(com.ideal.script.common.constant.enums.Enums.TaskInstanceStatus.RUNNING.getValue(), 0L, com.ideal.script.common.constant.Constants.SCRIPT_FINISH_SET);
    }

    @Test
    void testUpdateTaskInstanceState_exception() throws Exception {
        // Setup
        final TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setScriptTaskIpsId(0L);
        taskRuntimeDto.setAgentTaskId(0L);
        taskRuntimeDto.setId(0L);
        taskRuntimeDto.setScriptTaskId(0L);
        taskRuntimeDto.setTaskInstanceId(0L);

        // Configure TaskInstanceMapper.selectTaskInstanceById(...).
        final TaskInstance taskInstance = new TaskInstance();
        taskInstance.setId(0L);
        taskInstance.setScriptTaskId(0L);
        taskInstance.setSrcScriptUuid("srcScriptUuid");
        taskInstance.setEndTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        taskInstance.setServerNum(0);
        when(mockTaskInstanceMapper.selectTaskInstanceById(0L)).thenReturn(taskInstance);

        Map<String, Object> counts = new HashMap<>();
        counts.put("istate", com.ideal.script.common.constant.enums.Enums.TaskRuntimeState.RUNNING.getValue());
        counts.put("counts", 1L);
        List<Map<String,Object>> objects = new ArrayList<>();
        objects.add(counts);
        when(mockTaskInstanceMapper.getStatusSummary(anyLong())).thenReturn(objects);

        doThrow(RuntimeException.class).when(mockTaskInstanceMapper)
                .updateState(com.ideal.script.common.constant.enums.Enums.TaskInstanceStatus.RUNNING.getValue(), 0L, com.ideal.script.common.constant.Constants.SCRIPT_FINISH_SET);

        // Run the test
        assertThrows(Exception.class,()->{
            taskInstanceServiceImplUnderTest.updateTaskInstanceState(taskInstance.getId());
        });

        // Verify the results
        verify(mockTaskInstanceMapper).updateState(com.ideal.script.common.constant.enums.Enums.TaskInstanceStatus.RUNNING.getValue(), 0L, com.ideal.script.common.constant.Constants.SCRIPT_FINISH_SET);
    }


    @Test
    void testUpdateTaskInstanceState_hasFailCount() throws Exception {
        // Setup
        final TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setScriptTaskIpsId(0L);
        taskRuntimeDto.setAgentTaskId(0L);
        taskRuntimeDto.setId(0L);
        taskRuntimeDto.setScriptTaskId(0L);
        taskRuntimeDto.setTaskInstanceId(0L);

        // Configure TaskInstanceMapper.selectTaskInstanceById(...).
        final TaskInstance taskInstance = new TaskInstance();
        taskInstance.setId(0L);
        taskInstance.setScriptTaskId(0L);
        taskInstance.setSrcScriptUuid("srcScriptUuid");
        taskInstance.setEndTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        taskInstance.setServerNum(0);
        when(mockTaskInstanceMapper.selectTaskInstanceById(0L)).thenReturn(taskInstance);

        Map<String, Object> counts = new HashMap<>();
        counts.put("istate", com.ideal.script.common.constant.enums.Enums.TaskRuntimeState.SCRIPT_FAIL_STATE.getValue());
        counts.put("counts", 1L);
        List<Map<String,Object>> objects = new ArrayList<>();
        objects.add(counts);
        when(mockTaskInstanceMapper.getStatusSummary(anyLong())).thenReturn(objects);

        // Run the test
        taskInstanceServiceImplUnderTest.updateTaskInstanceState(taskInstance.getId());

        // Verify the results
        verify(mockTaskInstanceMapper).updateState(com.ideal.script.common.constant.enums.Enums.TaskInstanceStatus.EXCEPTION.getValue(), 0L, com.ideal.script.common.constant.Constants.SCRIPT_FINISH_SET);
    }
    @Test
    void testUpdateTaskInstanceState_hasRunCount() throws Exception {
        // Setup
        final TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setScriptTaskIpsId(0L);
        taskRuntimeDto.setAgentTaskId(0L);
        taskRuntimeDto.setId(0L);
        taskRuntimeDto.setScriptTaskId(0L);
        taskRuntimeDto.setTaskInstanceId(0L);

        // Configure TaskInstanceMapper.selectTaskInstanceById(...).
        final TaskInstance taskInstance = new TaskInstance();
        taskInstance.setId(0L);
        taskInstance.setScriptTaskId(0L);
        taskInstance.setSrcScriptUuid("srcScriptUuid");
        taskInstance.setEndTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        taskInstance.setServerNum(0);
        when(mockTaskInstanceMapper.selectTaskInstanceById(0L)).thenReturn(taskInstance);

        Map<String, Object> counts = new HashMap<>();
        counts.put("istate", com.ideal.script.common.constant.enums.Enums.TaskRuntimeState.RUNNING.getValue());
        counts.put("counts", 1L);
        List<Map<String,Object>> objects = new ArrayList<>();
        objects.add(counts);
        when(mockTaskInstanceMapper.getStatusSummary(anyLong())).thenReturn(objects);

        // Run the test
        taskInstanceServiceImplUnderTest.updateTaskInstanceState(taskInstance.getId());

        // Verify the results
        verify(mockTaskInstanceMapper).updateState(com.ideal.script.common.constant.enums.Enums.TaskInstanceStatus.RUNNING.getValue(), 0L, com.ideal.script.common.constant.Constants.SCRIPT_FINISH_SET);
    }


    @Test
    void testUpdateTaskInstanceState_hasKillCountOrSkipCount() throws Exception {
        // Setup
        final TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setScriptTaskIpsId(0L);
        taskRuntimeDto.setAgentTaskId(0L);
        taskRuntimeDto.setId(0L);
        taskRuntimeDto.setScriptTaskId(0L);
        taskRuntimeDto.setTaskInstanceId(0L);

        // Configure TaskInstanceMapper.selectTaskInstanceById(...).
        final TaskInstance taskInstance = new TaskInstance();
        taskInstance.setId(0L);
        taskInstance.setScriptTaskId(0L);
        taskInstance.setSrcScriptUuid("srcScriptUuid");
        taskInstance.setEndTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        taskInstance.setServerNum(0);
        when(mockTaskInstanceMapper.selectTaskInstanceById(0L)).thenReturn(taskInstance);

        Map<String, Object> counts = new HashMap<>();
        counts.put("istate", com.ideal.script.common.constant.enums.Enums.TaskRuntimeState.SKIP.getValue());
        counts.put("counts", 1L);
        List<Map<String,Object>> objects = new ArrayList<>();
        objects.add(counts);
        when(mockTaskInstanceMapper.getStatusSummary(anyLong())).thenReturn(objects);

        // Run the test
        taskInstanceServiceImplUnderTest.updateTaskInstanceState(taskInstance.getId());

        // Verify the results
        verify(mockTaskInstanceMapper).updateState(com.ideal.script.common.constant.enums.Enums.TaskInstanceStatus.COMPLETED_RED.getValue(), 0L, com.ideal.script.common.constant.Constants.SCRIPT_FINISH_SET);
    }

    @Test
    void testUpdateTaskInstanceStateCount() throws Exception {
        // Setup
        final TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setScriptTaskIpsId(0L);
        taskRuntimeDto.setAgentTaskId(0L);
        taskRuntimeDto.setId(0L);
        taskRuntimeDto.setScriptTaskId(0L);
        taskRuntimeDto.setTaskInstanceId(0L);

        // Configure TaskInstanceMapper.selectTaskInstanceById(...).
        final TaskInstance taskInstance = new TaskInstance();
        taskInstance.setId(0L);
        taskInstance.setScriptTaskId(0L);
        taskInstance.setSrcScriptUuid("srcScriptUuid");
        taskInstance.setEndTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        taskInstance.setServerNum(0);
        when(mockTaskInstanceMapper.selectTaskInstanceById(0L)).thenReturn(taskInstance);

        Map<String, Object> counts = new HashMap<>();
        counts.put("istate", com.ideal.script.common.constant.enums.Enums.TaskRuntimeState.COMPLETED.getValue());
        counts.put("counts", 1L);
        List<Map<String,Object>> objects = new ArrayList<>();
        objects.add(counts);
        when(mockTaskInstanceMapper.getStatusSummary(anyLong())).thenReturn(objects);

        // Run the test
        taskInstanceServiceImplUnderTest.updateTaskInstanceState(taskInstance.getId());

        // Verify the results
        verify(mockTaskInstanceMapper).updateState(com.ideal.script.common.constant.enums.Enums.TaskInstanceStatus.COMPLETED.getValue(), 0L, com.ideal.script.common.constant.Constants.SCRIPT_FINISH_SET);
    }


    @Test
    void getTaskInstanceByTaskInfoId() {
        Long taskInfoId = 1L;
        TaskInstance taskInstance = new TaskInstance();
        when(mockTaskInstanceMapper.getTaskInstanceByTaskInfoId(1L)).thenReturn(taskInstance);
        assertDoesNotThrow(() -> taskInstanceServiceImplUnderTest.getTaskInstanceByTaskInfoId(taskInfoId));

    }

    @Test
    void selectTaskInstanceByTaskId() {
        TaskInstanceDto taskInstanceDto = taskInstanceServiceImplUnderTest.selectTaskInstanceByTaskId(0L);
        assertNotNull(taskInstanceDto);
    }

    @Test
    void selectTaskInstanceById() {
        List<Long> longList = taskInstanceServiceImplUnderTest.selectTaskInstanceIdsById(new Long[]{1L});
        assertNotNull(longList);
    }


}
