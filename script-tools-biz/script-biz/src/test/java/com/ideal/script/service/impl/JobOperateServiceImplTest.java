package com.ideal.script.service.impl;

import com.ideal.jobapi.core.apiclient.IdealXxlJobApiUtil;
import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.exception.ScheduleJobOperateException;
import com.ideal.script.model.dto.ScheduleJobTaskDto;
import com.xxl.job.core.biz.model.ReturnT;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mock;

class JobOperateServiceImplTest {
    @Mock
    private IdealXxlJobApiUtil idealXxlJobApiUtil;
    private JobOperateServiceImpl jobOperateServiceImplUnderTest;

    private ReturnT<String> successReturn;


    @BeforeEach
    void setUp() throws Exception {
        successReturn = new ReturnT<>(); // 假设ReturnT是某个泛型类，这里创建成功返回的实例
        successReturn.setCode(Enums.HttpStatusCode.OK.getCode()); // 设置成功状态码
        successReturn.setMsg("Success"); // 设置成功消息
        idealXxlJobApiUtil = mock(IdealXxlJobApiUtil.class);
        jobOperateServiceImplUnderTest = new JobOperateServiceImpl();
    }

    @Test
    void testCreateAndStartJob() {
        // Setup
        final ScheduleJobTaskDto scheduleJobTaskDto = new ScheduleJobTaskDto();
        scheduleJobTaskDto.setTaskId(0L);
        scheduleJobTaskDto.setTaskName("taskName");
        scheduleJobTaskDto.setCreateName("createName");
        scheduleJobTaskDto.setCron("cron");
        scheduleJobTaskDto.setScheduleJobId(0L);

        assertThrows(ScheduleJobOperateException.class, () -> {
            jobOperateServiceImplUnderTest.createAndStartJob(scheduleJobTaskDto);
        });
        // Run the test
        //final Integer result = jobOperateServiceImplUnderTest.createAndStartJob(scheduleJobTaskDto);

        // Verify the results
//        assertThat(result).isZero();
    }

    @Test
    void testCreateAndStartJob_ThrowsScheduleJobOperateException() {
        // Setup
        final ScheduleJobTaskDto scheduleJobTaskDto = new ScheduleJobTaskDto();
        scheduleJobTaskDto.setTaskId(0L);
        scheduleJobTaskDto.setTaskName("taskName");
        scheduleJobTaskDto.setCreateName("createName");
        scheduleJobTaskDto.setCron("cron");
        scheduleJobTaskDto.setScheduleJobId(0L);

        // Run the test
        assertThatThrownBy(() -> jobOperateServiceImplUnderTest.createAndStartJob(scheduleJobTaskDto))
                .isInstanceOf(ScheduleJobOperateException.class);
    }

    @Test
    void testStartJob_false1() {
        assertThat(jobOperateServiceImplUnderTest.startJob(0)).isFalse();
    }

    @Test
    void testStartJob_false2() {
        assertThat(jobOperateServiceImplUnderTest.startJob(1)).isFalse();
    }


    @Test
    void testModifyJob() {
        // Setup
        final ScheduleJobTaskDto scheduleJobTaskDto = new ScheduleJobTaskDto();
        scheduleJobTaskDto.setTaskId(0L);
        scheduleJobTaskDto.setTaskName("taskName");
        scheduleJobTaskDto.setCreateName("createName");
        scheduleJobTaskDto.setCron("cron");
        scheduleJobTaskDto.setScheduleJobId(0L);

        // Run the test
        final boolean result = jobOperateServiceImplUnderTest.modifyJob(scheduleJobTaskDto);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    void testStopJob() {
        assertThat(jobOperateServiceImplUnderTest.stopJob(0)).isFalse();
    }
}
