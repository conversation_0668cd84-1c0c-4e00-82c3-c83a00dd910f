package com.ideal.script.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.ParameterManagerDto;
import com.ideal.script.service.IParameterManagerService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ParameterManagerControllerTest {

    @Mock
    private IParameterManagerService mockParameterManagerService;

    private ParameterManagerController parameterManagerControllerUnderTest;

    @BeforeEach
    void setUp() {
        parameterManagerControllerUnderTest = new ParameterManagerController(mockParameterManagerService);
    }

    @Test
    @DisplayName("测试查询参数管理列表-返回数据")
    void testListParameterManager() {
        // Setup
        final TableQueryDto<ParameterManagerDto> tableQueryDTO = new TableQueryDto<>();
        tableQueryDTO.setPageNum(0);
        tableQueryDTO.setPageSize(0);
        final ParameterManagerDto parameterManagerDto = new ParameterManagerDto();
        parameterManagerDto.setId(0L);
        parameterManagerDto.setParamName("paramName");
        tableQueryDTO.setQueryParam(parameterManagerDto);

        // Configure IParameterManagerService.selectParameterManagerList(...).
        final ParameterManagerDto parameterManagerDto1 = new ParameterManagerDto();
        parameterManagerDto1.setId(0L);
        parameterManagerDto1.setParamName("paramName");
        parameterManagerDto1.setParamValue("paramValue");
        parameterManagerDto1.setParamDesc("paramDesc");
        parameterManagerDto1.setScope("scope");
        final PageInfo<ParameterManagerDto> parameterManagerDtoPageInfo = new PageInfo<>(
                Arrays.asList(parameterManagerDto1), 0);
        doReturn(parameterManagerDtoPageInfo).when(mockParameterManagerService).selectParameterManagerList(any(ParameterManagerDto.class), anyInt(), anyInt());

        // Run the test
        final R<PageInfo<ParameterManagerDto>> result = parameterManagerControllerUnderTest.listParameterManager(
                tableQueryDTO);

        // Verify the results
    }

    @Test
    @DisplayName("测试查询参数管理列表-返回空数据")
    void testListParameterManager_IParameterManagerServiceReturnsNoItem() {
        // Setup
        final TableQueryDto<ParameterManagerDto> tableQueryDTO = new TableQueryDto<>();
        tableQueryDTO.setPageNum(0);
        tableQueryDTO.setPageSize(0);
        final ParameterManagerDto parameterManagerDto = new ParameterManagerDto();
        parameterManagerDto.setId(0L);
        parameterManagerDto.setParamName("paramName");
        tableQueryDTO.setQueryParam(parameterManagerDto);

        doReturn(PageInfo.emptyPageInfo()).when(mockParameterManagerService).selectParameterManagerList(any(ParameterManagerDto.class), anyInt(), anyInt());

        // Run the test
        final R<PageInfo<ParameterManagerDto>> result = parameterManagerControllerUnderTest.listParameterManager(
                tableQueryDTO);

        // Verify the results
    }

    @Test
    void testSaveParameterManager() throws Exception {
        // Setup
        final ParameterManagerDto parameterManagerDto = new ParameterManagerDto();
        parameterManagerDto.setId(0L);
        parameterManagerDto.setParamName("paramName");
        parameterManagerDto.setParamValue("paramValue");
        parameterManagerDto.setParamDesc("paramDesc");
        parameterManagerDto.setScope("scope");

        // Run the test
        final R<Object> result = parameterManagerControllerUnderTest.saveParameterManager(parameterManagerDto);

        // Verify the results
        verify(mockParameterManagerService).insertParameterManager(any(ParameterManagerDto.class));
    }

    @Test
    void testSaveParameterManager_IParameterManagerServiceThrowsScriptException() throws Exception {
        // Setup
        final ParameterManagerDto parameterManagerDto = new ParameterManagerDto();
        parameterManagerDto.setId(0L);
        parameterManagerDto.setParamName("paramName");
        parameterManagerDto.setParamValue("paramValue");
        parameterManagerDto.setParamDesc("paramDesc");
        parameterManagerDto.setScope("scope");

        doThrow(ScriptException.class).when(mockParameterManagerService).insertParameterManager(
                any(ParameterManagerDto.class));

        // Run the test
        final R<Object> result = parameterManagerControllerUnderTest.saveParameterManager(parameterManagerDto);

        // Verify the results
    }

    @Test
    void testUpdateParameterManager() throws Exception {
        // Setup
        final ParameterManagerDto parameterManagerDto = new ParameterManagerDto();
        parameterManagerDto.setId(0L);
        parameterManagerDto.setParamName("paramName");
        parameterManagerDto.setParamValue("paramValue");
        parameterManagerDto.setParamDesc("paramDesc");
        parameterManagerDto.setScope("scope");

        // Run the test
        final R<Object> result = parameterManagerControllerUnderTest.updateParameterManager(parameterManagerDto);

        // Verify the results
        verify(mockParameterManagerService).updateParameterManager(any(ParameterManagerDto.class));
    }

    @Test
    void testUpdateParameterManager_IParameterManagerServiceThrowsScriptException() throws Exception {
        // Setup
        final ParameterManagerDto parameterManagerDto = new ParameterManagerDto();
        parameterManagerDto.setId(0L);
        parameterManagerDto.setParamName("paramName");
        parameterManagerDto.setParamValue("paramValue");
        parameterManagerDto.setParamDesc("paramDesc");
        parameterManagerDto.setScope("scope");

        doThrow(ScriptException.class).when(mockParameterManagerService).updateParameterManager(
                any(ParameterManagerDto.class));

        // Run the test
        final R<Object> result = parameterManagerControllerUnderTest.updateParameterManager(parameterManagerDto);

        // Verify the results
    }

    @Test
    void testRemoveParameterManager() throws Exception {
        // Setup
        // Run the test
        final R<Object> result = parameterManagerControllerUnderTest.removeParameterManager(new Long[]{0L});

        // Verify the results
        verify(mockParameterManagerService).deleteParameterManagerByIds(any(Long[].class));
    }

    @Test
    void testRemoveParameterManager_IParameterManagerServiceThrowsScriptException() throws Exception {
        // Setup
        doThrow(ScriptException.class).when(mockParameterManagerService).deleteParameterManagerByIds(any(Long[].class));

        // Run the test
        assertThatThrownBy(
                () -> parameterManagerControllerUnderTest.removeParameterManager(new Long[]{0L}))
                .isInstanceOf(ScriptException.class);
    }

    @Test
    void testGetParameterManagerForScriptEdit() {
        // Setup
        // Configure IParameterManagerService.selectParameterManagerForScriptEdit(...).
        final ParameterManagerDto parameterManagerDto = new ParameterManagerDto();
        parameterManagerDto.setId(0L);
        parameterManagerDto.setParamName("paramName");
        parameterManagerDto.setParamValue("paramValue");
        parameterManagerDto.setParamDesc("paramDesc");
        parameterManagerDto.setScope("scope");
        final List<ParameterManagerDto> parameterManagerDtos = Arrays.asList(parameterManagerDto);
        when(mockParameterManagerService.selectParameterManagerForScriptEdit()).thenReturn(parameterManagerDtos);

        // Run the test
        final R<Object> result = parameterManagerControllerUnderTest.getParameterManagerForScriptEdit();

        // Verify the results
    }

    @Test
    void testGetParameterManagerForScriptEdit_IParameterManagerServiceReturnsNoItems() {
        // Setup
        when(mockParameterManagerService.selectParameterManagerForScriptEdit()).thenReturn(Collections.emptyList());

        // Run the test
        final R<Object> result = parameterManagerControllerUnderTest.getParameterManagerForScriptEdit();

        // Verify the results
    }
}
