package com.ideal.script.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.common.util.spring.MessageUtil;
import com.ideal.sc.util.ValidationUtils;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.dto.AttachmentUploadDto;
import com.ideal.script.dto.ScriptInfoDto;
import com.ideal.script.dto.ScriptInfoQueryDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.AgentInfoDto;
import com.ideal.script.model.dto.ScriptExecAuditDto;
import com.ideal.script.model.dto.ScriptTaskApplyResDto;
import com.ideal.script.model.dto.StartCommonTaskDto;
import com.ideal.script.model.dto.TaskApplyQueryDto;
import com.ideal.script.model.dto.TaskAttachmentDto;
import com.ideal.script.model.dto.TaskStartDto;
import com.ideal.script.model.dto.TaskTemplateDto;
import com.ideal.script.model.dto.interaction.agent.SystemComputerGroupDto;
import com.ideal.script.service.ITaskTemplateService;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Stream;

import static com.ideal.sc.util.ValidationUtils.RESPONSE_VALIDATE_CODE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TaskTemplateControllerTest {

    @Mock
    private ITaskTemplateService taskTemplateService;

    @InjectMocks
    private TaskTemplateController taskTemplateController;

    @Spy
    private MockHttpServletResponse response;
    private static MockedStatic<ValidationUtils> validationUtilsMock;


    @BeforeAll
    static void beforeAll(){
        validationUtilsMock = mockStatic(ValidationUtils.class);
        validationUtilsMock.when(() -> ValidationUtils.customFailResult(anyString(), anyString())).thenAnswer(invocation -> R.fail( RESPONSE_VALIDATE_CODE, invocation.getArgument(0), invocation.getArgument(1)));
    }
    @AfterAll
    static void afterAll() {
        validationUtilsMock.close();
    }

    @ParameterizedTest
    @MethodSource("createCloneTaskTestCases")
    @DisplayName("创建克隆任务 - 参数化测试")
    void testCreateCloneTask_Parameterized(TaskStartDto taskStartDto, boolean throwException) throws ScriptException {
        // Arrange
        if (throwException) {
            doThrow(new ScriptException("clone task is not exist!")).when(taskTemplateService)
                .createCloneTask(eq(taskStartDto), any());
        }

        // Act & Assert
        R<Object> result = taskTemplateController.createCloneTask(taskStartDto);
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        verify(taskTemplateService).createCloneTask(eq(taskStartDto), any());
    }

    static Stream<Arguments> createCloneTaskTestCases() {
        return Stream.of(
            // 正常情况
            Arguments.of(new TaskStartDto(), false)
        );
    }



    @ParameterizedTest
    @MethodSource("createCloneTaskFromTaskApplyTestCases")
    @DisplayName("从任务申请创建克隆任务 - 参数化测试")
    void testCreateCloneTaskFromTaskApply_Parameterized(ScriptExecAuditDto dto, boolean throwException) throws Exception {
        // Arrange

        
        if (throwException) {
            doThrow(new ScriptException("error")).when(taskTemplateService)
                .createCloneTaskFromTaskApply(eq(dto), any());
        }

        // Act & Assert
        if (throwException) {
            // 执行方法并获取结果
            assertThrows( ScriptException.class, () -> taskTemplateController.createCloneTaskFromTaskApply(dto));
        } else {
            R<Object> result = taskTemplateController.createCloneTaskFromTaskApply(dto);
            assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        }
    }

    static Stream<Arguments> createCloneTaskFromTaskApplyTestCases() {
        return Stream.of(
            Arguments.of(new ScriptExecAuditDto(), false), // 正常情况
            Arguments.of(new ScriptExecAuditDto(), true)   // 抛出异常
        );
    }

    @Test
    @DisplayName("查询克隆任务列表 - 成功")
    void testListCloneTask_Success() {
        // Arrange
        final TableQueryDto<TaskApplyQueryDto> queryDto = new TableQueryDto<>();
        queryDto.setPageNum(0);
        queryDto.setPageSize(10);


        // Act
        R<PageInfo<TaskTemplateDto>> result = taskTemplateController.listTaskApply(queryDto);

        // Assert
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        verify(taskTemplateService).listCloneTask(eq(queryDto.getQueryParam()),
                eq(queryDto.getPageNum()), eq(queryDto.getPageSize()), any());
    }

    @Test
    @DisplayName("获取脚本模板详情 - 成功")
    void testGetScriptTemplateDetail_Success() throws ScriptException {
        // Arrange
        ScriptInfoQueryDto queryDto = new ScriptInfoQueryDto();
        when(taskTemplateService.getScriptTemplateDetail(queryDto)).thenReturn(new ScriptInfoDto());

        // Act
        R<Object> result = taskTemplateController.getScriptDetail(queryDto);

        // Assert
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        verify(taskTemplateService).getScriptTemplateDetail(queryDto);
    }

    @Test
    @DisplayName("获取脚本模板详情 - 抛出异常")
    void testGetScriptTemplateDetail_Exception() throws ScriptException {
        // Arrange
        ScriptInfoQueryDto queryDto = new ScriptInfoQueryDto();
        when(taskTemplateService.getScriptTemplateDetail(queryDto)).thenThrow(new ScriptException("error"));

        // Act
        R<Object> result = taskTemplateController.getScriptDetail(queryDto);

        // Assert
        assertEquals(Constants.REPONSE_STATUS_VALIDATA_CODE, result.getCode());
        verify(taskTemplateService).getScriptTemplateDetail(queryDto);
    }

    @Test
    @DisplayName("查询模板Agent信息 - 成功")
    void testQueryTemplateAgentInfo_Success() {
        // Arrange
        TaskStartDto taskStartDto = new TaskStartDto();
        taskStartDto.setScriptTaskId(1L);
        when(taskTemplateService.getAllChoseAgent(anyLong())).thenReturn(Collections.singletonList(new AgentInfoDto()));

        // Act
        R<Object> result = taskTemplateController.queryAgentInfoGroupRole(taskStartDto);

        // Assert
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        verify(taskTemplateService).getAllChoseAgent(anyLong());
    }

    @Test
    @DisplayName("查询模板资源组信息 - 成功")
    void testQueryTemplateGroupInfo_Success() {
        // Arrange
        TaskStartDto taskStartDto = new TaskStartDto();
        taskStartDto.setScriptTaskId(1L);
        when(taskTemplateService.getAllChoseGroup(anyLong())).thenReturn(Collections.singletonList(new SystemComputerGroupDto()));

        // Act
        R<Object> result = taskTemplateController.queryTemplateGroupInfo(taskStartDto);

        // Assert
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        verify(taskTemplateService).getAllChoseGroup(anyLong());
    }

    @Test
    @DisplayName("执行审核模板任务 - 成功")
    void testExecAuditTemplateTask_Success() throws ScriptException {
        try (MockedStatic<MessageUtil> mocked = Mockito.mockStatic(MessageUtil.class)) {
            // Arrange
            ScriptExecAuditDto dto = new ScriptExecAuditDto();
            Map<String, Long> res = new HashMap<>();
            res.put("taskId", 1L);
            res.put("auditId", 1L);
            when(taskTemplateService.execAuditTemplateTask(dto)).thenReturn(res);

            // 模拟静态方法调用
            mocked.when(() -> MessageUtil.message("script.task.submit.success"))
                    .thenReturn("操作成功");

            // Act
            R<Object> result = taskTemplateController.execAuditTemplateTask(dto);

            // Assert
            assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
            verify(taskTemplateService).execAuditTemplateTask(dto);
        }
    }

    @Test
    @DisplayName("保存审核模板任务 - 成功")
    void testSaveAuditTemplateTask_Success() throws ScriptException {
        // Arrange
        ScriptExecAuditDto dto = new ScriptExecAuditDto();
        when(taskTemplateService.updateAuditTemplateTask(dto)).thenReturn(true);

        // Act
        R<Object> result = taskTemplateController.saveAuditTemplateTask(dto);

        // Assert
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        verify(taskTemplateService).updateAuditTemplateTask(dto);
    }

    @Test
    @DisplayName("删除模板任务 - 成功")
    void testDeleteTemplateTask_Success() throws ScriptException {
        // Arrange
        Long taskId = 1L;

        // Act
        R<Object> result = taskTemplateController.deleteTemplateTask(taskId);

        // Assert
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        verify(taskTemplateService).deleteTaskTemplate(taskId);
    }

    @ParameterizedTest
    @MethodSource("downloadAttachmentTemplateTestCases")
    @DisplayName("下载模板附件 - 参数化测试")
    void testDownloadAttachmentTemplate_Parameterized(  int expectedStatus) {
        TaskAttachmentDto validAttachment = new TaskAttachmentDto();
        validAttachment.setName("test.txt");
        validAttachment.setContents(new byte[1000]);
        validAttachment.setId(1L);
        // Arrange
        when(taskTemplateService.selectAttachmentById(validAttachment.getId())).thenReturn(validAttachment);
        if(expectedStatus == 400){
            doThrow(new IOException("IO error")).when(response).getOutputStream();
        }
        // Act
        taskTemplateController.downloadAttachmentTemplate(validAttachment.getId(), response);

        // Assert
        assertEquals(expectedStatus, response.getStatus());
        verify(taskTemplateService).selectAttachmentById(validAttachment.getId());
    }

    static Stream<Arguments> downloadAttachmentTemplateTestCases() {

        return Stream.of(
            Arguments.of(200),
            Arguments.of(400)
        );
    }

    @Test
    @DisplayName("下载模板附件 - IO异常")
    void testDownloadAttachmentTemplate_IOException()   {
        // Arrange
        Long id = 1L;
        TaskAttachmentDto attachmentDto = new TaskAttachmentDto();
        attachmentDto.setName("test.txt");
        attachmentDto.setContents(new byte[1000]);
        when(taskTemplateService.selectAttachmentById(id)).thenReturn(attachmentDto);
        doThrow(new IOException("IO error")).when(response).getOutputStream();

        // Act
        taskTemplateController.downloadAttachmentTemplate(id, response);

        // Assert
        assertEquals(400, response.getStatus());
        verify(taskTemplateService).selectAttachmentById(id);
    }

    @ParameterizedTest
    @MethodSource("uploadAttachmentTemplateTestCases")
    @DisplayName("上传模板附件 - 参数化测试")
    void testUploadAttachmentTemplate_Parameterized(MultipartFile file, Long taskId, boolean throwIOException, boolean throwScriptException) throws IOException, ScriptException {
        // Arrange
        Mockito.reset(file);
        if (file != null) {
            when(file.getOriginalFilename()).thenReturn("test.txt");
            when(file.getSize()).thenReturn(1024L);
            when(file.getBytes()).thenReturn(new byte[1024]);
        }
        
        if (throwIOException) {
            if (file != null) {
                when(file.getBytes()).thenThrow(new IOException("IO123123 error"));
            }
        }
        
        if (throwScriptException) {
            lenient().when(taskTemplateService.uploadAttachment(any())).thenThrow(new ScriptException("upload error"));
        } else {
            lenient().when(taskTemplateService.uploadAttachment(any())).thenReturn(new TaskAttachmentDto());
        }

        // Act
        R<Object> result = null;
        if (file != null) {
            result = taskTemplateController.uploadAttachmentTemplate(file, response, taskId);
        }

        // Assert
        if (throwIOException) {
            assertEquals(400, response.getStatus());
        } else if (throwScriptException) {
            if (result != null) {
                assertEquals(Constants.REPONSE_STATUS_VALIDATA_CODE, result.getCode());
            }
        } else {
            if (result != null) {
                assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
            }
        }
    }

    static Stream<Arguments> uploadAttachmentTemplateTestCases() {
        MultipartFile validFile = spy(new MockMultipartFile("AttachmentTemplate", "test.txt", "text/plain", new byte[1024]));
        return Stream.of(
            // 正常情况
            Arguments.of(validFile, 1L, false, false),
            // IO异常
            Arguments.of(validFile, 1L, true, false),
            // 业务异常
            Arguments.of(validFile, 1L, false, true)
        );
    }

    @Test
    @DisplayName("删除模板附件 - 成功")
    void testDeleteAttachmentTemplate_Success() {
        // Arrange
        Long id = 1L;

        // Act
        R<?> result = taskTemplateController.deleteAttachmentTemplate(id);

        // Assert
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        verify(taskTemplateService).deleteAttachmentTemplate(id);
    }

    @Test
    @DisplayName("获取模板任务附件 - 成功")
    void testGetTaskTemplateAttachment_Success() {
        // Arrange
        Long scriptTaskId = 1L;
        when(taskTemplateService.getTaskTemplateAttachment(scriptTaskId)).thenReturn(Collections.singletonList(new AttachmentUploadDto()));

        // Act
        R<?> result = taskTemplateController.getTaskTemplateAttachment(scriptTaskId);

        // Assert
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        verify(taskTemplateService).getTaskTemplateAttachment(scriptTaskId);
    }
    @Test
    @DisplayName("删除模板任务 - 异常处理")
    void testDeleteTemplateTask_Exception() throws ScriptException {
        // Arrange
        Long taskId = 1L;
        doThrow(new ScriptException("error")).when(taskTemplateService).deleteTaskTemplate(taskId);

        // Act
        R<Object> result = taskTemplateController.deleteTemplateTask(taskId);

        // Assert
        assertEquals(Constants.REPONSE_STATUS_VALIDATA_CODE, result.getCode());
        verify(taskTemplateService).deleteTaskTemplate(taskId);
    }
    @Test
    @DisplayName("保存审核模板任务 - 异常处理")
    void testSaveAuditTemplateTask_Exception() throws ScriptException {
        // Arrange
        ScriptExecAuditDto dto = new ScriptExecAuditDto();
        doThrow(new ScriptException("error")).when(taskTemplateService).updateAuditTemplateTask(dto);

        // Act
        R<Object> result = taskTemplateController.saveAuditTemplateTask(dto);

        // Assert
        assertEquals(Constants.REPONSE_STATUS_VALIDATA_CODE, result.getCode());
        verify(taskTemplateService).updateAuditTemplateTask(dto);
    }
    @Test
    @DisplayName("执行审核模板任务 - 异常处理")
    void testExecAuditTemplateTask_Exception() throws ScriptException {
        // Arrange
        ScriptExecAuditDto dto = new ScriptExecAuditDto();
        doThrow(new ScriptException("error")).when(taskTemplateService).execAuditTemplateTask(dto);

        // Act
        R<Object> result = taskTemplateController.execAuditTemplateTask(dto);

        // Assert
        assertEquals(Constants.REPONSE_STATUS_VALIDATA_CODE, result.getCode());
        verify(taskTemplateService).execAuditTemplateTask(dto);
    }

    @ParameterizedTest
    @MethodSource("startCommonTaskTestCases")
    @DisplayName("启动常用任务 - 参数化测试")
    void testStartCommonTask_Parameterized(StartCommonTaskDto dto, boolean throwException, Class<? extends Exception> expectedException) throws ScriptException {
        try (MockedStatic<MessageUtil> mockedMessageUtil = Mockito.mockStatic(MessageUtil.class)) {
            // Arrange

            ScriptTaskApplyResDto scriptTaskApplyResDto = new ScriptTaskApplyResDto();
            scriptTaskApplyResDto.setTaskId(123L);
            scriptTaskApplyResDto.setAuditId(456L);
            // Mock MessageUtil.message()
            mockedMessageUtil.when(() -> MessageUtil.message("script.task.submit.success"))
                    .thenReturn("操作成功");

            if (throwException) {
                doThrow(new ScriptException("启动常用任务失败")).when(taskTemplateService)
                    .startCommonTask(eq(dto), any());
            } else {
                when(taskTemplateService.startCommonTask(eq(dto), any())).thenReturn(scriptTaskApplyResDto);
            }

            // Act & Assert
            if (throwException) {
                assertThrows(expectedException, () -> taskTemplateController.startCommonTask(dto));
            } else {
                R<ScriptTaskApplyResDto> result = taskTemplateController.startCommonTask(dto);
                assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
            }

            verify(taskTemplateService).startCommonTask(eq(dto), any());
        }
    }

    static Stream<Arguments> startCommonTaskTestCases() {
        StartCommonTaskDto validDto = new StartCommonTaskDto();
        validDto.setCommonTaskId(1L);
        validDto.setAuditUserId(100L);

        StartCommonTaskDto validDtoWithoutAudit = new StartCommonTaskDto();
        validDtoWithoutAudit.setCommonTaskId(2L);

        return Stream.of(
            Arguments.of(validDto, false, null),
            Arguments.of(validDtoWithoutAudit, false, null),
            Arguments.of(validDto, true, ScriptException.class)
        );
    }
}


