package com.ideal.script.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.ParameterManagerMapper;
import com.ideal.script.model.dto.ParameterManagerDto;
import com.ideal.script.model.entity.ParameterManager;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ParameterManagerServiceImplTest {

    @Mock
    private ParameterManagerMapper mockParameterManagerMapper;
    @InjectMocks
    private ParameterManagerServiceImpl parameterManagerServiceImplUnderTest;


    @Test
    void testSelectParameterManagerById() {
        // Setup
        // Configure ParameterManagerMapper.selectParameterManagerById(...).
        final ParameterManager parameterManager = new ParameterManager();
        parameterManager.setId(1L);
        parameterManager.setParamName("paramName");
        parameterManager.setParamValue("paramValue");
        parameterManager.setParamDesc("paramDesc");
        parameterManager.setScope("scope");
        when(mockParameterManagerMapper.selectParameterManagerById(1L)).thenReturn(parameterManager);

        // Run the test
        final ParameterManagerDto result = parameterManagerServiceImplUnderTest.selectParameterManagerById(1L);
        assertNotNull(result);
        System.out.println(result);
        // Verify the results
        Mockito.verify(mockParameterManagerMapper,times(1)).selectParameterManagerById(1L);

        // Verify the results
    }

    @Test
    void testSelectParameterManagerList() {
        // Setup
        final ParameterManagerDto parameterManagerDto = new ParameterManagerDto();
        parameterManagerDto.setId(1L);
        parameterManagerDto.setParamName("paramName");
        parameterManagerDto.setParamValue("paramValue");
        parameterManagerDto.setParamDesc("paramDesc");
        parameterManagerDto.setScope("scope");

        // Configure ParameterManagerMapper.selectParameterManagerList(...).
        final ParameterManager parameterManager = new ParameterManager();
        parameterManager.setId(1L);
        parameterManager.setParamName("paramName");
        parameterManager.setParamValue("paramValue");
        parameterManager.setParamDesc("paramDesc");
        parameterManager.setScope("scope");
        Page<ParameterManager> page = new Page<>();
        page.add(parameterManager);
        when(mockParameterManagerMapper.selectParameterManagerList(any(ParameterManager.class)))
                .thenReturn(page);

        // Run the test
        final PageInfo<ParameterManagerDto> result = parameterManagerServiceImplUnderTest.selectParameterManagerList(
                parameterManagerDto, 1, 50);

        assertNotNull(result);
        System.out.println(result);
        // Verify the results
        Mockito.verify(mockParameterManagerMapper,times(1)).selectParameterManagerList(any(ParameterManager.class));
    }



    @Test
    void testInsertParameterManager() throws ScriptException {
        // Setup
        final ParameterManagerDto parameterManagerDto = new ParameterManagerDto();
        parameterManagerDto.setId(1L);
        parameterManagerDto.setParamName("paramName");
        parameterManagerDto.setParamValue("paramValue");
        parameterManagerDto.setParamDesc("paramDesc");
        parameterManagerDto.setScope("scope");

        // Run the test
        parameterManagerServiceImplUnderTest.insertParameterManager(parameterManagerDto);

        // Verify the results
        verify(mockParameterManagerMapper).insertParameterManager(any(ParameterManager.class));
    }

    @Test
    void testInsertParameterManager_checkDuplicateName() throws ScriptException {
        // Setup
        final ParameterManagerDto parameterManagerDto = new ParameterManagerDto();
        parameterManagerDto.setId(1L);
        parameterManagerDto.setParamName("paramName");
        parameterManagerDto.setParamValue("paramValue");
        parameterManagerDto.setParamDesc("paramDesc");
        parameterManagerDto.setScope("scope");

        // Create a parameter manager with the same name but different ID to simulate a duplicate
        ParameterManager parameterManager = new ParameterManager();
        parameterManager.setParamName("paramName");
        parameterManager.setId(2L); // Different ID from parameterManagerDto

        List<ParameterManager> parameterManagerList = new ArrayList<>();
        parameterManagerList.add(parameterManager);

        // Mock the selectSaveNameList method which is used in checkDuplicateName
        when(mockParameterManagerMapper.selectSaveNameList(any(ParameterManager.class))).thenReturn(parameterManagerList);

        // Run the test
        assertThrows(ScriptException.class, () -> {
            parameterManagerServiceImplUnderTest.insertParameterManager(parameterManagerDto);
        });

        // Verify that selectSaveNameList was called
        verify(mockParameterManagerMapper).selectSaveNameList(any(ParameterManager.class));
    }



    @Test
    void testUpdateParameterManager() throws ScriptException {
        // Setup
        final ParameterManagerDto parameterManagerDto = new ParameterManagerDto();
        parameterManagerDto.setId(1L);
        parameterManagerDto.setParamName("paramName");
        parameterManagerDto.setParamValue("paramValue");
        parameterManagerDto.setParamDesc("paramDesc");
        parameterManagerDto.setScope("scope");

        // Run the test
        parameterManagerServiceImplUnderTest.updateParameterManager(parameterManagerDto);

        // Verify the results
        verify(mockParameterManagerMapper).updateParameterManager(any(ParameterManager.class));
    }

    @Test
    void testDeleteParameterManagerByIds_Success() throws ScriptException {
        // Setup
        Long[] ids = new Long[]{1L, 2L};
        List<Long> existIds = Arrays.asList(1L, 2L);
        when(mockParameterManagerMapper.checkIdsExist(ids)).thenReturn(existIds);

        // Run the test
        parameterManagerServiceImplUnderTest.deleteParameterManagerByIds(ids);

        // Verify the results
        verify(mockParameterManagerMapper).checkIdsExist(ids);
        verify(mockParameterManagerMapper).deleteParameterManagerByIds(ids);
    }

    @Test
    void testDeleteParameterManagerByIds_WhenIdsNotExist() {
        // Setup
        Long[] ids = new Long[]{1L, 2L};
        when(mockParameterManagerMapper.checkIdsExist(ids)).thenReturn(new ArrayList<>());

        // Run the test and verify exception
        ScriptException exception = assertThrows(ScriptException.class, () ->
            parameterManagerServiceImplUnderTest.deleteParameterManagerByIds(ids)
        );
        assertThat(exception.getMessage()).isEqualTo("enum.parameter.not.exist");

        // Verify the mapper was called but delete was not
        verify(mockParameterManagerMapper).checkIdsExist(ids);
        verify(mockParameterManagerMapper, never()).deleteParameterManagerByIds(any(Long[].class));
    }

    @Test
    void testDeleteParameterManagerByIds_WhenSomeIdsNotExist() {
        // Setup
        Long[] ids = new Long[]{1L, 2L, 3L};
        List<Long> existIds = Arrays.asList(1L, 2L);
        when(mockParameterManagerMapper.checkIdsExist(ids)).thenReturn(existIds);

        // Run the test and verify exception
        ScriptException exception = assertThrows(ScriptException.class, () ->
            parameterManagerServiceImplUnderTest.deleteParameterManagerByIds(ids)
        );
        assertThat(exception.getMessage()).isEqualTo("ids:3不存在");

        // Verify the mapper was called but delete was not
        verify(mockParameterManagerMapper).checkIdsExist(ids);
        verify(mockParameterManagerMapper, never()).deleteParameterManagerByIds(any(Long[].class));
    }

    @Test
    void testDeleteParameterManagerById() {
        // Setup
        when(mockParameterManagerMapper.deleteParameterManagerById(1L)).thenReturn(1);

        // Run the test
        final int result = parameterManagerServiceImplUnderTest.deleteParameterManagerById(1L);

        // Verify the results
        assertThat(result).isEqualTo(1);
    }

    @Test
    void testSelectParameterManagerForScriptEdit() {
        // Setup
        // Configure ParameterManagerMapper.selectParameterManagerList(...).
        final ParameterManager parameterManager = new ParameterManager();
        parameterManager.setId(1L);
        parameterManager.setParamName("paramName");
        parameterManager.setParamValue("paramValue");
        parameterManager.setParamDesc("paramDesc");
        parameterManager.setScope("scope");
        Page<ParameterManager> page = new Page<>();
        page.add(parameterManager);
        when(mockParameterManagerMapper.selectParameterManagerList(any(ParameterManager.class)))
                .thenReturn(page);

        // Run the test
        final List<ParameterManagerDto> result = parameterManagerServiceImplUnderTest.selectParameterManagerForScriptEdit();
        assertNotNull(result);
        System.out.println(result);
        // Verify the results
        Mockito.verify(mockParameterManagerMapper,times(1)).selectParameterManagerList(any(ParameterManager.class));
    }

}
