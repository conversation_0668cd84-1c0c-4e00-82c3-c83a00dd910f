package com.ideal.script.controller;

import com.ideal.common.dto.R;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.model.dto.AuditRelationDto;
import com.ideal.script.service.IAuditRelationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Timestamp;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * AuditRelationController单元测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class AuditRelationControllerTest {

    @Mock
    private IAuditRelationService auditRelationService;

    @InjectMocks
    private AuditRelationController auditRelationController;

    private AuditRelationDto auditRelationDto;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        auditRelationDto = new AuditRelationDto();
        auditRelationDto.setId(1L);
        auditRelationDto.setInfoId(100L);
        auditRelationDto.setSrcScriptUuid("test-uuid-123");
        auditRelationDto.setScriptTaskId(200L);
        auditRelationDto.setApprWorkitemId(300L);
        auditRelationDto.setAuditType(1);
        auditRelationDto.setState(1);
        auditRelationDto.setAuditUser("testUser");
        auditRelationDto.setApplyUser("applyUser");
        auditRelationDto.setAuditUserId(10L);
        auditRelationDto.setApplyUserId(20L);
        auditRelationDto.setBanOldVersion(0);
        auditRelationDto.setPublicDesc("测试发布说明");
        auditRelationDto.setWorkOrderNumber("WO-2024-001");
        auditRelationDto.setApplyTime(new Timestamp(System.currentTimeMillis()));
        auditRelationDto.setAuditTime(new Timestamp(System.currentTimeMillis()));
    }

    @Test
    @DisplayName("测试根据双人复核id查询脚本id以及版本uuid - 正常情况")
    void selectInfoIdAndSrcScriptUuidByAuditRelationId_success() {
        // 准备数据
        Long id = 1L;

        // Mock方法
        when(auditRelationService.selectInfoIdAndSrcScriptUuidByAuditRelationId(id))
                .thenReturn(auditRelationDto);

        // 执行测试
        R<AuditRelationDto> result = auditRelationController.selectInfoIdAndSrcScriptUuidByAuditRelationId(id);

        // 验证结果
        assertNotNull(result);
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        assertEquals(auditRelationDto, result.getData());
        assertEquals(Constants.LIST_SUCCESS, result.getMessage());
        verify(auditRelationService, times(1)).selectInfoIdAndSrcScriptUuidByAuditRelationId(id);
    }

    @Test
    @DisplayName("测试根据双人复核id查询脚本id以及版本uuid - 返回null")
    void selectInfoIdAndSrcScriptUuidByAuditRelationId_returnNull() {
        // 准备数据
        Long id = 999L;

        // Mock方法 - 返回null
        when(auditRelationService.selectInfoIdAndSrcScriptUuidByAuditRelationId(id))
                .thenReturn(null);

        // 执行测试
        R<AuditRelationDto> result = auditRelationController.selectInfoIdAndSrcScriptUuidByAuditRelationId(id);

        // 验证结果
        assertNotNull(result);
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        assertEquals(null, result.getData());
        assertEquals(Constants.LIST_SUCCESS, result.getMessage());
        verify(auditRelationService, times(1)).selectInfoIdAndSrcScriptUuidByAuditRelationId(id);
    }

    @ParameterizedTest
    @MethodSource("provideTestIds")
    @DisplayName("测试根据双人复核id查询脚本id以及版本uuid - 参数化测试")
    void selectInfoIdAndSrcScriptUuidByAuditRelationId_parameterized(Long id, AuditRelationDto expectedDto) {
        // Mock方法
        when(auditRelationService.selectInfoIdAndSrcScriptUuidByAuditRelationId(anyLong()))
                .thenReturn(expectedDto);

        // 执行测试
        R<AuditRelationDto> result = auditRelationController.selectInfoIdAndSrcScriptUuidByAuditRelationId(id);

        // 验证结果
        assertNotNull(result);
        assertEquals(Constants.REPONSE_STATUS_SUSSCESS_CODE, result.getCode());
        assertEquals(expectedDto, result.getData());
        assertEquals(Constants.LIST_SUCCESS, result.getMessage());
        verify(auditRelationService, times(1)).selectInfoIdAndSrcScriptUuidByAuditRelationId(id);
    }

    static Stream<Arguments> provideTestIds() {
        AuditRelationDto dto1 = new AuditRelationDto();
        dto1.setId(1L);
        dto1.setInfoId(100L);
        dto1.setSrcScriptUuid("uuid-1");

        AuditRelationDto dto2 = new AuditRelationDto();
        dto2.setId(2L);
        dto2.setInfoId(200L);
        dto2.setSrcScriptUuid("uuid-2");

        return Stream.of(
                Arguments.of(1L, dto1),
                Arguments.of(2L, dto2),
                Arguments.of(0L, null),
                Arguments.of(-1L, null)
        );
    }
}
