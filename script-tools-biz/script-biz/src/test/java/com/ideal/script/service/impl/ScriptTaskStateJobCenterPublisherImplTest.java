package com.ideal.script.service.impl;

import com.ideal.message.center.IPublisher;
import com.ideal.message.center.exception.CommunicationException;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.model.dto.JobDto;
import com.ideal.script.model.dto.JobSourceDictSourceCode;
import com.ideal.script.model.dto.TaskStartDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ScriptTaskStateJobCenterPublisherImpl的单元测试类
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class ScriptTaskStateJobCenterPublisherImplTest {

    @Mock
    private IPublisher publisher;

    @InjectMocks
    private ScriptTaskStateJobCenterPublisherImpl scriptTaskStateJobCenterPublisher;

    private TaskStartDto taskStartDto;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        taskStartDto = new TaskStartDto();
        taskStartDto.setTaskName("测试任务");
        taskStartDto.setPublishDesc("测试描述");
        taskStartDto.setIscriptTaskInstanceId(1001L);
    }

    @Test
    @DisplayName("测试构造函数 - 验证依赖注入")
    void testConstructor() {
        // 验证依赖对象被正确注入
        assertNotNull(scriptTaskStateJobCenterPublisher);
        
        // 验证构造函数能正常执行
        ScriptTaskStateJobCenterPublisherImpl publisher = new ScriptTaskStateJobCenterPublisherImpl(this.publisher);
        assertNotNull(publisher);
        
        // 验证依赖对象被正确设置（通过反射验证私有字段）
        try {
            java.lang.reflect.Field publisherField = ScriptTaskStateJobCenterPublisherImpl.class.getDeclaredField("publisher");
            publisherField.setAccessible(true);
            assertSame(this.publisher, publisherField.get(publisher));
        } catch (Exception e) {
            fail("反射访问字段失败: " + e.getMessage());
        }
    }

    /**
     * 提供publish方法的测试参数
     */
    static Stream<Object[]> providePublishTestCases() {
        return Stream.of(
            // RUNNING状态测试
            new Object[]{Enums.TaskInstanceStatus.RUNNING, "RUNNING", Constants.SCRIPT_JOB_START_CHANEL, true, false},
            // COMPLETED状态测试
            new Object[]{Enums.TaskInstanceStatus.COMPLETED, "COMPLETED", Constants.SCRIPT_JOB_END_CHANEL, false, true},
            // TERMINATED状态测试
            new Object[]{Enums.TaskInstanceStatus.TERMINATED, "CANCELLED", Constants.SCRIPT_JOB_END_CHANEL, false, true},
            // default分支测试 - 使用其他状态
            new Object[]{Enums.TaskInstanceStatus.EXCEPTION, null, "", false, false}
        );
    }

    @ParameterizedTest
    @MethodSource("providePublishTestCases")
    @DisplayName("测试publish方法的所有状态枚举分支")
    void testPublish(Enums.TaskInstanceStatus state, String expectedStatus, String expectedChannel,
                     boolean shouldSetStartTime, boolean shouldSetEndTime) throws Exception {

        // 执行测试方法
        scriptTaskStateJobCenterPublisher.publish(taskStartDto, state);

        // 验证publisher.apply方法被调用
        if (!expectedChannel.isEmpty()) {
            verify(publisher).apply(eq(expectedChannel), argThat((JobDto jobDto) -> {
                // 验证JobDto的基本属性
                boolean basicPropsValid = "测试任务".equals(jobDto.getJobName()) &&
                        "测试描述".equals(jobDto.getJobDescription()) &&
                        "1001".equals(jobDto.getBizId()) &&
                        JobSourceDictSourceCode.SCRIPT.equals(jobDto.getJobSourceDictSourceCode());

                // 验证状态相关属性
                boolean statusValid = expectedStatus == null || expectedStatus.equals(jobDto.getStatus());

                // 验证时间属性
                boolean timeValid = true;
                if (shouldSetStartTime) {
                    timeValid = jobDto.getStartTime() != null;
                }
                if (shouldSetEndTime) {
                    timeValid = timeValid && jobDto.getEndTime() != null;
                }

                return basicPropsValid && statusValid && timeValid;
            }));
        } else {
            // default分支情况，channel为空，仍然会调用publisher.apply
            verify(publisher).apply(eq(""), any(JobDto.class));
        }
    }

    @Test
    @DisplayName("测试publish方法 - CommunicationException异常处理")
    void testPublish_CommunicationException() throws Exception {
        // 设置publisher.apply方法抛出CommunicationException异常
        doThrow(new CommunicationException("测试异常")).when(publisher).apply(anyString(), any(JobDto.class));

        // 使用MockedStatic处理Logger静态调用
        try (MockedStatic<LoggerFactory> loggerFactoryMock = mockStatic(LoggerFactory.class)) {
            Logger mockLogger = mock(Logger.class);
            loggerFactoryMock.when(() -> LoggerFactory.getLogger(ScriptTaskStateJobCenterPublisherImpl.class))
                    .thenReturn(mockLogger);

            // 创建新实例来测试异常处理（因为Logger是在构造函数中初始化的）
            ScriptTaskStateJobCenterPublisherImpl publisherImpl = new ScriptTaskStateJobCenterPublisherImpl(publisher);

            // 执行测试方法，应该不抛出异常
            assertDoesNotThrow(() -> {
                publisherImpl.publish(taskStartDto, Enums.TaskInstanceStatus.RUNNING);
            });

            // 验证publisher.apply方法被调用
            verify(publisher).apply(eq(Constants.SCRIPT_JOB_START_CHANEL), any(JobDto.class));

            // 验证Logger.error方法被调用
            verify(mockLogger).error(eq("推送作业中心任务状态变更失败,{}"), any(JobDto.class), any(CommunicationException.class));
        }
    }
}
