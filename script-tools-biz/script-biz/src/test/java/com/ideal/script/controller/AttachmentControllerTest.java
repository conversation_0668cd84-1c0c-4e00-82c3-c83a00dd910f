package com.ideal.script.controller;

import com.ideal.common.dto.R;
import com.ideal.sc.util.ValidationUtils;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.dto.AttachmentDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.TaskAttachmentDto;
import com.ideal.script.service.IAttachmentService;
import com.ideal.script.service.ITaskAttachmentService;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AttachmentControllerTest {

    @Mock
    private IAttachmentService attachmentService;

    @Mock
    private ITaskAttachmentService taskAttachmentService;

    @InjectMocks
    private AttachmentController attachmentController;

    private static MockedStatic<ValidationUtils> validationUtilsMock;

    @BeforeAll
    static void beforeAll() {
        validationUtilsMock = mockStatic(ValidationUtils.class);
        validationUtilsMock.when(() -> ValidationUtils.customFailResult(anyString(), anyString()))
                .thenAnswer(invocation -> R.fail(ValidationUtils.RESPONSE_VALIDATE_CODE, invocation.getArgument(0), invocation.getArgument(1)));
    }

    @AfterAll
    static void afterAll() {
        validationUtilsMock.close();
    }

    static Stream<Arguments> uploadAttachmentTestCases() {
        return Stream.of(
                // 正常情况 - 无func参数
                Arguments.of("test.txt", 1024L, null, false, false, Constants.REPONSE_STATUS_SUSSCESS_CODE),
                // 正常情况 - 有func参数
                Arguments.of("test.txt", 1024L, "func", false, false, Constants.REPONSE_STATUS_SUSSCESS_CODE),
                // IOException异常
                Arguments.of("test.txt", 1024L, null, true, false, null),
                // ScriptException异常
                Arguments.of("test.txt", 1024L, null, false, true, ValidationUtils.RESPONSE_VALIDATE_CODE)
        );
    }

    @ParameterizedTest
    @MethodSource("uploadAttachmentTestCases")
    @DisplayName("测试上传附件 - 参数化测试")
    void testUploadAttachment(String fileName, long fileSize, String func, boolean throwIOException, 
                              boolean throwScriptException, String expectedCode) throws IOException, ScriptException {
        // 准备测试数据
        MultipartFile file = spy(new MockMultipartFile("file", fileName, "text/plain", new byte[(int) fileSize]));
        HttpServletResponse response = spy(new MockHttpServletResponse());
        
        // 模拟文件操作
        if (throwIOException) {
            doThrow(new IOException("IO error")).when(file).getBytes();
        } else {
            doReturn(new byte[(int) fileSize]).when(file).getBytes();
        }
        
        // 模拟服务调用
        if (!throwIOException && !throwScriptException) {
            if (func == null) {
                AttachmentDto attachmentDto = new AttachmentDto();
                attachmentDto.setId(1L);
                when(attachmentService.uploadAttachment(any(AttachmentDto.class))).thenReturn(attachmentDto);
            } else {
                TaskAttachmentDto taskAttachmentDto = new TaskAttachmentDto();
                taskAttachmentDto.setId(1L);
                when(taskAttachmentService.uploadAttachment(any(TaskAttachmentDto.class))).thenReturn(taskAttachmentDto);
            }
        } else if (throwScriptException) {
            if (func == null) {
                when(attachmentService.uploadAttachment(any(AttachmentDto.class))).thenThrow(new ScriptException("上传失败"));
            } else {
                when(taskAttachmentService.uploadAttachment(any(TaskAttachmentDto.class))).thenThrow(new ScriptException("上传失败"));
            }
        }
        
        // 执行测试
        R<Object> result = attachmentController.uploadAttachment(file, response, func);
        
        // 验证结果
        if (throwIOException) {
            assertEquals(HttpServletResponse.SC_BAD_REQUEST, ((MockHttpServletResponse) response).getStatus());
            assertNull(result);
        } else if (throwScriptException) {
            assertEquals(expectedCode, result.getCode());
            verify(ValidationUtils.class);
            ValidationUtils.customFailResult(anyString(), anyString());
        } else {
            assertEquals(expectedCode, result.getCode());
            if (func == null) {
                verify(attachmentService).uploadAttachment(any(AttachmentDto.class));
            } else {
                verify(taskAttachmentService).uploadAttachment(any(TaskAttachmentDto.class));
            }
        }
    }

    @Test
    @DisplayName("测试下载附件 - 成功")
    void testDownloadAttachment_Success() throws IOException {
        // 准备测试数据
        Long id = 1L;
        AttachmentDto attachmentDto = new AttachmentDto();
        attachmentDto.setId(id);
        attachmentDto.setName("test.txt");
        attachmentDto.setContents(new byte[1024]);
        
        HttpServletResponse response = spy(new MockHttpServletResponse());
        ServletOutputStream outputStream = mock(ServletOutputStream.class);
        
        // 模拟服务调用
        when(attachmentService.selectAttachmentById(id)).thenReturn(attachmentDto);
        doReturn(outputStream).when(response).getOutputStream();
        
        // 执行测试
        attachmentController.downloadAttachment(id, response);
        
        // 验证结果
        verify(attachmentService).selectAttachmentById(id);
        verify(outputStream).write(any(byte[].class));
        verify(response).setContentType("application/octet-stream");
        verify(response).setCharacterEncoding("UTF-8");
        verify(response).setHeader(eq("Content-disposition"), contains("attachment; filename="));
    }

    @Test
    @DisplayName("测试下载附件 - IOException异常")
    void testDownloadAttachment_IOException() throws IOException {
        // 准备测试数据
        Long id = 1L;
        AttachmentDto attachmentDto = new AttachmentDto();
        attachmentDto.setId(id);
        attachmentDto.setName("test.txt");
        attachmentDto.setContents(new byte[1024]);
        
        HttpServletResponse response = spy(new MockHttpServletResponse());
        
        // 模拟服务调用
        when(attachmentService.selectAttachmentById(id)).thenReturn(attachmentDto);
        doThrow(new IOException("IO error")).when(response).getOutputStream();
        
        // 执行测试
        attachmentController.downloadAttachment(id, response);
        
        // 验证结果
        verify(attachmentService).selectAttachmentById(id);
        verify(response).setStatus(HttpServletResponse.SC_BAD_REQUEST);
    }
}
