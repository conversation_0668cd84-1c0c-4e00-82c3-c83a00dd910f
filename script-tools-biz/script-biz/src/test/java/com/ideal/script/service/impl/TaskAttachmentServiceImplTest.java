package com.ideal.script.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ideal.script.common.util.BatchDataUtil;
import com.ideal.script.common.util.FileSizeValidUtil;
import com.ideal.script.mapper.TaskAttachmentMapper;
import com.ideal.script.dto.AttachmentDto;
import com.ideal.script.model.dto.ScriptExecAuditDto;
import com.ideal.script.model.dto.TaskAttachmentDto;
import com.ideal.script.model.dto.TaskDto;
import com.ideal.script.model.entity.TaskAttachment;
import com.ideal.script.service.IAttachmentService;
import org.apache.ibatis.session.SqlSession;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SetOperations;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TaskAttachmentServiceImplTest {

    @Mock
    private TaskAttachmentMapper mockTaskAttachmentMapper;
    @Mock
    private BatchDataUtil mockBatchDataUtil;
    @Mock
    private IAttachmentService mockAttachmentService;

    private TaskAttachmentServiceImpl taskAttachmentServiceImplUnderTest;
    @Mock
    private FileSizeValidUtil fileSizeValidUtil;
    @Mock
    private RedisTemplate<String, String> redisTemplate;

    @BeforeEach
    void setUp() throws Exception {
        taskAttachmentServiceImplUnderTest = new TaskAttachmentServiceImpl(mockTaskAttachmentMapper, mockBatchDataUtil,
                mockAttachmentService, fileSizeValidUtil, redisTemplate);
    }

    @Test
    void testSelectTaskAttachmentById() {
        // Setup
        // Configure TaskAttachmentMapper.selectTaskAttachmentById(...).
        final TaskAttachment taskAttachment = new TaskAttachment();
        taskAttachment.setId(0L);
        taskAttachment.setScriptTaskId(0L);
        taskAttachment.setName("name");
        taskAttachment.setSize(0L);
        taskAttachment.setUploadTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockTaskAttachmentMapper.selectTaskAttachmentById(0L)).thenReturn(taskAttachment);

        // Run the test
        final TaskAttachmentDto result = taskAttachmentServiceImplUnderTest.selectTaskAttachmentById(0L);
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testSelectTaskAttachmentList() {
        // Setup
        final TaskAttachmentDto taskAttachmentDto = new TaskAttachmentDto();
        taskAttachmentDto.setId(0L);
        taskAttachmentDto.setScriptTaskId(0L);
        taskAttachmentDto.setName("name");
        taskAttachmentDto.setSize(0L);
        taskAttachmentDto.setUploadTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));

        // Configure TaskAttachmentMapper.selectTaskAttachmentList(...).
        final TaskAttachment taskAttachment = new TaskAttachment();
        taskAttachment.setId(0L);
        taskAttachment.setScriptTaskId(0L);
        taskAttachment.setName("name");
        taskAttachment.setSize(0L);
        taskAttachment.setUploadTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        Page<TaskAttachment> page = new Page<>();
        page.add(taskAttachment);
        when(mockTaskAttachmentMapper.selectTaskAttachmentList(any(TaskAttachment.class))).thenReturn(page);

        // Run the test
        final PageInfo<TaskAttachmentDto> result = taskAttachmentServiceImplUnderTest.selectTaskAttachmentList(
                taskAttachmentDto, 0, 0);
        assertNotNull(result);
        // Verify the results
    }


    @Test
    void testInsertTaskAttachment() {
        // Setup
        final TaskAttachmentDto taskAttachmentDto = new TaskAttachmentDto();
        taskAttachmentDto.setId(0L);
        taskAttachmentDto.setScriptTaskId(0L);
        taskAttachmentDto.setName("name");
        taskAttachmentDto.setSize(0L);
        taskAttachmentDto.setUploadTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));

        when(mockTaskAttachmentMapper.insertTaskAttachment(any(TaskAttachment.class))).thenReturn(0);

        // Run the test
        final int result = taskAttachmentServiceImplUnderTest.insertTaskAttachment(taskAttachmentDto);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testUpdateTaskAttachment() {
        // Setup
        final TaskAttachmentDto taskAttachmentDto = new TaskAttachmentDto();
        taskAttachmentDto.setId(0L);
        taskAttachmentDto.setScriptTaskId(0L);
        taskAttachmentDto.setName("name");
        taskAttachmentDto.setSize(0L);
        taskAttachmentDto.setUploadTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));

        when(mockTaskAttachmentMapper.updateTaskAttachment(any(TaskAttachment.class))).thenReturn(0);

        // Run the test
        final int result = taskAttachmentServiceImplUnderTest.updateTaskAttachment(taskAttachmentDto);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testDeleteTaskAttachmentByIds() {
        // Setup
        when(mockTaskAttachmentMapper.deleteTaskAttachmentByIds(any(Long[].class))).thenReturn(0);

        // Run the test
        final int result = taskAttachmentServiceImplUnderTest.deleteTaskAttachmentByIds(new Long[]{0L});

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testDeleteTaskAttachmentById() {
        // Setup
        when(mockTaskAttachmentMapper.deleteTaskAttachmentById(0L)).thenReturn(0);

        // Run the test
        final int result = taskAttachmentServiceImplUnderTest.deleteTaskAttachmentById(0L);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testSelectTaskAttachmentByServiceId() {
        // Setup
        // Configure TaskAttachmentMapper.selectTaskAttachmentByServiceId(...).
        final TaskAttachment taskAttachment = new TaskAttachment();
        taskAttachment.setId(0L);
        taskAttachment.setScriptTaskId(0L);
        taskAttachment.setName("name");
        taskAttachment.setSize(0L);
        taskAttachment.setUploadTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        final List<TaskAttachment> taskAttachments = Collections.singletonList(taskAttachment);
        when(mockTaskAttachmentMapper.selectTaskAttachmentByServiceId(0L,0L)).thenReturn(taskAttachments);

        // Run the test
        final List<TaskAttachmentDto> result = taskAttachmentServiceImplUnderTest.selectTaskAttachmentByServiceId(0L,0L);
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testSelectTaskAttachmentByServiceId_TaskAttachmentMapperReturnsNoItems() {
        // Setup
        when(mockTaskAttachmentMapper.selectTaskAttachmentByServiceId(0L,0L)).thenReturn(Collections.emptyList());

        // Run the test
        final List<TaskAttachmentDto> result = taskAttachmentServiceImplUnderTest.selectTaskAttachmentByServiceId(0L,0L);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testSaveTaskAttachement() throws Exception {
        // Setup
        final ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        final AttachmentDto attachmentDto = new AttachmentDto();
        attachmentDto.setIsTempFlag(0);
        attachmentDto.setId(0L);
        attachmentDto.setSrcScriptUuid("srcScriptUuid");
        attachmentDto.setName("name");
        scriptExecAuditDto.setScriptTempAttachments(Arrays.asList(attachmentDto));

        final TaskDto taskInfo = new TaskDto();
        taskInfo.setScriptTaskSource(0);
        taskInfo.setStartType(0);
        taskInfo.setReadyToExecute(0);
        taskInfo.setId(0L);
        taskInfo.setSrcScriptUuid("srcScriptUuid");

        final SqlSession sqlSession = mock(SqlSession.class);


        SetOperations setOperation = mock(SetOperations.class);
        when(redisTemplate.opsForSet()).thenReturn(setOperation);


        // Run the test
        taskAttachmentServiceImplUnderTest.saveTaskAttachement(scriptExecAuditDto, taskInfo, sqlSession);

        // Verify the results
        // Confirm BatchDataUtil.batchData(...).
        final TaskAttachment taskAttachment = new TaskAttachment();
        taskAttachment.setId(0L);
        taskAttachment.setScriptTaskId(0L);
        taskAttachment.setName("name");
        taskAttachment.setSize(0L);
        taskAttachment.setUploadTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        final List<TaskAttachment> listEntity = Collections.singletonList(taskAttachment);
       assertNotNull(listEntity);
    }

}
