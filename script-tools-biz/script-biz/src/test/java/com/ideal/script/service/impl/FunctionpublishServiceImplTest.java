package com.ideal.script.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ideal.script.mapper.FunctionpublishMapper;
import com.ideal.script.model.dto.FunctionpublishDto;
import com.ideal.script.model.dto.VarAndFuncForEditDto;
import com.ideal.script.model.entity.Functionpublish;
import com.ideal.script.model.entity.VarAndFuncForEdit;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class FunctionpublishServiceImplTest {

    @Mock
    private FunctionpublishMapper mockFunctionpublishMapper;
    @InjectMocks
    private FunctionpublishServiceImpl functionpublishServiceImplUnderTest;


    @Test
    void testSelectFunctionpublishById() {
        // Setup
        // Configure FunctionpublishMapper.selectFunctionpublishById(...).
        final Functionpublish functionpublish = new Functionpublish();
        functionpublish.setId(1L);
        functionpublish.setName("name");
        functionpublish.setLanguagetype(1);
        functionpublish.setDesc("desc");
        functionpublish.setAttribute(1);
        when(mockFunctionpublishMapper.selectFunctionpublishById(1L)).thenReturn(functionpublish);

        // Run the test
        final FunctionpublishDto result = functionpublishServiceImplUnderTest.selectFunctionpublishById(1L);

        assertNotNull(result);
        System.out.println(result);
        Mockito.verify(mockFunctionpublishMapper,times(1)).selectFunctionpublishById(1L);
        // Verify the results
    }

    @Test
    void testSelectFunctionpublishList() {
        // Setup
        final FunctionpublishDto functionpublishDto = new FunctionpublishDto();
        functionpublishDto.setId(1L);
        functionpublishDto.setName("name");
        functionpublishDto.setLanguagetype(1);
        functionpublishDto.setDesc("desc");
        functionpublishDto.setAttribute(1);

        // Configure FunctionpublishMapper.selectFunctionpublishList(...).
        final Functionpublish functionpublish = new Functionpublish();
        functionpublish.setId(1L);
        functionpublish.setName("name");
        functionpublish.setLanguagetype(1);
        functionpublish.setDesc("desc");
        functionpublish.setAttribute(1);
        Page<Functionpublish> page = new Page<>();
        page.add(functionpublish);
//        final List<Functionpublish> functionpublishes = Arrays.asList(functionpublish);
        when(mockFunctionpublishMapper.selectFunctionpublishList(any(Functionpublish.class)))
                .thenReturn(page);

        // Run the test
        final PageInfo<FunctionpublishDto> result = functionpublishServiceImplUnderTest.selectFunctionpublishList(
                functionpublishDto, 0, 0);
        assertNotNull(result);
        System.out.println(result);
        // Verify the results
        Mockito.verify(mockFunctionpublishMapper,times(1)).selectFunctionpublishList(any(Functionpublish.class));
    }


    @Test
    void testInsertFunctionpublish() {
        // Setup
        final FunctionpublishDto functionpublishDto = new FunctionpublishDto();
        functionpublishDto.setId(1L);
        functionpublishDto.setName("name");
        functionpublishDto.setLanguagetype(1);
        functionpublishDto.setDesc("desc");
        functionpublishDto.setAttribute(1);

        // Run the test
        functionpublishServiceImplUnderTest.insertFunctionpublish(functionpublishDto);

        // Verify the results
        verify(mockFunctionpublishMapper).insertFunctionpublish(any(Functionpublish.class));
    }

    @Test
    void testUpdateFunctionpublish() {
        // Setup
        final FunctionpublishDto functionpublishDto = new FunctionpublishDto();
        functionpublishDto.setId(1L);
        functionpublishDto.setName("name");
        functionpublishDto.setLanguagetype(1);
        functionpublishDto.setDesc("desc");
        functionpublishDto.setAttribute(1);

        // Run the test
        functionpublishServiceImplUnderTest.updateFunctionpublish(functionpublishDto);

        // Verify the results
        verify(mockFunctionpublishMapper).updateFunctionpublish(any(Functionpublish.class));
    }

    @Test
    void testDeleteFunctionpublishByIds() {
        // Setup
        // Run the test
        functionpublishServiceImplUnderTest.deleteFunctionpublishByIds(new Long[]{1L});

        // Verify the results
        verify(mockFunctionpublishMapper).deleteFunctionpublishByIds(any(Long[].class));
    }

    @Test
    void testDeleteFunctionpublishById() {
        // Setup
        when(mockFunctionpublishMapper.deleteFunctionpublishById(1L)).thenReturn(1);

        // Run the test
        final int result = functionpublishServiceImplUnderTest.deleteFunctionpublishById(1L);

        // Verify the results
        assertThat(result).isEqualTo(1);
    }

    @Test
    void testSelectFunctionpublishListForScriptEdit() {
        // Setup
        final VarAndFuncForEditDto varAndFuncForEditDto = new VarAndFuncForEditDto();
        varAndFuncForEditDto.setBindState(0);
        varAndFuncForEditDto.setBindIds(new Long[]{1L});
        varAndFuncForEditDto.setKeyword("keyword");

        // Configure FunctionpublishMapper.selectFunctionpublishListForScriptEdit(...).
        final Functionpublish functionpublish = new Functionpublish();
        functionpublish.setId(1L);
        functionpublish.setName("name");
        functionpublish.setLanguagetype(1);
        functionpublish.setDesc("desc");
        functionpublish.setAttribute(1);
        Page<Functionpublish> page = new Page<>();
        page.add(functionpublish);
        //final List<Functionpublish> functionpublishes = Arrays.asList(functionpublish);
        when(mockFunctionpublishMapper.selectFunctionpublishListForScriptEdit(any(VarAndFuncForEdit.class)))
                .thenReturn(page);

        // Run the test
        final PageInfo<FunctionpublishDto> result = functionpublishServiceImplUnderTest.selectFunctionpublishListForScriptEdit(
                varAndFuncForEditDto, 1, 50);
        assertNotNull(result);
        System.out.println(result);
        // Verify the results
        Mockito.verify(mockFunctionpublishMapper,times(1)).selectFunctionpublishListForScriptEdit(any(VarAndFuncForEdit.class));
    }

}
