package com.ideal.script.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ideal.script.dto.*;
import com.ideal.script.mapper.InfoMapper;
import com.ideal.script.mapper.InfoVersionTextMapper;
import com.ideal.script.model.dto.*;
import com.ideal.script.model.entity.InfoVersionText;
import com.ideal.script.service.IAttachmentService;
import com.ideal.script.service.IParameterService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class InfoVersionTextServiceImplTest {

    @Mock
    private InfoVersionTextMapper mockInfoVersionTextMapper;
    @Mock
    private  IParameterService mockParameterService;
    @Mock
    private  IAttachmentService mockAttachmentService;

    @Mock
    private InfoMapper mockInfoMapper;
    @InjectMocks
    private InfoVersionTextServiceImpl infoVersionTextServiceImplUnderTest;


    @Test
    void testSelectInfoVersionTextById() {
        // Setup
        // Configure InfoVersionTextMapper.selectInfoVersionTextById(...).
        final InfoVersionText infoVersionText = new InfoVersionText();
        infoVersionText.setId(1L);
        infoVersionText.setSrcScriptUuid("srcScriptUuid");
        infoVersionText.setContent("content");
        infoVersionText.setCreatorId(1L);
        infoVersionText.setCreatorName("creatorName");
        when(mockInfoVersionTextMapper.selectInfoVersionTextById(1L)).thenReturn(infoVersionText);

        // Run the test
        final InfoVersionTextDto result = infoVersionTextServiceImplUnderTest.selectInfoVersionTextById(1L);
        assertNotNull(result);
        System.out.println(result);
        // Verify the results
        Mockito.verify(mockInfoVersionTextMapper,times(1)).selectInfoVersionTextById(1L);

        // Verify the results
    }

    @Test
    void testSelectInfoVersionTextList() {
        // Setup
        final InfoVersionTextDto infoVersionTextDto = new InfoVersionTextDto();
        infoVersionTextDto.setId(1L);
        infoVersionTextDto.setSrcScriptUuid("srcScriptUuid");
        infoVersionTextDto.setContent("content");
        infoVersionTextDto.setCreatorId(1L);
        infoVersionTextDto.setCreatorName("creatorName");

        // Configure InfoVersionTextMapper.selectInfoVersionTextList(...).
        final InfoVersionText infoVersionText = new InfoVersionText();
        infoVersionText.setId(1L);
        infoVersionText.setSrcScriptUuid("srcScriptUuid");
        infoVersionText.setContent("content");
        infoVersionText.setCreatorId(1L);
        infoVersionText.setCreatorName("creatorName");
        Page<InfoVersionText> page = new Page<>();
        page.add(infoVersionText);
        when(mockInfoVersionTextMapper.selectInfoVersionTextList(any(InfoVersionText.class)))
                .thenReturn(page);

        // Run the test
        final PageInfo<InfoVersionTextDto> result = infoVersionTextServiceImplUnderTest.selectInfoVersionTextList(
                infoVersionTextDto, 1, 50);

        assertNotNull(result);
        System.out.println(result);
        // Verify the results
        Mockito.verify(mockInfoVersionTextMapper,times(1)).selectInfoVersionTextList(any(InfoVersionText.class));
    }


    @Test
    void testInsertInfoVersionText() {
        // Setup
        final InfoVersionTextDto infoVersionTextDto = new InfoVersionTextDto();
        infoVersionTextDto.setId(1L);
        infoVersionTextDto.setSrcScriptUuid("srcScriptUuid");
        infoVersionTextDto.setContent("content");
        infoVersionTextDto.setCreatorId(1L);
        infoVersionTextDto.setCreatorName("creatorName");

        // Run the test
        infoVersionTextServiceImplUnderTest.insertInfoVersionText(infoVersionTextDto);

        // Verify the results
        verify(mockInfoVersionTextMapper).insertInfoVersionText(any(InfoVersionText.class));
    }

    @Test
    void testUpdateInfoVersionText() {
        // Setup
        final InfoVersionTextDto infoVersionTextDto = new InfoVersionTextDto();
        infoVersionTextDto.setId(1L);
        infoVersionTextDto.setSrcScriptUuid("srcScriptUuid");
        infoVersionTextDto.setContent("content");
        infoVersionTextDto.setCreatorId(1L);
        infoVersionTextDto.setCreatorName("creatorName");

        // Run the test
        infoVersionTextServiceImplUnderTest.updateInfoVersionText(infoVersionTextDto);

        // Verify the results
        verify(mockInfoVersionTextMapper).updateInfoVersionText(any(InfoVersionText.class));
    }

    @Test
    void testDeleteInfoVersionTextByIds() {
        // Setup
        // Run the test
        infoVersionTextServiceImplUnderTest.deleteInfoVersionTextByIds(new Long[]{1L, 2L});

        // Verify the results
        verify(mockInfoVersionTextMapper).deleteInfoVersionTextByIds(any(Long[].class));
    }

    @Test
    void testDeleteInfoVersionTextById() {
        // Setup
        when(mockInfoVersionTextMapper.deleteInfoVersionTextById(1L)).thenReturn(1);

        // Run the test
        final int result = infoVersionTextServiceImplUnderTest.deleteInfoVersionTextById(1L);

        // Verify the results
        assertThat(result).isEqualTo(1);
    }

    @Test
    void testSelectInfoVersionTextByScriptId() {
        // Setup
        // Configure InfoVersionTextMapper.selectInfoVersionTextByScriptId(...).
        final InfoVersionText infoVersionText = new InfoVersionText();
        infoVersionText.setId(1L);
        infoVersionText.setSrcScriptUuid("srcScriptUuid");
        infoVersionText.setContent("content");
        infoVersionText.setCreatorId(1L);
        infoVersionText.setCreatorName("creatorName");
        when(mockInfoVersionTextMapper.selectInfoVersionTextByScriptId(1L)).thenReturn(infoVersionText);

        // Run the test
        final InfoVersionTextDto result = infoVersionTextServiceImplUnderTest.selectInfoVersionTextByScriptId(1L);
        assertNotNull(result);
        System.out.println(result);
        // Verify the results
        Mockito.verify(mockInfoVersionTextMapper,times(1)).selectInfoVersionTextByScriptId(1L);
    }

    @Test
    void testCreateInfoVersionText() {
        // Setup
        final ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        final ScriptVersionDto scriptVersionDto = new ScriptVersionDto();
        scriptVersionDto.setExpectType(0);
        scriptVersionDto.setExpectLastline("expectLastline");
        final ScriptContentDto scriptContentDto = new ScriptContentDto();
        scriptContentDto.setContent("content");
        scriptVersionDto.setScriptContentDto(scriptContentDto);
        scriptInfoDto.setScriptVersionDto(scriptVersionDto);
        scriptVersionDto.setSrcScriptUuid("srcScriptUuid");
        scriptInfoDto.setScriptVersionDto(scriptVersionDto);

        // Run the test
        infoVersionTextServiceImplUnderTest.createInfoVersionText(scriptInfoDto);

        // Verify the results
        verify(mockInfoVersionTextMapper).insertInfoVersionText(any(InfoVersionText.class));
    }

    @Test
    void testUpdateInfoVersionText2() {
        // Setup
        final ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        final ScriptVersionDto scriptVersionDto = new ScriptVersionDto();
        scriptVersionDto.setExpectType(0);
        scriptVersionDto.setExpectLastline("expectLastline");
        final ScriptContentDto scriptContentDto = new ScriptContentDto();
        scriptContentDto.setContent("content");
        scriptVersionDto.setScriptContentDto(scriptContentDto);
        scriptInfoDto.setScriptVersionDto(scriptVersionDto);
        scriptVersionDto.setSrcScriptUuid("srcScriptUuid");
        scriptInfoDto.setScriptVersionDto(scriptVersionDto);

        // Run the test
        final boolean result = infoVersionTextServiceImplUnderTest.updateInfoVersionText(scriptInfoDto);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockInfoVersionTextMapper).updateInfoVersionTextByScriptUuid(any(InfoVersionText.class));
    }

    @Test
    void selectInfoVersionTextByScriptUuid() {
        InfoVersionText infoVersionText = new InfoVersionText();

        when(mockInfoVersionTextMapper.selectInfoVersionTextByScriptUuid(anyString())).thenReturn(infoVersionText);

        final InfoVersionTextDto result = infoVersionTextServiceImplUnderTest.selectInfoVersionTextByScriptUuid(anyString());
        assertNotNull(result);


    }

}
