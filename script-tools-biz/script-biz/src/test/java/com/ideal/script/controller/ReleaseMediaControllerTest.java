package com.ideal.script.controller;

import com.ideal.common.dto.R;
import com.ideal.common.util.spring.MessageUtil;
import com.ideal.common.util.spring.SpringUtil;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.ToProductQueryDto;
import com.ideal.script.service.IReleaseMediaService;
import com.ideal.system.common.component.model.CurrentUser;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.context.MessageSource;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class ReleaseMediaControllerTest {

    @Mock
    private IReleaseMediaService mockReleaseMediaService;

    @Mock
    private MessageSource messageSource;

    private ReleaseMediaController releaseMediaControllerUnderTest;

    private static MockedStatic<SpringUtil> springUtilMockedStatic;
    private static MockedStatic<MessageUtil> messageUtilMockedStatic;

    @BeforeAll
    static void setUpStaticMethod() {
        // Mock SpringUtil 静态方法
        springUtilMockedStatic = Mockito.mockStatic(SpringUtil.class);
        messageUtilMockedStatic = Mockito.mockStatic(MessageUtil.class);

        // 设置 MessageUtil.message 方法的返回值
        messageUtilMockedStatic.when(() -> MessageUtil.message(anyString())).thenAnswer(invocation -> {
            String key = invocation.getArgument(0);
            return key; // 直接返回消息键作为消息内容
        });
    }

    @AfterAll
    static void tearDownStaticMethod() {
        if (springUtilMockedStatic != null) {
            springUtilMockedStatic.close();
        }
        if (messageUtilMockedStatic != null) {
            messageUtilMockedStatic.close();
        }
    }

    @BeforeEach
    void setUp() {
        // 设置 SpringUtil.getBean 方法的返回值
        springUtilMockedStatic.when(() -> SpringUtil.getBean(MessageSource.class)).thenReturn(messageSource);
        when(messageSource.getMessage(anyString(), any(), any())).thenAnswer(invocation -> {
            String key = invocation.getArgument(0);
            return key; // 直接返回消息键作为消息内容
        });

        releaseMediaControllerUnderTest = new ReleaseMediaController(mockReleaseMediaService);
    }

    @Test
    void testExportReleaseMedia() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Run the test
        releaseMediaControllerUnderTest.exportReleaseMedia(new Long[]{0L}, response);

        // Verify the results
        verify(mockReleaseMediaService).exportReleaseMedia(any(Long[].class), any(HttpServletResponse.class));
    }

    @Test
    void testExportReleaseMedia_IReleaseMediaServiceThrowsScriptException() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        doThrow(ScriptException.class).when(mockReleaseMediaService).exportReleaseMedia(any(Long[].class),
                any(HttpServletResponse.class));

        // 确保消息工具类被正确调用
        messageUtilMockedStatic.when(() -> MessageUtil.message(anyString())).thenReturn("error message");

        // Run the test
        releaseMediaControllerUnderTest.exportReleaseMedia(new Long[]{0L}, response);

        // Verify the results
    }

    @Test
    void testImportReleaseMedia() throws Exception {
        // Setup
        final MultipartFile file = new MockMultipartFile("name", "content".getBytes());
        final ToProductQueryDto toProductQueryDto = new ToProductQueryDto();
        toProductQueryDto.setSrcScriptUuidList(Arrays.asList("value"));
        toProductQueryDto.setUserName("userName");
        toProductQueryDto.setId(0L);
        toProductQueryDto.setFileName("fileName");
        toProductQueryDto.setDescription("description");

        // Run the test
        final R<Object> result = releaseMediaControllerUnderTest.importReleaseMedia(file, toProductQueryDto);

        // Verify the results
        verify(mockReleaseMediaService).importReleaseMedia(any(MultipartFile.class), any(CurrentUser.class),
                any(ToProductQueryDto.class));
    }

    @Test
    void testImportReleaseMedia_IReleaseMediaServiceThrowsScriptException() throws Exception {
        // Setup
        final MultipartFile file = new MockMultipartFile("name", "content".getBytes());
        final ToProductQueryDto toProductQueryDto = new ToProductQueryDto();
        toProductQueryDto.setSrcScriptUuidList(Arrays.asList("value"));
        toProductQueryDto.setUserName("userName");
        toProductQueryDto.setId(0L);
        toProductQueryDto.setFileName("fileName");
        toProductQueryDto.setDescription("description");

        doThrow(ScriptException.class).when(mockReleaseMediaService).importReleaseMedia(any(MultipartFile.class),
                any(CurrentUser.class), any(ToProductQueryDto.class));

        // 确保消息工具类被正确调用
        messageUtilMockedStatic.when(() -> MessageUtil.message(anyString())).thenReturn("error message");

        // Run the test
        final R<Object> result = releaseMediaControllerUnderTest.importReleaseMedia(file, toProductQueryDto);

        // Verify the results
    }
}
