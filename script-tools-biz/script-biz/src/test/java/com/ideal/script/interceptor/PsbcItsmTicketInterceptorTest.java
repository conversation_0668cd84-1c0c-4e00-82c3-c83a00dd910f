package com.ideal.script.interceptor;

import com.alibaba.fastjson.JSONObject;
import com.ideal.common.util.spring.SpringUtil;
import com.ideal.sc.util.CurrentUserUtil;
import com.ideal.sc.util.HttpClientUtil;
import com.ideal.sc.util.MD5Utils;
import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.config.PsbcProperties;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.filter.CachedRequestWrapper;
import com.ideal.script.model.dto.ItsmTaskOrderNumberDto;
import com.ideal.script.model.dto.ScriptExecAuditDto;
import com.ideal.script.model.dto.TaskDto;
import com.ideal.script.model.entity.Category;
import com.ideal.script.model.entity.InfoVersion;
import com.ideal.script.service.impl.builders.MyScriptServiceScripts;
import com.ideal.system.api.IUserInfo;
import com.ideal.system.common.component.model.CurrentUser;
import com.ideal.system.dto.UserInfoApiDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class PsbcItsmTicketInterceptorTest {

    @Mock
    private IUserInfo userInfo;

    @Mock
    private MyScriptServiceScripts scripts;

    @InjectMocks
    private PsbcItsmTicketInterceptor interceptor;

    @Mock
    private HttpServletResponse response;

    @Mock
    private Object handler;

    @Mock
    private CachedRequestWrapper cachedRequestWrapper;

    @Mock
    private InfoVersion infoVersion;

    @Mock
    private Category category;

    private ScriptExecAuditDto scriptExecAuditDto;
    private TaskDto taskDto;
    private CurrentUser currentUser;
    private List<UserInfoApiDto> userInfoList;
    private ItsmTaskOrderNumberDto itsmTaskOrderNumberDto;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        scriptExecAuditDto = new ScriptExecAuditDto();
        scriptExecAuditDto.setScriptInfoVersionId(1L);

        taskDto = new TaskDto();
        taskDto.setTaskName("测试任务");
        scriptExecAuditDto.setTaskInfo(taskDto);

        currentUser = new CurrentUser();
        currentUser.setId(1L);
        currentUser.setLoginName("testUser");
        currentUser.setFullName("测试用户");

        UserInfoApiDto userInfoApiDto = new UserInfoApiDto();
        userInfoApiDto.setId(1L);
        userInfoApiDto.setTelephone("13800138000");
        userInfoList = new ArrayList<>();
        userInfoList.add(userInfoApiDto);

        itsmTaskOrderNumberDto = new ItsmTaskOrderNumberDto();
    }

    @ParameterizedTest
    @MethodSource("provideTestCases")
    @DisplayName("测试preHandle方法在不同场景下的行为")
    void preHandle_WithDifferentScenarios(Integer scriptLevel, String workOrderNum, String resCode, String resMsg, boolean expectedResult) throws Exception {
        // 准备测试数据
        scriptExecAuditDto.setWorkOrderNum(workOrderNum);

        // Mock CachedRequestWrapper
        when(cachedRequestWrapper.getBodyString()).thenReturn(JSONObject.toJSONString(scriptExecAuditDto));
        doNothing().when(cachedRequestWrapper).modifyBody(anyString());

        try (MockedStatic<SpringUtil> springUtilMock = Mockito.mockStatic(SpringUtil.class)) {
            // Mock SpringUtil
            PsbcProperties psbcProperties = mock(PsbcProperties.class);
            springUtilMock.when(() -> SpringUtil.getBean(PsbcProperties.class)).thenReturn(psbcProperties);

            // Mock InfoVersionMapper
            when(scripts.getInfoVersionMapper()).thenReturn(mock(com.ideal.script.mapper.InfoVersionMapper.class));
            when(scripts.getInfoVersionMapper().selectInfoVersionById(anyLong())).thenReturn(infoVersion);
            when(infoVersion.getLevel()).thenReturn(scriptLevel);

            // 如果不是白名单脚本，需要模拟ITSM接口调用
            if (!Enums.ScriptLevel.WHITE_SCRIPT.getValue().equals(scriptLevel)) {
                // Mock CategoryMapper
                when(scripts.getCategoryMapper()).thenReturn(mock(com.ideal.script.mapper.CategoryMapper.class));
                when(scripts.getCategoryMapper().getCategoryByScriptInfoVersionId(anyLong())).thenReturn(category);
                when(category.getName()).thenReturn("测试分类");

                // Mock CurrentUserUtil
                try (MockedStatic<CurrentUserUtil> currentUserUtilMock = Mockito.mockStatic(CurrentUserUtil.class)) {
                    currentUserUtilMock.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);

                    // Mock IUserInfo
                    when(userInfo.getUserInfoList(anyList())).thenReturn(userInfoList);

                    // Mock PsbcProperties
                    when(psbcProperties.getItsmCreateOrderUrl()).thenReturn("http://test.com/create");
                    when(psbcProperties.getItsmCheckOrderUrl()).thenReturn("http://test.com/check");
                    when(psbcProperties.getItsmAuthenticationKey()).thenReturn("testKey");

                    // Mock HttpClientUtil
                    try (MockedStatic<HttpClientUtil> httpClientUtilMock = Mockito.mockStatic(HttpClientUtil.class);
                         MockedStatic<MD5Utils> md5UtilsMock = Mockito.mockStatic(MD5Utils.class)) {

                        md5UtilsMock.when(() -> MD5Utils.md5(anyString())).thenReturn("testSignature");

                        itsmTaskOrderNumberDto.setResCode(resCode);
                        itsmTaskOrderNumberDto.setResMsg(resMsg);

                        httpClientUtilMock.when(() -> HttpClientUtil.postByJson(
                                anyString(), anyMap(), any(), eq(ItsmTaskOrderNumberDto.class)))
                                .thenReturn(itsmTaskOrderNumberDto);

                        // 执行测试
                        if (expectedResult) {
                            boolean result = interceptor.preHandle(cachedRequestWrapper, response, handler);
                            assertTrue(result);
                        } else {
                            if (Enums.ItsmWorkOrderResultCode.ORDER_TELEPHONE_ERROR.getValue().equals(resCode)) {
                                // 测试手机号验证失败的情况
                                ScriptException exception = assertThrows(ScriptException.class, () ->
                                    interceptor.preHandle(cachedRequestWrapper, response, handler)
                                );
                                assertEquals("error.apply.script.itsm.telephone", exception.getMessage());
                            } else {
                                // 测试其他失败情况
                                ScriptException exception = assertThrows(ScriptException.class, () ->
                                    interceptor.preHandle(cachedRequestWrapper, response, handler)
                                );
                                assertEquals("error.apply.script.itsm.task", exception.getMessage());
                            }
                        }
                    }
                }
            } else {
                // 白名单脚本直接返回true
                boolean result = interceptor.preHandle(cachedRequestWrapper, response, handler);
                assertTrue(result);
            }
        }
    }

    @Test
    @DisplayName("测试非CachedRequestWrapper类型的请求")
    void preHandle_WithNonCachedRequestWrapper_ShouldThrowException() throws Exception {
        // 创建一个非CachedRequestWrapper类型的请求
        HttpServletRequest request = mock(HttpServletRequest.class);

        // 执行测试，应该抛出异常
        ScriptException exception = assertThrows(ScriptException.class, () ->
            interceptor.preHandle(request, response, handler)
        );

        assertEquals("error request type:" + request.getClass().getName(), exception.getMessage());
    }

    /**
     * 提供测试用例参数
     */
    private static Stream<Arguments> provideTestCases() {
        return Stream.of(
            // 白名单脚本，直接返回true
            Arguments.of(Enums.ScriptLevel.WHITE_SCRIPT.getValue(), "", "", "", true),

            // 风险脚本，无工单号，创建工单成功
            Arguments.of(Enums.ScriptLevel.HIGH_RISK.getValue(), "", Enums.ItsmWorkOrderResultCode.RIGHT_RESULT_CODE.getValue(), "**********", true),

            // 风险脚本，无工单号，手机号验证失败
            Arguments.of(Enums.ScriptLevel.HIGH_RISK.getValue(), "", Enums.ItsmWorkOrderResultCode.ORDER_TELEPHONE_ERROR.getValue(), "", false),

            // 风险脚本，无工单号，创建工单失败
            Arguments.of(Enums.ScriptLevel.HIGH_RISK.getValue(), "", "9999", "创建失败", false),

            // 风险脚本，有工单号，校验工单成功
            Arguments.of(Enums.ScriptLevel.HIGH_RISK.getValue(), "**********", Enums.ItsmWorkOrderResultCode.RIGHT_RESULT_CODE.getValue(), "", true),

            // 风险脚本，有工单号，校验工单失败
            Arguments.of(Enums.ScriptLevel.HIGH_RISK.getValue(), "**********", "9999", "校验失败", false)
        );
    }
}
