package com.ideal.script.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ideal.agent.gateway.model.AgentTaskResultDto;
import com.ideal.common.util.spring.SpringUtil;
import com.ideal.sc.CustomerProperty;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.common.util.TransactionSyncUtil;
import com.ideal.script.config.PsbcProperties;
import com.ideal.script.dto.TaskRuntimeApiDto;
import com.ideal.script.dto.TaskRuntimeQueryApiDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.InfoMapper;
import com.ideal.script.mapper.TaskRuntimeMapper;
import com.ideal.script.mapper.TaskRuntimeStdoutMapper;
import com.ideal.script.model.bean.TaskHandleParam;
import com.ideal.script.model.bean.TaskRunTimeBindAgentBean;
import com.ideal.script.model.dto.TaskInstanceDto;
import com.ideal.script.model.dto.TaskRuntimeDto;
import com.ideal.script.model.dto.TaskStartDto;
import com.ideal.script.model.entity.TaskRuntime;
import com.ideal.script.model.entity.TaskRuntimeStdout;
import com.ideal.script.observer.itsm.ItsmScriptTaskResultPush;
import com.ideal.script.observer.numerical.ScheduledTaskNumericalResultPush;
import com.ideal.script.service.IAfterRuntimeHandlerService;
import com.ideal.script.service.IExectimeService;
import com.ideal.script.service.IScriptTaskStatePublisher;
import com.ideal.script.service.ITaskExecuteService;
import com.ideal.script.service.ITaskRuntimeService;
import com.ideal.script.service.ITaskRuntimeStdoutService;
import com.ideal.script.service.impl.builders.MyScriptServiceScripts;
import com.ideal.script.service.impl.builders.MyScriptServiceScriptsBuilder;
import com.ideal.system.common.component.model.CurrentUser;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.Collections;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TaskRuntimeServiceImplTest {

    @Mock
    private TaskRuntimeMapper mockTaskRuntimeMapper;
    @Mock
    private IExectimeService mockExectimeService;
    @Mock
    private TaskRuntimeStdoutMapper taskRuntimeStdoutMapper;
    @Mock
    private ITaskExecuteService taskExecuteService;
    @Mock
    private RedissonClient redissonClient;
    @Mock
    private ApplicationContext applicationContext;

    private TaskRuntimeServiceImpl taskRuntimeServiceImplUnderTest;

    private ItsmScriptTaskResultPush itsmScriptTaskResultPush;
    @InjectMocks
    private MyScriptServiceScriptsBuilder builderMyScriptServiceScriptsBuilder;
    private MyScriptServiceScripts scripts;
    @Mock
    private InfoMapper infoMapper;

    @Mock
    private TaskInstanceServiceImpl taskInstanceService;
    @Mock
    private  RedisTemplate<String, String> redisTemplate;
    @Mock
    private ScheduledTaskNumericalResultPush scheduledTaskNumericalResultPush;

    @Mock
    private  IScriptTaskStatePublisher scriptTaskStatePublisher;
    @Mock
    private  CustomerProperty customerProperty;
    @Mock
    private  ITaskRuntimeStdoutService taskRuntimeStdoutService;
    @BeforeEach
    void setUp() {
        scripts = new MyScriptServiceScripts(builderMyScriptServiceScriptsBuilder);
        // 创建真实对象，然后用spy包装
        TaskRuntimeServiceImpl realService = new TaskRuntimeServiceImpl(scheduledTaskNumericalResultPush,mockTaskRuntimeMapper,
                mockExectimeService, taskRuntimeStdoutMapper,redissonClient,taskExecuteService,itsmScriptTaskResultPush,scriptTaskStatePublisher,customerProperty,taskInstanceService,taskRuntimeStdoutService);
        taskRuntimeServiceImplUnderTest = spy(realService);
        itsmScriptTaskResultPush = new ItsmScriptTaskResultPush(scripts,taskRuntimeServiceImplUnderTest,taskInstanceService,redisTemplate,applicationContext);
        Field itsmScriptTaskResultPushField = ReflectionUtils.findField(TaskRuntimeServiceImpl.class, "itsmScriptTaskResultPush");
        if (itsmScriptTaskResultPushField != null) {
            ReflectionUtils.makeAccessible(itsmScriptTaskResultPushField);
            ReflectionUtils.setField(itsmScriptTaskResultPushField, taskRuntimeServiceImplUnderTest, itsmScriptTaskResultPush);
        }
    }

    @Test
    void testSelectTaskRuntimeById() {
        // Setup
        // Configure TaskRuntimeMapper.selectTaskRuntimeById(...).
        final TaskRuntime taskRuntime = new TaskRuntime();
        taskRuntime.setBizId("bizId");
        taskRuntime.setScriptTaskIpsId(0L);
        taskRuntime.setAgentTaskId(0L);
        taskRuntime.setId(0L);
        taskRuntime.setScriptTaskId(0L);
        when(mockTaskRuntimeMapper.selectTaskRuntimeById(0L)).thenReturn(taskRuntime);

        // Run the test
        final TaskRuntimeDto result = taskRuntimeServiceImplUnderTest.selectTaskRuntimeById(0L);
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testSelectTaskRuntimeList() {
        // Setup
        final TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setScriptTaskIpsId(0L);
        taskRuntimeDto.setAgentTaskId(0L);
        taskRuntimeDto.setId(0L);
        taskRuntimeDto.setScriptTaskId(0L);
        taskRuntimeDto.setSrcScriptUuid("srcScriptUuid");

        // Configure TaskRuntimeMapper.selectTaskRuntimeList(...).
        final TaskRuntime taskRuntime = new TaskRuntime();
        taskRuntime.setBizId("bizId");
        taskRuntime.setScriptTaskIpsId(0L);
        taskRuntime.setAgentTaskId(0L);
        taskRuntime.setId(0L);
        taskRuntime.setScriptTaskId(0L);
        taskRuntime.setStartTime(new Timestamp(System.currentTimeMillis())); // 设置 startTime
        taskRuntime.setTimeoutValue(60L); // 设置 timeoutValue（单位：秒）

        Page<TaskRuntime> page = new Page<>();
        page.add(taskRuntime);
        when(mockTaskRuntimeMapper.selectTaskRuntimeList(any(TaskRuntime.class))).thenReturn(page);

        // Run the test
        final PageInfo<TaskRuntimeDto> result = taskRuntimeServiceImplUnderTest.selectTaskRuntimeList(taskRuntimeDto, 0, 0);
        assertNotNull(result);

        // Verify the results
        assertThat(result.getList()).hasSize(1);
        assertThat(result.getList().get(0).getTimeout()).isIn(0, 1); // 检查是否计算了超时
    }


    @Test
    void testInsertTaskRuntime() {
        // Setup
        final TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setScriptTaskIpsId(0L);
        taskRuntimeDto.setAgentTaskId(0L);
        taskRuntimeDto.setId(0L);
        taskRuntimeDto.setScriptTaskId(0L);
        taskRuntimeDto.setSrcScriptUuid("srcScriptUuid");

        when(mockTaskRuntimeMapper.insertTaskRuntime(any(TaskRuntime.class))).thenReturn(0);

        // Run the test
        final int result = taskRuntimeServiceImplUnderTest.insertTaskRuntime(taskRuntimeDto);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testUpdateTaskRuntime() {
        // Setup
        final TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setScriptTaskIpsId(0L);
        taskRuntimeDto.setAgentTaskId(0L);
        taskRuntimeDto.setId(0L);
        taskRuntimeDto.setScriptTaskId(0L);
        taskRuntimeDto.setSrcScriptUuid("srcScriptUuid");

        when(mockTaskRuntimeMapper.updateTaskRuntime(any(TaskRuntime.class))).thenReturn(0);

        // Run the test
        final int result = taskRuntimeServiceImplUnderTest.updateTaskRuntime(taskRuntimeDto);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testDeleteTaskRuntimeByIds() {
        // Setup
        when(mockTaskRuntimeMapper.deleteTaskRuntimeByIds(any(Long[].class))).thenReturn(0);

        // Run the test
        final int result = taskRuntimeServiceImplUnderTest.deleteTaskRuntimeByIds(new Long[]{0L});

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testDeleteTaskRuntimeById() {
        // Setup
        when(mockTaskRuntimeMapper.deleteTaskRuntimeById(0L)).thenReturn(0);

        // Run the test
        final int result = taskRuntimeServiceImplUnderTest.deleteTaskRuntimeById(0L);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testUpdateTaskRuntimeState() {
        // Setup
        TaskRuntime taskRuntime = new TaskRuntime();
        Timestamp timestamp = new Timestamp(1695945600000L);
        taskRuntime.setStartTime(timestamp);
        Timestamp newTimestamp = new Timestamp(timestamp.getTime() + 1);
        taskRuntime.setEndTime(newTimestamp);
        when(mockTaskRuntimeMapper.getTaskRuntimeStartTime(0L)).thenReturn(taskRuntime);
        // Run the test
        taskRuntimeServiceImplUnderTest.updateTaskRuntimeState(0, Collections.singletonList(0), 0L);

        // Verify the results

        verify(mockTaskRuntimeMapper).getTaskRuntimeStartTime(0L);
    }

    @ParameterizedTest
    @ValueSource(strings = {"stdout",""})
    void testGetOutPutMessage(String stdout) throws Exception {
        // Setup
        // Configure TaskRuntimeMapper.selectTaskRuntimeById(...).

        TaskRuntimeStdout taskRuntimeStdout =new TaskRuntimeStdout();
        taskRuntimeStdout.setIstdout(stdout);
        if(stdout.isEmpty()) {
            taskRuntimeStdout.setIstderror("stderror");
        }

        // Configure AgentOperateApi.get(...).
        final AgentTaskResultDto agentTaskResultDto = new AgentTaskResultDto();
        agentTaskResultDto.setId(1L);
        agentTaskResultDto.setTaskId(1L);
        agentTaskResultDto.setMessage("message");
        agentTaskResultDto.setResult("{\"name\": \"John Doe\", \"age\": 30, \"stdout\": \"SGVsbG8gV29ybGQhCg==\"}");
        when(taskRuntimeStdoutMapper.selectStdoutByTaskRuntimeId(anyLong())).thenReturn(taskRuntimeStdout);

        // Run the test
        final String result = taskRuntimeServiceImplUnderTest.getOutPutMessage(0L);

        // Verify the results
        assertThat(result).isNotEmpty();
    }


    @Test
    void testGetOutPutMessage_Exception() {
        // Setup
        // Configure AgentOperateApi.get(...).
        final AgentTaskResultDto agentTaskResultDto = new AgentTaskResultDto();
        agentTaskResultDto.setId(1L);
        agentTaskResultDto.setTaskId(1L);
        agentTaskResultDto.setMessage("message");
        agentTaskResultDto.setResult("{\"name\": \"John Doe\", \"age\": 30, \"stdout\": \"SGVsbG8gV29ybGQhCg==\"}");
        doThrow(new RuntimeException("查询失败")).when(taskRuntimeStdoutMapper).selectStdoutByTaskRuntimeId(anyLong());

        // Run the test
        assertThrows(Exception.class,()->{
            taskRuntimeServiceImplUnderTest.getOutPutMessage(0L);
        });
    }



    @Test
    void testSelectCountByTaskInstanceId() {
        // Setup
        when(mockTaskRuntimeMapper.selectCountByTaskInstanceId(0L)).thenReturn(0);

        // Run the test
        final Integer result = taskRuntimeServiceImplUnderTest.selectCountByTaskInstanceId(0L);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testGetRunningAgentInstanceCount() {
        // Setup
        when(mockTaskRuntimeMapper.getRunningAgentInstanceCount(0L)).thenReturn(0);

        // Run the test
        final Integer result = taskRuntimeServiceImplUnderTest.getRunningAgentInstanceCount(0L);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testGetBindAgentForTaskRuntime() {
        // Setup
        // Configure TaskRuntimeMapper.getBindAgentForTaskRuntime(...).
        final TaskRunTimeBindAgentBean taskRunTimeBindAgentBean = new TaskRunTimeBindAgentBean();
        taskRunTimeBindAgentBean.setTaskRuntimeId(0L);
        taskRunTimeBindAgentBean.setSysmAgentInfoId(0L);
        taskRunTimeBindAgentBean.setTaskIpsId(0L);
        taskRunTimeBindAgentBean.setAgentIp("agentIp");
        taskRunTimeBindAgentBean.setAgentPort("agentPort");
        when(mockTaskRuntimeMapper.getBindAgentForTaskRuntime(0L)).thenReturn(taskRunTimeBindAgentBean);

        // Run the test
        final TaskRunTimeBindAgentBean result = taskRuntimeServiceImplUnderTest.getBindAgentForTaskRuntime(0L);
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testUpdateExecTimeAndState() {
        // Setup
        final TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setScriptTaskIpsId(0L);
        taskRuntimeDto.setAgentTaskId(0L);
        taskRuntimeDto.setId(0L);
        taskRuntimeDto.setScriptTaskId(0L);
        taskRuntimeDto.setSrcScriptUuid("srcScriptUuid");

        TaskRuntime taskRuntime = new TaskRuntime();
        Timestamp timestamp = new Timestamp(1695945600000L);
        taskRuntime.setStartTime(timestamp);
        Timestamp newTimestamp = new Timestamp(timestamp.getTime() + 1);
        taskRuntime.setEndTime(newTimestamp);
        when(mockTaskRuntimeMapper.getTaskRuntimeStartTime(1L)).thenReturn(taskRuntime);
        // Run the test
        taskRuntimeServiceImplUnderTest.updateExecTimeAndState("1", 0, taskRuntimeDto);

        // Verify the results
        verify(mockExectimeService).updateScriptExecTime(0, "srcScriptUuid", null);
    }

    @Test
    void getTaskRuntimeInfoByInstanceId() {
        TaskRuntimeQueryApiDto taskRuntimeQueryApiDto = new TaskRuntimeQueryApiDto();
        taskRuntimeQueryApiDto.setTaskInstanceId(1L);

        TaskRuntime taskRuntime = new TaskRuntime();
        taskRuntime.setTaskInstanceId(1L);

        Page<TaskRuntime> page = new Page<>();
        page.add(taskRuntime);

        when(mockTaskRuntimeMapper.selectTaskRuntimeList(any(TaskRuntime.class))).thenReturn(page);
        PageInfo<TaskRuntimeApiDto> taskRuntimeInfoByInstanceId = taskRuntimeServiceImplUnderTest.getTaskRuntimeInfoByInstanceId(taskRuntimeQueryApiDto);

        assertNotNull(taskRuntimeInfoByInstanceId);
    }

    @ParameterizedTest
    @ValueSource(longs = {0L,1L,-1L})
    void driverNextBatchAgent(Long nowCount) throws ScriptException, NoSuchFieldException, IllegalAccessException {
        TaskRuntime taskRuntime = new TaskRuntime();
        taskRuntime.setScriptTaskId(1L);
        taskRuntime.setTaskInstanceId(1L);
        Long taskRuntimeId = 1L;

        when(mockTaskRuntimeMapper.selectTaskRuntimeById(taskRuntimeId)).thenReturn(taskRuntime);

        // mock Redisson Lua 脚本调用返回
        org.redisson.api.RScript rScript = mock(org.redisson.api.RScript.class);
        when(redissonClient.getScript()).thenReturn(rScript);

        String userStr = JSON.toJSONString(new CurrentUser());
        TaskStartDto taskStartDto = new TaskStartDto();
        // 让驱动模式为分批执行
        taskStartDto.setDriveMode(Enums.DriverModel.BATCH_EXEC.getValue());
        String taskStartDtoStr = JSON.toJSONString(taskStartDto);

        java.util.List<Object> resultList;
        if(nowCount == 1L) { // 模拟剩余1个
            resultList = java.util.Arrays.asList(
                    userStr,              // user
                    taskStartDtoStr,      // taskStartDto
                    "1",                 // eachNum
                    "2",                 // driveMode (BATCH)
                    "2",                 // total
                    1L                    // nowCount
            );
            when(mockTaskRuntimeMapper.selectErrorRuntimeList(any())).thenReturn(Collections.emptyList());
        } else if(nowCount == 0L) { // nowCount == 0，任务完成分支
            // itsm 推送相关依赖
            ConfigurableListableBeanFactory configurableListableBeanFactory = mock(ConfigurableListableBeanFactory.class);
            PsbcProperties psbcProperties = new PsbcProperties();
            psbcProperties.setPushTaskMessageToItsmUrl("http://pushitsm");
            when(configurableListableBeanFactory.getBean(PsbcProperties.class)).thenReturn(psbcProperties);
            when(applicationContext.getBeanNamesForType(PsbcProperties.class)).thenReturn(new String[]{"psbcProperties"});
            ValueOperations<String,String> valueOperations = mock(ValueOperations.class);
            when(redisTemplate.opsForValue()).thenReturn(valueOperations);
            when(redisTemplate.opsForValue().get("itsm_task_" + 1)).thenReturn("1");

            TaskInstanceDto taskInstanceDto = new TaskInstanceDto();
            taskInstanceDto.setId(1L);
            taskInstanceDto.setScriptTaskId(1L);
            taskInstanceDto.setSrcScriptUuid("srcScriptUuid");
            taskInstanceDto.setStatus(20);
            when(taskInstanceService.getTaskInstanceByTaskInfoId(1L)).thenReturn(taskInstanceDto);

            Field beanFactory = SpringUtil.class.getDeclaredField("beanFactory");
            beanFactory.setAccessible(true);
            beanFactory.set(null, configurableListableBeanFactory);

            resultList = java.util.Arrays.asList(
                    userStr,
                    taskStartDtoStr,
                    "1",
                    "2",
                    "1",
                    0L
            );
        } else { // nowCount == -1L，模拟Redis返回空结果
            resultList = Collections.emptyList(); // 返回空列表，触发getTaskInfoFromRedis返回null的分支
        }

        // 统一使用doReturn方式来避免严格模式问题
        doReturn(resultList).when(rScript).eval(
                org.mockito.ArgumentMatchers.eq(org.redisson.api.RScript.Mode.READ_WRITE),
                org.mockito.ArgumentMatchers.anyString(),
                org.mockito.ArgumentMatchers.eq(org.redisson.api.RScript.ReturnType.MULTI),
                org.mockito.ArgumentMatchers.anyList(),
                org.mockito.ArgumentMatchers.<Object[]>any()
        );

        // 调用测试的方法
        taskRuntimeServiceImplUnderTest.driverNextBatchAgent(taskRuntimeId);

        // 验证行为
        if(nowCount == 1L) {
            verify(taskExecuteService).scriptTaskStart(any(TaskStartDto.class), any(CurrentUser.class));
        } else if(nowCount == 0L) {
            verify(mockTaskRuntimeMapper,times(2)).selectTaskRuntimeById(taskRuntimeId);
        } else { // nowCount == -1L，Redis返回空结果的情况
            // 验证只调用了基本的方法，但没有调用任务执行服务
            verify(mockTaskRuntimeMapper).selectTaskRuntimeById(taskRuntimeId);
            verify(redissonClient).getScript();
            verify(taskExecuteService, never()).scriptTaskStart(any(), any());
        }
    }

    @Test
    @DisplayName("测试updateScriptTaskRuntime方法-运行状态更新为完成")
    void updateScriptTaskRuntime_runningToCompleted_test() {
        // Setup
        String taskRuntimeId = "1";
        int status = 20; // 完成状态
        int oldStatus = Enums.TaskRuntimeState.RUNNING.getValue(); // 运行状态

        TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setId(1L);
        taskRuntimeDto.setTaskInstanceId(1L);
        taskRuntimeDto.setState(oldStatus);
        taskRuntimeDto.setSrcScriptUuid("srcScriptUuid");

        try (MockedStatic<SpringUtil> springUtilMock = mockStatic(SpringUtil.class)) {
            ITaskRuntimeService mockTaskRuntimeService = mock(ITaskRuntimeService.class);
            springUtilMock.when(() -> SpringUtil.getBean(ITaskRuntimeService.class)).thenReturn(mockTaskRuntimeService);

            // 注意：源码中调用的是 selectTaskRuntimeById(Long.parseLong(taskRuntimeId))
            // 这个方法返回的是 TaskRuntimeDto，所以需要正确mock
            doReturn(taskRuntimeDto).when(taskRuntimeServiceImplUnderTest).selectTaskRuntimeById(1L);

            // 执行测试
            taskRuntimeServiceImplUnderTest.updateScriptTaskRuntime(taskRuntimeId, status);

            // 验证行为
            verify(mockTaskRuntimeService).updateExecTimeAndState(taskRuntimeId, status, taskRuntimeDto);
        }
    }

    @Test
    @DisplayName("测试updateScriptTaskRuntime方法-运行状态更新为异常")
    void updateScriptTaskRuntime_runningToFailed_test() {
        // Setup
        String taskRuntimeId = "1";
        int status = Enums.TaskRuntimeState.SCRIPT_FAIL_STATE.getValue(); // 异常状态
        int oldStatus = Enums.TaskRuntimeState.RUNNING.getValue(); // 运行状态

        TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setId(1L);
        taskRuntimeDto.setTaskInstanceId(1L);
        taskRuntimeDto.setState(oldStatus);
        taskRuntimeDto.setSrcScriptUuid("srcScriptUuid");

        // 根据实际代码逻辑，当状态为异常时，会创建包含异常状态的新数组
        int[] scriptFinishError = {20, 60, 70, 30}; // COMPLETED, TERMINATED, COMPLETED_RED, SCRIPT_FAIL_STATE
        when(taskInstanceService.updateState(
            eq(Enums.TaskInstanceStatus.EXCEPTION.getValue()),
            eq(1L),
            eq(scriptFinishError)
        )).thenReturn(1);

        try (MockedStatic<SpringUtil> springUtilMock = mockStatic(SpringUtil.class)) {
            ITaskRuntimeService mockTaskRuntimeService = mock(ITaskRuntimeService.class);
            springUtilMock.when(() -> SpringUtil.getBean(ITaskRuntimeService.class)).thenReturn(mockTaskRuntimeService);

            doReturn(taskRuntimeDto).when(taskRuntimeServiceImplUnderTest).selectTaskRuntimeById(1L);

            // 执行测试
            taskRuntimeServiceImplUnderTest.updateScriptTaskRuntime(taskRuntimeId, status);

            // 验证行为
            verify(mockTaskRuntimeService).updateExecTimeAndState(taskRuntimeId, status, taskRuntimeDto);
            verify(taskInstanceService).updateState(
                eq(Enums.TaskInstanceStatus.EXCEPTION.getValue()),
                eq(1L),
                eq(scriptFinishError)
            );
        }
    }

    @Test
    @DisplayName("测试updateScriptTaskRuntime方法-非运行状态不更新")
    void updateScriptTaskRuntime_notRunningState_test() {
        // Setup
        String taskRuntimeId = "1";
        int status = 20;
        int oldStatus = Enums.TaskRuntimeState.COMPLETED.getValue(); // 非运行状态

        TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setId(1L);
        taskRuntimeDto.setTaskInstanceId(1L);
        taskRuntimeDto.setState(oldStatus);
        taskRuntimeDto.setSrcScriptUuid("srcScriptUuid");

        try (MockedStatic<SpringUtil> springUtilMock = mockStatic(SpringUtil.class)) {
            ITaskRuntimeService mockTaskRuntimeService = mock(ITaskRuntimeService.class);
            springUtilMock.when(() -> SpringUtil.getBean(ITaskRuntimeService.class)).thenReturn(mockTaskRuntimeService);

            doReturn(taskRuntimeDto).when(taskRuntimeServiceImplUnderTest).selectTaskRuntimeById(1L);

            // 执行测试
            taskRuntimeServiceImplUnderTest.updateScriptTaskRuntime(taskRuntimeId, status);

            // 验证不会调用更新方法
            verify(mockTaskRuntimeService, times(0)).updateExecTimeAndState(anyString(), eq(status), any());
        }
    }

    @Test
    @DisplayName("测试updateScriptTaskRuntime方法-taskRuntimeDto为null")
    void updateScriptTaskRuntime_nullTaskRuntimeDto_test() {
        // Setup
        String taskRuntimeId = "1";
        int status = 20;

        try (MockedStatic<SpringUtil> springUtilMock = mockStatic(SpringUtil.class)) {
            ITaskRuntimeService mockTaskRuntimeService = mock(ITaskRuntimeService.class);
            springUtilMock.when(() -> SpringUtil.getBean(ITaskRuntimeService.class)).thenReturn(mockTaskRuntimeService);

            doReturn(null).when(taskRuntimeServiceImplUnderTest).selectTaskRuntimeById(1L);

            // 执行测试 - 不应抛出异常
            taskRuntimeServiceImplUnderTest.updateScriptTaskRuntime(taskRuntimeId, status);

            // 验证没有调用其他方法
            verify(mockTaskRuntimeService, times(0)).updateExecTimeAndState(anyString(), eq(status), any());
        }
    }

    @Test
    @DisplayName("测试updateScriptTaskRuntime方法-异常状态且updateState返回0")
    void updateScriptTaskRuntime_exceptionStatusUpdateStateReturns0_test() {
        // Setup
        String taskRuntimeId = "1";
        int status = Enums.TaskRuntimeState.SCRIPT_FAIL_STATE.getValue(); // 30

        TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setId(1L); // 设置ID
        taskRuntimeDto.setTaskInstanceId(1L);
        taskRuntimeDto.setState(Enums.TaskRuntimeState.RUNNING.getValue());
        taskRuntimeDto.setSrcScriptUuid("srcScriptUuid");

        // 根据实际代码逻辑，当状态为异常时，会创建包含异常状态的新数组
        int[] scriptFinishError = {20, 60, 70, 30}; // COMPLETED, TERMINATED, COMPLETED_RED, SCRIPT_FAIL_STATE
        when(taskInstanceService.updateState(
            eq(Enums.TaskInstanceStatus.EXCEPTION.getValue()),
            eq(1L),
            eq(scriptFinishError)
        )).thenReturn(0); // 返回0触发警告日志

        try (MockedStatic<SpringUtil> springUtilMock = mockStatic(SpringUtil.class)) {
            ITaskRuntimeService mockTaskRuntimeService = mock(ITaskRuntimeService.class);
            springUtilMock.when(() -> SpringUtil.getBean(ITaskRuntimeService.class)).thenReturn(mockTaskRuntimeService);

            doReturn(taskRuntimeDto).when(taskRuntimeServiceImplUnderTest).selectTaskRuntimeById(1L);

            // 执行测试
            taskRuntimeServiceImplUnderTest.updateScriptTaskRuntime(taskRuntimeId, status);

            // 验证行为
            verify(mockTaskRuntimeService).updateExecTimeAndState(taskRuntimeId, status, taskRuntimeDto);
            verify(taskInstanceService).updateState(
                eq(Enums.TaskInstanceStatus.EXCEPTION.getValue()),
                eq(1L),
                eq(scriptFinishError)
            );
        }
    }



    @ParameterizedTest
    @ValueSource(strings = {"agent-script-start-123", "agent-script-retry-456", "other-biz-id"})
    @DisplayName("测试handleScriptExecuteResult方法-不同bizId前缀")
    void handleScriptExecuteResult_differentBizIdPrefix_test(String bizId) throws ScriptException {
        // Setup
        String taskRuntimeId = "1";
        int status = 20;
        TaskHandleParam taskHandleParam = new TaskHandleParam();
        taskHandleParam.setIstdout("stdout");
        taskHandleParam.setIstderror("stderr");

        TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setBizId(bizId);
        taskRuntimeDto.setState(Enums.TaskRuntimeState.RUNNING.getValue());
        taskRuntimeDto.setTaskInstanceId(1L);

        TaskInstanceDto taskInstanceDto = new TaskInstanceDto();
        taskInstanceDto.setStartType(2); // TIMETASK

        try (MockedStatic<SpringUtil> springUtilMock = mockStatic(SpringUtil.class);
             MockedStatic<TransactionSyncUtil> transactionSyncUtilMock = mockStatic(TransactionSyncUtil.class)) {

            ITaskRuntimeService mockTaskRuntimeService = mock(ITaskRuntimeService.class);
            when(mockTaskRuntimeService.selectTaskRuntimeById(1L)).thenReturn(taskRuntimeDto);
            springUtilMock.when(() -> SpringUtil.getBean(ITaskRuntimeService.class)).thenReturn(mockTaskRuntimeService);

            when(taskInstanceService.getTaskInstanceByRuntimeId(1L)).thenReturn(taskInstanceDto);

            if (bizId.startsWith(Enums.AgentExecRunFlag.RETRY.getValue())) {
                // Mock Redis counter for retry scenario
                RAtomicLong mockCounter = mock(RAtomicLong.class);
                when(redissonClient.getAtomicLong(anyString())).thenReturn(mockCounter);
                when(mockCounter.isExists()).thenReturn(false); // 计数器不存在
            }

            if (taskInstanceDto.getStartType().toString().equals(Enums.ScriptStartType.TIMETASK.getStartValue())) {
                IAfterRuntimeHandlerService mockAfterRuntimeHandler = mock(IAfterRuntimeHandlerService.class);
                String beanName = Enums.ScriptStartType.getValueByStartValue(taskInstanceDto.getStartType().toString());
                springUtilMock.when(() -> SpringUtil.getBean(beanName))
                    .thenReturn(mockAfterRuntimeHandler);
            }

            // Mock TransactionSyncUtil.execute 方法
            transactionSyncUtilMock.when(() -> TransactionSyncUtil.execute(any(), any(), any(), anyLong()))
                .thenAnswer(invocation -> null);

            // 执行测试
            taskRuntimeServiceImplUnderTest.handleScriptExecuteResult(taskRuntimeId, status, taskHandleParam);

            // 验证行为
            verify(taskRuntimeStdoutService).saveRuntimeStdoutStdError(taskHandleParam, bizId, taskRuntimeDto);
            verify(mockTaskRuntimeService).updateScriptTaskRuntime(taskRuntimeId, status);

            if (bizId.startsWith(Enums.AgentExecRunFlag.START.getValue())) {
                // 启动情况会驱动下一批次，但由于TransactionSyncUtil需要事务环境，这里只验证调用
                // 实际的驱动逻辑在事务提交后异步执行
            }

            if (bizId.startsWith(Enums.AgentExecRunFlag.RETRY.getValue())) {
                verify(taskInstanceService).updateTaskInstanceState(1L);
            }

            if (taskInstanceDto.getStartType().toString().equals(Enums.ScriptStartType.TIMETASK.getStartValue())) {
                verify(taskInstanceService).getTaskInstanceByRuntimeId(1L);
            }
        }
    }

    @Test
    @DisplayName("测试handleScriptExecuteResult方法-bizId为空")
    void handleScriptExecuteResult_emptyBizId_test() {
        // Setup
        String taskRuntimeId = "1";
        int status = 20;
        TaskHandleParam taskHandleParam = new TaskHandleParam();

        TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setBizId(""); // 空bizId

        try (MockedStatic<SpringUtil> springUtilMock = mockStatic(SpringUtil.class)) {
            ITaskRuntimeService mockTaskRuntimeService = mock(ITaskRuntimeService.class);
            when(mockTaskRuntimeService.selectTaskRuntimeById(1L)).thenReturn(taskRuntimeDto);
            springUtilMock.when(() -> SpringUtil.getBean(ITaskRuntimeService.class)).thenReturn(mockTaskRuntimeService);

            // 执行测试 - 应该抛出ScriptException
            assertThrows(ScriptException.class, () -> {
                taskRuntimeServiceImplUnderTest.handleScriptExecuteResult(taskRuntimeId, status, taskHandleParam);
            });
        }
    }

    @Test
    @DisplayName("测试handleScriptExecuteResult方法-bizId为null")
    void handleScriptExecuteResult_nullBizId_test() {
        // Setup
        String taskRuntimeId = "1";
        int status = 20;
        TaskHandleParam taskHandleParam = new TaskHandleParam();

        TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setBizId(null); // null bizId

        try (MockedStatic<SpringUtil> springUtilMock = mockStatic(SpringUtil.class)) {
            ITaskRuntimeService mockTaskRuntimeService = mock(ITaskRuntimeService.class);
            when(mockTaskRuntimeService.selectTaskRuntimeById(1L)).thenReturn(taskRuntimeDto);
            springUtilMock.when(() -> SpringUtil.getBean(ITaskRuntimeService.class)).thenReturn(mockTaskRuntimeService);

            // 执行测试 - 应该抛出ScriptException
            assertThrows(ScriptException.class, () -> {
                taskRuntimeServiceImplUnderTest.handleScriptExecuteResult(taskRuntimeId, status, taskHandleParam);
            });
        }
    }

    @Test
    @DisplayName("测试handleScriptExecuteResult方法-非运行状态")
    void handleScriptExecuteResult_notRunningState_test() throws ScriptException {
        // Setup
        String taskRuntimeId = "1";
        int status = 20;
        TaskHandleParam taskHandleParam = new TaskHandleParam();

        TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setBizId("agent-script-start-123");
        taskRuntimeDto.setState(Enums.TaskRuntimeState.COMPLETED.getValue()); // 非运行状态

        try (MockedStatic<SpringUtil> springUtilMock = mockStatic(SpringUtil.class)) {
            ITaskRuntimeService mockTaskRuntimeService = mock(ITaskRuntimeService.class);
            when(mockTaskRuntimeService.selectTaskRuntimeById(1L)).thenReturn(taskRuntimeDto);
            springUtilMock.when(() -> SpringUtil.getBean(ITaskRuntimeService.class)).thenReturn(mockTaskRuntimeService);

            // 执行测试
            taskRuntimeServiceImplUnderTest.handleScriptExecuteResult(taskRuntimeId, status, taskHandleParam);

            // 验证不会调用更新相关方法
            verify(taskRuntimeStdoutService, times(0)).saveRuntimeStdoutStdError(any(), anyString(), any());
            verify(mockTaskRuntimeService, times(0)).updateScriptTaskRuntime(anyString(), eq(status));
        }
    }

    @Test
    @DisplayName("测试handleScriptExecuteResult方法-定时任务异常状态")
    void handleScriptExecuteResult_timeTaskFailStatus_test() throws ScriptException {
        // Setup
        String taskRuntimeId = "1";
        int status = Enums.TaskRuntimeState.SCRIPT_FAIL_STATE.getValue(); // 异常状态
        TaskHandleParam taskHandleParam = new TaskHandleParam();

        TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setBizId("agent-script-start-123");
        taskRuntimeDto.setState(Enums.TaskRuntimeState.RUNNING.getValue());
        taskRuntimeDto.setTaskInstanceId(1L);

        TaskInstanceDto taskInstanceDto = new TaskInstanceDto();
        taskInstanceDto.setStartType(2); // TIMETASK

        try (MockedStatic<SpringUtil> springUtilMock = mockStatic(SpringUtil.class);
             MockedStatic<TransactionSyncUtil> transactionSyncUtilMock = mockStatic(TransactionSyncUtil.class)) {

            ITaskRuntimeService mockTaskRuntimeService = mock(ITaskRuntimeService.class);
            when(mockTaskRuntimeService.selectTaskRuntimeById(1L)).thenReturn(taskRuntimeDto);
            springUtilMock.when(() -> SpringUtil.getBean(ITaskRuntimeService.class)).thenReturn(mockTaskRuntimeService);

            when(taskInstanceService.getTaskInstanceByRuntimeId(1L)).thenReturn(taskInstanceDto);

            IAfterRuntimeHandlerService mockAfterRuntimeHandler = mock(IAfterRuntimeHandlerService.class);
            String beanName = Enums.ScriptStartType.getValueByStartValue(taskInstanceDto.getStartType().toString());
            springUtilMock.when(() -> SpringUtil.getBean(beanName))
                .thenReturn(mockAfterRuntimeHandler);

            // Mock TransactionSyncUtil.execute 方法
            transactionSyncUtilMock.when(() -> TransactionSyncUtil.execute(any(), any(), any(), anyLong()))
                .thenAnswer(invocation -> null);

            // 执行测试
            taskRuntimeServiceImplUnderTest.handleScriptExecuteResult(taskRuntimeId, status, taskHandleParam);

            // 验证行为
            verify(taskRuntimeStdoutService).saveRuntimeStdoutStdError(taskHandleParam, taskRuntimeDto.getBizId(), taskRuntimeDto);
            verify(mockTaskRuntimeService).updateScriptTaskRuntime(taskRuntimeId, status);
            verify(taskInstanceService).getTaskInstanceByRuntimeId(1L);
            verify(mockAfterRuntimeHandler).monitorMqToSend(
                eq(Constants.CRONTABS_ERROR_RESULT_DEV),
                eq(taskHandleParam),
                eq(taskRuntimeDto)
            );
        }
    }



    @Test
    @DisplayName("测试handleScriptExecuteResult方法-重试场景计数器存在")
    void handleScriptExecuteResult_retryScenarioCounterExists_test() throws ScriptException {
        // Setup
        String taskRuntimeId = "1";
        int status = 20;
        TaskHandleParam taskHandleParam = new TaskHandleParam();

        TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setBizId("agent-script-retry-456");
        taskRuntimeDto.setState(Enums.TaskRuntimeState.RUNNING.getValue());
        taskRuntimeDto.setTaskInstanceId(1L);

        TaskInstanceDto taskInstanceDto = new TaskInstanceDto();
        taskInstanceDto.setStartType(0); // 非定时任务

        RAtomicLong mockCounter = mock(RAtomicLong.class);
        when(redissonClient.getAtomicLong(anyString())).thenReturn(mockCounter);
        when(mockCounter.isExists()).thenReturn(true); // 计数器存在

        try (MockedStatic<SpringUtil> springUtilMock = mockStatic(SpringUtil.class);
             MockedStatic<TransactionSyncUtil> transactionSyncUtilMock = mockStatic(TransactionSyncUtil.class)) {

            ITaskRuntimeService mockTaskRuntimeService = mock(ITaskRuntimeService.class);
            when(mockTaskRuntimeService.selectTaskRuntimeById(1L)).thenReturn(taskRuntimeDto);
            springUtilMock.when(() -> SpringUtil.getBean(ITaskRuntimeService.class)).thenReturn(mockTaskRuntimeService);

            when(taskInstanceService.getTaskInstanceByRuntimeId(1L)).thenReturn(taskInstanceDto);

            // Mock TransactionSyncUtil.execute 方法
            transactionSyncUtilMock.when(() -> TransactionSyncUtil.execute(any(), any(), any(), anyLong()))
                .thenAnswer(invocation -> null);

            // 执行测试
            taskRuntimeServiceImplUnderTest.handleScriptExecuteResult(taskRuntimeId, status, taskHandleParam);

            // 验证行为
            verify(taskRuntimeStdoutService).saveRuntimeStdoutStdError(taskHandleParam, taskRuntimeDto.getBizId(), taskRuntimeDto);
            verify(mockTaskRuntimeService).updateScriptTaskRuntime(taskRuntimeId, status);
            verify(mockCounter).isExists();
            // 计数器存在时不应该调用updateTaskInstanceState
            verify(taskInstanceService, times(0)).updateTaskInstanceState(1L);
        }
    }
}
