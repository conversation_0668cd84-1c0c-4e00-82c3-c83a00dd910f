package com.ideal.script.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ideal.sc.util.ZipUtil;
import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.config.CiticProperties;
import com.ideal.script.config.ScriptBusinessConfig;
import com.ideal.script.dto.ItsmPublishScriptAuditResultDto;
import com.ideal.script.dto.ItsmPublishScriptDto;
import com.ideal.script.dto.ScriptInfoDto;
import com.ideal.script.dto.ScriptInfoQueryDto;
import com.ideal.script.dto.ScriptVersionDto;
import com.ideal.script.model.bean.ReleaseMediaBean;
import com.ideal.script.model.dto.*;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.*;
import com.ideal.script.model.bean.MyScriptBean;
import com.ideal.script.model.entity.*;
import com.ideal.script.service.IMyScriptService;
import com.ideal.script.service.IReleaseMediaService;
import com.ideal.script.service.impl.builders.MyScriptServiceScripts;
import com.ideal.script.service.impl.builders.MyScriptServiceScriptsBuilder;
import com.ideal.system.api.IPublicInfo;
import com.ideal.system.common.component.model.CurrentUser;
import com.ideal.system.dto.PublicInfoPropertiesApiDto;
import com.ideal.sc.util.CurrentUserUtil;
import com.ideal.sc.util.HttpClientUtil;
import com.ideal.common.util.spring.SpringUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.client.RestTemplate;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.io.File;
import java.io.ByteArrayInputStream;
import java.io.Reader;
import java.io.FilenameFilter;
import java.io.FileFilter;
import java.nio.charset.StandardCharsets;
import java.util.Comparator;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.ideal.script.dto.ScriptContentDto;
import com.ideal.script.dto.AttachmentUploadDto;
import com.ideal.script.dto.ParameterValidationDto;
import com.ideal.common.util.BeanUtils;

@ExtendWith(MockitoExtension.class)
class ToProductServiceImplTest {

    @Mock
    private ToProductMapper mockToProductMapper;

    @Spy
    @InjectMocks
    private ToProductServiceImpl toProductServiceImplUnderTest;

    @Mock
    private ToProductServiceImpl toProductService;

    @Mock
    private ItsmPublishInfoMapper itsmPublishInfoMapper;
    @Mock
    private IReleaseMediaService releaseMediaService;
    @Mock
    private MyScriptServiceScripts scripts;
    @Mock
    private ObjectMapper objectMapper;
    @Mock
    private IPublicInfo publicInfo;
    @Mock
    private ItsmProductAttachmentMapper itsmProductAttachmentMapper;
    @Mock
    private IMyScriptService myScriptService;
    @Mock
    private ItsmProductInfoMapper itsmProductInfoMapper;
    @Mock
    private CiticProperties citicProperties;
    @Mock
    private MyScriptMapper myScriptMapper;
    @Mock
    private MyScriptServiceScriptsBuilder mockBuilder;
    @Mock
    private HttpURLConnection mockConnection;


    @BeforeEach
    void setUp() throws NoSuchFieldException, IllegalAccessException, ScriptException {
        Field myScriptMapperField = MyScriptServiceScriptsBuilder.class.getDeclaredField("myScriptMapper");
        myScriptMapperField.setAccessible(true);
        myScriptMapperField.set(mockBuilder, myScriptMapper);

        Field myScriptMapperField1 = MyScriptServiceScripts.class.getDeclaredField("myScriptMapper");
        myScriptMapperField1.setAccessible(true);
        myScriptMapperField1.set(scripts, myScriptMapper);

        // 全局mock设置，避免NPE
        lenient().when(itsmPublishInfoMapper.getDetailByOrderNumber(anyString())).thenReturn(null);
        lenient().when(mockToProductMapper.updateToProductByOrderNumber(any())).thenReturn(1);
        lenient().when(itsmProductInfoMapper.deleteByPublishInfoId(any())).thenReturn(1);
        lenient().when(itsmProductAttachmentMapper.deleteByPublishInfoId(any())).thenReturn(1);
        lenient().when(itsmPublishInfoMapper.deleteByIid(any())).thenReturn(1);
        lenient().doNothing().when(releaseMediaService).handlerImportMedia(any(File.class), any(ToProductQueryDto.class));
        lenient().when(releaseMediaService.getProductFilePath()).thenReturn("/test/path");
        lenient().when(publicInfo.getPublicInfo()).thenReturn(Mockito.mock(PublicInfoPropertiesApiDto.class));
        lenient().when(scripts.getScriptBusinessConfig()).thenReturn(Mockito.mock(ScriptBusinessConfig.class));
        lenient().when(citicProperties.getItsmDownloadProductFileUrl()).thenReturn("http://test-url");
        lenient().when(citicProperties.getItsmRepoKey()).thenReturn("test-repo-key");
    }

    @Test
    void testSelectToProductById() {
        // Setup
        // Configure ToProductMapper.selectToProductById(...).
        final ToProductEntity toProductEntity = new ToProductEntity();
        toProductEntity.setUserName("userName");
        toProductEntity.setId(0L);
        toProductEntity.setFileName("fileName");
        toProductEntity.setDescription("description");
        toProductEntity.setDateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockToProductMapper.selectToProductById(0L)).thenReturn(toProductEntity);

        // Run the test
        final ToProductDto result = toProductServiceImplUnderTest.selectToProductById(0L);
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testSelectToProductList() {
        // Setup
        final ToProductQueryDto toProductQueryDto = new ToProductQueryDto();
        toProductQueryDto.setSrcScriptUuidList(Collections.singletonList("value"));
        toProductQueryDto.setUserName("userName");
        toProductQueryDto.setId(0L);
        toProductQueryDto.setFileName("fileName");
        toProductQueryDto.setDescription("description");

        // Configure ToProductMapper.selectToProductList(...).
        final ToProductEntity toProductEntity = new ToProductEntity();
        toProductEntity.setUserName("userName");
        toProductEntity.setId(0L);
        toProductEntity.setFileName("fileName");
        toProductEntity.setDescription("description");
        toProductEntity.setDateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        Page<ToProductEntity> page =new Page<>();
        page.add(toProductEntity);
        when(mockToProductMapper.selectToProductList(any(ToProductEntity.class))).thenReturn(page);

        // Run the test
        final PageInfo<ToProductDto> result = toProductServiceImplUnderTest.selectToProductList(toProductQueryDto, 0,
                0);
        assertNotNull(result);
        // Verify the results
    }


    @Test
    void testInsertToProduct() {
        // Setup
        final ToProductDto toProductDto = new ToProductDto();
        toProductDto.setId(0L);
        toProductDto.setUserName("userName");
        toProductDto.setFileName("fileName");
        toProductDto.setDescription("description");
        toProductDto.setDateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));

        when(mockToProductMapper.insertToProduct(any(ToProductEntity.class))).thenReturn(0);

        // Run the test
        final int result = toProductServiceImplUnderTest.insertToProduct(toProductDto);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testUpdateToProduct() {
        // Setup
        final ToProductDto toProductDto = new ToProductDto();
        toProductDto.setId(0L);
        toProductDto.setUserName("userName");
        toProductDto.setFileName("fileName");
        toProductDto.setDescription("description");
        toProductDto.setDateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));

        when(mockToProductMapper.updateToProduct(any(ToProductEntity.class))).thenReturn(0);

        // Run the test
        final int result = toProductServiceImplUnderTest.updateToProduct(toProductDto);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testDeleteToProductByIds() {
        // Setup
        when(mockToProductMapper.deleteToProductByIds(any(Long[].class))).thenReturn(0);

        // Run the test
        final int result = toProductServiceImplUnderTest.deleteToProductByIds(new Long[]{0L});

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    void testPublishItsm() throws ScriptException {
        ItsmPublishScriptDto itsmPublishScriptDto = new ItsmPublishScriptDto();
        itsmPublishScriptDto.setUniqueUuid(Collections.singletonList("test-uuid"));
        itsmPublishScriptDto.setProductionDescription("测试投产描述");

        CurrentUser currentUser = new CurrentUser();
        currentUser.setId(1L);
        currentUser.setLoginName("test");
        currentUser.setFullName("测试用户");
        currentUser.setOrgId(1L);
        currentUser.setOrgCode("orgCode1#");
        currentUser.setSupervisor(false);

        // Mock CurrentUserUtil.getCurrentUser()
        try (MockedStatic<CurrentUserUtil> currentUserUtilMock = Mockito.mockStatic(CurrentUserUtil.class);
             MockedStatic<Files> filesMock = Mockito.mockStatic(Files.class)) {

            currentUserUtilMock.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);

            // Mock scripts.getMyScriptMapper()
            when(scripts.getMyScriptMapper()).thenReturn(myScriptMapper);

            // Mock getScriptInfoByUniqueUuid
            List<MyScriptBean> myScriptBeanList = new ArrayList<>();
            MyScriptBean myScriptBean = new MyScriptBean();
            myScriptBean.setScriptInfoId(1L);
            myScriptBeanList.add(myScriptBean);
            when(myScriptMapper.getScriptInfoByUniqueUuid(any())).thenReturn(myScriptBeanList);

            // Mock getScriptDetail
            ScriptInfoDto scriptDetail = new ScriptInfoDto();
            ScriptVersionDto scriptVersionDto = new ScriptVersionDto();
            scriptVersionDto.setSrcScriptUuid("test-src-uuid");
            scriptVersionDto.setId(1L);
            scriptDetail.setScriptVersionDto(scriptVersionDto);
            scriptDetail.setId(1L);
            scriptDetail.setScriptName("测试脚本");
            when(myScriptService.getScriptDetail(any())).thenReturn(scriptDetail);

            // Mock getAuditingDataBySrcScriptUuid - 表示没有审核数据
            List<ItsmPublishScript> itsmPublishList = new ArrayList<>();
            when(itsmPublishInfoMapper.getAuditingDataBySrcScriptUuid(any())).thenReturn(itsmPublishList);

            // Mock validateScriptWithKeywords - 表示没有关键命令
            List<ScriptValidationResultDto> validationResults = new ArrayList<>();
            ScriptValidationResultDto scriptValidationResultDto = new ScriptValidationResultDto();
            scriptValidationResultDto.setLine(1);
            scriptValidationResultDto.setData("testdata");
            scriptValidationResultDto.setType(1);
            scriptValidationResultDto.setTargetContent(Arrays.asList("test"));
            validationResults.add(scriptValidationResultDto);
            when(myScriptService.validateScriptWithKeywords(any())).thenReturn(validationResults);

            // Mock getZipFile
            Map<String, Object> zipFileMap = new HashMap<>();
            File mockZipFile = Mockito.mock(File.class);
            Path mockPath = Mockito.mock(Path.class);
            Path mockTargetPath = Mockito.mock(Path.class);

            when(mockZipFile.toPath()).thenReturn(mockPath);
//            when(mockZipFile.getName()).thenReturn("test.zip");
//            when(mockZipFile.getPath()).thenReturn("test/test.zip");
            when(mockPath.resolveSibling(anyString())).thenReturn(mockTargetPath);

            // Mock Files.move() 执行实际操作
            filesMock.when(() -> Files.move(mockPath, mockTargetPath))
                    .thenAnswer(invocation -> null);

            zipFileMap.put(Enums.ProductionFile.FILENAME.getValue(), mockZipFile);
            zipFileMap.put(Enums.ProductionFile.ZIPNAME.getValue(), "test.zip");
            when(releaseMediaService.scriptZipFile(any())).thenReturn(zipFileMap);

            // Mock citicProperties
//            when(citicProperties.getItsmApiKey()).thenReturn("test-api-key");
//            when(citicProperties.getItsmUploadProductFileUrl()).thenReturn("http://test-url");
//            when(citicProperties.getItsmRepoKey()).thenReturn("test-repo-key");
//            when(citicProperties.getItsmRepoKey()).thenReturn(null);

            // Mock toProductServiceImplUnderTest的方法，使用doReturn因为它是@Spy对象
            doReturn("path_test").when(toProductServiceImplUnderTest).uploadZipFileToProductLibrary(any(), any());
            doReturn("orderNumber_test").when(toProductServiceImplUnderTest).getOrderNumber(any(), any(), any(), any());

            when(mockToProductMapper.updateToProduct(any())).thenReturn(1);

            assertThrows(
                    Exception.class,
                    () -> toProductServiceImplUnderTest.publishItsm(itsmPublishScriptDto)
            );
//            assertEquals("script.itsm.properties.error", exception.getMessage());
        }
    }


    @Test
    void uploadZipFileToProductLibrary_ShouldReturnEmptyWhenPropertiesInvalid() throws Exception {
        // 模拟参数缺失
        when(citicProperties.getItsmApiKey()).thenReturn("");
        when(citicProperties.getItsmUploadProductFileUrl()).thenReturn(null);
        when(citicProperties.getItsmRepoKey()).thenReturn("  "); // 空字符串

        File mockFile = mock(File.class);
        String result = toProductServiceImplUnderTest.uploadZipFileToProductLibrary(mockFile, "test.zip");

        assertEquals("", result);
    }

    @Test
    void uploadZipFileToProductLibrary_ShouldThrowWhenUploadFails() throws Exception {
        when(citicProperties.getItsmApiKey()).thenReturn("test-api-key");
        when(citicProperties.getItsmUploadProductFileUrl()).thenReturn("http://test-url");
        when(citicProperties.getItsmRepoKey()).thenReturn("test-repo");

        File mockFile = mock(File.class);
        when(mockFile.getName()).thenReturn("test.zip");
        when(mockFile.getPath()).thenReturn("/tmp/test.zip");

        try (MockedStatic<Files> mockedFiles = mockStatic(Files.class);
             MockedStatic<Paths> mockedPaths = mockStatic(Paths.class)) {

            mockedFiles.when(() -> Files.readAllBytes(any(Path.class)))
                    .thenThrow(new IOException("File read error"));

            ScriptException exception = assertThrows(ScriptException.class,
                    () -> toProductServiceImplUnderTest.uploadZipFileToProductLibrary(mockFile, "test.zip"));

            assertEquals("upload.script.zip.file.product.library.error", exception.getMessage());
            assertTrue(exception.getCause() instanceof IOException);
        }
    }

    @Test
    void uploadZipFileToProductLibrary_ShouldSuccess() throws Exception {
        when(citicProperties.getItsmApiKey()).thenReturn("valid-key");
        when(citicProperties.getItsmUploadProductFileUrl()).thenReturn("http://valid-url");
        when(citicProperties.getItsmRepoKey()).thenReturn("valid-repo");

        File mockFile = mock(File.class);
        when(mockFile.getName()).thenReturn("success.zip");
        when(mockFile.getPath()).thenReturn("/path/to/success.zip");

        HttpURLConnection mockConnection = mock(HttpURLConnection.class);
        Path mockPath = mock(Path.class);

        try (MockedStatic<Files> mockedFiles = mockStatic(Files.class);
             MockedStatic<Paths> mockedPaths = mockStatic(Paths.class)) {

            mockedPaths.when(() -> Paths.get("/path/to/success.zip"))
                    .thenReturn(mockPath);

            mockedFiles.when(() -> Files.readAllBytes(mockPath))
                    .thenReturn(new byte[]{1, 2, 3});

            String expectedUrl = "http://valid-url/valid-repo/"
                    + LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE)
                    + "/success.zip";

            URL mockUrl = new URL(expectedUrl);
            try (MockedConstruction<URL> mockedUrl = mockConstruction(URL.class,
                    (mock, context) -> {
                        when(mock.openConnection()).thenReturn(mockConnection);
                    })) {

                when(mockConnection.getResponseCode()).thenReturn(HttpURLConnection.HTTP_CREATED);

                ByteArrayOutputStream mockOutputStream = new ByteArrayOutputStream();

                when(mockConnection.getOutputStream()).thenReturn(mockOutputStream);
                toProductServiceImplUnderTest.uploadZipFileToProductLibrary(mockFile, "success.zip");
            }
        }
    }

    @Test
    @DisplayName("测试testEnvironmentAuditResult方法 - 正常情况")
    void testTestEnvironmentAuditResult_Success() {
        // 准备测试数据
        ItsmPublishScriptAuditResultDto itsmPublishScriptAuditResultDto = new ItsmPublishScriptAuditResultDto();
        itsmPublishScriptAuditResultDto.setOrderNumber("ORDER123456");
        itsmPublishScriptAuditResultDto.setAuditState(1); // 1-完成
        itsmPublishScriptAuditResultDto.setAuditResultRemark("审核通过");
        itsmPublishScriptAuditResultDto.setSrcScriptUuid("uuid-123-456");

        // Mock ToProductMapper.updateToProductByOrderNumber方法
        when(mockToProductMapper.updateToProductByOrderNumber(any(ToProductEntity.class))).thenReturn(1);

        // 执行测试
        toProductServiceImplUnderTest.testEnvironmentAuditResult(itsmPublishScriptAuditResultDto);

        // 验证调用
        verify(mockToProductMapper).updateToProductByOrderNumber(argThat(toProductEntity -> {
            return "ORDER123456".equals(toProductEntity.getOrderNumber()) &&
                   Integer.valueOf(1).equals(toProductEntity.getProductState());
        }));
    }

    @Test
    @DisplayName("测试testEnvironmentAuditResult方法 - 审核中状态")
    void testTestEnvironmentAuditResult_AuditingState() {
        // 准备测试数据
        ItsmPublishScriptAuditResultDto itsmPublishScriptAuditResultDto = new ItsmPublishScriptAuditResultDto();
        itsmPublishScriptAuditResultDto.setOrderNumber("ORDER789012");
        itsmPublishScriptAuditResultDto.setAuditState(2); // 2-审核中
        itsmPublishScriptAuditResultDto.setAuditResultRemark("正在审核中");

        // Mock ToProductMapper.updateToProductByOrderNumber方法
        when(mockToProductMapper.updateToProductByOrderNumber(any(ToProductEntity.class))).thenReturn(1);

        // 执行测试
        toProductServiceImplUnderTest.testEnvironmentAuditResult(itsmPublishScriptAuditResultDto);

        // 验证调用
        verify(mockToProductMapper).updateToProductByOrderNumber(argThat(toProductEntity -> {
            return "ORDER789012".equals(toProductEntity.getOrderNumber()) &&
                   Integer.valueOf(2).equals(toProductEntity.getProductState());
        }));
    }

    @Test
    @DisplayName("测试testEnvironmentAuditResult方法 - 作废状态")
    void testTestEnvironmentAuditResult_RejectedState() {
        // 准备测试数据
        ItsmPublishScriptAuditResultDto itsmPublishScriptAuditResultDto = new ItsmPublishScriptAuditResultDto();
        itsmPublishScriptAuditResultDto.setOrderNumber("ORDER345678");
        itsmPublishScriptAuditResultDto.setAuditState(3); // 3-作废
        itsmPublishScriptAuditResultDto.setAuditResultRemark("审核不通过，已作废");

        // Mock ToProductMapper.updateToProductByOrderNumber方法
        when(mockToProductMapper.updateToProductByOrderNumber(any(ToProductEntity.class))).thenReturn(1);

        // 执行测试
        toProductServiceImplUnderTest.testEnvironmentAuditResult(itsmPublishScriptAuditResultDto);

        // 验证调用
        verify(mockToProductMapper).updateToProductByOrderNumber(argThat(toProductEntity -> {
            return "ORDER345678".equals(toProductEntity.getOrderNumber()) &&
                   Integer.valueOf(3).equals(toProductEntity.getProductState());
        }));
    }

    @Test
    @DisplayName("测试testEnvironmentAuditResult方法 - 空工单号")
    void testTestEnvironmentAuditResult_EmptyOrderNumber() {
        // 准备测试数据
        ItsmPublishScriptAuditResultDto itsmPublishScriptAuditResultDto = new ItsmPublishScriptAuditResultDto();
        itsmPublishScriptAuditResultDto.setOrderNumber(""); // 空工单号
        itsmPublishScriptAuditResultDto.setAuditState(1);

        // Mock ToProductMapper.updateToProductByOrderNumber方法
        when(mockToProductMapper.updateToProductByOrderNumber(any(ToProductEntity.class))).thenReturn(1);

        // 执行测试
        toProductServiceImplUnderTest.testEnvironmentAuditResult(itsmPublishScriptAuditResultDto);

        // 验证调用
        verify(mockToProductMapper).updateToProductByOrderNumber(argThat(toProductEntity -> {
            return "".equals(toProductEntity.getOrderNumber()) &&
                   Integer.valueOf(1).equals(toProductEntity.getProductState());
        }));
    }

    @Test
    @DisplayName("测试testEnvironmentAuditResult方法 - null工单号")
    void testTestEnvironmentAuditResult_NullOrderNumber() {
        // 准备测试数据
        ItsmPublishScriptAuditResultDto itsmPublishScriptAuditResultDto = new ItsmPublishScriptAuditResultDto();
        itsmPublishScriptAuditResultDto.setOrderNumber(null); // null工单号
        itsmPublishScriptAuditResultDto.setAuditState(1);

        // Mock ToProductMapper.updateToProductByOrderNumber方法
        when(mockToProductMapper.updateToProductByOrderNumber(any(ToProductEntity.class))).thenReturn(1);

        // 执行测试
        toProductServiceImplUnderTest.testEnvironmentAuditResult(itsmPublishScriptAuditResultDto);

        // 验证调用
        verify(mockToProductMapper).updateToProductByOrderNumber(argThat(toProductEntity -> {
            return toProductEntity.getOrderNumber() == null &&
                   Integer.valueOf(1).equals(toProductEntity.getProductState());
        }));
    }

    @Test
    @DisplayName("测试testEnvironmentAuditResult方法 - null审核状态")
    void testTestEnvironmentAuditResult_NullAuditState() {
        // 准备测试数据
        ItsmPublishScriptAuditResultDto itsmPublishScriptAuditResultDto = new ItsmPublishScriptAuditResultDto();
        itsmPublishScriptAuditResultDto.setOrderNumber("ORDER999999");
        itsmPublishScriptAuditResultDto.setAuditState(null); // null审核状态

        // Mock ToProductMapper.updateToProductByOrderNumber方法
        when(mockToProductMapper.updateToProductByOrderNumber(any(ToProductEntity.class))).thenReturn(1);

        // 执行测试
        toProductServiceImplUnderTest.testEnvironmentAuditResult(itsmPublishScriptAuditResultDto);

        // 验证调用
        verify(mockToProductMapper).updateToProductByOrderNumber(argThat(toProductEntity -> {
            return "ORDER999999".equals(toProductEntity.getOrderNumber()) &&
                   toProductEntity.getProductState() == null;
        }));
    }

    @Test
    @DisplayName("测试testEnvironmentAuditResult方法 - 数据库更新失败")
    void testTestEnvironmentAuditResult_DatabaseUpdateFailed() {
        // 准备测试数据
        ItsmPublishScriptAuditResultDto itsmPublishScriptAuditResultDto = new ItsmPublishScriptAuditResultDto();
        itsmPublishScriptAuditResultDto.setOrderNumber("ORDER111111");
        itsmPublishScriptAuditResultDto.setAuditState(1);

        // Mock ToProductMapper.updateToProductByOrderNumber方法返回0（更新失败）
        when(mockToProductMapper.updateToProductByOrderNumber(any(ToProductEntity.class))).thenReturn(0);

        // 执行测试 - 方法应该正常执行，不会抛出异常
        toProductServiceImplUnderTest.testEnvironmentAuditResult(itsmPublishScriptAuditResultDto);

        // 验证调用
        verify(mockToProductMapper).updateToProductByOrderNumber(argThat(toProductEntity -> {
            return "ORDER111111".equals(toProductEntity.getOrderNumber()) &&
                   Integer.valueOf(1).equals(toProductEntity.getProductState());
        }));
    }

    @Test
    @DisplayName("测试testEnvironmentAuditResult方法 - 数据库更新异常")
    void testTestEnvironmentAuditResult_DatabaseException() {
        // 准备测试数据
        ItsmPublishScriptAuditResultDto itsmPublishScriptAuditResultDto = new ItsmPublishScriptAuditResultDto();
        itsmPublishScriptAuditResultDto.setOrderNumber("ORDER222222");
        itsmPublishScriptAuditResultDto.setAuditState(1);

        // Mock ToProductMapper.updateToProductByOrderNumber方法抛出异常
        when(mockToProductMapper.updateToProductByOrderNumber(any(ToProductEntity.class)))
            .thenThrow(new RuntimeException("数据库连接异常"));

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            toProductServiceImplUnderTest.testEnvironmentAuditResult(itsmPublishScriptAuditResultDto);
        });

        // 验证调用
        verify(mockToProductMapper).updateToProductByOrderNumber(argThat(toProductEntity -> {
            return "ORDER222222".equals(toProductEntity.getOrderNumber()) &&
                   Integer.valueOf(1).equals(toProductEntity.getProductState());
        }));
    }

    @Test
    @DisplayName("测试getOrderNumber方法 - 正常情况")
    void testGetOrderNumber_Success() {
        // 准备测试数据
        CurrentUser currentUser = new CurrentUser();
        currentUser.setId(1L);
        currentUser.setLoginName("testUser");
        currentUser.setFullName("测试用户");

        String allDangerCmd = "脚本testScript存在关键命令，第10行存在关键命令：rm -rf /;";

        ToProductQueryDto toProductQueryDto = new ToProductQueryDto();
        toProductQueryDto.setId(123L);
        toProductQueryDto.setDescription("测试投产描述");

        String itsmFilePath = "/path/to/itsm/file.zip";

        // Mock CiticProperties
        when(citicProperties.getItsmGetOrderNumberUrl()).thenReturn("http://test.itsm.com/api/getOrderNumber");

        // Mock HttpClientUtil.postByJson静态方法
        RestTemplate mockRestTemplate = mock(RestTemplate.class);
        try (MockedStatic<SpringUtil> springUtilMock = Mockito.mockStatic(SpringUtil.class)) {
            springUtilMock.when(() -> SpringUtil.getBean(RestTemplate.class)).thenReturn(mockRestTemplate);
            try (MockedStatic<HttpClientUtil> httpClientUtilMock = mockStatic(HttpClientUtil.class)) {
                // 创建模拟的返回对象
                ItsmPublishOrderNumberDto mockResponse = new ItsmPublishOrderNumberDto();
                mockResponse.setFlowNo("ORDER123456");
                mockResponse.setId("1");
                mockResponse.setTitle("测试工单");

                // Mock静态方法调用
                httpClientUtilMock.when(() -> HttpClientUtil.postByJson(
                    eq("http://test.itsm.com/api/getOrderNumber"),
                    any(Map.class),
                    any(String.class),
                    eq(ItsmPublishOrderNumberDto.class)
                )).thenReturn(mockResponse);

                // 执行测试
                String result = toProductServiceImplUnderTest.getOrderNumber(currentUser, allDangerCmd, toProductQueryDto, itsmFilePath);

                // 验证结果
                assertEquals("ORDER123456", result);

                // 验证HttpClientUtil.postByJson被正确调用
                httpClientUtilMock.verify(() -> HttpClientUtil.postByJson(
                    eq("http://test.itsm.com/api/getOrderNumber"),
                    any(Map.class),
                    any(String.class),
                    eq(ItsmPublishOrderNumberDto.class)
                ));
            }
        }
    }

    @Test
    @DisplayName("测试getOrderNumber方法 - 返回null")
    void testGetOrderNumber_ReturnNull() {
        // 准备测试数据
        CurrentUser currentUser = new CurrentUser();
        currentUser.setId(1L);
        currentUser.setLoginName("testUser");

        String allDangerCmd = "脚本testScript存在关键命令";
        ToProductQueryDto toProductQueryDto = new ToProductQueryDto();
        toProductQueryDto.setId(123L);
        toProductQueryDto.setDescription("测试投产描述");
        String itsmFilePath = "/path/to/itsm/file.zip";

        // Mock CiticProperties
        when(citicProperties.getItsmGetOrderNumberUrl()).thenReturn("http://test.itsm.com/api/getOrderNumber");

        // Mock HttpClientUtil.postByJson静态方法返回null
        RestTemplate mockRestTemplate = mock(RestTemplate.class);
        try (MockedStatic<SpringUtil> springUtilMock = Mockito.mockStatic(SpringUtil.class)) {
            springUtilMock.when(() -> SpringUtil.getBean(RestTemplate.class)).thenReturn(mockRestTemplate);
            try (MockedStatic<HttpClientUtil> httpClientUtilMock = mockStatic(HttpClientUtil.class)) {
                httpClientUtilMock.when(() -> HttpClientUtil.postByJson(
                    eq("http://test.itsm.com/api/getOrderNumber"),
                    any(Map.class),
                    any(String.class),
                    eq(ItsmPublishOrderNumberDto.class)
                )).thenReturn(null);

                // 执行测试
                String result = toProductServiceImplUnderTest.getOrderNumber(currentUser, allDangerCmd, toProductQueryDto, itsmFilePath);

                // 验证结果
                assertEquals("", result);
            }
        }
    }

    @Test
    @DisplayName("测试getOrderNumber方法 - 异常情况")
    void testGetOrderNumber_Exception() {
        // 准备测试数据
        CurrentUser currentUser = new CurrentUser();
        currentUser.setId(1L);
        currentUser.setLoginName("testUser");

        String allDangerCmd = "脚本testScript存在关键命令";
        ToProductQueryDto toProductQueryDto = new ToProductQueryDto();
        toProductQueryDto.setId(123L);
        toProductQueryDto.setDescription("测试投产描述");
        String itsmFilePath = "/path/to/itsm/file.zip";

        // Mock CiticProperties
        when(citicProperties.getItsmGetOrderNumberUrl()).thenReturn("http://test.itsm.com/api/getOrderNumber");

        // Mock HttpClientUtil.postByJson静态方法抛出异常
        RestTemplate mockRestTemplate = mock(RestTemplate.class);
        try (MockedStatic<SpringUtil> springUtilMock = Mockito.mockStatic(SpringUtil.class)) {
            springUtilMock.when(() -> SpringUtil.getBean(RestTemplate.class)).thenReturn(mockRestTemplate);
            try (MockedStatic<HttpClientUtil> httpClientUtilMock = mockStatic(HttpClientUtil.class)) {
                httpClientUtilMock.when(() -> HttpClientUtil.postByJson(
                    eq("http://test.itsm.com/api/getOrderNumber"),
                    any(Map.class),
                    any(String.class),
                    eq(ItsmPublishOrderNumberDto.class)
                )).thenThrow(new RuntimeException("网络异常"));

                // 执行测试并验证异常
                assertThrows(RuntimeException.class, () -> {
                    toProductServiceImplUnderTest.getOrderNumber(currentUser, allDangerCmd, toProductQueryDto, itsmFilePath);
                });
            }
        }
    }

    @Test
    @DisplayName("测试getOrderNumber方法 - 验证请求参数构建")
    void testGetOrderNumber_VerifyRequestParameters() {
        // 准备测试数据
        CurrentUser currentUser = new CurrentUser();
        currentUser.setId(1L);
        currentUser.setLoginName("testUser");

        String allDangerCmd = "脚本testScript存在关键命令";
        ToProductQueryDto toProductQueryDto = new ToProductQueryDto();
        toProductQueryDto.setId(123L);
        toProductQueryDto.setDescription("测试投产描述");
        String itsmFilePath = "/path/to/itsm/file.zip";

        // Mock CiticProperties
        when(citicProperties.getItsmGetOrderNumberUrl()).thenReturn("http://test.itsm.com/api/getOrderNumber");

        // Mock HttpClientUtil.postByJson静态方法
        RestTemplate mockRestTemplate = mock(RestTemplate.class);
        try (MockedStatic<SpringUtil> springUtilMock = Mockito.mockStatic(SpringUtil.class)) {
            springUtilMock.when(() -> SpringUtil.getBean(RestTemplate.class)).thenReturn(mockRestTemplate);
            try (MockedStatic<HttpClientUtil> httpClientUtilMock = mockStatic(HttpClientUtil.class)) {
                ItsmPublishOrderNumberDto mockResponse = new ItsmPublishOrderNumberDto();
                mockResponse.setFlowNo("ORDER123456");

                httpClientUtilMock.when(() -> HttpClientUtil.postByJson(
                    any(String.class),
                    any(Map.class),
                    any(String.class),
                    eq(ItsmPublishOrderNumberDto.class)
                )).thenReturn(mockResponse);

                // 执行测试
                String result = toProductServiceImplUnderTest.getOrderNumber(currentUser, allDangerCmd, toProductQueryDto, itsmFilePath);

                // 验证结果
                assertEquals("ORDER123456", result);

                // 验证请求参数构建
                httpClientUtilMock.verify(() -> HttpClientUtil.postByJson(
                    eq("http://test.itsm.com/api/getOrderNumber"),
                    argThat(headers -> {
                        // 验证请求头
                        return headers.containsKey("Content-Type") &&
                               headers.get("Content-Type").equals("application/json") &&
                               headers.containsKey("Content-Length");
                    }),
                    argThat(paramsStr -> {
                        // 验证请求体包含必要的参数
                        String paramsString = (String) paramsStr;
                        return paramsString.contains("testUser") && // createUser
                               paramsString.contains("a") && // modelCode, routeCode, activityCode
                               paramsString.contains("脚本testScript存在关键命令") && // dangerCmdRemark
                               paramsString.contains("123") && // productId
                               paramsString.contains("测试投产描述") && // productionDescription
                               paramsString.contains("/path/to/itsm/file.zip"); // itsmFilePath
                    }),
                    eq(ItsmPublishOrderNumberDto.class)
                ));
            }
        }
    }

    @Test
    @DisplayName("测试getOrderNumber方法 - 边界条件测试")
    void testGetOrderNumber_BoundaryConditions() {
        // 准备测试数据
        CurrentUser currentUser = new CurrentUser();
        currentUser.setId(1L);
        currentUser.setLoginName("testUser");

        // 测试空字符串参数
        String allDangerCmd = "";
        ToProductQueryDto toProductQueryDto = new ToProductQueryDto();
        toProductQueryDto.setId(123L);
        toProductQueryDto.setDescription("");
        String itsmFilePath = "";

        // Mock CiticProperties
        when(citicProperties.getItsmGetOrderNumberUrl()).thenReturn("http://test.itsm.com/api/getOrderNumber");

        // Mock HttpClientUtil.postByJson静态方法
        RestTemplate mockRestTemplate = mock(RestTemplate.class);
        try (MockedStatic<SpringUtil> springUtilMock = Mockito.mockStatic(SpringUtil.class)) {
            springUtilMock.when(() -> SpringUtil.getBean(RestTemplate.class)).thenReturn(mockRestTemplate);
            try (MockedStatic<HttpClientUtil> httpClientUtilMock = mockStatic(HttpClientUtil.class)) {
                ItsmPublishOrderNumberDto mockResponse = new ItsmPublishOrderNumberDto();
                mockResponse.setFlowNo("ORDER789");

                httpClientUtilMock.when(() -> HttpClientUtil.postByJson(
                    any(String.class),
                    any(Map.class),
                    any(String.class),
                    eq(ItsmPublishOrderNumberDto.class)
                )).thenReturn(mockResponse);

                // 执行测试
                String result = toProductServiceImplUnderTest.getOrderNumber(currentUser, allDangerCmd, toProductQueryDto, itsmFilePath);

                // 验证结果
                assertEquals("ORDER789", result);
            }
        }
    }

    @Test
    @DisplayName("测试getOrderNumber方法 - 不同用户信息")
    void testGetOrderNumber_DifferentUserInfo() {
        // 准备测试数据 - 不同用户
        CurrentUser currentUser = new CurrentUser();
        currentUser.setId(999L);
        currentUser.setLoginName("differentUser");
        currentUser.setFullName("不同用户");

        String allDangerCmd = "脚本testScript存在关键命令";
        ToProductQueryDto toProductQueryDto = new ToProductQueryDto();
        toProductQueryDto.setId(456L);
        toProductQueryDto.setDescription("不同投产描述");
        String itsmFilePath = "/different/path/file.zip";

        // Mock CiticProperties
        when(citicProperties.getItsmGetOrderNumberUrl()).thenReturn("http://test.itsm.com/api/getOrderNumber");

        // Mock HttpClientUtil.postByJson静态方法
        RestTemplate mockRestTemplate = mock(RestTemplate.class);
        try (MockedStatic<SpringUtil> springUtilMock = Mockito.mockStatic(SpringUtil.class)) {
            springUtilMock.when(() -> SpringUtil.getBean(RestTemplate.class)).thenReturn(mockRestTemplate);
            try (MockedStatic<HttpClientUtil> httpClientUtilMock = mockStatic(HttpClientUtil.class)) {
                ItsmPublishOrderNumberDto mockResponse = new ItsmPublishOrderNumberDto();
                mockResponse.setFlowNo("ORDER999");

                httpClientUtilMock.when(() -> HttpClientUtil.postByJson(
                    any(String.class),
                    any(Map.class),
                    any(String.class),
                    eq(ItsmPublishOrderNumberDto.class)
                )).thenReturn(mockResponse);

                // 执行测试
                String result = toProductServiceImplUnderTest.getOrderNumber(currentUser, allDangerCmd, toProductQueryDto, itsmFilePath);

                // 验证结果
                assertEquals("ORDER999", result);

                // 验证请求参数包含正确的用户信息
                httpClientUtilMock.verify(() -> HttpClientUtil.postByJson(
                    any(String.class),
                    any(Map.class),
                    argThat(paramsStr -> {
                        String paramsString = (String) paramsStr;
                        return paramsString.contains("differentUser"); // 验证使用正确的用户名
                    }),
                    eq(ItsmPublishOrderNumberDto.class)
                ));
            }
        }
    }

    @Test
    @DisplayName("测试getOrderNumber方法 - URL为空")
    void testGetOrderNumber_UrlIsBlank() {
        // 准备测试数据
        CurrentUser currentUser = new CurrentUser();
        currentUser.setId(1L);
        currentUser.setLoginName("testUser");

        String allDangerCmd = "脚本testScript存在关键命令";
        ToProductQueryDto toProductQueryDto = new ToProductQueryDto();
        toProductQueryDto.setId(123L);
        toProductQueryDto.setDescription("测试投产描述");
        String itsmFilePath = "/path/to/itsm/file.zip";

        // Mock CiticProperties返回空URL
        when(citicProperties.getItsmGetOrderNumberUrl()).thenReturn("");

        // 执行测试
        String result = toProductServiceImplUnderTest.getOrderNumber(currentUser, allDangerCmd, toProductQueryDto, itsmFilePath);

        // 验证结果
        assertEquals("", result);
    }

    @Test
    @DisplayName("测试getOrderNumber方法 - URL为null")
    void testGetOrderNumber_UrlIsNull() {
        // 准备测试数据
        CurrentUser currentUser = new CurrentUser();
        currentUser.setId(1L);
        currentUser.setLoginName("testUser");

        String allDangerCmd = "脚本testScript存在关键命令";
        ToProductQueryDto toProductQueryDto = new ToProductQueryDto();
        toProductQueryDto.setId(123L);
        toProductQueryDto.setDescription("测试投产描述");
        String itsmFilePath = "/path/to/itsm/file.zip";

        // Mock CiticProperties返回null URL
        when(citicProperties.getItsmGetOrderNumberUrl()).thenReturn(null);

        // 执行测试
        String result = toProductServiceImplUnderTest.getOrderNumber(currentUser, allDangerCmd, toProductQueryDto, itsmFilePath);

        // 验证结果
        assertEquals("", result);
    }

    @Test
    @DisplayName("测试getOrderNumber方法 - 返回对象flowNo为null")
    void testGetOrderNumber_FlowNoIsNull() {
        // 准备测试数据
        CurrentUser currentUser = new CurrentUser();
        currentUser.setId(1L);
        currentUser.setLoginName("testUser");

        String allDangerCmd = "脚本testScript存在关键命令";
        ToProductQueryDto toProductQueryDto = new ToProductQueryDto();
        toProductQueryDto.setId(123L);
        toProductQueryDto.setDescription("测试投产描述");
        String itsmFilePath = "/path/to/itsm/file.zip";

        // Mock CiticProperties
        when(citicProperties.getItsmGetOrderNumberUrl()).thenReturn("http://test.itsm.com/api/getOrderNumber");

        // Mock HttpClientUtil.postByJson静态方法返回flowNo为null的对象
        RestTemplate mockRestTemplate = mock(RestTemplate.class);
        try (MockedStatic<SpringUtil> springUtilMock = Mockito.mockStatic(SpringUtil.class)) {
            springUtilMock.when(() -> SpringUtil.getBean(RestTemplate.class)).thenReturn(mockRestTemplate);
            try (MockedStatic<HttpClientUtil> httpClientUtilMock = mockStatic(HttpClientUtil.class)) {
                ItsmPublishOrderNumberDto mockResponse = new ItsmPublishOrderNumberDto();
                mockResponse.setFlowNo(null); // flowNo为null
                mockResponse.setId("1");
                mockResponse.setTitle("测试工单");

                httpClientUtilMock.when(() -> HttpClientUtil.postByJson(
                    eq("http://test.itsm.com/api/getOrderNumber"),
                    any(Map.class),
                    any(String.class),
                    eq(ItsmPublishOrderNumberDto.class)
                )).thenReturn(mockResponse);

                // 执行测试
                toProductServiceImplUnderTest.getOrderNumber(currentUser, allDangerCmd, toProductQueryDto, itsmFilePath);
            }
        }
    }

    @Test
    @DisplayName("测试getOrderNumber方法 - 返回对象flowNo为空字符串")
    void testGetOrderNumber_FlowNoIsEmpty() {
        // 准备测试数据
        CurrentUser currentUser = new CurrentUser();
        currentUser.setId(1L);
        currentUser.setLoginName("testUser");

        String allDangerCmd = "脚本testScript存在关键命令";
        ToProductQueryDto toProductQueryDto = new ToProductQueryDto();
        toProductQueryDto.setId(123L);
        toProductQueryDto.setDescription("测试投产描述");
        String itsmFilePath = "/path/to/itsm/file.zip";

        // Mock CiticProperties
        when(citicProperties.getItsmGetOrderNumberUrl()).thenReturn("http://test.itsm.com/api/getOrderNumber");

        // Mock HttpClientUtil.postByJson静态方法返回flowNo为空字符串的对象
        RestTemplate mockRestTemplate = mock(RestTemplate.class);
        try (MockedStatic<SpringUtil> springUtilMock = Mockito.mockStatic(SpringUtil.class)) {
            springUtilMock.when(() -> SpringUtil.getBean(RestTemplate.class)).thenReturn(mockRestTemplate);
            try (MockedStatic<HttpClientUtil> httpClientUtilMock = mockStatic(HttpClientUtil.class)) {
                ItsmPublishOrderNumberDto mockResponse = new ItsmPublishOrderNumberDto();
                mockResponse.setFlowNo(""); // flowNo为空字符串
                mockResponse.setId("1");
                mockResponse.setTitle("测试工单");

                httpClientUtilMock.when(() -> HttpClientUtil.postByJson(
                    eq("http://test.itsm.com/api/getOrderNumber"),
                    any(Map.class),
                    any(String.class),
                    eq(ItsmPublishOrderNumberDto.class)
                )).thenReturn(mockResponse);

                // 执行测试
                String result = toProductServiceImplUnderTest.getOrderNumber(currentUser, allDangerCmd, toProductQueryDto, itsmFilePath);

                // 验证结果 - 当flowNo为空字符串时应该返回空字符串
                assertEquals("", result);
            }
        }
    }

    @Test
    @DisplayName("测试productEnvironmentAuditResult方法 - 审核通过但zip文件内容为空")
    void testProductEnvironmentAuditResult_AuditPassedWithEmptyZipContent() throws Exception {
        // 准备测试数据
        ItsmPublishScriptAuditResultDto auditResultDto = new ItsmPublishScriptAuditResultDto();
        auditResultDto.setOrderNumber("ORDER123");
        auditResultDto.setAuditState(1); // 审核通过
        auditResultDto.setIid(1001L);
        // 不再设置zipFileContent字段

        // Mock CurrentUserUtil
        CurrentUser currentUser = new CurrentUser();
        currentUser.setId(1L);
        currentUser.setLoginName("test");
        currentUser.setFullName("测试用户");
        try (MockedStatic<CurrentUserUtil> currentUserUtilMock = Mockito.mockStatic(CurrentUserUtil.class)) {
            currentUserUtilMock.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);
            // Mock ItsmPublishScript
            ItsmPublishScript itsmPublishScript = new ItsmPublishScript();
            itsmPublishScript.setIid(1001L);
            itsmPublishScript.setZipFileContent(new byte[0]);
            itsmPublishScript.setOrderNumber("ORDER123");
            lenient().when(itsmPublishInfoMapper.getDetailByOrderNumber("ORDER123")).thenReturn(itsmPublishScript);
            lenient().when(mockToProductMapper.updateToProductByOrderNumber(any(ToProductEntity.class))).thenReturn(1);
            lenient().when(itsmProductInfoMapper.deleteByPublishInfoId(1001L)).thenReturn(1);
            lenient().when(itsmProductAttachmentMapper.deleteByPublishInfoId(1001L)).thenReturn(1);
            lenient().when(itsmPublishInfoMapper.deleteByIid(1001L)).thenReturn(1);
            // 关键：补全releaseMediaService、scripts等依赖mock
            doNothing().when(releaseMediaService).handlerImportMedia(any(File.class), any(ToProductQueryDto.class));
            lenient().when(scripts.getScriptBusinessConfig()).thenReturn(new ScriptBusinessConfig());
            // 执行测试
            toProductServiceImplUnderTest.productEnvironmentAuditResult(auditResultDto);
        }
    }

    @Test
    @DisplayName("测试productEnvironmentAuditResult方法 - 审核未通过")
    void testProductEnvironmentAuditResult_AuditNotPassed() throws Exception {
        // 准备测试数据
        ItsmPublishScriptAuditResultDto auditResultDto = new ItsmPublishScriptAuditResultDto();
        auditResultDto.setOrderNumber("ORDER123");
        auditResultDto.setAuditState(3); // 审核未通过
        auditResultDto.setIid(1001L);

        // Mock CurrentUserUtil
        CurrentUser currentUser = new CurrentUser();
        currentUser.setId(1L);
        currentUser.setLoginName("test");
        currentUser.setFullName("测试用户");

        try (MockedStatic<CurrentUserUtil> currentUserUtilMock = Mockito.mockStatic(CurrentUserUtil.class)) {
            currentUserUtilMock.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);

            // Mock ItsmPublishScript
            ItsmPublishScript itsmPublishScript = new ItsmPublishScript();
            itsmPublishScript.setIid(1001L);
            itsmPublishScript.setZipFileContent("test content".getBytes());
            itsmPublishScript.setOrderNumber("ORDER123");

            // Mock mapper查询
            when(itsmPublishInfoMapper.getDetailByOrderNumber("ORDER123")).thenReturn(itsmPublishScript);

            // Mock mockToProductMapper.updateToProductByOrderNumber
            when(mockToProductMapper.updateToProductByOrderNumber(any(ToProductEntity.class))).thenReturn(1);

            // Mock 删除操作
            when(itsmProductInfoMapper.deleteByPublishInfoId(1001L)).thenReturn(1);
            when(itsmProductAttachmentMapper.deleteByPublishInfoId(1001L)).thenReturn(1);
            when(itsmPublishInfoMapper.deleteByIid(1001L)).thenReturn(1);

            // 执行测试
            toProductServiceImplUnderTest.productEnvironmentAuditResult(auditResultDto);

            // 验证调用 - 不应该调用handlerImportMedia
            verify(releaseMediaService, never()).handlerImportMedia(any(File.class), any(ToProductQueryDto.class));
            verify(mockToProductMapper).updateToProductByOrderNumber(argThat(entity ->
                "ORDER123".equals(entity.getOrderNumber()) &&
                Integer.valueOf(3).equals(entity.getProductState())
            ));
            verify(itsmProductInfoMapper).deleteByPublishInfoId(1001L);
            verify(itsmProductAttachmentMapper).deleteByPublishInfoId(1001L);
            verify(itsmPublishInfoMapper).deleteByIid(1001L);
        }
    }

    @Test
    @DisplayName("测试productEnvironmentAuditResult方法 - 查询不到ItsmPublishScript")
    void testProductEnvironmentAuditResult_NoItsmPublishScriptFound() throws Exception {
        // 准备测试数据
        ItsmPublishScriptAuditResultDto auditResultDto = new ItsmPublishScriptAuditResultDto();
        auditResultDto.setOrderNumber("ORDER123");
        auditResultDto.setAuditState(1); // 审核通过
        auditResultDto.setIid(1001L);

        // Mock CurrentUserUtil
        CurrentUser currentUser = new CurrentUser();
        currentUser.setId(1L);
        currentUser.setLoginName("test");
        currentUser.setFullName("测试用户");

        try (MockedStatic<CurrentUserUtil> currentUserUtilMock = Mockito.mockStatic(CurrentUserUtil.class)) {
            currentUserUtilMock.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);

            // Mock mapper查询返回null
            when(itsmPublishInfoMapper.getDetailByOrderNumber("ORDER123")).thenReturn(null);

            // Mock mockToProductMapper.updateToProductByOrderNumber
            when(mockToProductMapper.updateToProductByOrderNumber(any(ToProductEntity.class))).thenReturn(1);

            // 执行测试
            toProductServiceImplUnderTest.productEnvironmentAuditResult(auditResultDto);

            // 验证调用 - 不应该调用handlerImportMedia和删除操作
            verify(releaseMediaService, never()).handlerImportMedia(any(File.class), any(ToProductQueryDto.class));
            verify(mockToProductMapper).updateToProductByOrderNumber(argThat(entity ->
                "ORDER123".equals(entity.getOrderNumber()) &&
                Integer.valueOf(1).equals(entity.getProductState())
            ));
            verify(itsmProductInfoMapper, never()).deleteByPublishInfoId(anyLong());
            verify(itsmProductAttachmentMapper, never()).deleteByPublishInfoId(anyLong());
            verify(itsmPublishInfoMapper, never()).deleteByIid(anyLong());
        }
    }

    @Test
    @DisplayName("测试productEnvironmentAuditResult方法 - 数据库更新失败")
    void testProductEnvironmentAuditResult_DatabaseUpdateFailed() throws Exception {
        // 准备测试数据
        ItsmPublishScriptAuditResultDto auditResultDto = new ItsmPublishScriptAuditResultDto();
        auditResultDto.setOrderNumber("ORDER123");
        auditResultDto.setAuditState(1); // 审核通过
        auditResultDto.setIid(1001L);

        // Mock CurrentUserUtil
        CurrentUser currentUser = new CurrentUser();
        currentUser.setId(1L);
        currentUser.setLoginName("test");
        currentUser.setFullName("测试用户");

        try (MockedStatic<CurrentUserUtil> currentUserUtilMock = Mockito.mockStatic(CurrentUserUtil.class)) {
            currentUserUtilMock.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);

            // Mock ItsmPublishScript
            ItsmPublishScript itsmPublishScript = new ItsmPublishScript();
            itsmPublishScript.setIid(1001L);
            itsmPublishScript.setZipFileContent("test content".getBytes());
            itsmPublishScript.setOrderNumber("ORDER123");

            // Mock mapper查询
            lenient().when(itsmPublishInfoMapper.getDetailByOrderNumber("ORDER123")).thenReturn(itsmPublishScript);

            // Mock mockToProductMapper.updateToProductByOrderNumber返回0（更新失败）
            lenient().when(mockToProductMapper.updateToProductByOrderNumber(any(ToProductEntity.class))).thenReturn(0);

            // Mock 删除操作
            lenient().when(itsmProductInfoMapper.deleteByPublishInfoId(1001L)).thenReturn(1);
            lenient().when(itsmProductAttachmentMapper.deleteByPublishInfoId(1001L)).thenReturn(1);
            lenient().when(itsmPublishInfoMapper.deleteByIid(1001L)).thenReturn(1);

            // 执行测试
            toProductServiceImplUnderTest.productEnvironmentAuditResult(auditResultDto);

            // 验证调用
            verify(mockToProductMapper).updateToProductByOrderNumber(argThat(entity ->
                "ORDER123".equals(entity.getOrderNumber()) &&
                Integer.valueOf(1).equals(entity.getProductState())
            ));
            verify(itsmProductInfoMapper).deleteByPublishInfoId(1001L);
            verify(itsmProductAttachmentMapper).deleteByPublishInfoId(1001L);
            verify(itsmPublishInfoMapper).deleteByIid(1001L);
        }
    }

    @Test
    @DisplayName("测试productEnvironmentAuditResult方法 - 删除操作失败")
    void testProductEnvironmentAuditResult_DeleteOperationFailed() throws Exception {
        // 准备测试数据
        ItsmPublishScriptAuditResultDto auditResultDto = new ItsmPublishScriptAuditResultDto();
        auditResultDto.setOrderNumber("ORDER123");
        auditResultDto.setAuditState(1); // 审核通过
        auditResultDto.setIid(1001L);

        // Mock CurrentUserUtil
        CurrentUser currentUser = new CurrentUser();
        currentUser.setId(1L);
        currentUser.setLoginName("test");
        currentUser.setFullName("测试用户");

        try (MockedStatic<CurrentUserUtil> currentUserUtilMock = Mockito.mockStatic(CurrentUserUtil.class)) {
            currentUserUtilMock.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);

            // Mock ItsmPublishScript
            ItsmPublishScript itsmPublishScript = new ItsmPublishScript();
            itsmPublishScript.setIid(1001L);
            itsmPublishScript.setZipFileContent("test content".getBytes());
            itsmPublishScript.setOrderNumber("ORDER123");

            // Mock mapper查询
            lenient().when(itsmPublishInfoMapper.getDetailByOrderNumber("ORDER123")).thenReturn(itsmPublishScript);

            // Mock mockToProductMapper.updateToProductByOrderNumber
            lenient().when(mockToProductMapper.updateToProductByOrderNumber(any(ToProductEntity.class))).thenReturn(1);

            // Mock 删除操作返回0（删除失败）
            lenient().when(itsmProductInfoMapper.deleteByPublishInfoId(1001L)).thenReturn(0);
            lenient().when(itsmProductAttachmentMapper.deleteByPublishInfoId(1001L)).thenReturn(0);
            lenient().when(itsmPublishInfoMapper.deleteByIid(1001L)).thenReturn(0);

            // 执行测试
            toProductServiceImplUnderTest.productEnvironmentAuditResult(auditResultDto);

            // 验证调用
            verify(mockToProductMapper).updateToProductByOrderNumber(argThat(entity ->
                "ORDER123".equals(entity.getOrderNumber()) &&
                Integer.valueOf(1).equals(entity.getProductState())
            ));
            verify(itsmProductInfoMapper).deleteByPublishInfoId(1001L);
            verify(itsmProductAttachmentMapper).deleteByPublishInfoId(1001L);
            verify(itsmPublishInfoMapper).deleteByIid(1001L);
        }
    }

    @Test
    @DisplayName("测试productEnvironmentAuditResult方法 - 简化版本")
    void testProductEnvironmentAuditResult_Simple() throws Exception {
        // 准备测试数据
        ItsmPublishScriptAuditResultDto auditResultDto = new ItsmPublishScriptAuditResultDto();
        // 使用反射设置orderNumber字段
        Field orderNumberField = ItsmPublishScriptAuditResultDto.class.getDeclaredField("orderNumber");
        orderNumberField.setAccessible(true);
        orderNumberField.set(auditResultDto, "ORDER123");

        auditResultDto.setAuditState(1); // 审核通过
        auditResultDto.setIid(1001L);

        // Mock CurrentUserUtil
        CurrentUser currentUser = new CurrentUser();
        currentUser.setId(1L);
        currentUser.setLoginName("test");
        currentUser.setFullName("测试用户");

        try (MockedStatic<CurrentUserUtil> currentUserUtilMock = Mockito.mockStatic(CurrentUserUtil.class)) {
            currentUserUtilMock.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);

            // Mock ItsmPublishScript
            ItsmPublishScript itsmPublishScript = new ItsmPublishScript();
            itsmPublishScript.setIid(1001L);
            itsmPublishScript.setZipFileContent("test content".getBytes());
            // 使用反射设置orderNumber字段
            Field orderNumberField2 = ItsmPublishScript.class.getDeclaredField("orderNumber");
            orderNumberField2.setAccessible(true);
            orderNumberField2.set(itsmPublishScript, "ORDER123");

            // Mock mapper查询
            lenient().when(itsmPublishInfoMapper.getDetailByOrderNumber("ORDER123")).thenReturn(itsmPublishScript);

            // Mock mockToProductMapper.updateToProductByOrderNumber
            lenient().when(mockToProductMapper.updateToProductByOrderNumber(any(ToProductEntity.class))).thenReturn(1);

            // Mock 删除操作
            lenient().when(itsmProductInfoMapper.deleteByPublishInfoId(1001L)).thenReturn(1);
            lenient().when(itsmProductAttachmentMapper.deleteByPublishInfoId(1001L)).thenReturn(1);
            lenient().when(itsmPublishInfoMapper.deleteByIid(1001L)).thenReturn(1);

            // 执行测试
            toProductServiceImplUnderTest.productEnvironmentAuditResult(auditResultDto);

            // 验证调用
            verify(mockToProductMapper).updateToProductByOrderNumber(argThat(entity ->
                "ORDER123".equals(entity.getOrderNumber()) &&
                Integer.valueOf(1).equals(entity.getProductState())
            ));
            verify(itsmProductInfoMapper).deleteByPublishInfoId(1001L);
            verify(itsmProductAttachmentMapper).deleteByPublishInfoId(1001L);
            verify(itsmPublishInfoMapper).deleteByIid(1001L);
        }
    }

    @Test
    @DisplayName("测试insertToProduct私有方法 - 正常情况")
    void testInsertToProduct_PrivateMethod_Normal() throws Exception {
        // 准备测试数据
        CurrentUser currentUser = new CurrentUser();
        currentUser.setId(1L);
        currentUser.setFullName("测试用户");

        List<String> srcScriptUuids = Arrays.asList("uuid1", "uuid2", "uuid3");
        String fileName = "test_script.zip";

        ItsmPublishScriptDto itsmPublishScriptDto = new ItsmPublishScriptDto();
        itsmPublishScriptDto.setProductionDescription("测试投产描述");
        itsmPublishScriptDto.setProductId(1001L);
        itsmPublishScriptDto.setOrderNumber("ORDER123");

        // Mock releaseMediaService.saveProductInfo方法
        doNothing().when(releaseMediaService).saveProductInfo(any(ToProductQueryDto.class));

        // 使用反射调用私有方法
        Method insertToProductMethod = ToProductServiceImpl.class.getDeclaredMethod(
            "insertToProduct",
            CurrentUser.class,
            List.class,
            String.class,
            ItsmPublishScriptDto.class
        );
        insertToProductMethod.setAccessible(true);

        // 执行测试
        ToProductQueryDto result = (ToProductQueryDto) insertToProductMethod.invoke(
            toProductServiceImplUnderTest,
            currentUser,
            srcScriptUuids,
            fileName,
            itsmPublishScriptDto
        );

        // 验证结果
        assertNotNull(result);
        assertEquals(srcScriptUuids, result.getSrcScriptUuidList());
        assertEquals(fileName, result.getFileName());
        assertEquals(currentUser.getFullName(), result.getUserName());
        assertEquals(currentUser.getId(), result.getUserId());
        assertEquals(currentUser.getFullName(), result.getScriptUserName());
        assertEquals(currentUser.getId(), result.getScriptUserId());
        assertEquals(itsmPublishScriptDto.getProductionDescription(), result.getDescription());
        assertEquals(itsmPublishScriptDto.getProductId(), result.getId());
        assertEquals(itsmPublishScriptDto.getOrderNumber(), result.getOrderNumber());
        assertEquals(Integer.valueOf(2), result.getProductState());
        assertNotNull(result.getDateTime());

        // 验证releaseMediaService.saveProductInfo被调用
        verify(releaseMediaService).saveProductInfo(any(ToProductQueryDto.class));
    }

    @Test
    @DisplayName("测试insertToProduct私有方法 - 空脚本UUID列表")
    void testInsertToProduct_PrivateMethod_EmptySrcScriptUuids() throws Exception {
        // 准备测试数据
        CurrentUser currentUser = new CurrentUser();
        currentUser.setId(1L);
        currentUser.setFullName("测试用户");

        List<String> srcScriptUuids = new ArrayList<>();
        String fileName = "test_script.zip";

        ItsmPublishScriptDto itsmPublishScriptDto = new ItsmPublishScriptDto();
        itsmPublishScriptDto.setProductionDescription("测试投产描述");
        itsmPublishScriptDto.setProductId(1001L);
        itsmPublishScriptDto.setOrderNumber("ORDER123");

        // Mock releaseMediaService.saveProductInfo方法
        doNothing().when(releaseMediaService).saveProductInfo(any(ToProductQueryDto.class));

        // 使用反射调用私有方法
        Method insertToProductMethod = ToProductServiceImpl.class.getDeclaredMethod(
            "insertToProduct",
            CurrentUser.class,
            List.class,
            String.class,
            ItsmPublishScriptDto.class
        );
        insertToProductMethod.setAccessible(true);

        // 执行测试
        ToProductQueryDto result = (ToProductQueryDto) insertToProductMethod.invoke(
            toProductServiceImplUnderTest,
            currentUser,
            srcScriptUuids,
            fileName,
            itsmPublishScriptDto
        );

        // 验证结果
        assertNotNull(result);
        assertEquals(srcScriptUuids, result.getSrcScriptUuidList());
        assertTrue(result.getSrcScriptUuidList().isEmpty());
        assertEquals(fileName, result.getFileName());
        assertEquals(currentUser.getFullName(), result.getUserName());
        assertEquals(currentUser.getId(), result.getUserId());
        assertEquals(itsmPublishScriptDto.getProductionDescription(), result.getDescription());
        assertEquals(itsmPublishScriptDto.getProductId(), result.getId());
        assertEquals(itsmPublishScriptDto.getOrderNumber(), result.getOrderNumber());
        assertEquals(Integer.valueOf(2), result.getProductState());

        // 验证releaseMediaService.saveProductInfo被调用
        verify(releaseMediaService).saveProductInfo(any(ToProductQueryDto.class));
    }

    @Test
    @DisplayName("测试insertToProduct私有方法 - 空文件名")
    void testInsertToProduct_PrivateMethod_EmptyFileName() throws Exception {
        // 准备测试数据
        CurrentUser currentUser = new CurrentUser();
        currentUser.setId(1L);
        currentUser.setFullName("测试用户");

        List<String> srcScriptUuids = Arrays.asList("uuid1");
        String fileName = "";

        ItsmPublishScriptDto itsmPublishScriptDto = new ItsmPublishScriptDto();
        itsmPublishScriptDto.setProductionDescription("测试投产描述");
        itsmPublishScriptDto.setProductId(1001L);
        itsmPublishScriptDto.setOrderNumber("ORDER123");

        // Mock releaseMediaService.saveProductInfo方法
        doNothing().when(releaseMediaService).saveProductInfo(any(ToProductQueryDto.class));

        // 使用反射调用私有方法
        Method insertToProductMethod = ToProductServiceImpl.class.getDeclaredMethod(
            "insertToProduct",
            CurrentUser.class,
            List.class,
            String.class,
            ItsmPublishScriptDto.class
        );
        insertToProductMethod.setAccessible(true);

        // 执行测试
        ToProductQueryDto result = (ToProductQueryDto) insertToProductMethod.invoke(
            toProductServiceImplUnderTest,
            currentUser,
            srcScriptUuids,
            fileName,
            itsmPublishScriptDto
        );

        // 验证结果
        assertNotNull(result);
        assertEquals(fileName, result.getFileName());
        assertEquals(currentUser.getFullName(), result.getUserName());
        assertEquals(currentUser.getId(), result.getUserId());
        assertEquals(itsmPublishScriptDto.getProductionDescription(), result.getDescription());
        assertEquals(itsmPublishScriptDto.getProductId(), result.getId());
        assertEquals(itsmPublishScriptDto.getOrderNumber(), result.getOrderNumber());
        assertEquals(Integer.valueOf(2), result.getProductState());

        // 验证releaseMediaService.saveProductInfo被调用
        verify(releaseMediaService).saveProductInfo(any(ToProductQueryDto.class));
    }

    @Test
    @DisplayName("测试insertToProduct私有方法 - null参数")
    void testInsertToProduct_PrivateMethod_NullParameters() throws Exception {
        // 准备测试数据
        CurrentUser currentUser = new CurrentUser();
        currentUser.setId(1L);
        currentUser.setFullName("测试用户");

        List<String> srcScriptUuids = Arrays.asList("uuid1");
        String fileName = "test.zip";

        ItsmPublishScriptDto itsmPublishScriptDto = new ItsmPublishScriptDto();
        itsmPublishScriptDto.setProductionDescription(null);
        itsmPublishScriptDto.setProductId(null);
        itsmPublishScriptDto.setOrderNumber(null);

        // Mock releaseMediaService.saveProductInfo方法
        doNothing().when(releaseMediaService).saveProductInfo(any(ToProductQueryDto.class));

        // 使用反射调用私有方法
        Method insertToProductMethod = ToProductServiceImpl.class.getDeclaredMethod(
            "insertToProduct",
            CurrentUser.class,
            List.class,
            String.class,
            ItsmPublishScriptDto.class
        );
        insertToProductMethod.setAccessible(true);

        // 执行测试
        ToProductQueryDto result = (ToProductQueryDto) insertToProductMethod.invoke(
            toProductServiceImplUnderTest,
            currentUser,
            srcScriptUuids,
            fileName,
            itsmPublishScriptDto
        );

        // 验证结果
        assertNotNull(result);
        assertEquals(srcScriptUuids, result.getSrcScriptUuidList());
        assertEquals(fileName, result.getFileName());
        assertEquals(currentUser.getFullName(), result.getUserName());
        assertEquals(currentUser.getId(), result.getUserId());
        assertEquals(currentUser.getFullName(), result.getScriptUserName());
        assertEquals(currentUser.getId(), result.getScriptUserId());
        assertNull(result.getDescription());
        assertNull(result.getId());
        assertNull(result.getOrderNumber());
        assertEquals(Integer.valueOf(2), result.getProductState());
        assertNotNull(result.getDateTime());

        // 验证releaseMediaService.saveProductInfo被调用
        verify(releaseMediaService).saveProductInfo(any(ToProductQueryDto.class));
    }

    @Test
    @DisplayName("测试insertToProduct私有方法 - releaseMediaService抛出异常")
    void testInsertToProduct_PrivateMethod_ReleaseMediaServiceException() throws Exception {
        // 准备测试数据
        CurrentUser currentUser = new CurrentUser();
        currentUser.setId(1L);
        currentUser.setFullName("测试用户");

        List<String> srcScriptUuids = Arrays.asList("uuid1");
        String fileName = "test.zip";

        ItsmPublishScriptDto itsmPublishScriptDto = new ItsmPublishScriptDto();
        itsmPublishScriptDto.setProductionDescription("测试投产描述");
        itsmPublishScriptDto.setProductId(1001L);
        itsmPublishScriptDto.setOrderNumber("ORDER123");

        // Mock releaseMediaService.saveProductInfo方法抛出异常
        doThrow(new RuntimeException("保存失败")).when(releaseMediaService).saveProductInfo(any(ToProductQueryDto.class));

        // 使用反射调用私有方法
        Method insertToProductMethod = ToProductServiceImpl.class.getDeclaredMethod(
            "insertToProduct",
            CurrentUser.class,
            List.class,
            String.class,
            ItsmPublishScriptDto.class
        );
        insertToProductMethod.setAccessible(true);

        // 执行测试并验证异常
        Exception exception = assertThrows(Exception.class, () -> {
            insertToProductMethod.invoke(
                toProductServiceImplUnderTest,
                currentUser,
                srcScriptUuids,
                fileName,
                itsmPublishScriptDto
            );
        });

        // 验证异常原因
        assertTrue(exception.getCause() instanceof RuntimeException);
        assertEquals("保存失败", exception.getCause().getMessage());

        // 验证releaseMediaService.saveProductInfo被调用
        verify(releaseMediaService).saveProductInfo(any(ToProductQueryDto.class));
    }

    @Test
    @DisplayName("测试itsmProductDetails方法 - 生产环境且找到产品信息")
    void testItsmProductDetails_ProductionEnvironment_Found() throws Exception {
        // 准备测试数据
        ItsmPublishScriptDto itsmPublishScriptDto = new ItsmPublishScriptDto();
        itsmPublishScriptDto.setChildrenId(1001L);

        // Mock PublicInfoPropertiesApiDto
        PublicInfoPropertiesApiDto publicInfoDto = new PublicInfoPropertiesApiDto();
        publicInfoDto.setEnvName("生产环境");
        when(publicInfo.getPublicInfo()).thenReturn(publicInfoDto);

        // Mock ScriptBusinessConfig
        ScriptBusinessConfig scriptBusinessConfig = new ScriptBusinessConfig();
        scriptBusinessConfig.setEnvironmentFlag("生产");
        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);

        // Mock ItsmProductInfo
        ItsmProductInfo itsmProductInfo = new ItsmProductInfo();
        itsmProductInfo.setIid(1001L);
        itsmProductInfo.setSrcScriptUuid("test-uuid-123");
        itsmProductInfo.setScriptJsonStr("{\"scriptName\":\"测试脚本\",\"scriptNameZh\":\"测试脚本中文名\"}");
        when(itsmProductInfoMapper.getItsmProductInfoByProductId(1001L)).thenReturn(itsmProductInfo);

        // 执行测试方法
        ScriptInfoDto result = toProductServiceImplUnderTest.itsmProductDetails(itsmPublishScriptDto);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getScriptName()).isEqualTo("测试脚本");
        assertThat(result.getScriptNameZh()).isEqualTo("测试脚本中文名");

        // 验证方法调用
        verify(publicInfo).getPublicInfo();
        verify(scripts).getScriptBusinessConfig();
        verify(itsmProductInfoMapper).getItsmProductInfoByProductId(1001L);
    }

    @Test
    @DisplayName("测试itsmProductDetails方法 - 生产环境但未找到产品信息")
    void testItsmProductDetails_ProductionEnvironment_NotFound() throws Exception {
        // 准备测试数据
        ItsmPublishScriptDto itsmPublishScriptDto = new ItsmPublishScriptDto();
        itsmPublishScriptDto.setChildrenId(1001L);

        // Mock PublicInfoPropertiesApiDto
        PublicInfoPropertiesApiDto publicInfoDto = new PublicInfoPropertiesApiDto();
        publicInfoDto.setEnvName("生产环境");
        when(publicInfo.getPublicInfo()).thenReturn(publicInfoDto);

        // Mock ScriptBusinessConfig
        ScriptBusinessConfig scriptBusinessConfig = new ScriptBusinessConfig();
        scriptBusinessConfig.setEnvironmentFlag("生产");
        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);

        // Mock ItsmProductInfo返回null
        when(itsmProductInfoMapper.getItsmProductInfoByProductId(1001L)).thenReturn(null);

        // 执行测试方法
        ScriptInfoDto result = toProductServiceImplUnderTest.itsmProductDetails(itsmPublishScriptDto);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result).isInstanceOf(ScriptInfoDto.class);

        // 验证方法调用
        verify(publicInfo).getPublicInfo();
        verify(scripts).getScriptBusinessConfig();
        verify(itsmProductInfoMapper).getItsmProductInfoByProductId(1001L);
    }

    @Test
    @DisplayName("测试itsmProductDetails方法 - 非生产环境且找到产品信息")
    void testItsmProductDetails_NonProductionEnvironment_Found() throws Exception {
        // 准备测试数据
        ItsmPublishScriptDto itsmPublishScriptDto = new ItsmPublishScriptDto();
        itsmPublishScriptDto.setChildrenId(1001L);

        // Mock PublicInfoPropertiesApiDto
        PublicInfoPropertiesApiDto publicInfoDto = new PublicInfoPropertiesApiDto();
        publicInfoDto.setEnvName("开发环境");
        when(publicInfo.getPublicInfo()).thenReturn(publicInfoDto);

        // Mock ScriptBusinessConfig
        ScriptBusinessConfig scriptBusinessConfig = new ScriptBusinessConfig();
        scriptBusinessConfig.setEnvironmentFlag("生产");
        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);

        // Mock ItsmProductInfo
        ItsmProductInfo itsmProductInfo = new ItsmProductInfo();
        itsmProductInfo.setIid(1001L);
        itsmProductInfo.setSrcScriptUuid("test-uuid-123");
        when(itsmProductInfoMapper.getItsmProductInfoById(1001L)).thenReturn(itsmProductInfo);

        // Mock ScriptInfoDto
        ScriptInfoDto expectedScriptInfo = new ScriptInfoDto();
        expectedScriptInfo.setScriptName("测试脚本");
        expectedScriptInfo.setScriptNameZh("测试脚本中文名");
        when(myScriptService.getScriptDetail(any(ScriptInfoQueryDto.class))).thenReturn(expectedScriptInfo);

        // 执行测试方法
        ScriptInfoDto result = toProductServiceImplUnderTest.itsmProductDetails(itsmPublishScriptDto);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getScriptName()).isEqualTo("测试脚本");
        assertThat(result.getScriptNameZh()).isEqualTo("测试脚本中文名");

        // 验证方法调用
        verify(publicInfo).getPublicInfo();
        verify(scripts).getScriptBusinessConfig();
        verify(itsmProductInfoMapper).getItsmProductInfoById(1001L);
        verify(myScriptService).getScriptDetail(argThat(scriptInfoQueryDto ->
            scriptInfoQueryDto.getDubboFlag().equals(false) &&
            scriptInfoQueryDto.getSrcScriptUuid().equals("test-uuid-123")
        ));
    }

    @Test
    @DisplayName("测试itsmProductDetails方法 - 非生产环境但未找到产品信息")
    void testItsmProductDetails_NonProductionEnvironment_NotFound() throws Exception {
        // 准备测试数据
        ItsmPublishScriptDto itsmPublishScriptDto = new ItsmPublishScriptDto();
        itsmPublishScriptDto.setChildrenId(1001L);

        // Mock PublicInfoPropertiesApiDto
        PublicInfoPropertiesApiDto publicInfoDto = new PublicInfoPropertiesApiDto();
        publicInfoDto.setEnvName("开发环境");
        when(publicInfo.getPublicInfo()).thenReturn(publicInfoDto);

        // Mock ScriptBusinessConfig
        ScriptBusinessConfig scriptBusinessConfig = new ScriptBusinessConfig();
        scriptBusinessConfig.setEnvironmentFlag("生产");
        when(scripts.getScriptBusinessConfig()).thenReturn(scriptBusinessConfig);

        // Mock ItsmProductInfo返回null
        when(itsmProductInfoMapper.getItsmProductInfoById(1001L)).thenReturn(null);

        // 执行测试方法
        ScriptInfoDto result = toProductServiceImplUnderTest.itsmProductDetails(itsmPublishScriptDto);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result).isInstanceOf(ScriptInfoDto.class);

        // 验证方法调用
        verify(publicInfo).getPublicInfo();
        verify(scripts).getScriptBusinessConfig();
        verify(itsmProductInfoMapper).getItsmProductInfoById(1001L);
        verify(myScriptService, never()).getScriptDetail(any(ScriptInfoQueryDto.class));
    }

    @Test
    @DisplayName("测试promotionProducts方法 - 正常流程")
    void testPromotionProducts_Normal() throws Exception {
        // 构造参数
        ItsmPublishScriptDto dto = new ItsmPublishScriptDto();
        dto.setItsmProductUrl("http://test.com/path/to/PRODUCTION_test.zip");
        dto.setProductId(1001L);
        dto.setOrderNumber("ORDER123");
        dto.setProductionDescription("测试投产描述");

        // Mock CurrentUserUtil
        CurrentUser user = new CurrentUser();
        user.setId(1L);
        user.setLoginName("test");
        user.setFullName("测试用户");

        try (MockedStatic<CurrentUserUtil> userUtilMock = Mockito.mockStatic(CurrentUserUtil.class)) {
            userUtilMock.when(CurrentUserUtil::getCurrentUser).thenReturn(user);

            // Mock citicProperties
            lenient().when(citicProperties.getItsmRepoKey()).thenReturn("repoKey");
            lenient().when(citicProperties.getItsmDownloadProductFileUrl()).thenReturn("http://download.url");
            // Mock releaseMediaService
            lenient().when(releaseMediaService.getProductFilePath()).thenReturn("/tmp");

            // Mock releaseMediaService
            when(releaseMediaService.getProductFilePath()).thenReturn("/tmp");

            // Mock HttpClientUtil
            try (MockedStatic<HttpClientUtil> httpClientUtilMock = Mockito.mockStatic(HttpClientUtil.class)) {
                httpClientUtilMock.when(() -> HttpClientUtil.downloadFile(anyString(), anyMap(), anyMap())).thenReturn(new byte[]{1,2,3});

                // Mock Files/Paths等
                Path mockPath = Mockito.mock(Path.class);
                Path mockParentPath = Mockito.mock(Path.class);

                try (MockedStatic<Paths> pathsMock = Mockito.mockStatic(Paths.class);
                     MockedStatic<Files> filesMock = Mockito.mockStatic(Files.class);
                     MockedStatic<ZipUtil> zipUtilMock = Mockito.mockStatic(ZipUtil.class)) {

                    pathsMock.when(() -> Paths.get(anyString())).thenReturn(mockPath);
                    filesMock.when(() -> Files.createDirectories(any())).thenReturn(mockParentPath);
                    filesMock.when(() -> Files.write(any(), any(byte[].class))).thenReturn(mockPath);
                    filesMock.when(() -> Files.exists(any())).thenReturn(false);
                    when(mockPath.getParent()).thenReturn(mockParentPath);

                    // Mock File
                    File mockFile = Mockito.mock(File.class);
                    when(mockPath.toFile()).thenReturn(mockFile);
                    when(mockFile.getName()).thenReturn("PRODUCTION_test.zip");
//                    when(mockFile.getAbsolutePath()).thenReturn("/tmp/PRODUCTION_test.zip");

                    // 修复void方法mock
                    doNothing().when(ZipUtil.class);
                    ZipUtil.upZipFile(anyString(), anyString());

                    // Mock 文件系统操作
                    File mockTempFolder = Mockito.mock(File.class);

                    // Mock itsmPublishInfoMapper
                    lenient().when(itsmPublishInfoMapper.insertItsmPublishInfo(any())).thenReturn(1);
                    // 执行测试
                    toProductServiceImplUnderTest.promotionProducts(dto);

                    // 验证调用
                    verify(citicProperties).getItsmRepoKey();
                    verify(citicProperties).getItsmDownloadProductFileUrl();
                    verify(releaseMediaService).getProductFilePath();
                    httpClientUtilMock.verify(() -> HttpClientUtil.downloadFile(anyString(), anyMap(), anyMap()));
                }
            }
        }
    }

    @Test
    @DisplayName("测试getNowData私有方法")
    void testGetNowData_PrivateMethod() throws Exception {
        // 使用反射调用私有方法
        Method getNowDataMethod = ToProductServiceImpl.class.getDeclaredMethod("getNowData");
        getNowDataMethod.setAccessible(true);

        String result = (String) getNowDataMethod.invoke(toProductServiceImplUnderTest);

        // 验证返回格式为yyyyMMdd
        assertNotNull(result);
        assertEquals(8, result.length());
        assertTrue(result.matches("\\d{8}"));

        // 验证日期格式正确
        LocalDate.parse(result, DateTimeFormatter.ofPattern("yyyyMMdd"));
    }

    @Test
    @DisplayName("测试analysisZipFile私有方法 - 正常流程")
    void testAnalysisZipFile_PrivateMethod_Normal() throws Exception {
        // 使用反射调用私有方法
        Method analysisZipFileMethod = ToProductServiceImpl.class.getDeclaredMethod("analysisZipFile",
            ItsmPublishScriptDto.class, File.class, CurrentUser.class, boolean.class);
        analysisZipFileMethod.setAccessible(true);

        // 准备测试数据
        ItsmPublishScriptDto dto = new ItsmPublishScriptDto();
        dto.setProductId(1001L);
        dto.setOrderNumber("ORDER123");
        dto.setCreatedUserId(1L);

        CurrentUser user = new CurrentUser();
        user.setId(1L);

        // 创建mock的File对象
        File mockZipFile = Mockito.mock(File.class);
        when(mockZipFile.getName()).thenReturn("投产介质_1750294256552.zip");
        when(mockZipFile.getAbsolutePath()).thenReturn("/tmp/投产介质_1750294256552.zip");

        try (MockedStatic<Paths> pathsMock = Mockito.mockStatic(Paths.class);
             MockedStatic<Files> filesMock = Mockito.mockStatic(Files.class);
             MockedStatic<ZipUtil> zipUtilMock = Mockito.mockStatic(ZipUtil.class)) {

            // Mock Paths.get() 返回mockPath
            Path mockPath = Mockito.mock(Path.class);
            pathsMock.when(() -> Paths.get(anyString())).thenReturn(mockPath);
//            when(mockPath.toFile()).thenReturn(mockOutputDir);

            // Mock Files.exists() 返回false，让代码走创建目录分支
            filesMock.when(() -> Files.exists(any())).thenReturn(false);
            filesMock.when(() -> Files.createDirectories(any())).thenReturn(mockPath);

            // Mock 文件读取
            String jsonContent = "{\"infoVersion\":{\"srcScriptUuid\":\"uuid123\"},\"infoDto\":{\"scriptName\":\"test\"},\"attachmentList\":[{\"name\":\"test.txt\",\"size\":100,\"contents\":\"test content\"}]}";
            ByteArrayInputStream mockInputStream = new ByteArrayInputStream(jsonContent.getBytes());
            filesMock.when(() -> Files.newInputStream(any(Path.class))).thenReturn(mockInputStream);

            // Mock ObjectMapper - 返回完整的ReleaseMediaBean
            ReleaseMediaBean mockReleaseMediaBean = new ReleaseMediaBean();
            InfoVersion mockInfoVersion = new InfoVersion();
            mockInfoVersion.setSrcScriptUuid("uuid123");
            mockReleaseMediaBean.setInfoVersion(mockInfoVersion);

            ScriptInfoDto mockInfoDto = new ScriptInfoDto();
            mockInfoDto.setScriptName("test");
            mockReleaseMediaBean.setInfoDto(mockInfoDto);

            List<Attachment> mockAttachments = new ArrayList<>();
            Attachment mockAttachment = new Attachment();
            mockAttachment.setName("test.txt");
            mockAttachment.setSize(100L);
            mockAttachment.setContents("test content".getBytes(StandardCharsets.UTF_8));
            mockAttachments.add(mockAttachment);
            mockReleaseMediaBean.setAttachmentList(mockAttachments);

//            when(objectMapper.readValue(any(Reader.class), eq(ReleaseMediaBean.class))).thenReturn(mockReleaseMediaBean);
//
//            // Mock 所有依赖方法
//            when(itsmPublishInfoMapper.insertItsmPublishInfo(any())).thenReturn(1);
//            when(itsmProductInfoMapper.insertProductInfo(any())).thenReturn(1);
//            when(itsmProductInfoMapper.updateScriptInfoJsonById(any())).thenReturn(1);
//            when(itsmProductAttachmentMapper.insert(any())).thenReturn(1);
//
//            doNothing().when(releaseMediaService).handleAttachmentsFolder(any(), any(), anyBoolean());
//            doNothing().when(releaseMediaService).saveProductInfo(any());

            doNothing().when(ZipUtil.class);
            ZipUtil.upZipFile(anyString(), anyString());

            // 执行测试
            analysisZipFileMethod.invoke(toProductServiceImplUnderTest, dto, mockZipFile, user, true);

        }
    }

    @Test
    @DisplayName("测试analysisZipFile私有方法 - 文件名不符合条件")
    void testAnalysisZipFile_PrivateMethod_InvalidFileName() throws Exception {
        // 使用反射调用私有方法
        Method analysisZipFileMethod = ToProductServiceImpl.class.getDeclaredMethod("analysisZipFile",
            ItsmPublishScriptDto.class, File.class, CurrentUser.class, boolean.class);
        analysisZipFileMethod.setAccessible(true);

        // 准备测试数据
        ItsmPublishScriptDto dto = new ItsmPublishScriptDto();
        CurrentUser user = new CurrentUser();

        File mockZipFile = Mockito.mock(File.class);
        when(mockZipFile.getName()).thenReturn("invalid_file.txt"); // 不符合条件的文件名

        // 执行测试
        analysisZipFileMethod.invoke(toProductServiceImplUnderTest, dto, mockZipFile, user, true);

        // 验证没有调用相关方法
        verify(itsmPublishInfoMapper, never()).insertItsmPublishInfo(any());
        verify(releaseMediaService, never()).saveProductInfo(any());
    }

    @Test
    @DisplayName("测试insertItsmPublishScript私有方法")
    void testInsertItsmPublishScript_PrivateMethod() throws Exception {
        // 使用反射调用私有方法
        Method insertItsmPublishScriptMethod = ToProductServiceImpl.class.getDeclaredMethod("insertItsmPublishScript", 
            ItsmPublishScriptDto.class, boolean.class);
        insertItsmPublishScriptMethod.setAccessible(true);
        
        // 准备测试数据
        ItsmPublishScriptDto dto = new ItsmPublishScriptDto();
        dto.setProductId(1001L);
        dto.setItsmProductUrl("http://test.com/test.zip");
        dto.setOrderNumber("ORDER123");
        dto.setZipFileContent(new byte[]{1,2,3});
        dto.setFileName("test.zip");
        dto.setCreatedUserId(1L);
        dto.setAuditState(1);
        
        // Mock itsmPublishInfoMapper
        when(itsmPublishInfoMapper.insertItsmPublishInfo(any())).thenReturn(1);
        
        // 执行测试
        ItsmPublishScriptDto result = (ItsmPublishScriptDto) insertItsmPublishScriptMethod.invoke(toProductServiceImplUnderTest, dto, true);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(dto.getProductId(), result.getProductId());
        assertEquals(dto.getItsmProductUrl(), result.getItsmProductUrl());
        assertEquals(Integer.valueOf(2), result.getAuditState()); // 晋级接口调用时设置为审核中
        assertEquals(dto.getOrderNumber(), result.getOrderNumber());
        
        // 验证调用
        verify(itsmPublishInfoMapper).insertItsmPublishInfo(any());
    }

    @Test
    @DisplayName("测试analysisFiles方法 - 正常流程")
    void testAnalysisFiles_Normal() throws Exception {
        // 准备测试数据
        File mockScriptFile = Mockito.mock(File.class);
        File[] scriptFiles = {mockScriptFile};

        ItsmPublishScriptDto dto = new ItsmPublishScriptDto();
        dto.setCreatedUserId(1L);

        List<ItsmProductAttachment> attachmentList = new ArrayList<>();

        ItsmPublishScriptDto dto1 = new ItsmPublishScriptDto();
        dto1.setIid(1001L);

        List<String> srcUuidList = new ArrayList<>();

        // Mock 文件系统 - 使用不是"scriptFiles"的文件名，让条件为true
        when(mockScriptFile.getName()).thenReturn("test_script_folder");
        when(mockScriptFile.isDirectory()).thenReturn(true);

        File mockJsonFile = Mockito.mock(File.class);
        File[] jsonFiles = {mockJsonFile};
        when(mockScriptFile.listFiles((FilenameFilter) any())).thenReturn(jsonFiles);

        Path mockJsonPath = Mockito.mock(Path.class);
        when(mockJsonFile.toPath()).thenReturn(mockJsonPath);

        // Mock 文件读取
        String jsonContent = "{\"infoVersion\":{\"srcScriptUuid\":\"uuid123\"},\"infoDto\":{},\"attachmentList\":[]}";
        ByteArrayInputStream mockInputStream = new ByteArrayInputStream(jsonContent.getBytes());

        // Mock ReleaseMediaBean
        ReleaseMediaBean mockReleaseMediaBean = new ReleaseMediaBean();
        InfoVersion mockInfoVersion = new InfoVersion();
        mockInfoVersion.setSrcScriptUuid("uuid123");
        mockReleaseMediaBean.setInfoVersion(mockInfoVersion);
        mockReleaseMediaBean.setInfoDto(new ScriptInfoDto());
        mockReleaseMediaBean.setAttachmentList(new ArrayList<>());

        // Mock ObjectMapper
        when(objectMapper.readValue(any(Reader.class), eq(ReleaseMediaBean.class))).thenReturn(mockReleaseMediaBean);

        // Mock 附件文件夹 - 返回空数组，走else分支
        File[] attachmentFolders = new File[0];
        when(mockScriptFile.listFiles((FileFilter) any())).thenReturn(attachmentFolders);

        // Mock itsmProductInfoMapper
        when(itsmProductInfoMapper.insertProductInfo(any())).thenReturn(1);

        // 使用try-with-resources确保静态mock正确关闭
        try (MockedStatic<Files> filesMock = Mockito.mockStatic(Files.class)) {
            filesMock.when(() -> Files.newInputStream(any(Path.class))).thenReturn(mockInputStream);

            // 执行测试
            toProductServiceImplUnderTest.analysisFiles(scriptFiles, dto, attachmentList, dto1, srcUuidList);

            // 验证调用
            verify(itsmProductInfoMapper).insertProductInfo(any());
            assertEquals(1, srcUuidList.size());
            assertEquals("uuid123", srcUuidList.get(0));
        }
    }

    @Test
    @DisplayName("测试tidyDataToScriptInfoDto私有方法")
    void testTidyDataToScriptInfoDto_PrivateMethod() throws Exception {
        // 使用反射调用私有方法
        Method tidyDataToScriptInfoDtoMethod = ToProductServiceImpl.class.getDeclaredMethod("tidyDataToScriptInfoDto",
            ReleaseMediaBean.class);
        tidyDataToScriptInfoDtoMethod.setAccessible(true);

        // 准备测试数据
        ReleaseMediaBean releaseMediaBean = new ReleaseMediaBean();

        // Mock ScriptInfoDto
        ScriptInfoDto mockInfoDto = new ScriptInfoDto();
        mockInfoDto.setScriptName("测试脚本");
        releaseMediaBean.setInfoDto(mockInfoDto);

        // Mock InfoVersion
        InfoVersion mockInfoVersion = new InfoVersion();
        mockInfoVersion.setSrcScriptUuid("uuid123");
        releaseMediaBean.setInfoVersion(mockInfoVersion);

        List<Parameter> parameterList = new ArrayList<>();
        Parameter parameter = new Parameter();
        parameter.setId(1L);
        parameter.setParamDefaultValue("11");
        parameterList.add(parameter);
        releaseMediaBean.setParameters(parameterList);

        // Mock InfoVersionText
        InfoVersionText mockInfoVersionText = new InfoVersionText();
        releaseMediaBean.setInfoVersionText(mockInfoVersionText);

        // Mock AttachmentList
        List<Attachment> mockAttachments = new ArrayList<>();
        Attachment mockAttachment = new Attachment();
        mockAttachment.setName("test.txt");
        mockAttachments.add(mockAttachment);
        releaseMediaBean.setAttachmentList(mockAttachments);

        // --- BeanUtils.copy静态mock链路 ---
        ScriptInfoDto copiedInfoDto = new ScriptInfoDto();
        copiedInfoDto.setScriptName("测试脚本");
        ScriptVersionDto copiedScriptVersionDto = new ScriptVersionDto();
        copiedScriptVersionDto.setSrcScriptUuid("uuid123");
        ScriptContentDto copiedScriptContentDto = new ScriptContentDto();
        List<AttachmentUploadDto> copiedAttachmentUploadDtoList = new ArrayList<>();
        AttachmentUploadDto copiedAttachmentUploadDto = new AttachmentUploadDto();
        copiedAttachmentUploadDto.setName("test.txt");
        copiedAttachmentUploadDtoList.add(copiedAttachmentUploadDto);

        try (MockedStatic<BeanUtils> mockBeanUtils = Mockito.mockStatic(BeanUtils.class)) {
            // InfoDto -> ScriptInfoDto
            mockBeanUtils.when(() -> BeanUtils.copy(any(ScriptInfoDto.class), eq(ScriptInfoDto.class)))
                    .thenReturn(copiedInfoDto);
            // InfoVersion -> ScriptVersionDto
            mockBeanUtils.when(() -> BeanUtils.copy(any(InfoVersion.class), eq(ScriptVersionDto.class)))
                    .thenReturn(copiedScriptVersionDto);
            // InfoVersionText -> ScriptContentDto
            mockBeanUtils.when(() -> BeanUtils.copy(any(InfoVersionText.class), eq(ScriptContentDto.class)))
                    .thenReturn(copiedScriptContentDto);
            // Attachment -> AttachmentUploadDto List
            mockBeanUtils.when(() -> BeanUtils.copy(anyList(), eq(AttachmentUploadDto.class)))
                    .thenReturn(copiedAttachmentUploadDtoList);
            // 其它copy（如参数）返回空list
            mockBeanUtils.when(() -> BeanUtils.copy(anyList(), eq(ParameterValidationDto.class)))
                    .thenReturn(new ArrayList<>());

            // 执行测试
            ScriptInfoDto result = (ScriptInfoDto) tidyDataToScriptInfoDtoMethod.invoke(toProductServiceImplUnderTest, releaseMediaBean);
            // 验证结果
            assertNotNull(result);
            assertEquals("测试脚本", result.getScriptName());
            assertNotNull(result.getScriptVersionDto());
            assertEquals("uuid123", result.getScriptVersionDto().getSrcScriptUuid());
            assertNotNull(result.getScriptVersionDto().getScriptContentDto());
            assertNotNull(result.getScriptVersionDto().getAttachmentUploadDtoList());
            assertEquals(1, result.getScriptVersionDto().getAttachmentUploadDtoList().size());
            assertEquals("test.txt", result.getScriptVersionDto().getAttachmentUploadDtoList().get(0).getName());
        }
    }

    @Test
    @DisplayName("测试tidyDataToScriptInfoDto私有方法 - infoDto为null")
    void testTidyDataToScriptInfoDto_PrivateMethod_NullInfoDto() throws Exception {
        // 使用反射调用私有方法
        Method tidyDataToScriptInfoDtoMethod = ToProductServiceImpl.class.getDeclaredMethod("tidyDataToScriptInfoDto",
            ReleaseMediaBean.class);
        tidyDataToScriptInfoDtoMethod.setAccessible(true);

        // 准备测试数据
        ReleaseMediaBean releaseMediaBean = new ReleaseMediaBean();
        releaseMediaBean.setInfoDto(null); // infoDto为null

        // 执行测试
        ScriptInfoDto result = (ScriptInfoDto) tidyDataToScriptInfoDtoMethod.invoke(toProductServiceImplUnderTest, releaseMediaBean);

        // 验证结果
        assertNotNull(result);
        assertTrue(result instanceof ScriptInfoDto);
    }

    @Test
    @DisplayName("测试promotionProducts方法 - 下载文件异常")
    void testPromotionProducts_DownloadException() throws Exception {
        // 构造参数
        ItsmPublishScriptDto dto = new ItsmPublishScriptDto();
        dto.setItsmProductUrl("http://test.com/path/to/test.zip");

        // Mock CurrentUserUtil
        CurrentUser user = new CurrentUser();
        try (MockedStatic<CurrentUserUtil> userUtilMock = Mockito.mockStatic(CurrentUserUtil.class)) {
            userUtilMock.when(CurrentUserUtil::getCurrentUser).thenReturn(user);

            // Mock citicProperties
            when(citicProperties.getItsmRepoKey()).thenReturn("repoKey");
            when(citicProperties.getItsmDownloadProductFileUrl()).thenReturn("http://download.url");

            RestTemplate mockRestTemplate = mock(RestTemplate.class);
            try (MockedStatic<SpringUtil> springUtilMock = Mockito.mockStatic(SpringUtil.class)) {
                springUtilMock.when(() -> SpringUtil.getBean(RestTemplate.class)).thenReturn(mockRestTemplate);
                // Mock HttpClientUtil抛出异常
                try (MockedStatic<HttpClientUtil> httpClientUtilMock = Mockito.mockStatic(HttpClientUtil.class)) {
                    httpClientUtilMock.when(() -> HttpClientUtil.downloadFile(anyString(), anyMap(), anyMap()))
                            .thenThrow(new RuntimeException("Download failed"));

                    // 执行测试并验证异常 - 实际抛出的是RuntimeException，不是ScriptException
                    RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                        toProductServiceImplUnderTest.promotionProducts(dto);
                    });

                    assertEquals("Download failed", exception.getMessage());
                }
            }
        }
    }

    @Test
    @DisplayName("测试promotionProducts方法 - 文件写入异常")
    void testPromotionProducts_FileWriteException() throws Exception {
        // 构造参数
        ItsmPublishScriptDto dto = new ItsmPublishScriptDto();
        dto.setItsmProductUrl("http://test.com/path/to/test.zip");

        // Mock CurrentUserUtil
        CurrentUser user = new CurrentUser();
        try (MockedStatic<CurrentUserUtil> userUtilMock = Mockito.mockStatic(CurrentUserUtil.class);
             MockedStatic<HttpClientUtil> httpClientUtilMock = Mockito.mockStatic(HttpClientUtil.class);
             MockedStatic<Paths> pathsMock = Mockito.mockStatic(Paths.class);
             MockedStatic<Files> filesMock = Mockito.mockStatic(Files.class)) {

            userUtilMock.when(CurrentUserUtil::getCurrentUser).thenReturn(user);

            // Mock citicProperties
            when(citicProperties.getItsmRepoKey()).thenReturn("repoKey");
            when(citicProperties.getItsmDownloadProductFileUrl()).thenReturn("http://download.url");

            // Mock releaseMediaService
            when(releaseMediaService.getProductFilePath()).thenReturn("/tmp");

            // Mock HttpClientUtil
            httpClientUtilMock.when(() -> HttpClientUtil.downloadFile(anyString(), anyMap(), anyMap())).thenReturn(new byte[]{1,2,3});

            // Mock Paths和Files
            Path mockPath = Mockito.mock(Path.class);
            pathsMock.when(() -> Paths.get(anyString())).thenReturn(mockPath);
            filesMock.when(() -> Files.createDirectories(any())).thenThrow(new IOException("Directory creation failed"));

            // 执行测试并验证异常
            ScriptException exception = assertThrows(ScriptException.class, () -> {
                toProductServiceImplUnderTest.promotionProducts(dto);
            });

            assertEquals("download Itsm Product error", exception.getMessage());
        }
    }

    @Test
    @DisplayName("测试analysisFiles方法 - 文件名等于SCRIPT_FILE_DIR_NAME分支")
    void testAnalysisFiles_files_ScriptFileDirName() throws Exception {
        // Setup - 创建测试数据
        File mockScriptFile = Mockito.mock(File.class);
        File[] scriptFiles = {mockScriptFile};

        ItsmPublishScriptDto dto = new ItsmPublishScriptDto();
        dto.setCreatedUserId(1L);

        List<ItsmProductAttachment> attachmentList = new ArrayList<>();

        ItsmPublishScriptDto dto1 = new ItsmPublishScriptDto();
        dto1.setIid(1001L);

        List<String> srcUuidList = new ArrayList<>();

        // Mock 文件名等于"scriptFiles"，让条件为false，直接跳过
        when(mockScriptFile.getName()).thenReturn("scriptFiles");

        // 执行测试
        toProductServiceImplUnderTest.analysisFiles(scriptFiles, dto, attachmentList, dto1, srcUuidList);

        // 验证结果 - 应该没有调用任何业务方法
        assertEquals(0, srcUuidList.size());
    }

    @Test
    @DisplayName("测试analysisFiles方法 - 文件不是目录分支")
    void testAnalysisFiles_files_NotDirectory() throws Exception {
        // Setup - 创建测试数据
        File mockScriptFile = Mockito.mock(File.class);
        File[] scriptFiles = {mockScriptFile};

        ItsmPublishScriptDto dto = new ItsmPublishScriptDto();
        dto.setCreatedUserId(1L);

        List<ItsmProductAttachment> attachmentList = new ArrayList<>();

        ItsmPublishScriptDto dto1 = new ItsmPublishScriptDto();
        dto1.setIid(1001L);

        List<String> srcUuidList = new ArrayList<>();

        // Mock 文件名不是"scriptFiles"，但文件不是目录
        when(mockScriptFile.getName()).thenReturn("test_script_folder");
        when(mockScriptFile.isDirectory()).thenReturn(false);

        // 执行测试
        toProductServiceImplUnderTest.analysisFiles(scriptFiles, dto, attachmentList, dto1, srcUuidList);

        // 验证结果 - 应该直接返回，没有调用任何业务方法
        assertEquals(0, srcUuidList.size());
    }

    @Test
    @DisplayName("测试analysisFiles方法 - JSON文件为空分支")
    void testAnalysisFiles_files_EmptyJsonFiles() throws Exception {
        // Setup - 创建测试数据
        File mockScriptFile = Mockito.mock(File.class);
        File[] scriptFiles = {mockScriptFile};

        ItsmPublishScriptDto dto = new ItsmPublishScriptDto();
        dto.setCreatedUserId(1L);

        List<ItsmProductAttachment> attachmentList = new ArrayList<>();

        ItsmPublishScriptDto dto1 = new ItsmPublishScriptDto();
        dto1.setIid(1001L);

        List<String> srcUuidList = new ArrayList<>();

        // Mock 文件系统
        when(mockScriptFile.getName()).thenReturn("test_script_folder");
        when(mockScriptFile.isDirectory()).thenReturn(true);

        // Mock JSON文件为空
        when(mockScriptFile.listFiles((FilenameFilter) any())).thenReturn(null);

        // 执行测试
        toProductServiceImplUnderTest.analysisFiles(scriptFiles, dto, attachmentList, dto1, srcUuidList);

        // 验证结果 - 应该直接返回，没有调用任何业务方法
        assertEquals(0, srcUuidList.size());
    }

    @Test
    @DisplayName("测试analysisFiles方法 - JSON文件读取异常分支")
    void testAnalysisFiles_files_JsonReadException() throws Exception {
        // Setup - 创建测试数据
        File mockScriptFile = Mockito.mock(File.class);
        File[] scriptFiles = {mockScriptFile};

        ItsmPublishScriptDto dto = new ItsmPublishScriptDto();
        dto.setCreatedUserId(1L);

        List<ItsmProductAttachment> attachmentList = new ArrayList<>();

        ItsmPublishScriptDto dto1 = new ItsmPublishScriptDto();
        dto1.setIid(1001L);

        List<String> srcUuidList = new ArrayList<>();

        // Mock 文件系统
        when(mockScriptFile.getName()).thenReturn("test_script_folder");
        when(mockScriptFile.isDirectory()).thenReturn(true);

        File mockJsonFile = Mockito.mock(File.class);
        File[] jsonFiles = {mockJsonFile};
        when(mockScriptFile.listFiles((FilenameFilter) any())).thenReturn(jsonFiles);

        Path mockJsonPath = Mockito.mock(Path.class);
        when(mockJsonFile.toPath()).thenReturn(mockJsonPath);

        // Mock ObjectMapper抛出异常
        when(objectMapper.readValue(any(Reader.class), eq(ReleaseMediaBean.class)))
                .thenThrow(new RuntimeException("JSON parsing error"));

        try (MockedStatic<Files> filesMock = Mockito.mockStatic(Files.class)) {
            filesMock.when(() -> Files.newInputStream(any(Path.class)))
                    .thenReturn(new ByteArrayInputStream("invalid json".getBytes()));

            // 执行测试并验证异常
            ScriptException exception = assertThrows(ScriptException.class, () -> {
                toProductServiceImplUnderTest.analysisFiles(scriptFiles, dto, attachmentList, dto1, srcUuidList);
            });

            assertEquals("handle.json.file.error", exception.getMessage());
        }
    }

    @Test
    @DisplayName("测试analysisFiles方法 - 附件文件夹为空分支")
    void testAnalysisFiles_files_EmptyAttachmentFolders() throws Exception {
        // Setup - 创建测试数据
        File mockScriptFile = Mockito.mock(File.class);
        File[] scriptFiles = {mockScriptFile};

        ItsmPublishScriptDto dto = new ItsmPublishScriptDto();
        dto.setCreatedUserId(1L);

        List<ItsmProductAttachment> attachmentList = new ArrayList<>();

        ItsmPublishScriptDto dto1 = new ItsmPublishScriptDto();
        dto1.setIid(1001L);

        List<String> srcUuidList = new ArrayList<>();

        // Mock 文件系统
        when(mockScriptFile.getName()).thenReturn("test_script_folder");
        when(mockScriptFile.isDirectory()).thenReturn(true);

        File mockJsonFile = Mockito.mock(File.class);
        File[] jsonFiles = {mockJsonFile};
        when(mockScriptFile.listFiles((FilenameFilter) any())).thenReturn(jsonFiles);

        Path mockJsonPath = Mockito.mock(Path.class);
        when(mockJsonFile.toPath()).thenReturn(mockJsonPath);

        // Mock 文件读取
        String jsonContent = "{\"infoVersion\":{\"srcScriptUuid\":\"uuid123\"},\"infoDto\":{\"scriptName\":\"测试脚本\"}}";
        ByteArrayInputStream mockInputStream = new ByteArrayInputStream(jsonContent.getBytes());

        // Mock ReleaseMediaBean
        ReleaseMediaBean mockReleaseMediaBean = new ReleaseMediaBean();
        InfoVersion mockInfoVersion = new InfoVersion();
        mockInfoVersion.setSrcScriptUuid("uuid123");
        mockReleaseMediaBean.setInfoVersion(mockInfoVersion);
        
        ScriptInfoDto mockInfoDto = new ScriptInfoDto();
        mockInfoDto.setScriptName("测试脚本");
        mockReleaseMediaBean.setInfoDto(mockInfoDto);
        mockReleaseMediaBean.setAttachmentList(new ArrayList<>());

        // Mock ObjectMapper
        when(objectMapper.readValue(any(Reader.class), eq(ReleaseMediaBean.class))).thenReturn(mockReleaseMediaBean);

        // Mock 附件文件夹为空
        when(mockScriptFile.listFiles((FileFilter) any())).thenReturn(new File[0]);

        // Mock itsmProductInfoMapper
        when(itsmProductInfoMapper.insertProductInfo(any())).thenReturn(1);

        try (MockedStatic<Files> filesMock = Mockito.mockStatic(Files.class)) {
            filesMock.when(() -> Files.newInputStream(any(Path.class))).thenReturn(mockInputStream);

            // 执行测试
            toProductServiceImplUnderTest.analysisFiles(scriptFiles, dto, attachmentList, dto1, srcUuidList);

            // 验证调用 - 只调用insertProductInfo，不调用附件相关方法
            verify(itsmProductInfoMapper).insertProductInfo(any());
            verify(itsmProductInfoMapper, never()).updateScriptInfoJsonById(any());
            verify(itsmProductAttachmentMapper, never()).insert(any());
            verify(releaseMediaService, never()).handleAttachmentsFolder(any(File.class), any(ReleaseMediaBean.class), anyBoolean());
            
            // 验证结果
            assertEquals(1, srcUuidList.size());
            assertEquals("uuid123", srcUuidList.get(0));
        }
    }

    @Test
    @DisplayName("测试analysisFiles方法 - 附件列表为空分支")
    void testAnalysisFiles_files_EmptyAttachmentList() throws Exception {
        // Setup - 创建测试数据
        File mockScriptFile = Mockito.mock(File.class);
        File[] scriptFiles = {mockScriptFile};

        ItsmPublishScriptDto dto = new ItsmPublishScriptDto();
        dto.setCreatedUserId(1L);

        List<ItsmProductAttachment> attachmentList = new ArrayList<>();
        ItsmProductAttachment itsmProductAttachment = new ItsmProductAttachment();
        itsmProductAttachment.setIid(1L);
        itsmProductAttachment.setAttachmentName("testname");
        attachmentList.add(itsmProductAttachment);

        ItsmPublishScriptDto dto1 = new ItsmPublishScriptDto();
        dto1.setIid(1001L);

        List<String> srcUuidList = new ArrayList<>();

        // Mock 文件系统
        when(mockScriptFile.getName()).thenReturn("test_script_folder");
        when(mockScriptFile.isDirectory()).thenReturn(true);

        File mockJsonFile = Mockito.mock(File.class);
        File[] jsonFiles = {mockJsonFile};
        when(mockScriptFile.listFiles((FilenameFilter) any())).thenReturn(jsonFiles);

        Path mockJsonPath = Mockito.mock(Path.class);
        when(mockJsonFile.toPath()).thenReturn(mockJsonPath);

        // Mock 文件读取
        String jsonContent = "{\"infoVersion\":{\"srcScriptUuid\":\"uuid123\"},\"infoDto\":{\"scriptName\":\"测试脚本\"}}";
        ByteArrayInputStream mockInputStream = new ByteArrayInputStream(jsonContent.getBytes());

        // Mock ReleaseMediaBean - 附件列表为空
        ReleaseMediaBean mockReleaseMediaBean = new ReleaseMediaBean();
        InfoVersion mockInfoVersion = new InfoVersion();
        mockInfoVersion.setSrcScriptUuid("uuid123");
        mockReleaseMediaBean.setInfoVersion(mockInfoVersion);
        
        ScriptInfoDto mockInfoDto = new ScriptInfoDto();
        mockInfoDto.setScriptName("测试脚本");
        mockReleaseMediaBean.setInfoDto(mockInfoDto);
        mockReleaseMediaBean.setAttachmentList(null); // 附件列表为null

        ScriptVersionDto mockScriptVersionDto = new ScriptVersionDto();
        List<AttachmentUploadDto> list = new ArrayList<>();
        AttachmentUploadDto attachmentUploadDto = new AttachmentUploadDto();
        attachmentUploadDto.setId(1L);
        attachmentUploadDto.setName("testname");
        attachmentUploadDto.setSize(10L);
        attachmentUploadDto.setContents("test".getBytes());
        list.add(attachmentUploadDto);
        mockScriptVersionDto.setAttachmentUploadDtoList(list);
        mockInfoDto.setScriptVersionDto(mockScriptVersionDto);

        // Mock ObjectMapper
        when(objectMapper.readValue(any(Reader.class), eq(ReleaseMediaBean.class))).thenReturn(mockReleaseMediaBean);

        // Mock 附件文件夹
        File mockAttachmentFolder = Mockito.mock(File.class);
        File[] attachmentFolders = {mockAttachmentFolder};
        when(mockScriptFile.listFiles((FileFilter) any())).thenReturn(attachmentFolders);

        // Mock releaseMediaService.handleAttachmentsFolder
        doNothing().when(releaseMediaService).handleAttachmentsFolder(any(File.class), any(ReleaseMediaBean.class), eq(false));

        // Mock itsmProductInfoMapper
        when(itsmProductInfoMapper.insertProductInfo(any())).thenReturn(1);

        List<Attachment> attachments = new ArrayList<>();
        Attachment attachment = new Attachment();
        attachment.setName("testname");
        attachment.setSize(10L);
        attachment.setContents("testname".getBytes());
        attachments.add(attachment);

        mockReleaseMediaBean.setAttachmentList(attachments);
        when(itsmProductAttachmentMapper.insert(any())).thenReturn(1);

        // Mock BeanUtils.copy静态方法
        ScriptInfoDto copiedInfoDto = new ScriptInfoDto();
        copiedInfoDto.setScriptName("测试脚本");
        ScriptVersionDto copiedScriptVersionDto = new ScriptVersionDto();
        copiedScriptVersionDto.setSrcScriptUuid("uuid123");

        try (MockedStatic<Files> filesMock = Mockito.mockStatic(Files.class);
             MockedStatic<BeanUtils> mockBeanUtils = Mockito.mockStatic(BeanUtils.class)) {
            
            filesMock.when(() -> Files.newInputStream(any(Path.class))).thenReturn(mockInputStream);
            
            // Mock BeanUtils.copy调用
            mockBeanUtils.when(() -> BeanUtils.copy(any(ScriptInfoDto.class), eq(ScriptInfoDto.class)))
                    .thenReturn(copiedInfoDto);
            mockBeanUtils.when(() -> BeanUtils.copy(any(InfoVersion.class), eq(ScriptVersionDto.class)))
                    .thenReturn(copiedScriptVersionDto);

            // 执行测试
            toProductServiceImplUnderTest.analysisFiles(scriptFiles, dto, attachmentList, dto1, srcUuidList);

        }
    }

    @Test
    @DisplayName("测试selectAttachmentById方法 - 根据附件ID查询附件")
    void testSelectAttachmentById() {
        // Setup - 创建测试数据
        Long attachmentId = 1L;
        
        // Mock ItsmProductAttachment
        ItsmProductAttachment mockAttachment = new ItsmProductAttachment();
        mockAttachment.setIid(attachmentId);
        mockAttachment.setAttachmentName("test.txt");
        mockAttachment.setAttachmentSize(1024L);
        mockAttachment.setAttachmentContent("test content".getBytes());
        mockAttachment.setSrcScriptUuid("uuid123");
        mockAttachment.setPublishInfoId(1001L);
        mockAttachment.setItsmProductInfoId(2001L);
        
        // Mock mapper调用
        when(itsmProductAttachmentMapper.selectAttachmentById(attachmentId)).thenReturn(mockAttachment);
        
        // 执行测试方法
        ItsmProductAttachment result = toProductServiceImplUnderTest.selectAttachmentById(attachmentId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(attachmentId, result.getIid());
        assertEquals("test.txt", result.getAttachmentName());
        assertEquals(1024L, result.getAttachmentSize());
        assertEquals("test content", new String(result.getAttachmentContent()));
        assertEquals("uuid123", result.getSrcScriptUuid());
        assertEquals(1001L, result.getPublishInfoId());
        assertEquals(2001L, result.getItsmProductInfoId());
        
        // 验证mapper调用
        verify(itsmProductAttachmentMapper).selectAttachmentById(attachmentId);
    }
}
