package com.ideal.script.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.ScriptTemplateMapper;
import com.ideal.script.model.dto.ScriptTemplateDto;
import com.ideal.script.model.entity.ScriptTemplate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ScriptTemplateServiceImplTest {

    @Mock
    private ScriptTemplateMapper mockScriptTemplateMapper;

    private ScriptTemplateServiceImpl scriptTemplateServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        scriptTemplateServiceImplUnderTest = new ScriptTemplateServiceImpl(mockScriptTemplateMapper);
    }

    @Test
    void testSelectScriptTemplateList() {
        // Setup
        final ScriptTemplateDto scriptTemplateDto = new ScriptTemplateDto();
        scriptTemplateDto.setId(0L);
        scriptTemplateDto.setName("name");
        scriptTemplateDto.setScriptType("scriptType");
        scriptTemplateDto.setContent("content");
        scriptTemplateDto.setCreatorId(0L);

        // Configure ScriptTemplateMapper.selectScriptTemplateList(...).
        final ScriptTemplate scriptTemplate = new ScriptTemplate();
        scriptTemplate.setId(0L);
        scriptTemplate.setName("name");
        scriptTemplate.setScriptType("scriptType");
        scriptTemplate.setContent("content");
        scriptTemplate.setCreatorId(0L);

        Page<ScriptTemplate> page =  new Page<>();
        page.add(scriptTemplate);
        when(mockScriptTemplateMapper.selectScriptTemplateList(any(ScriptTemplate.class)))
                .thenReturn(page);

        // Run the test
        final PageInfo<ScriptTemplateDto> result = scriptTemplateServiceImplUnderTest.selectScriptTemplateList(
                scriptTemplateDto, 0, 0);

        // Verify the results
        assertNotNull(result);
    }


    @Test
    void testInsertScriptTemplate() throws Exception {
        // Setup
        final ScriptTemplateDto scriptTemplateDto = new ScriptTemplateDto();
        scriptTemplateDto.setId(0L);
        scriptTemplateDto.setName("name");
        scriptTemplateDto.setScriptType("scriptType");
        scriptTemplateDto.setContent("content");
        scriptTemplateDto.setCreatorId(0L);

        List<ScriptTemplate> scriptTemplateList = new ArrayList<>();

        when(mockScriptTemplateMapper.getScriptTemplateByName("name")).thenReturn(scriptTemplateList);

        // Run the test
        scriptTemplateServiceImplUnderTest.insertScriptTemplate(scriptTemplateDto);

        // Verify the results
        verify(mockScriptTemplateMapper).insertScriptTemplate(any(ScriptTemplate.class));
    }

    @Test
    void testInsertScriptTemplate_checkScriptTemplateName_exception() throws Exception {
        // Setup
        final ScriptTemplateDto scriptTemplateDto = new ScriptTemplateDto();
        scriptTemplateDto.setId(0L);
        scriptTemplateDto.setName("name");
        scriptTemplateDto.setScriptType("scriptType");
        scriptTemplateDto.setContent("content");
        scriptTemplateDto.setCreatorId(0L);

        List<ScriptTemplate> scriptTemplateList = new ArrayList<>();
        ScriptTemplate scriptTemplate = new ScriptTemplate();
        scriptTemplate.setId(1L);
        scriptTemplateList.add(scriptTemplate);

        when(mockScriptTemplateMapper.getScriptTemplateByName("name")).thenReturn(scriptTemplateList);

        // Run the test
        assertThrows(ScriptException.class,()->{
            scriptTemplateServiceImplUnderTest.updateScriptTemplate(scriptTemplateDto);
        });
    }

    @Test
    void testUpdateScriptTemplate() throws Exception {
        // Setup
        final ScriptTemplateDto scriptTemplateDto = new ScriptTemplateDto();
        scriptTemplateDto.setId(0L);
        scriptTemplateDto.setName("name");
        scriptTemplateDto.setScriptType("scriptType");
        scriptTemplateDto.setContent("content");
        scriptTemplateDto.setCreatorId(0L);

        List<ScriptTemplate> scriptTemplateList = new ArrayList<>();

        when(mockScriptTemplateMapper.getScriptTemplateByName("name")).thenReturn(scriptTemplateList);

        // Run the test
        scriptTemplateServiceImplUnderTest.updateScriptTemplate(scriptTemplateDto);

        // Verify the results
        verify(mockScriptTemplateMapper).updateScriptTemplate(any(ScriptTemplate.class));
    }


    @Test
    void testUpdateScriptTemplate_checkScriptTemplateName_exception() throws Exception {
        // Setup
        final ScriptTemplateDto scriptTemplateDto = new ScriptTemplateDto();
        scriptTemplateDto.setId(0L);
        scriptTemplateDto.setName("name");
        scriptTemplateDto.setScriptType("scriptType");
        scriptTemplateDto.setContent("content");
        scriptTemplateDto.setCreatorId(0L);

        List<ScriptTemplate> scriptTemplateList = new ArrayList<>();
        ScriptTemplate scriptTemplate = new ScriptTemplate();
        scriptTemplate.setId(1L);
        scriptTemplateList.add(scriptTemplate);

        when(mockScriptTemplateMapper.getScriptTemplateByName("name")).thenReturn(scriptTemplateList);

        // Run the test
        assertThrows(ScriptException.class,()->{
            scriptTemplateServiceImplUnderTest.updateScriptTemplate(scriptTemplateDto);
        });


    }


    @Test
    void testDeleteScriptTemplateByIds() {
        // Setup
        // Run the test
        scriptTemplateServiceImplUnderTest.deleteScriptTemplateByIds(new Long[]{0L});

        // Verify the results
        verify(mockScriptTemplateMapper).deleteScriptTemplateByIds(any(Long[].class));
    }

    @Test
    void testGetScriptTemplateDetail() {
        // Setup
        // Configure ScriptTemplateMapper.getScriptTemplateDetail(...).
        final ScriptTemplate scriptTemplate = new ScriptTemplate();
        scriptTemplate.setId(0L);
        scriptTemplate.setName("name");
        scriptTemplate.setScriptType("scriptType");
        scriptTemplate.setContent("content");
        scriptTemplate.setCreatorId(0L);
        when(mockScriptTemplateMapper.getScriptTemplateDetail(0L)).thenReturn(scriptTemplate);

        // Run the test
        final ScriptTemplateDto result = scriptTemplateServiceImplUnderTest.getScriptTemplateDetail(0L);

        // Verify the results
        assertNotNull(result);
    }
}
