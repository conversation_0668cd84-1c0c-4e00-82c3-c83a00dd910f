package com.ideal.script.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.script.dto.ItsmChildrenDto;
import com.ideal.script.dto.ItsmPublishScriptAuditResultDto;
import com.ideal.script.dto.ItsmPublishScriptDto;
import com.ideal.script.dto.ScriptInfoDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.ToProductDto;
import com.ideal.script.model.dto.ToProductQueryDto;
import com.ideal.script.model.entity.ItsmProductAttachment;
import com.ideal.script.service.IToProductService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.ServletOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ToProductController单元测试
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@ExtendWith(MockitoExtension.class)
class ToProductControllerTest {

    @Mock
    private IToProductService toProductService;

    @InjectMocks
    private ToProductController toProductController;

    private ToProductQueryDto toProductQueryDto;
    private TableQueryDto<ToProductQueryDto> tableQueryDto;
    private ItsmPublishScriptDto itsmPublishScriptDto;
    private ItsmPublishScriptAuditResultDto itsmPublishScriptAuditResultDto;
    private ItsmProductAttachment itsmProductAttachment;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        toProductQueryDto = new ToProductQueryDto();
        toProductQueryDto.setId(1L);
        toProductQueryDto.setFileName("test.zip");
        toProductQueryDto.setDescription("测试描述");

        tableQueryDto = new TableQueryDto<>();
        tableQueryDto.setQueryParam(toProductQueryDto);
        tableQueryDto.setPageNum(1);
        tableQueryDto.setPageSize(10);

        itsmPublishScriptDto = new ItsmPublishScriptDto();
        itsmPublishScriptDto.setUniqueUuid(Arrays.asList("test-uuid"));
        itsmPublishScriptDto.setProductionDescription("测试投产描述");

        itsmPublishScriptAuditResultDto = new ItsmPublishScriptAuditResultDto();
        itsmPublishScriptAuditResultDto.setOrderNumber("ORDER123456");
        itsmPublishScriptAuditResultDto.setAuditState(1);
        itsmPublishScriptAuditResultDto.setAuditResultRemark("审核通过");

        itsmProductAttachment = new ItsmProductAttachment();
        itsmProductAttachment.setIid(1L);
        itsmProductAttachment.setAttachmentName("test.txt");
        itsmProductAttachment.setAttachmentContent("test content".getBytes());
    }

    @Test
    @DisplayName("测试list方法 - 正常情况")
    void testList() {
        // 准备测试数据
        List<ToProductDto> toProductDtoList = new ArrayList<>();
        ToProductDto toProductDto = new ToProductDto();
        toProductDto.setId(1L);
        toProductDto.setFileName("test.zip");
        toProductDtoList.add(toProductDto);

        PageInfo<ToProductDto> pageInfo = new PageInfo<>();
        pageInfo.setList(toProductDtoList);
        pageInfo.setTotal(1L);

        // Mock service方法
        when(toProductService.selectToProductList(any(ToProductQueryDto.class), anyInt(), anyInt()))
                .thenReturn(pageInfo);

        // 执行测试
        R<PageInfo<ToProductDto>> result = toProductController.list(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());

        // 验证service方法被调用
        verify(toProductService).selectToProductList(eq(toProductQueryDto), eq(1), eq(10));
    }

    @Test
    @DisplayName("测试publishItsm方法 - 正常情况")
    void testPublishItsm() throws Exception {
        // Mock service方法
        doNothing().when(toProductService).publishItsm(any(ItsmPublishScriptDto.class));

        // 执行测试
        R<Object> result = toProductController.publishItsm(itsmPublishScriptDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());

        // 验证service方法被调用
        verify(toProductService).publishItsm(eq(itsmPublishScriptDto));
    }

    @Test
    @DisplayName("测试publishItsm方法 - 异常情况")
    void testPublishItsm_Exception() throws Exception {
        // Mock service方法抛出异常
        doThrow(new ScriptException("测试异常")).when(toProductService).publishItsm(any(ItsmPublishScriptDto.class));

        // 执行测试并验证异常
        ScriptException exception = assertThrows(ScriptException.class,
                () -> toProductController.publishItsm(itsmPublishScriptDto));

        assertEquals("测试异常", exception.getMessage());

        // 验证service方法被调用
        verify(toProductService).publishItsm(eq(itsmPublishScriptDto));
    }

    @Test
    @DisplayName("测试testEnvironmentAuditResult方法 - 正常情况")
    void testTestEnvironmentAuditResult() throws ScriptException {
        // Mock service方法
        doNothing().when(toProductService).testEnvironmentAuditResult(any(ItsmPublishScriptAuditResultDto.class));

        // 执行测试
        R<Object> result = toProductController.testEnvironmentAuditResult(itsmPublishScriptAuditResultDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("审批成功", result.getMessage());

        // 验证service方法被调用
        verify(toProductService).testEnvironmentAuditResult(eq(itsmPublishScriptAuditResultDto));
    }

    @Test
    @DisplayName("测试productEnvironmentAuditResult方法 - 正常情况")
    void testProductEnvironmentAuditResult() throws ScriptException, IOException {
        // Mock service方法
        doNothing().when(toProductService).productEnvironmentAuditResult(any(ItsmPublishScriptAuditResultDto.class));

        // 执行测试
        R<Object> result = toProductController.productEnvironmentAuditResult(itsmPublishScriptAuditResultDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("审批成功", result.getMessage());

        // 验证service方法被调用
        verify(toProductService).productEnvironmentAuditResult(eq(itsmPublishScriptAuditResultDto));
    }

    @Test
    @DisplayName("测试productEnvironmentAuditResult方法 - ScriptException异常")
    void testProductEnvironmentAuditResult_ScriptException() throws ScriptException, IOException {
        // Mock service方法抛出ScriptException
        doThrow(new ScriptException("测试异常")).when(toProductService).productEnvironmentAuditResult(any(ItsmPublishScriptAuditResultDto.class));

        // 执行测试并验证异常
        ScriptException exception = assertThrows(ScriptException.class,
                () -> toProductController.productEnvironmentAuditResult(itsmPublishScriptAuditResultDto));

        assertEquals("测试异常", exception.getMessage());

        // 验证service方法被调用
        verify(toProductService).productEnvironmentAuditResult(eq(itsmPublishScriptAuditResultDto));
    }

    @Test
    @DisplayName("测试productEnvironmentAuditResult方法 - IOException异常")
    void testProductEnvironmentAuditResult_IOException() throws ScriptException, IOException {
        // Mock service方法抛出IOException
        doThrow(new IOException("IO异常")).when(toProductService).productEnvironmentAuditResult(any(ItsmPublishScriptAuditResultDto.class));

        // 执行测试并验证异常
        IOException exception = assertThrows(IOException.class,
                () -> toProductController.productEnvironmentAuditResult(itsmPublishScriptAuditResultDto));

        assertEquals("IO异常", exception.getMessage());

        // 验证service方法被调用
        verify(toProductService).productEnvironmentAuditResult(eq(itsmPublishScriptAuditResultDto));
    }

    @Test
    @DisplayName("测试getChildrenDataList方法 - 正常情况")
    void testGetChildrenDataList() throws ScriptException {
        // 准备测试数据
        List<ItsmChildrenDto> childrenDataList = new ArrayList<>();
        ItsmChildrenDto child1 = new ItsmChildrenDto();
        child1.setIid(1L);
        childrenDataList.add(child1);

        // Mock service方法
        when(toProductService.getChildrenDataList(any(ItsmPublishScriptDto.class)))
                .thenReturn(childrenDataList);

        // 执行测试
        R<Object> result = toProductController.getChildrenDataList(itsmPublishScriptDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());

        // 验证service方法被调用
        verify(toProductService).getChildrenDataList(eq(itsmPublishScriptDto));
    }


    @Test
    @DisplayName("测试itsmProductDetails方法 - 正常情况")
    void testItsmProductDetails() throws ScriptException {
        // 准备测试数据
        ScriptInfoDto productDetails = new ScriptInfoDto();
        productDetails.setId(1L);
        productDetails.setScriptName("测试脚本");

        // Mock service方法
        when(toProductService.itsmProductDetails(any(ItsmPublishScriptDto.class)))
                .thenReturn(productDetails);

        // 执行测试
        R<Object> result = toProductController.itsmProductDetails(itsmPublishScriptDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());

        // 验证service方法被调用
        verify(toProductService).itsmProductDetails(eq(itsmPublishScriptDto));
    }

    @Test
    @DisplayName("测试itsmProductDetails方法 - 异常情况")
    void testItsmProductDetails_Exception() throws ScriptException {
        // Mock service方法抛出异常
        when(toProductService.itsmProductDetails(any(ItsmPublishScriptDto.class)))
                .thenThrow(new ScriptException("测试异常"));

        // 执行测试并验证异常
        ScriptException exception = assertThrows(ScriptException.class,
                () -> toProductController.itsmProductDetails(itsmPublishScriptDto));

        assertEquals("测试异常", exception.getMessage());

        // 验证service方法被调用
        verify(toProductService).itsmProductDetails(eq(itsmPublishScriptDto));
    }

    @Test
    @DisplayName("测试promotionProducts方法 - 正常情况")
    void testPromotionProducts() throws ScriptException {
        // Mock service方法
        doNothing().when(toProductService).promotionProducts(any(ItsmPublishScriptDto.class));

        // 执行测试
        R<Object> result = toProductController.promotionProducts(itsmPublishScriptDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());

        // 验证service方法被调用
        verify(toProductService).promotionProducts(eq(itsmPublishScriptDto));
    }

    @Test
    @DisplayName("测试promotionProducts方法 - 异常情况")
    void testPromotionProducts_Exception() throws ScriptException {
        // Mock service方法抛出异常
        doThrow(new ScriptException("测试异常")).when(toProductService).promotionProducts(any(ItsmPublishScriptDto.class));

        // 执行测试并验证异常
        ScriptException exception = assertThrows(ScriptException.class,
                () -> toProductController.promotionProducts(itsmPublishScriptDto));

        assertEquals("测试异常", exception.getMessage());

        // 验证service方法被调用
        verify(toProductService).promotionProducts(eq(itsmPublishScriptDto));
    }

    @Test
    @DisplayName("测试downloadAttachment方法 - 正常情况")
    void testDownloadAttachment() throws IOException {
        // Mock service方法
        when(toProductService.selectAttachmentById(anyLong()))
                .thenReturn(itsmProductAttachment);

        // Mock ServletOutputStream
        MockHttpServletResponse response = new MockHttpServletResponse();

        // 执行测试
        toProductController.downloadAttachment(1L, response);

        // 验证结果
        assertEquals(200, response.getStatus());

        // 验证service方法被调用
        verify(toProductService).selectAttachmentById(eq(1L));
    }

    @Test
    @DisplayName("测试downloadAttachment方法 - 附件为null")
    void testDownloadAttachment_NullAttachment() {
        ItsmProductAttachment itsmProductAttachment1 = new ItsmProductAttachment();
        itsmProductAttachment1.setAttachmentName("test.txt");
        itsmProductAttachment1.setAttachmentContent("test".getBytes());
        when(toProductService.selectAttachmentById(anyLong()))
                .thenReturn(itsmProductAttachment1);

        // Mock ServletOutputStream
        MockHttpServletResponse response = new MockHttpServletResponse();
        response.setContentType("application/octet-stream");
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-disposition", "attachment; filename=test.txt");

        // 执行测试
        toProductController.downloadAttachment(1L, response);

        // 验证结果 - 应该抛出NullPointerException
        assertEquals(200, response.getStatus());

        // 验证service方法被调用
        verify(toProductService).selectAttachmentById(eq(1L));
    }

    @Test
    @DisplayName("测试downloadAttachment方法 - IOException异常情况")
    void testDownloadAttachment_IOException() throws IOException {
        // Mock service方法返回附件
        when(toProductService.selectAttachmentById(anyLong()))
                .thenReturn(itsmProductAttachment);

        // Mock ServletOutputStream抛出IOException
        MockHttpServletResponse response = new MockHttpServletResponse();
        // 创建一个会抛出IOException的ServletOutputStream
        ServletOutputStream mockOutputStream = Mockito.mock(ServletOutputStream.class);
        doThrow(new IOException("模拟IO异常")).when(mockOutputStream).write(any(byte[].class));
        
        // 使用反射设置response的outputStream
        try {
            Field outputStreamField = MockHttpServletResponse.class.getDeclaredField("outputStream");
            outputStreamField.setAccessible(true);
            outputStreamField.set(response, mockOutputStream);
        } catch (Exception e) {
            // 如果反射失败，使用其他方式模拟
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding("UTF-8");
        }

        // 执行测试
        toProductController.downloadAttachment(1L, response);

        // 验证结果 - 当发生IOException时，应该设置状态码为400
        assertEquals(400, response.getStatus());

        // 验证service方法被调用
        verify(toProductService).selectAttachmentById(eq(1L));
    }

}