package com.ideal.script.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.script.model.dto.TaskStatementDto;
import com.ideal.script.service.ITaskStatementService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * TaskStatementController单元测试类
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class TaskStatementControllerTest {

    @Mock
    private ITaskStatementService taskStatementService;

    @InjectMocks
    private TaskStatementController taskStatementController;

    private TaskStatementDto taskStatementDto;
    private TableQueryDto<TaskStatementDto> tableQueryDto;
    private PageInfo<TaskStatementDto> pageInfo;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        taskStatementDto = new TaskStatementDto();
        taskStatementDto.setKey("task123@instance456@*************@8080");
        taskStatementDto.setScriptNameZh("测试脚本");
        taskStatementDto.setScriptName("test_script");
        taskStatementDto.setCategoryPath("/root/test");
        taskStatementDto.setCreatorName("测试创建人");
        taskStatementDto.setStartUser("测试执行人");
        taskStatementDto.setAuditUser("测试审核人");
        taskStatementDto.setAgentIp("*************");
        taskStatementDto.setCenterName("测试中心");
        taskStatementDto.setCategoryId(1L);
        taskStatementDto.setStartTime(new Timestamp(System.currentTimeMillis()));

        tableQueryDto = new TableQueryDto<>();
        tableQueryDto.setPageNum(1);
        tableQueryDto.setPageSize(10);
        tableQueryDto.setQueryParam(taskStatementDto);

        pageInfo = new PageInfo<>();
        pageInfo.setList(Arrays.asList(taskStatementDto));
        pageInfo.setTotal(1);
        pageInfo.setPageNum(1);
        pageInfo.setPageSize(10);
    }

    @Test
    @DisplayName("查询任务报表列表-成功")
    void scriptStatementList_success() {
        // Mock service方法
        doReturn(pageInfo).when(taskStatementService).selectTaskStatementPage(any(TaskStatementDto.class), eq(1), eq(10));

        // 执行测试方法
        R<PageInfo<TaskStatementDto>> result = taskStatementController.scriptStatementList(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("list.success", result.getMessage());
        assertEquals(pageInfo, result.getData());
        assertEquals(1, result.getData().getTotal());
        assertEquals(1, result.getData().getList().size());
        assertEquals("测试脚本", result.getData().getList().get(0).getScriptNameZh());
        assertEquals("test_script", result.getData().getList().get(0).getScriptName());
        assertEquals("*************", result.getData().getList().get(0).getAgentIp());
        
        // 验证service方法被调用
        verify(taskStatementService, times(1)).selectTaskStatementPage(any(TaskStatementDto.class), eq(1), eq(10));
    }

    @Test
    @DisplayName("查询任务报表列表-空结果")
    void scriptStatementList_emptyResult() {
        // 创建空的分页结果
        PageInfo<TaskStatementDto> emptyPageInfo = new PageInfo<>();
        emptyPageInfo.setList(Collections.emptyList());
        emptyPageInfo.setTotal(0);
        emptyPageInfo.setPageNum(1);
        emptyPageInfo.setPageSize(10);

        // Mock service方法
        doReturn(emptyPageInfo).when(taskStatementService).selectTaskStatementPage(any(TaskStatementDto.class), eq(1), eq(10));

        // 执行测试方法
        R<PageInfo<TaskStatementDto>> result = taskStatementController.scriptStatementList(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("list.success", result.getMessage());
        assertEquals(emptyPageInfo, result.getData());
        assertEquals(0, result.getData().getTotal());
        assertTrue(result.getData().getList().isEmpty());
        
        // 验证service方法被调用
        verify(taskStatementService, times(1)).selectTaskStatementPage(any(TaskStatementDto.class), eq(1), eq(10));
    }

    @Test
    @DisplayName("查询任务报表列表-空查询参数")
    void scriptStatementList_nullQueryParam() {
        // 设置空查询参数
        tableQueryDto.setQueryParam(null);

        // Mock service方法
        doReturn(pageInfo).when(taskStatementService).selectTaskStatementPage(any(), eq(1), eq(10));

        // 执行测试方法
        R<PageInfo<TaskStatementDto>> result = taskStatementController.scriptStatementList(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("list.success", result.getMessage());
        assertEquals(pageInfo, result.getData());
        
        // 验证service方法被调用
        verify(taskStatementService, times(1)).selectTaskStatementPage(any(), eq(1), eq(10));
    }

    @Test
    @DisplayName("查询任务报表列表-分页参数测试")
    void scriptStatementList_paginationTest() {
        // 设置不同的分页参数
        tableQueryDto.setPageNum(2);
        tableQueryDto.setPageSize(20);

        // 创建对应的分页结果
        PageInfo<TaskStatementDto> customPageInfo = new PageInfo<>();
        customPageInfo.setList(Arrays.asList(taskStatementDto));
        customPageInfo.setTotal(1);
        customPageInfo.setPageNum(2);
        customPageInfo.setPageSize(20);

        // Mock service方法
        doReturn(customPageInfo).when(taskStatementService).selectTaskStatementPage(any(TaskStatementDto.class), eq(2), eq(20));

        // 执行测试方法
        R<PageInfo<TaskStatementDto>> result = taskStatementController.scriptStatementList(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("list.success", result.getMessage());
        assertEquals(customPageInfo, result.getData());
        assertEquals(2, result.getData().getPageNum());
        assertEquals(20, result.getData().getPageSize());
        
        // 验证service方法被调用
        verify(taskStatementService, times(1)).selectTaskStatementPage(any(TaskStatementDto.class), eq(2), eq(20));
    }

    @Test
    @DisplayName("查询任务报表列表-多条记录")
    void scriptStatementList_multipleRecords() {
        // 创建多条记录
        TaskStatementDto taskStatementDto2 = new TaskStatementDto();
        taskStatementDto2.setKey("task789@instance012@*************@9090");
        taskStatementDto2.setScriptNameZh("测试脚本2");
        taskStatementDto2.setScriptName("test_script2");
        taskStatementDto2.setCategoryPath("/root/test2");
        taskStatementDto2.setCreatorName("测试创建人2");
        taskStatementDto2.setStartUser("测试执行人2");
        taskStatementDto2.setAuditUser("测试审核人2");
        taskStatementDto2.setAgentIp("*************");
        taskStatementDto2.setCenterName("测试中心2");

        // 创建包含多条记录的分页结果
        PageInfo<TaskStatementDto> multiPageInfo = new PageInfo<>();
        multiPageInfo.setList(Arrays.asList(taskStatementDto, taskStatementDto2));
        multiPageInfo.setTotal(2);
        multiPageInfo.setPageNum(1);
        multiPageInfo.setPageSize(10);

        // Mock service方法
        doReturn(multiPageInfo).when(taskStatementService).selectTaskStatementPage(any(TaskStatementDto.class), eq(1), eq(10));

        // 执行测试方法
        R<PageInfo<TaskStatementDto>> result = taskStatementController.scriptStatementList(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("list.success", result.getMessage());
        assertEquals(multiPageInfo, result.getData());
        assertEquals(2, result.getData().getTotal());
        assertEquals(2, result.getData().getList().size());
        assertEquals("测试脚本", result.getData().getList().get(0).getScriptNameZh());
        assertEquals("测试脚本2", result.getData().getList().get(1).getScriptNameZh());
        
        // 验证service方法被调用
        verify(taskStatementService, times(1)).selectTaskStatementPage(any(TaskStatementDto.class), eq(1), eq(10));
    }

    @Test
    @DisplayName("查询任务报表列表-service抛出异常")
    void scriptStatementList_serviceException() {
        // Mock service方法抛出异常
        doThrow(new RuntimeException("Database connection failed")).when(taskStatementService)
                .selectTaskStatementPage(any(TaskStatementDto.class), eq(1), eq(10));

        // 执行测试方法并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            taskStatementController.scriptStatementList(tableQueryDto);
        });

        // 验证异常信息
        assertEquals("Database connection failed", exception.getMessage());
        
        // 验证service方法被调用
        verify(taskStatementService, times(1)).selectTaskStatementPage(any(TaskStatementDto.class), eq(1), eq(10));
    }

    @Test
    @DisplayName("查询任务报表列表-BeanUtils.copy处理null参数")
    void scriptStatementList_beanUtilsCopyWithNull() {
        // 设置空查询参数，测试BeanUtils.copy的处理
        tableQueryDto.setQueryParam(null);

        // Mock service方法 - BeanUtils.copy(null, TaskStatementDto.class)会创建一个新的TaskStatementDto对象
        doReturn(pageInfo).when(taskStatementService).selectTaskStatementPage(any(TaskStatementDto.class), eq(1), eq(10));

        // 执行测试方法
        R<PageInfo<TaskStatementDto>> result = taskStatementController.scriptStatementList(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("list.success", result.getMessage());
        assertEquals(pageInfo, result.getData());

        // 验证service方法被调用
        verify(taskStatementService, times(1)).selectTaskStatementPage(any(TaskStatementDto.class), eq(1), eq(10));
    }

    @Test
    @DisplayName("查询任务报表列表-验证分类ID过滤")
    void scriptStatementList_categoryIdFilter() {
        // 设置特定的分类ID
        taskStatementDto.setCategoryId(999L);
        taskStatementDto.setCategoryPath("/special/category");

        // Mock service方法
        doReturn(pageInfo).when(taskStatementService).selectTaskStatementPage(any(TaskStatementDto.class), eq(1), eq(10));

        // 执行测试方法
        R<PageInfo<TaskStatementDto>> result = taskStatementController.scriptStatementList(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("list.success", result.getMessage());
        assertEquals(pageInfo, result.getData());
        
        // 验证service方法被调用
        verify(taskStatementService, times(1)).selectTaskStatementPage(any(TaskStatementDto.class), eq(1), eq(10));
    }

    @Test
    @DisplayName("查询任务报表列表-验证时间范围过滤")
    void scriptStatementList_timeRangeFilter() {
        // 设置时间范围
        Timestamp startTime = new Timestamp(System.currentTimeMillis() - 86400000); // 1天前
        Timestamp endTime = new Timestamp(System.currentTimeMillis());
        taskStatementDto.setStartTimeRange(Arrays.asList(startTime, endTime));

        // Mock service方法
        doReturn(pageInfo).when(taskStatementService).selectTaskStatementPage(any(TaskStatementDto.class), eq(1), eq(10));

        // 执行测试方法
        R<PageInfo<TaskStatementDto>> result = taskStatementController.scriptStatementList(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals("list.success", result.getMessage());
        assertEquals(pageInfo, result.getData());
        
        // 验证service方法被调用
        verify(taskStatementService, times(1)).selectTaskStatementPage(any(TaskStatementDto.class), eq(1), eq(10));
    }
}
