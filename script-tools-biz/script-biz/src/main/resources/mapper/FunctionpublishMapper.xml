<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.FunctionpublishMapper">

    <resultMap type="com.ideal.script.model.entity.Functionpublish" id="FunctionpublishResult">
            <result property="id" column="iid"/>
            <result property="name" column="iname"/>
            <result property="languagetype" column="ilanguagetype"/>
            <result property="desc" column="idesc"/>
            <result property="attribute" column="iattribute"/>
            <result property="status" column="istatus"/>
            <result property="global" column="iglobal"/>
            <result property="userglobal" column="iuserglobal"/>
            <result property="classid" column="iclassid"/>
            <result property="functionMd" column="ifunction_md"/>
            <result property="creatorId" column="icreator_id"/>
            <result property="creatorName" column="icreator_name"/>
            <result property="updatorId" column="iupdator_id"/>
            <result property="updatorName" column="iupdator_name"/>
            <result property="createTime" column="icreate_time"/>
            <result property="updateTime" column="iupdate_time"/>
    </resultMap>

    <sql id="selectFunctionpublishVo">
        select iid, iname, ilanguagetype, idesc, iattribute, istatus, iglobal, iuserglobal, iclassid, ifunction_md, icreator_id, icreator_name, iupdator_id, iupdator_name, icreate_time, iupdate_time
        from ieai_script_functionpublish
    </sql>

    <select id="selectFunctionpublishList" parameterType="com.ideal.script.model.entity.Functionpublish" resultMap="FunctionpublishResult">
        <include refid="selectFunctionpublishVo"/>
        <where>
                        <if test="name != null  and name != ''">
                            and iname like concat('%', #{name}, '%')
                        </if>
                        <if test="languagetype != null ">
                            and ilanguagetype = #{languagetype}
                        </if>
                        <if test="desc != null  and desc != ''">
                            and idesc = #{desc}
                        </if>
                        <if test="attribute != null ">
                            and iattribute = #{attribute}
                        </if>
                        <if test="status != null ">
                            and istatus = #{status}
                        </if>
                        <if test="global != null ">
                            and iglobal = #{global}
                        </if>
                        <if test="userglobal != null ">
                            and iuserglobal = #{userglobal}
                        </if>
                        <if test="classid != null ">
                            and iclassid = #{classid}
                        </if>
                        <if test="functionMd != null  and functionMd != ''">
                            and ifunction_md = #{functionMd}
                        </if>
                        <if test="creatorId != null ">
                            and icreator_id = #{creatorId}
                        </if>
                        <if test="creatorName != null  and creatorName != ''">
                            and icreator_name like concat('%', #{creatorName}, '%')
                        </if>
                        <if test="updatorId != null ">
                            and iupdator_id = #{updatorId}
                        </if>
                        <if test="updatorName != null  and updatorName != ''">
                            and iupdator_name like concat('%', #{updatorName}, '%')
                        </if>
        </where>
    </select>

    <select id="selectFunctionpublishById" parameterType="Long"
            resultMap="FunctionpublishResult">
    </select>

    <insert id="insertFunctionpublish" parameterType="com.ideal.script.model.entity.Functionpublish" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_script_functionpublish
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">iid,
                    </if>
                    <if test="name != null">iname,
                    </if>
                    <if test="languagetype != null">ilanguagetype,
                    </if>
                    <if test="desc != null">idesc,
                    </if>
                    <if test="attribute != null">iattribute,
                    </if>
                    <if test="status != null">istatus,
                    </if>
                    <if test="global != null">iglobal,
                    </if>
                    <if test="userglobal != null">iuserglobal,
                    </if>
                    <if test="classid != null">iclassid,
                    </if>
                    <if test="functionMd != null">ifunction_md,
                    </if>
                    <if test="creatorId != null">icreator_id,
                    </if>
                    <if test="creatorName != null">icreator_name,
                    </if>
                    <if test="updatorId != null">iupdator_id,
                    </if>
                    <if test="updatorName != null">iupdator_name,
                    </if>
                    icreate_time,
                    iupdate_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},
                    </if>
                    <if test="name != null">#{name},
                    </if>
                    <if test="languagetype != null">#{languagetype},
                    </if>
                    <if test="desc != null">#{desc},
                    </if>
                    <if test="attribute != null">#{attribute},
                    </if>
                    <if test="status != null">#{status},
                    </if>
                    <if test="global != null">#{global},
                    </if>
                    <if test="userglobal != null">#{userglobal},
                    </if>
                    <if test="classid != null">#{classid},
                    </if>
                    <if test="functionMd != null">#{functionMd},
                    </if>
                    <if test="creatorId != null">#{creatorId},
                    </if>
                    <if test="creatorName != null">#{creatorName},
                    </if>
                    <if test="updatorId != null">#{updatorId},
                    </if>
                    <if test="updatorName != null">#{updatorName},
                    </if>
                    ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                    ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
    </insert>

    <update id="updateFunctionpublish" parameterType="com.ideal.script.model.entity.Functionpublish">
        update ieai_script_functionpublish
        <trim prefix="SET" suffixOverrides=",">
                    <if test="name != null">iname =
                        #{name},
                    </if>
                    <if test="languagetype != null">ilanguagetype =
                        #{languagetype},
                    </if>
                    <if test="desc != null">idesc =
                        #{desc},
                    </if>
                    <if test="attribute != null">iattribute =
                        #{attribute},
                    </if>
                    <if test="status != null">istatus =
                        #{status},
                    </if>
                    <if test="global != null">iglobal =
                        #{global},
                    </if>
                    <if test="userglobal != null">iuserglobal =
                        #{userglobal},
                    </if>
                    <if test="classid != null">iclassid =
                        #{classid},
                    </if>
                    <if test="functionMd != null">ifunction_md =
                        #{functionMd},
                    </if>
                    <if test="creatorId != null">icreator_id =
                        #{creatorId},
                    </if>
                    <if test="creatorName != null">icreator_name =
                        #{creatorName},
                    </if>
                    <if test="updatorId != null">iupdator_id =
                        #{updatorId},
                    </if>
                    <if test="updatorName != null">iupdator_name =
                        #{updatorName},
                    </if>
                    icreate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                    iupdate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
        where iid = #{id}
    </update>

    <delete id="deleteFunctionpublishById" parameterType="Long">
        delete
        from ieai_script_functionpublish where iid = #{id}
    </delete>

    <delete id="deleteFunctionpublishByIds" parameterType="String">
        delete from ieai_script_functionpublish where iid in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectFunctionpublishListForScriptEdit" parameterType="com.ideal.script.model.entity.VarAndFuncForEdit"
            resultMap="FunctionpublishResult">
        select f.iid, f.iname, f.ilanguagetype, f.idesc, f.iattribute
        from ieai_script_functionpublish f,
        (select max(iid) as maxIid, iname
        from ieai_script_functionpublish
        group by iname) a
        where iid = a.maxIid
        <trim suffixOverrides="and">
            <if test="bindState > 0 and bindIds.length > 0">
                and (
                <if test="bindState == 1">
                    iid in
                </if>
                <if test="bindState == 2">
                    iid not in
                </if>
                <foreach item="id" collection="bindIds" open="(" separator="," close=")">
                    #{id}
                </foreach>
                <if test="bindState == 1">
                    or iattribute = 2
                </if>
                <if test="bindState == 2">
                    and iattribute != 2
                </if>
                )
            </if>
            <if test="keyword != null and keyword != '' and keyword != 'null'">
                and (upper(f.iname) like concat('%', upper(#{keyword}), '%') or upper(f.idesc) like concat('%', upper(#{keyword}), '%'))
            </if>
        </trim>
    </select>
</mapper>