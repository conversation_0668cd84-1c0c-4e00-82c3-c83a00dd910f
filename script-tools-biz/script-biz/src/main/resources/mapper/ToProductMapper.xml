<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.ToProductMapper">

    <resultMap type="com.ideal.script.model.entity.ToProductEntity" id="ToProductResult">
            <result property="id" column="iid"/>
            <result property="fileName" column="ifile_name"/>
            <result property="description" column="idescription"/>
            <result property="dateTime" column="idate_time"/>
            <result property="userId" column="iuser_id"/>
            <result property="userName" column="iuser_name"/>
            <result property="scriptUserId" column="iscript_user_id"/>
            <result property="scriptUserName" column="iscript_user_name"/>
            <result property="orderNumber" column="iorder_number"/>
            <result property="productState" column="iaudit_state"/>
    </resultMap>

    <sql id="selectToProduct">
        select iid, ifile_name, idescription, idate_time, iuser_id,iuser_name, iscript_user_id, iscript_user_name
        from ieai_script_to_product
    </sql>

    <select id="selectToProductList" parameterType="com.ideal.script.model.entity.ToProductEntity" resultMap="ToProductResult">
        SELECT
        a.iid,
        a.ifile_name,
        a.idescription,
        a.idate_time,
        a.iuser_id,
        a.iuser_name,
        a.iscript_user_id,
        a.iscript_user_name,
        a.iorder_number,
        a.iaudit_state
        FROM
        ieai_script_to_product a
        <where>
            <if test="fileName != null  and fileName != ''">
                and a.ifile_name like concat('%', #{fileName}, '%')
            </if>
            <if test="description != null  and description != ''">
                and a.idescription = #{description}
            </if>
            <!--<if test="dateTime != null ">
                and idate_time = #{dateTime}
            </if>-->
              <!-- 添加新的开始时间条件 -->
            <if test="dateTimeStart != null">
                and a.idate_time &gt;= #{startTime}
            </if>
            <!-- 添加新的结束时间条件 -->
            <if test="dateTimeEnd != null">
                and a.idate_time &lt;= #{endTime}
            </if>

            <if test="userId != null ">
                and a.iuser_id = #{userId}
            </if>
             <if test="userName != null  and userName != ''">
                and a.iuser_name like concat('%', #{userName}, '%')
            </if>
            <if test="scriptUserId != null ">
                and a.iscript_user_id = #{scriptUserId}
            </if>
            <if test="scriptUserName != null  and scriptUserName != ''">
                and a.iscript_user_name like concat('%', #{scriptUserName}, '%')
            </if>
            <if test="productState != null ">
                and a.iaudit_state = #{productState}
            </if>
            <if test="orderNumber != null  and orderNumber != ''">
                and a.iorder_number like concat('%', #{orderNumber}, '%')
            </if>
        </where>
        order by iid desc
    </select>

    <select id="selectToProductById" parameterType="Long"
            resultMap="ToProductResult">
            <include refid="selectToProduct"/>
            where iid = #{id}
    </select>

    <insert id="insertToProduct" parameterType="com.ideal.script.model.entity.ToProductEntity" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_script_to_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">iid,
                    </if>
                    <if test="fileName != null and fileName != ''">ifile_name,
                    </if>
                    <if test="description != null and description != ''">idescription,
                    </if>
                    idate_time,
                    <if test="userId != null">iuser_id,
                    </if>
                     <if test="userName != null">iuser_name,
                    </if>
                    <if test="scriptUserId != null">iscript_user_id,
                    </if>
                    <if test="scriptUserName != null">iscript_user_name,
                    </if>
                    <if test="orderNumber != null and orderNumber != ''">iorder_number,
                    </if>
                    <if test="productState != null">iaudit_state,
                    </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},
                    </if>
                    <if test="fileName != null and fileName != ''">#{fileName},
                    </if>
                    <if test="description != null and description != ''">#{description},
                    </if>
                     ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                    <if test="userId != null">#{userId},
                    </if>
                     <if test="userName != null">#{userName},
                    </if>
                    <if test="scriptUserId != null">#{scriptUserId},
                    </if>
                    <if test="scriptUserName != null">#{scriptUserName},
                    </if>
                    <if test="orderNumber != null and orderNumber != ''">#{orderNumber},
                    </if>
                    <if test="productState != null">#{productState},
                    </if>
        </trim>
    </insert>

    <update id="updateToProduct" parameterType="com.ideal.script.model.entity.ToProductEntity">
        update ieai_script_to_product
        <trim prefix="SET" suffixOverrides=",">
                    <if test="fileName != null and fileName != ''">ifile_name =
                        #{fileName},
                    </if>
                    <if test="description != null and description != ''">idescription =
                        #{description},
                    </if>
                    <if test="dateTime != null">idate_time =
                        #{dateTime},
                    </if>
                    <if test="userId != null">iuser_id =
                        #{userId},
                    </if>
                    <if test="userName != null">iuser_name =
                        #{userName},
                    </if>
                    <if test="scriptUserId != null">iscript_user_id =
                        #{scriptUserId},
                    </if>
                    <if test="scriptUserName != null">iscript_user_name =
                        #{scriptUserName},
                    </if>
                    <if test="productState != null">iaudit_state =
                        #{productState},
                    </if>
                    <if test="orderNumber != null and orderNumber != ''">iorder_number =
                        #{orderNumber},
                    </if>
        </trim>
        where iid = #{id}
    </update>

    <update id="updateToProductByOrderNumber" parameterType="com.ideal.script.model.entity.ToProductEntity">
        update ieai_script_to_product
        <trim prefix="SET" suffixOverrides=",">
            <if test="fileName != null and fileName != ''">ifile_name =
                #{fileName},
            </if>
            <if test="description != null and description != ''">idescription =
                #{description},
            </if>
            <if test="dateTime != null">idate_time =
                #{dateTime},
            </if>
            <if test="userId != null">iuser_id =
                #{userId},
            </if>
            <if test="userName != null">iuser_name =
                #{userName},
            </if>
            <if test="scriptUserId != null">iscript_user_id =
                #{scriptUserId},
            </if>
            <if test="scriptUserName != null">iscript_user_name =
                #{scriptUserName},
            </if>
            <if test="productState != null">iaudit_state =
                #{productState},
            </if>
        </trim>
        where iorder_number = #{orderNumber}
    </update>

    <delete id="deleteToProductById" parameterType="Long">
        delete
        from ieai_script_to_product where iid = #{id}
    </delete>

    <delete id="deleteToProductByIds" parameterType="String">
        delete from ieai_script_to_product where iid in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>