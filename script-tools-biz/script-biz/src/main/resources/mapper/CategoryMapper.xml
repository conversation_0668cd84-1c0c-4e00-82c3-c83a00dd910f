<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.CategoryMapper">

    <resultMap type="com.ideal.script.model.entity.Category" id="CategoryResult">
            <result property="id" column="iid"/>
            <result property="parentId" column="iparent_id"/>
            <result property="code" column="icode"/>
            <result property="name" column="iname"/>
            <result property="sort" column="isort"/>
            <result property="level" column="ilevel"/>
            <result property="description" column="idescription"/>
            <result property="creatorId" column="icreator_id"/>
            <result property="creatorName" column="icreator_name"/>
            <result property="updatorId" column="iupdator_id"/>
            <result property="updatorName" column="iupdator_name"/>
            <result property="createTime" column="icreate_time"/>
            <result property="updateTime" column="iupdate_time"/>
            <result property="icon" column="iicon_name" />
    </resultMap>

    <resultMap id="CategoryOrgResult" type="com.ideal.script.model.bean.OrgBean">
        <result property="code" column="isys_org_code"/>
        <result property="orgId" column="iorg_id"/>
        <result property="id" column="iid"/>
        <result property="categoryId" column="icategory_id"/>
    </resultMap>

    <resultMap id="CategoryUserResult" type="com.ideal.script.model.bean.UserBean">
        <result property="id" column="iid"/>
        <result property="loginName" column="ilogin_name"/>
        <result property="userId" column="iuser_id"/>
    </resultMap>

    <resultMap id="CategoryRoleResult" type="com.ideal.script.model.bean.CategoryRoleBean">
        <result property="id" column="iid"/>
        <result property="categoryId" column="icategory_id"/>
        <result property="roleId" column="irole_id"/>
    </resultMap>


    <sql id="selectCategoryVo">
        select iid, iparent_id, icode, iname, isort, ilevel, idescription, icreator_id,
        icreator_name, iupdator_id, iupdator_name, icreate_time, iupdate_time,iicon_name
        from ieai_script_category
    </sql>

    <select id="selectCategoryList" parameterType="com.ideal.script.model.entity.Category" resultMap="CategoryResult">
        <include refid="selectCategoryVo"/>
        <where>
                        <if test="id != null ">
                            and iid = #{id}
                        </if>
                        <if test="parentId != null ">
                            and iparent_id = #{parentId}
                        </if>
                        <if test="code != null ">
                            and icode = #{code}
                        </if>
                        <if test="name != null  and name != ''">
                            and iname like concat('%', #{name}, '%')
                        </if>
                        <if test="sort != null ">
                            and isort = #{sort}
                        </if>
                        <if test="level != null ">
                            and ilevel = #{level}
                        </if>
                        <if test="description != null  and description != ''">
                            and idescription = #{description}
                        </if>
                        <if test="creatorId != null ">
                            and icreator_id = #{creatorId}
                        </if>
                        <if test="creatorName != null  and creatorName != ''">
                            and icreator_name like concat('%', #{creatorName}, '%')
                        </if>
                        <if test="updatorId != null ">
                            and iupdator_id = #{updatorId}
                        </if>
                        <if test="updatorName != null  and updatorName != ''">
                            and iupdator_name like concat('%', #{updatorName}, '%')
                        </if>
        </where>
        order by iid desc
    </select>

    <select id="selectCategoryById" parameterType="Long"
            resultMap="CategoryResult">
               <include refid="selectCategoryVo"/>
            where iid = #{id}
    </select>

    <select id="selectCategoryByIds" resultMap="CategoryResult">
        select * from ieai_script_category
        <where>
            <if test="ids!= null ">
                and iid in
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>


    <select id="getParent" resultMap="CategoryResult" parameterType="Long">
        select iid, iparent_id, icode, iname, isort, ilevel, idescription, icreator_id, icreator_name, iupdator_id, iupdator_name, icreate_time, iupdate_time from
            ieai_script_category where iid = (select iparent_id from ieai_script_category where iid = #{childId})
    </select>

    <select id="getCategoryByScriptInfoVersionId" resultMap="CategoryResult" parameterType="Long">
        select iid, iparent_id, icode, iname, isort, ilevel, idescription, icreator_id, icreator_name, iupdator_id, iupdator_name, icreate_time, iupdate_time from
            ieai_script_category where iid = (select icategory_id from ieai_script_info where iunique_uuid = (select iinfo_unique_uuid from ieai_script_info_version where iid = #{scriptInfoVersionId}) )
    </select>

    <select id="checkFirstCategoryExist" resultMap="CategoryResult">
        select iid, iparent_id, icode, iname, isort, ilevel, idescription, icreator_id, icreator_name, iupdator_id, iupdator_name, icreate_time, iupdate_time
        from ieai_script_category
        <where>
            <if test="name != null">
                and iname = #{name}
            </if>
        </where>

    </select>

    <select id="getCategoryByCode" resultMap="CategoryResult">
        select iid, iparent_id, icode, iname, isort, ilevel, idescription, icreator_id, icreator_name, iupdator_id, iupdator_name, icreate_time, iupdate_time
        from ieai_script_category
        <where>
            <if test="code != null">
                and icode = #{code}
            </if>
        </where>

    </select>

    <select id="getCategoryWithOutRoleRelation" resultMap="CategoryResult">
        select sc.iid, sc.iparent_id, sc.icode, sc.iname, sc.isort, sc.ilevel, sc.idescription, sc.icreator_id, sc.icreator_name, sc.iupdator_id, sc.iupdator_name, sc.icreate_time, sc.iupdate_time
        from ieai_script_category sc
        WHERE NOT EXISTS (
            SELECT 1
            FROM ieai_script_category_role_relation scrr
            WHERE scrr.icategory_id = sc.iid
        )
    </select>

    <select id="checkNextCategoryExist" resultMap="CategoryResult">
        select iid, iparent_id, icode, iname, isort, ilevel, idescription, icreator_id, icreator_name, iupdator_id, iupdator_name, icreate_time, iupdate_time
        from ieai_script_category
        <where>
            <if test="name != null">
                and iname = #{name}
            </if>
            <if test="iid != null ">
                and iparent_id = #{iid}
            </if>
        </where>
    </select>
    <select id="selectChildCategoryIdList" resultType="java.lang.Long">
        select iid
        from ieai_script_category
        <where>
            <if test="categoryId != null">
                and iparent_id = #{categoryId}
            </if>
        </where>
    </select>
    <select id="getCategoryIds" resultType="java.lang.Long">
        select iid
        from ieai_script_category
        <where>
            <if test="categoryId != null">
                iparent_id in
                <foreach item="parentId" collection="categoryId" open="(" separator="," close=")">
                    #{parentId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="findByLevelAndNameAndParentId" parameterType="com.ideal.script.model.entity.Category" resultMap="CategoryResult">
        <include refid="selectCategoryVo"/>
        where ilevel= #{level}
             <if test="parentId != null">
              and iparent_id= #{parentId}
            </if>
            <if test="parentId == null">
                and iparent_id is null
            </if>
            and iname=#{name}
     </select>

    <insert id="insertCategory" parameterType="com.ideal.script.model.entity.Category" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_script_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">iid,
                    </if>
                    <if test="parentId != null">iparent_id,
                    </if>
                    <if test="code != null">icode,
                    </if>
                    <if test="name != null">iname,
                    </if>
                    <if test="sort != null">isort,
                    </if>
                    <if test="level != null">ilevel,
                    </if>
                    <if test="description != null">idescription,
                    </if>
                    <if test="creatorId != null">icreator_id,
                    </if>
                    <if test="creatorName != null">icreator_name,
                    </if>
                    <if test="updatorId != null">iupdator_id,
                    </if>
                    <if test="updatorName != null">iupdator_name,
                    </if>
                    <if test="icon != null">iicon_name,
                    </if>
                    icreate_time,
                    iupdate_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},
                    </if>
                    <if test="parentId != null">#{parentId},
                    </if>
                    <if test="code != null">#{code},
                    </if>
                    <if test="name != null">#{name},
                    </if>
                    <if test="sort != null">#{sort},
                    </if>
                    <if test="level != null">#{level},
                    </if>
                    <if test="description != null">#{description},
                    </if>
                    <if test="creatorId != null">#{creatorId},
                    </if>
                    <if test="creatorName != null">#{creatorName},
                    </if>
                    <if test="updatorId != null">#{updatorId},
                    </if>
                    <if test="updatorName != null">#{updatorName},
                    </if>
                    <if test="icon != null">#{icon},
                    </if>
                    ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                    ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
    </insert>

    <update id="updateCategory" parameterType="com.ideal.script.model.entity.Category">
        update ieai_script_category
        <trim prefix="SET" suffixOverrides=",">
                    <if test="parentId != null">iparent_id =
                        #{parentId},
                    </if>
                    <if test="code != null">icode =
                        #{code},
                    </if>
                    <if test="name != null">iname =
                        #{name},
                    </if>
                    <if test="sort != null">isort =
                        #{sort},
                    </if>
                    <if test="level != null">ilevel =
                        #{level},
                    </if>
                    <if test="description != null">idescription =
                        #{description},
                    </if>
                    <if test="creatorId != null">icreator_id =
                        #{creatorId},
                    </if>
                    <if test="creatorName != null">icreator_name =
                        #{creatorName},
                    </if>
                    <if test="updatorId != null">iupdator_id =
                        #{updatorId},
                    </if>
                    <if test="updatorName != null">iupdator_name =
                        #{updatorName},
                    </if>
                    <if test="icon != null">iicon_name =
                        #{icon},
                    </if>
                    iupdate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
        where iid = #{id}
    </update>

    <delete id="deleteCategoryById" parameterType="Long">
        delete
        from ieai_script_category where iid = #{id}
    </delete>

    <delete id="deleteCategoryByIds" parameterType="String">
        delete from ieai_script_category where iid in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteCategoryDepartmentByIds">
        delete from ieai_script_cat_org_relation where iid in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
    </delete>
    <delete id="deleteCategoryUserByIds">
        delete from ieai_script_cat_user_relation where iid in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteCategoryUserByCategoryIds">
        delete from ieai_script_cat_user_relation where icategory_id in
        <foreach collection="categoryIds" item="categoryId" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
    </delete>

    <select id="getSubCategoryIds" resultType="java.lang.Long">
          <include refid="selectCategoryVo"/>
          where iparent_id = #{categoryId}
    </select>

    <select id="getCategoryReferencedCount" resultType="java.lang.Integer">
        select count(iid) from ieai_script_info where icategory_id in
        <foreach item="categoryId" collection="categoryIds" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
    </select>



    <select id="getCategoryOrgRelations" resultMap="CategoryOrgResult">
        select iid,iorg_id,isys_org_code,icategory_id from ieai_script_cat_org_relation
            <where>
                <if test="categoryId != null">
                    icategory_id = #{categoryId}
                </if>
            </where>
    </select>
    <select id="getCategoryByOrgCode" resultType="java.lang.Long" >
        select icategory_id from ieai_script_cat_org_relation
        <where>
            <if test="orgCode != null and orgCode != ''">
            <bind name="orgCode" value="orgCode + '%'"/>
                 isys_org_code like  #{orgCode}
            </if>
        </where>
    </select>
    <select id="getCategoryUsertRelations" resultMap="CategoryUserResult">
        select iid,iuser_id from ieai_script_cat_user_relation where icategory_id = #{categoryId}
    </select>



    <insert id="insertCategoryDepartment" parameterType="com.ideal.script.model.bean.CategoryOrgBean" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_script_cat_org_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">iid,
            </if>
            <if test="categoryId != null">icategory_id,
            </if>
            <if test="level != null">ilevel,
            </if>
            <if test="sysOrgCode != null">isys_org_code,
            </if>
            <if test="orgId != null">iorg_id,
            </if>
            <if test="creatorId != null">icreator_id,</if>
            icreate_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},
            </if>
            <if test="categoryId != null">#{categoryId},
            </if>
            <if test="level != null">#{level},
            </if>
            <if test="sysOrgCode != null">#{sysOrgCode},
            </if>
            <if test="orgId != null">#{orgId},
            </if>
            <if test="creatorId != null">#{creatorId},</if>
            ${@com.ideal.common.util.DbUtils@getCurrentTime()}
        </trim>

    </insert>
    <insert id="insertCategoryUser" parameterType="com.ideal.script.model.bean.CategoryUserBean" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_script_cat_user_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">iid,
            </if>
            <if test="categoryId != null">icategory_id,
            </if>
            <if test="userId != null">iuser_id,
            </if>
            <if test="creatorId != null">icreator_id,</if>
            icreate_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},
            </if>
            <if test="categoryId != null">#{categoryId},
            </if>
            <if test="userId != null">#{userId},
            </if>
            <if test="creatorId != null">#{creatorId},</if>
            ${@com.ideal.common.util.DbUtils@getCurrentTime()}
        </trim>

    </insert>



    <select id="selectAllCategories" resultMap="CategoryResult">
        select * from ieai_script_category
    </select>
    <select id="selectAllOrgRelations" resultMap="CategoryOrgResult">
        select * from ieai_script_cat_org_relation
    </select>
    <select id="getUserByCategoryIds" resultMap="CategoryUserResult">
        select iid,iuser_id from ieai_script_cat_user_relation where icategory_id in
            <foreach collection="categoryIdList" item="categoryId" open="(" separator="," close=")">
                #{categoryId}
            </foreach>
    </select>

<!--auto generated by MybatisCodeHelper on 2025-01-22-->
    <select id="checkIdsExist" resultType="java.lang.Long">
        select iid
        from ieai_script_category
        where iid in
        <foreach item="item" index="index" collection="ids"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getCategoryRoleRelations" resultMap="CategoryRoleResult">
        SELECT iid, icategory_id, irole_id FROM ieai_script_category_role_relation
        <where>
            <if test="categoryId != null">
                icategory_id = #{categoryId}
            </if>
        </where>
    </select>

    <insert id="insertCategoryRole" parameterType="com.ideal.script.model.bean.CategoryRoleBean" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_script_category_role_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">iid,
            </if>
            <if test="categoryId != null">icategory_id,
            </if>
            <if test="roleId != null">irole_id,
            </if>
            icreated_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},
            </if>
            <if test="categoryId != null">#{categoryId},
            </if>
            <if test="roleId != null">#{roleId},
            </if>
            ${@com.ideal.common.util.DbUtils@getCurrentTime()}
        </trim>

    </insert>

    <delete id="deleteCategoryRoleByIds">
        delete from ieai_script_category_role_relation where iid in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteCategoryRoleByCategoryIds">
        delete from ieai_script_category_role_relation where icategory_id in
        <foreach collection="categoryIds" item="categoryId" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
    </delete>

    <select id="getCategoryIdsByRoleIds" resultType="java.lang.Long">
        select a.iid from ieai_script_category a left join ieai_script_category_role_relation b on a.iid = b.icategory_id where b.irole_id in
        <foreach item="roleId" collection="roleIds" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </select>

    <select id="getSaveRoleCategoryIdsByRoleIds" resultType="java.lang.Long">
        select distinct(a.iid) from ieai_script_category a left join ieai_script_category_role_relation b on a.iid = b.icategory_id where b.irole_id is null or b.irole_id in
        <foreach item="roleId" collection="roleIds" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </select>

    <select id="getCategoryIdsByRole" resultMap="CategoryRoleResult">
        select a.iid as icategory_id from ieai_script_category a left join ieai_script_category_role_relation b on a.iid = b.icategory_id where b.irole_id in
        <foreach item="roleId" collection="roleIds" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </select>

    <select id="getFirstCategoryByCategoryNameList" resultMap="CategoryResult">
        <include refid="selectCategoryVo" />
        where ilevel = 1
        and iname in
        <foreach item="categoryName" collection="categoryNameList" open="(" separator="," close=")">
            #{categoryName}
        </foreach>
    </select>

</mapper>