<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.DangerCmdMapper">

    <resultMap type="com.ideal.script.model.entity.DangerCmd" id="DangerCmdResult">
            <result property="id" column="iid"/>
            <result property="scriptCmd" column="iscript_cmd"/>
            <result property="whiteCommand" column="iwhite_command"/>
            <result property="scriptType" column="iscript_type"/>
            <result property="scriptCmdLevel" column="iscript_cmd_level"/>
            <result property="scriptCmdRemark" column="iscript_cmd_remark"/>
            <result property="hecktype" column="checktype"/>
            <result property="creatorId" column="icreator_id"/>
            <result property="creatorName" column="icreator_name"/>
            <result property="updatorId" column="iupdator_id"/>
            <result property="updatorName" column="iupdator_name"/>
            <result property="createTime" column="icreate_time"/>
            <result property="updateTime" column="iupdate_time"/>
            <result property="scriptLabel" column="iscript_label"/>
            <result property="categoryId" column="iscript_category_id"/>
    </resultMap>

    <sql id="selectDangerCmdVo">

        SELECT
            iid,
            iscript_cmd,
            iwhite_command,
            CASE
        WHEN iscript_type = 'py' THEN
            'python'
        WHEN iscript_type = 'sh' THEN
            'shell'
        WHEN iscript_type = 'ps1' THEN
            'powershell'
        ELSE
            iscript_type
        END AS iscript_type,
         iscript_cmd_level,
         iscript_cmd_remark,
         checktype,
         icreator_id,
         icreator_name,
         iupdator_id,
         iupdator_name,
         icreate_time,
         iupdate_time,
         iscript_label,
         iscript_category_id
        FROM
            ieai_script_danger_cmd
    </sql>

    <select id="selectDangerCmdList" parameterType="com.ideal.script.model.entity.DangerCmd" resultMap="DangerCmdResult">
        <include refid="selectDangerCmdVo"/>
        <where>
                        <if test="scriptCmd != null  and scriptCmd != ''">
                            and iscript_cmd like concat('%', #{scriptCmd}, '%')
                        </if>
                        <if test="whiteCommand != null ">
                            and iwhite_command = #{whiteCommand}
                        </if>
                        <if test="scriptType != null ">
                            and iscript_type = #{scriptType}
                        </if>
                        <if test="scriptCmdLevel != null ">
                            and iscript_cmd_level = #{scriptCmdLevel}
                        </if>
                        <if test="scriptCmdRemark != null  and scriptCmdRemark != ''">
                            and iscript_cmd_remark = #{scriptCmdRemark}
                        </if>
                        <if test="hecktype != null ">
                            and checktype = #{hecktype}
                        </if>
                        <if test="creatorId != null ">
                            and icreator_id = #{creatorId}
                        </if>
                        <if test="creatorName != null  and creatorName != ''">
                            and icreator_name like concat('%', #{creatorName}, '%')
                        </if>
                        <if test="updatorId != null ">
                            and iupdator_id = #{updatorId}
                        </if>
                        <if test="updatorName != null  and updatorName != ''">
                            and iupdator_name like concat('%', #{updatorName}, '%')
                        </if>
        </where>
            order by iid desc
    </select>

    <select id="selectDangerCmdByCategoryIdList" parameterType="java.lang.Long" resultMap="DangerCmdResult">
        <include refid="selectDangerCmdVo"/>
        <where>
            and (iscript_category_id = #{categoryId} or iscript_category_id is null)
        </where>
        order by iid desc
    </select>

    <select id="selectDangerCmdById" parameterType="Long"
            resultMap="DangerCmdResult">
            <include refid="selectDangerCmdVo"/>
            where iid = #{id}
    </select>
    <select id="selectDangerCmdsByLabels" resultMap="DangerCmdResult">
        <include refid="selectDangerCmdVo"/>
        <where>
            <if test="deletedLabels != null and deletedLabels.size != 0">
                <foreach collection="deletedLabels" item="label" separator="or">
                    iscript_label like #{label}
                </foreach>

            </if>
        </where>
    </select>

    <insert id="insertDangerCmd" parameterType="com.ideal.script.model.entity.DangerCmd">
        insert into ieai_script_danger_cmd
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">iid,
                    </if>
                    <if test="scriptCmd != null">iscript_cmd,
                    </if>
                    <if test="whiteCommand != null">iwhite_command,
                    </if>
                    <if test="scriptType != null">iscript_type,
                    </if>
                    <if test="scriptCmdLevel != null">iscript_cmd_level,
                    </if>
                    <if test="scriptCmdRemark != null">iscript_cmd_remark,
                    </if>
                    <if test="hecktype != null">checktype,
                    </if>
                    <if test="creatorId != null">icreator_id,
                    </if>
                    <if test="creatorName != null">icreator_name,
                    </if>
                    <if test="updatorId != null">iupdator_id,
                    </if>
                    <if test="updatorName != null">iupdator_name,
                    </if>
                    <if test="scriptLabel != null and scriptLabel != ''">
                        iscript_label,
                    </if>
                    icreate_time,
                    iupdate_time,
                    <if test="categoryId != null">iscript_category_id,
                    </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},
                    </if>
                    <if test="scriptCmd != null">#{scriptCmd},
                    </if>
                    <if test="whiteCommand != null">#{whiteCommand},
                    </if>
                    <if test="scriptType != null">#{scriptType},
                    </if>
                    <if test="scriptCmdLevel != null">#{scriptCmdLevel},
                    </if>
                    <if test="scriptCmdRemark != null">#{scriptCmdRemark},
                    </if>
                    <if test="hecktype != null">#{hecktype},
                    </if>
                    <if test="creatorId != null">#{creatorId},
                    </if>
                    <if test="creatorName != null">#{creatorName},
                    </if>
                    <if test="updatorId != null">#{updatorId},
                    </if>
                    <if test="updatorName != null">#{updatorName},
                    </if>
                    <if test="scriptLabel != null and scriptLabel != ''">
                        #{scriptLabel},
                    </if>
                    ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                    ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                    <if test="categoryId != null">
                        #{categoryId},
                    </if>
        </trim>
    </insert>

    <update id="updateDangerCmd" parameterType="com.ideal.script.model.entity.DangerCmd">
        update ieai_script_danger_cmd
        <trim prefix="SET" suffixOverrides=",">
                    <if test="scriptCmd != null">iscript_cmd =
                        #{scriptCmd},
                    </if>
                    <if test="whiteCommand != null">iwhite_command =
                        #{whiteCommand},
                    </if>
                    <if test="scriptType != null">iscript_type =
                        #{scriptType},
                    </if>
                    <if test="scriptCmdLevel != null">iscript_cmd_level =
                        #{scriptCmdLevel},
                    </if>
                    <if test="scriptCmdRemark != null">iscript_cmd_remark =
                        #{scriptCmdRemark},
                    </if>
                    <if test="hecktype != null">checktype =
                        #{hecktype},
                    </if>
                    <if test="creatorId != null">icreator_id =
                        #{creatorId},
                    </if>
                    <if test="creatorName != null">icreator_name =
                        #{creatorName},
                    </if>
                    <if test="updatorId != null">iupdator_id =
                        #{updatorId},
                    </if>
                    <if test="updatorName != null">iupdator_name =
                        #{updatorName},
                    </if>
                    <if test="scriptLabel != null">
                        iscript_label = #{scriptLabel},
                    </if>
                    iupdate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                    <if test="categoryId != null">
                        iscript_category_id = #{categoryId},
                    </if>
                    <if test="categoryId == null">
                        iscript_category_id = null,
                    </if>
        </trim>
        where iid = #{id}
    </update>

    <delete id="deleteDangerCmdById" parameterType="Long">
        delete
        from ieai_script_danger_cmd where iid = #{id}
    </delete>

    <delete id="deleteDangerCmdByIds" parameterType="String">
        delete from ieai_script_danger_cmd where iid in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>