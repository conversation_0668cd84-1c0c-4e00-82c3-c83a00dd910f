<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.ParameterCheckMapper">

    <resultMap type="com.ideal.script.model.entity.ParameterCheck" id="ParameterCheckResult">
        <result property="id" column="iid"/>
        <result property="ruleName" column="irule_name"/>
        <result property="checkRule" column="icheck_rule"/>
        <result property="ruleDes" column="irule_des"/>
        <result property="creatorId" column="icreator_id"/>
        <result property="creatorName" column="icreator_name"/>
        <result property="updatorId" column="iupdator_id"/>
        <result property="updatorName" column="iupdator_name"/>
        <result property="createTime" column="icreate_time"/>
        <result property="updateTime" column="iupdate_time"/>
    </resultMap>

    <insert id="insertParameterCheck" parameterType="com.ideal.script.model.entity.ParameterCheck" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_script_parameter_check
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">iid,
            </if>
            <if test="ruleName != null">irule_name,
            </if>
            <if test="checkRule != null">icheck_rule,
            </if>
            <if test="ruleDes != null">irule_des,
            </if>
            <if test="creatorId != null">icreator_id,
            </if>
            <if test="creatorName != null">icreator_name,
            </if>
            <if test="updatorId != null">iupdator_id,
            </if>
            <if test="updatorName != null">iupdator_name,
            </if>
            icreate_time,
            iupdate_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},
            </if>
            <if test="ruleName != null">#{ruleName},
            </if>
            <if test="checkRule != null">#{checkRule},
            </if>
            <if test="ruleDes != null">#{ruleDes},
            </if>
            <if test="creatorId != null">#{creatorId},
            </if>
            <if test="creatorName != null">#{creatorName},
            </if>
            <if test="updatorId != null">#{updatorId},
            </if>
            <if test="updatorName != null">#{updatorName},
            </if>
            ${@com.ideal.common.util.DbUtils@getCurrentTime()},
            ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
    </insert>
    <update id="updateParameterCheck" parameterType="com.ideal.script.model.entity.ParameterCheck">
        update ieai_script_parameter_check
        <trim prefix="SET" suffixOverrides=",">
            <if test="ruleName != null">irule_name =
                #{ruleName},
            </if>
            <if test="checkRule != null">icheck_rule =
                #{checkRule},
            </if>
            <if test="ruleDes != null">irule_des =
                #{ruleDes},
            </if>
            <if test="creatorId != null">icreator_id =
                #{creatorId},
            </if>
            <if test="creatorName != null">icreator_name =
                #{creatorName},
            </if>
            <if test="updatorId != null">iupdator_id =
                #{updatorId},
            </if>
            <if test="updatorName != null">iupdator_name =
                #{updatorName},
            </if>
            icreate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
            iupdate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
        where iid = #{id}
    </update>
    <delete id="deleteParameterCheckByIds" parameterType="Long">
        delete from ieai_script_parameter_check where iid in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <sql id="selectParameterCheckVo">
        select iid, irule_name, icheck_rule, irule_des, icreator_id, icreator_name, iupdator_id, iupdator_name, icreate_time, iupdate_time
        from ieai_script_parameter_check
    </sql>
    <select id="selectParameterCheckList" parameterType="com.ideal.script.model.entity.ParameterCheck" resultMap="ParameterCheckResult">
        <include refid="selectParameterCheckVo"/>
        <where>
            <if test="ruleName != null ">
                and irule_name  like concat('%', #{ruleName}, '%')
            </if>
            <if test="checkRule != null ">
                and icheck_rule  like concat('%', #{checkRule}, '%')
            </if>
            <if test="ruleDes != null ">
                and irule_des  like concat('%', #{ruleDes}, '%')
            </if>
            <if test="creatorId != null ">
                and icreator_id = #{creatorId}
            </if>
            <if test="creatorName != null  and creatorName != ''">
                and icreator_name like concat('%', #{creatorName}, '%')
            </if>
            <if test="updatorId != null ">
                and iupdator_id = #{updatorId}
            </if>
            <if test="updatorName != null  and updatorName != ''">
                and iupdator_name like concat('%', #{updatorName}, '%')
            </if>
        </where>
        order by iid desc
    </select>

    <select id="selectParameterCheckById" parameterType="Long"
        resultMap="ParameterCheckResult">
        <include refid="selectParameterCheckVo"/>
        where iid = #{id}
    </select>

    <select id="validParamterCheckExist" resultType="java.lang.Boolean">
        select
            exists (
                select
                    1
                from
                    ieai_script_parameter_check
                where
                    irule_name = #{ruleName}
            )
     </select>

     <select id="selectParameterCheckByName" parameterType="com.ideal.script.model.entity.ParameterCheck" resultMap="ParameterCheckResult">
        <include refid="selectParameterCheckVo"/>
        where irule_name=#{ruleName}
      </select>
</mapper>