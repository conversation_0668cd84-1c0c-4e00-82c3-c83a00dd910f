<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.ParameterMapper">
    
    <resultMap type="com.ideal.script.model.entity.Parameter" id="ParameterResult">
        <result property="id"    column="iid"    />
        <result property="srcScriptUuid"    column="isrc_script_uuid"    />
        <result property="paramType"    column="iparam_type"    />
        <result property="paramDefaultValue"    column="iparam_default_value"    />
        <result property="paramDesc"    column="iparam_desc"    />
        <result property="paramOrder"    column="iparam_order"    />
        <result property="paramCheckIid"    column="iparamcheckiid"    />
        <result property="scriptParameterManagerId"    column="iscript_parameter_manager_id"    />
        <result property="creatorId"    column="icreator_id"    />
        <result property="creatorName"    column="icreator_name"    />
        <result property="createTime"    column="icreate_time"    />
        <result property="paramName"    column="iparam_name"    />
    </resultMap>

    <resultMap type="com.ideal.script.model.bean.ParameterValidationBean" id="ParameterAndValidationResult">
        <result property="id"    column="iid"    />
        <result property="srcScriptUuid"    column="isrc_script_uuid"    />
        <result property="paramType"    column="iparam_type"    />
        <result property="paramDefaultValue"    column="iparam_default_value"    />
        <result property="paramDesc"    column="iparam_desc"    />
        <result property="paramOrder"    column="iparam_order"    />
        <result property="paramCheckIid"    column="iparamcheckiid"    />
        <result property="scriptParameterManagerId"    column="iscript_parameter_manager_id"    />
        <result property="creatorId"    column="icreator_id"    />
        <result property="creatorName"    column="icreator_name"    />
        <result property="createTime"    column="icreate_time"    />
        <result column="irule_name" property="ruleName" />
        <result column="icheck_rule" property="checkRule" />
        <result column="irule_des" property="ruleDes" />
        <result column="iparam_name" property="paramName" />
    </resultMap>
    <resultMap id="selectParameterInfo" type="com.ideal.script.model.entity.Parameter">
        <result property="srcScriptUuid" column="isrc_script_uuid"/>
        <result property="paramType" column="iparam_type"/>
        <result property="paramDefaultValue" column="iparam_default_value"/>
        <result property="paramDesc" column="iparam_desc"/>
        <result property="paramOrder" column="iparam_order"/>
        <result property="paramCheckIid" column="iparamcheckiid"/>
        <result property="scriptParameterManagerId" column="iscript_parameter_manager_id"/>
        <result property="creatorId" column="icreator_id"/>
        <result property="creatorName" column="icreator_name"/>
        <result property="createTime" column="icreate_time"/>
        <result column="iparam_name" property="paramName" />
    </resultMap>


    <resultMap id="selectTaskParameterInfo" type="com.ideal.script.model.entity.TaskParams">
        <result property="type" column="itype"/>
        <result property="value" column="ivalue"/>
        <result property="order" column="iorder"/>
    </resultMap>
    <sql id="selectParameterVo">
        select iid, isrc_script_uuid, iparam_type, iparam_name, iparam_default_value, iparam_desc, iparam_order, iparamcheckiid, iscript_parameter_manager_id, icreator_id, icreator_name, icreate_time from ieai_script_parameter
    </sql>

    <select id="selectParameterList" parameterType="com.ideal.script.model.entity.Parameter" resultMap="ParameterResult">
        <include refid="selectParameterVo"/>
        <where>  
            <if test="srcScriptUuid != null  and srcScriptUuid != ''"> and isrc_script_uuid = #{srcScriptUuid}</if>
            <if test="paramType != null  and paramType != ''"> and iparam_type = #{paramType}</if>
            <if test="paramDefaultValue != null  and paramDefaultValue != ''"> and iparam_default_value = #{paramDefaultValue}</if>
            <if test="paramDesc != null  and paramDesc != ''"> and iparam_desc = #{paramDesc}</if>
            <if test="paramOrder != null "> and iparam_order = #{paramOrder}</if>
            <if test="paramCheckIid != null "> and iparamcheckiid = #{paramCheckIid}</if>
            <if test="scriptParameterManagerId != null "> and iscript_parameter_manager_id = #{scriptParameterManagerId}</if>
            <if test="creatorId != null "> and icreator_id = #{creatorId}</if>
            <if test="creatorName != null  and creatorName != ''"> and icreator_name like concat('%', #{creatorName}, '%')</if>
        </where>
    </select>
    
    <select id="selectParameterById" parameterType="Long" resultMap="ParameterResult">
        <include refid="selectParameterVo"/>
        where iid = #{id}
    </select>
        
    <insert id="insertParameter" parameterType="com.ideal.script.model.entity.Parameter" useGeneratedKeys="true" keyProperty="id">
        insert into ieai_script_parameter
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                iid,
            </if>
            <if test="srcScriptUuid != null and srcScriptUuid != ''">isrc_script_uuid,</if>
            <if test="paramType != null and paramType != ''">iparam_type,</if>
            <if test="paramName != null">iparam_name,</if>
            <if test="paramDefaultValue != null">iparam_default_value,</if>
            <if test="paramDesc != null">iparam_desc,</if>
            <if test="paramOrder != null">iparam_order,</if>
            <if test="paramCheckIid != null">iparamcheckiid,</if>
            <if test="scriptParameterManagerId != null">iscript_parameter_manager_id,</if>
            <if test="creatorId != null">icreator_id,</if>
            <if test="creatorName != null">icreator_name,</if>
            icreate_time,
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="srcScriptUuid != null and srcScriptUuid != ''">#{srcScriptUuid},</if>
            <if test="paramType != null and paramType != ''">#{paramType},</if>
            <if test="paramName != null">#{paramName},</if>
            <if test="paramDefaultValue != null">#{paramDefaultValue},</if>
            <if test="paramDesc != null">#{paramDesc},</if>
            <if test="paramOrder != null">#{paramOrder},</if>
            <if test="paramCheckIid != null">#{paramCheckIid},</if>
            <if test="scriptParameterManagerId != null">#{scriptParameterManagerId},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="creatorName != null">#{creatorName},</if>
            ${@com.ideal.common.util.DbUtils@getCurrentTime()},
         </trim>
    </insert>

    <update id="updateParameter" parameterType="com.ideal.script.model.entity.Parameter">
        update ieai_script_parameter
        <trim prefix="SET" suffixOverrides=",">
            <if test="srcScriptUuid != null and srcScriptUuid != ''">isrc_script_uuid = #{srcScriptUuid},</if>
            <if test="paramType != null and paramType != ''">iparam_type = #{paramType},</if>
            <if test="paramName != null">iparam_name = #{paramName},</if>
            <if test="paramDefaultValue != null">iparam_default_value = #{paramDefaultValue},</if>
            <if test="paramDesc != null">iparam_desc = #{paramDesc},</if>
            <if test="paramOrder != null">iparam_order = #{paramOrder},</if>
            <if test="paramCheckIid != null">iparamcheckiid = #{paramCheckIid},</if>
            <if test="scriptParameterManagerId != null">iscript_parameter_manager_id = #{scriptParameterManagerId},</if>
            <if test="creatorId != null">icreator_id = #{creatorId},</if>
            <if test="creatorName != null">icreator_name = #{creatorName},</if>
        </trim>
        where iid = #{id}
    </update>

    <delete id="deleteParameterById" parameterType="Long">
        delete from ieai_script_parameter where iid = #{id}
    </delete>

    <delete id="deleteParameterByScriptUuid" parameterType="String">
        delete from ieai_script_parameter where isrc_script_uuid = #{scriptUuid}
    </delete>

    <delete id="deleteParameterByScriptUuids" parameterType="String">
        delete from ieai_script_parameter where isrc_script_uuid in
        <foreach item="uuid" collection="array" open="(" separator="," close=")">
            #{uuid}
        </foreach>
    </delete>

    <delete id="deleteParameterByIds" parameterType="String">
        delete from ieai_script_parameter where iid in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
     <select id="selectParameterListByScriptId" parameterType="com.ideal.script.model.entity.Parameter" resultMap="ParameterResult">
        SELECT t2.iparam_default_value,t2.iparam_desc,t2.iparam_order FROM ieai_script_info_version t,ieai_script_info t1,ieai_script_parameter t2 WHERE t.iinfo_unique_uuid = t1.iunique_uuid 
        AND t.isrc_script_uuid = t2.isrc_script_uuid AND t1.iid = #{scriptId}
    </select>

    <sql id="selectParameterAndValidationVo">
        select p.iid, isrc_script_uuid, iparam_type,iparam_name, iparam_default_value, iparam_desc, iparam_order, iparamcheckiid, iscript_parameter_manager_id, p.icreator_id, p.icreator_name, p.icreate_time,c.irule_name,c.icheck_rule,c.irule_des from ieai_script_parameter p left join ieai_script_parameter_check c on p.iparamcheckiid = c.iid
    </sql>

    <select id="selectParameterValidationList" parameterType="com.ideal.script.model.entity.Parameter" resultMap="ParameterAndValidationResult">
        <include refid="selectParameterAndValidationVo"/>
        <where>
            <if test="srcScriptUuid != null  and srcScriptUuid != ''"> and isrc_script_uuid = #{srcScriptUuid}</if>
            <if test="paramType != null  and paramType != ''"> and iparam_type = #{paramType}</if>
            <if test="paramDefaultValue != null  and paramDefaultValue != ''"> and iparam_default_value = #{paramDefaultValue}</if>
            <if test="paramDesc != null  and paramDesc != ''"> and iparam_desc = #{paramDesc}</if>
            <if test="paramOrder != null "> and iparam_order = #{paramOrder}</if>
            <if test="paramCheckIid != null "> and iparamcheckiid = #{paramCheckIid}</if>
            <if test="scriptParameterManagerId != null "> and iscript_parameter_manager_id = #{scriptParameterManagerId}</if>
            <if test="creatorId != null "> and icreator_id = #{creatorId}</if>
            <if test="creatorName != null  and creatorName != ''"> and icreator_name like concat('%', #{creatorName}, '%')</if>
        </where>
        order by iparam_order;
    </select>
    <delete id="deleteParameterByUuid">
        delete from ieai_script_parameter
        where isrc_script_uuid =
        <if test="oldUuid != null">
            #{oldUuid}
        </if>
    </delete>
    <select id="getParameterByUuid" resultMap="selectParameterInfo">
        select * from ieai_script_parameter
        where isrc_script_uuid =
        <if test="uuid != null">
            #{uuid}
        </if>
        order by iparam_order
    </select>
    <select id="getParameterByTaskId" resultMap="selectTaskParameterInfo">
        select * from ieai_script_task_params
        where iscript_task_id =
        <if test="taskId != null">
            #{taskId}
        </if>
        order by iorder
    </select>
</mapper>