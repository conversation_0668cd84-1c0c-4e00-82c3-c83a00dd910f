<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.ScriptVersionShareMapper">
    
    <resultMap type="com.ideal.script.model.entity.ScriptVersionShare" id="ScriptVersionShareResult">
        <result property="iid"    column="iid"    />
        <result property="shareType"    column="ishare_type"    />
        <result property="scriptInfoId"    column="iscript_info_id"    />
        <result property="shareObjectId"    column="ishare_object_id"    />
        <result property="createTime"    column="icreate_time"    />
        <result property="shareObjectId"    column="ishare_object_id"    />
        <result property="scriptNameZh"    column="iscript_name_zh"    />
    </resultMap>

    <sql id="selectShareVo">
        select iid, ishare_type, iscript_info_id, icreate_time, ishare_object_id from ieai_script_share_relation
    </sql>

    <insert id="insertScriptVersionShare" parameterType="com.ideal.script.model.entity.ScriptVersionShare" useGeneratedKeys="true" keyProperty="iid">
        insert into ieai_script_share_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="iid != null">
                iid,
            </if>
            <if test="shareType != null">ishare_type,</if>
            <if test="scriptInfoId != null">iscript_info_id,</if>
            <if test="shareObjectId != null">ishare_object_id,</if>
            icreate_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="iid != null">
                #{iid},
            </if>
            <if test="shareType != null">#{shareType},</if>
            <if test="scriptInfoId != null">#{scriptInfoId},</if>
            <if test="shareObjectId != null">#{shareObjectId},</if>
            ${@com.ideal.common.util.DbUtils@getCurrentTime()},
         </trim>
    </insert>

    <delete id="deleteScriptVersionShareByIds">
        delete from ieai_script_share_relation where iid in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getObjectIdList" resultType="java.lang.String">
        select ishare_object_id from ieai_script_share_relation
        <where>
            <if test="shareType != null  and shareType != ''"> and ishare_type = #{shareType}</if>
            <if test="scriptInfoId != null  and scriptInfoId != ''"> and iscript_info_id = #{scriptInfoId}</if>
        </where>
    </select>

    <select id="selectShareScriptData" resultMap="ScriptVersionShareResult">
        select c.iscript_name_zh,a.iid, a.ishare_type, a.iscript_info_id, a.icreate_time, a.ishare_object_id
        from
            ieai_script_share_relation a
            left join ieai_script_info c on a.iscript_info_id = c.iid
        where a.iscript_info_id = #{scriptVersionShare.scriptInfoId}
        order by a.icreate_time desc
    </select>

    <select id="getSharedUser" resultType="java.lang.Long">
        select ishare_object_id from ieai_script_share_relation
        where iscript_info_id = #{scriptInfoId}
        and ishare_type = 0
    </select>

    <select id="getShareScriptIds" resultType="java.lang.Long">
        select
            DISTINCT(iscript_info_id)
        from
            ieai_script_share_relation
        where
            (ishare_type = 0 and ishare_object_id = #{userId})
           or
            (ishare_type = 1 and ishare_object_id = #{departmentId}))
           or
            ishare_type = 2
    </select>

</mapper>