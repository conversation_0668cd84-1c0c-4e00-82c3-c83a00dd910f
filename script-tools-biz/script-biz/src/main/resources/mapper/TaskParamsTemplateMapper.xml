<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.TaskParamsTemplateMapper">

    <resultMap type="com.ideal.script.model.entity.TaskParams" id="TaskParamsResult">
            <result property="id" column="iid"/>
            <result property="scriptTaskId" column="iscript_task_id"/>
            <result property="scriptParameterCheckId" column="iscript_parameter_check_id"/>
            <result property="scriptParameterManagerId" column="iscript_parameter_manager_id"/>
            <result property="type" column="itype"/>
            <result property="value" column="ivalue"/>
            <result property="desc" column="idesc"/>
            <result property="order" column="iorder"/>
            <result property="startType" column="istart_type"/>
            <result property="createTime" column="icreate_time"/>
    </resultMap>


    <resultMap type="com.ideal.script.model.bean.TaskHisParamsBean" id="TaskHisParamsResult">
            <result property="srcScriptUuid" column="isrc_script_uuid"/>
            <result property="taskParamsId" column="itask_param_id"/>
            <result property="type" column="itype"/>
            <result property="value" column="ivalue"/>
            <result property="desc" column="idesc"/>
            <result property="order" column="iorder"/>
    </resultMap>

    <!--查询历史参数-->

    <insert id="insertTaskParams" parameterType="com.ideal.script.model.entity.TaskParams" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_script_task_params_temp
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">
                        iid,
                    </if>
                    <if test="scriptTaskId != null">iscript_task_id,
                    </if>
                    <if test="scriptParameterCheckId != null">iscript_parameter_check_id,
                    </if>
                    <if test="scriptParameterManagerId != null">iscript_parameter_manager_id,
                    </if>
                    <if test="type != null">itype,
                    </if>
                    <if test="value != null">ivalue,
                    </if>
                    <if test="desc != null">idesc,
                    </if>
                    <if test="order != null">iorder,
                    </if>
                    <if test="startType != null">istart_type,
                    </if>
                    icreate_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">
                        #{id},
                    </if>
                    <if test="scriptTaskId != null">#{scriptTaskId},
                    </if>
                    <if test="scriptParameterCheckId != null">#{scriptParameterCheckId},
                    </if>
                    <if test="scriptParameterManagerId != null">#{scriptParameterManagerId},
                    </if>
                    <if test="type != null">#{type},
                    </if>
                    <if test="value != null">#{value},
                    </if>
                    <if test="desc != null">#{desc},
                    </if>
                    <if test="order != null">#{order},
                    </if>
                    <if test="startType != null">#{startType},
                    </if>
                    ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
    </insert>

    <sql id="selectTaskParamsVo">
        select iid, iscript_task_id, iscript_parameter_check_id, iscript_parameter_manager_id, itype, ivalue, idesc, iorder, istart_type, icreate_time
        from ieai_script_task_params_temp
    </sql>

    <select id="selectTaskParamsByTaskId" parameterType="Long"
            resultMap="TaskParamsResult">
        <include refid="selectTaskParamsVo"/>
        <where>
            <if test="taskId != null">
                iscript_task_id = #{taskId}
            </if>
        </where>

    </select>

    <delete id="deleteByTaskId">
        delete from ieai_script_task_params_temp where iscript_task_id = #{taskId}
    </delete>

    <update id="batchUpdateByIds" parameterType="java.util.List">
        UPDATE ieai_script_task_params_temp
        SET ivalue = CASE iid
        <foreach collection="list" item="listValue">
            WHEN #{listValue.id} THEN #{listValue.value}
        </foreach>
        END
        WHERE iid IN
        <foreach collection="list" item="listValue" open="(" separator="," close=")">
            #{listValue.id}
        </foreach>
    </update>

</mapper>