<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.TaskGroupsMapper">

    <resultMap type="com.ideal.script.model.entity.TaskGroups" id="TaskGroupsResult">
            <result property="id" column="iid"/>
            <result property="scriptTaskId" column="iscript_task_id"/>
            <result property="sysmComputerGroupId" column="isysm_computer_group_id"/>
            <result property="cpname" column="icpname"/>
            <result property="createTime" column="icreate_time"/>
    </resultMap>

    <sql id="selectTaskGroupsVo">
        select iid, iscript_task_id, isysm_computer_group_id, icpname, icreate_time
        from ieai_script_task_groups
    </sql>

    <select id="selectTaskGroupsList" parameterType="com.ideal.script.model.entity.TaskGroups" resultMap="TaskGroupsResult">
        <include refid="selectTaskGroupsVo"/>
        <where>
                        <if test="scriptTaskId != null ">
                            and iscript_task_id = #{scriptTaskId}
                        </if>
                        <if test="sysmComputerGroupId != null ">
                            and isysm_computer_group_id = #{sysmComputerGroupId}
                        </if>
                        <if test="cpname != null  and cpname != ''">
                            and icpname like concat('%', #{cpname}, '%')
                        </if>
        </where>
    </select>

    <select id="selectTaskGroupsById" parameterType="Long"
            resultMap="TaskGroupsResult">
            <include refid="selectTaskGroupsVo"/>
            where iid = #{id}
    </select>

    <select id="selectTaskGroupsByServiceId" parameterType="Long"
            resultMap="TaskGroupsResult">
            <include refid="selectTaskGroupsVo"/>
            where
            <if test="serviceId != null">
                iscript_task_id in (
                select
                iscript_task_id
                from
                ieai_script_audit_relation
                where
                iid = #{serviceId}
                )
            </if>
            <if test="serviceId == null and taskId != null">
                iscript_task_id = #{taskId}
            </if>

    </select>

    <insert id="insertTaskGroups" parameterType="com.ideal.script.model.entity.TaskGroups" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_script_task_groups
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">
                        iid,
                    </if>
                    <if test="scriptTaskId != null">iscript_task_id,
                    </if>
                    <if test="sysmComputerGroupId != null">isysm_computer_group_id,
                    </if>
                    <if test="cpname != null">icpname,
                    </if>
                    icreate_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">
                        #{id},
                    </if>
                    <if test="scriptTaskId != null">#{scriptTaskId},
                    </if>
                    <if test="sysmComputerGroupId != null">#{sysmComputerGroupId},
                    </if>
                    <if test="cpname != null">#{cpname},
                    </if>
                    ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
    </insert>

    <update id="updateTaskGroups" parameterType="com.ideal.script.model.entity.TaskGroups">
        update ieai_script_task_groups
        <trim prefix="SET" suffixOverrides=",">
                    <if test="scriptTaskId != null">iscript_task_id =
                        #{scriptTaskId},
                    </if>
                    <if test="sysmComputerGroupId != null">isysm_computer_group_id =
                        #{sysmComputerGroupId},
                    </if>
                    <if test="cpname != null">icpname =
                        #{cpname},
                    </if>
        </trim>
        where iid = #{id}
    </update>

    <delete id="deleteTaskGroupsById" parameterType="Long">
        delete
        from ieai_script_task_groups where iid = #{id}
    </delete>

    <delete id="deleteTaskGroupsByTaskId" parameterType="Long">
        delete
        from ieai_script_task_groups where iscript_task_id = #{taskId}
    </delete>


    <delete id="deleteTaskGroupsByIds" parameterType="String">
        delete from ieai_script_task_groups where iid in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>