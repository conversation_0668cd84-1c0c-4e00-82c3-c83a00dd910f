<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.VariablePublishMapper">

    <resultMap type="com.ideal.script.model.entity.VariablePublish" id="VariablePResult">
            <result property="id" column="iid"/>
            <result property="scriptVariableClassId" column="iscript_variable_class_id"/>
            <result property="name" column="iname"/>
            <result property="type" column="itype"/>
            <result property="value" column="ivalue"/>
            <result property="desc" column="idesc"/>
            <result property="attribute" column="iattribute"/>
            <result property="global" column="iglobal"/>
            <result property="status" column="istatus"/>
            <result property="userglobal" column="iuserglobal"/>
            <result property="md5" column="imd5"/>
            <result property="creatorId" column="icreator_id"/>
            <result property="creatorName" column="icreator_name"/>
            <result property="updatorId" column="iupdator_id"/>
            <result property="updatorName" column="iupdator_name"/>
            <result property="createTime" column="icreate_time"/>
            <result property="updateTime" column="iupdate_time"/>
    </resultMap>

    <sql id="selectVariablePVo">
        select iid, iscript_variable_class_id, iname, itype, ivalue, idesc, iattribute, iglobal, istatus, iuserglobal, imd5, icreator_id, icreator_name, iupdator_id, iupdator_name, icreate_time, iupdate_time
        from ieai_script_variable_p
    </sql>

    <select id="selectVariablePublishList" parameterType="com.ideal.script.model.entity.VariablePublish" resultMap="VariablePResult">
        <include refid="selectVariablePVo"/>
        <where>
                        <if test="scriptVariableClassId != null ">
                            and iscript_variable_class_id = #{scriptVariableClassId}
                        </if>
                        <if test="name != null  and name != ''">
                            and iname like concat('%', #{name}, '%')
                        </if>
                        <if test="type != null ">
                            and itype = #{type}
                        </if>
                        <if test="value != null  and value != ''">
                            and ivalue = #{value}
                        </if>
                        <if test="desc != null  and desc != ''">
                            and idesc = #{desc}
                        </if>
                        <if test="attribute != null ">
                            and iattribute = #{attribute}
                        </if>
                        <if test="global != null ">
                            and iglobal = #{global}
                        </if>
                        <if test="status != null ">
                            and istatus = #{status}
                        </if>
                        <if test="userglobal != null ">
                            and iuserglobal = #{userglobal}
                        </if>
                        <if test="md5 != null  and md5 != ''">
                            and imd5 = #{md5}
                        </if>
                        <if test="creatorId != null ">
                            and icreator_id = #{creatorId}
                        </if>
                        <if test="creatorName != null  and creatorName != ''">
                            and icreator_name like concat('%', #{creatorName}, '%')
                        </if>
                        <if test="updatorId != null ">
                            and iupdator_id = #{updatorId}
                        </if>
                        <if test="updatorName != null  and updatorName != ''">
                            and iupdator_name like concat('%', #{updatorName}, '%')
                        </if>
                        <if test="keyword != null  and keyword != ''">
                            and iname like concat('%', #{keyword}, '%') or  idesc like concat('%', #{keyword}, '%')
                        </if>
        </where>
    </select>

    <select id="selectVariablePublishListForEdit" parameterType="com.ideal.script.model.entity.VarAndFuncForEdit"
            resultMap="VariablePResult">
        select iid, iname, iattribute, idesc from ieai_script_variable_p
        <where>
            <trim prefixOverrides="and">
                <if test="bindState > 0 and bindIds.length > 0">
                    and (
                    <if test="bindState == 1">
                        iid in
                    </if>
                    <if test="bindState == 2">
                        iid not in
                    </if>
                    <foreach item="id" collection="bindIds" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                    <if test="bindState == 1">
                        or iattribute = 2
                    </if>
                    <if test="bindState == 2">
                        and iattribute != 2
                    </if>
                    )
                </if>
                <if test="keyword != null and keyword != '' and keyword != 'null'">
                    and (upper(iname) like concat('%', upper(#{keyword}), '%') or upper(idesc) like concat('%', upper(#{keyword}), '%'))
                </if>
            </trim>
        </where>
    </select>

    <select id="selectVariablePublishById" parameterType="Long"
            resultMap="VariablePResult">
    </select>

    <insert id="insertVariableP" parameterType="com.ideal.script.model.entity.VariablePublish" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_script_variable_p
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">iid,
                    </if>
                    <if test="scriptVariableClassId != null">iscript_variable_class_id,
                    </if>
                    <if test="name != null">iname,
                    </if>
                    <if test="type != null">itype,
                    </if>
                    <if test="value != null">ivalue,
                    </if>
                    <if test="desc != null">idesc,
                    </if>
                    <if test="attribute != null">iattribute,
                    </if>
                    <if test="global != null">iglobal,
                    </if>
                    <if test="status != null">istatus,
                    </if>
                    <if test="userglobal != null">iuserglobal,
                    </if>
                    <if test="md5 != null">imd5,
                    </if>
                    <if test="creatorId != null">icreator_id,
                    </if>
                    <if test="creatorName != null">icreator_name,
                    </if>
                    <if test="updatorId != null">iupdator_id,
                    </if>
                    <if test="updatorName != null">iupdator_name,
                    </if>
                    icreate_time,
                    iupdate_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},
                    </if>
                    <if test="scriptVariableClassId != null">#{scriptVariableClassId},
                    </if>
                    <if test="name != null">#{name},
                    </if>
                    <if test="type != null">#{type},
                    </if>
                    <if test="value != null">#{value},
                    </if>
                    <if test="desc != null">#{desc},
                    </if>
                    <if test="attribute != null">#{attribute},
                    </if>
                    <if test="global != null">#{global},
                    </if>
                    <if test="status != null">#{status},
                    </if>
                    <if test="userglobal != null">#{userglobal},
                    </if>
                    <if test="md5 != null">#{md5},
                    </if>
                    <if test="creatorId != null">#{creatorId},
                    </if>
                    <if test="creatorName != null">#{creatorName},
                    </if>
                    <if test="updatorId != null">#{updatorId},
                    </if>
                    <if test="updatorName != null">#{updatorName},
                    </if>
                    ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                    ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
    </insert>

    <update id="updateVariableP" parameterType="com.ideal.script.model.entity.VariablePublish">
        update ieai_script_variable_p
        <trim prefix="SET" suffixOverrides=",">
                    <if test="scriptVariableClassId != null">iscript_variable_class_id =
                        #{scriptVariableClassId},
                    </if>
                    <if test="name != null">iname =
                        #{name},
                    </if>
                    <if test="type != null">itype =
                        #{type},
                    </if>
                    <if test="value != null">ivalue =
                        #{value},
                    </if>
                    <if test="desc != null">idesc =
                        #{desc},
                    </if>
                    <if test="attribute != null">iattribute =
                        #{attribute},
                    </if>
                    <if test="global != null">iglobal =
                        #{global},
                    </if>
                    <if test="status != null">istatus =
                        #{status},
                    </if>
                    <if test="userglobal != null">iuserglobal =
                        #{userglobal},
                    </if>
                    <if test="md5 != null">imd5 =
                        #{md5},
                    </if>
                    <if test="creatorId != null">icreator_id =
                        #{creatorId},
                    </if>
                    <if test="creatorName != null">icreator_name =
                        #{creatorName},
                    </if>
                    <if test="updatorId != null">iupdator_id =
                        #{updatorId},
                    </if>
                    <if test="updatorName != null">iupdator_name =
                        #{updatorName},
                    </if>
                    iupdate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
        where iid = #{id}
    </update>

    <delete id="deleteVariablePublishById" parameterType="Long">
        delete
        from ieai_script_variable_p where iid = #{id}
    </delete>

    <delete id="deleteVariablePublishByIds" parameterType="String">
        delete from ieai_script_variable_p where iid in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>