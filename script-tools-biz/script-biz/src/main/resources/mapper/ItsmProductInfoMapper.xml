<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.ItsmProductInfoMapper">
    
    <resultMap type="com.ideal.script.model.entity.ItsmProductInfo" id="dataResult">
        <result property="iid"    column="iid"    />
        <result property="publishInfoId"    column="ipublish_info_id"    />
        <result property="srcScriptUuid"    column="isrc_script_uuid"    />
        <result property="scriptJsonStr"    column="iscript_json_str"    />
        <result property="zipFileContent"    column="izip_file_content"    />
        <result property="createdTime"    column="icreated_time"    />
        <result property="createdUserId"    column="icreated_user_id"    />
    </resultMap>

    <delete id="deleteByPublishInfoId" parameterType="Long">
        delete
        from ieai_script_itsm_product_info where ipublish_info_id = #{publishInfoId}
    </delete>

    <select id="getItsmProductInfoByProductId" parameterType="java.lang.Long" resultMap="dataResult">
            select iid,ipublish_info_id,isrc_script_uuid,iscript_json_str,icreated_time,icreated_user_id from ieai_script_itsm_product_info
            where iid = #{productId}
    </select>

    <select id="getItsmProductInfoById" parameterType="java.lang.Long" resultMap="dataResult">
        select iid,ipublish_info_id,isrc_script_uuid,iscript_json_str,icreated_time,icreated_user_id from ieai_script_itsm_product_info
        where iid = #{id}
    </select>


    <insert id="insertProductInfo" parameterType="com.ideal.script.model.entity.ItsmProductInfo" useGeneratedKeys="true" keyProperty="iid">
        insert into ieai_script_itsm_product_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="iid != null">
                iid,
            </if>
            <if test="srcScriptUuid != null and srcScriptUuid != ''">isrc_script_uuid,</if>
            <if test="publishInfoId != null">ipublish_info_id,</if>
            <if test="scriptJsonStr != null and scriptJsonStr != ''">iscript_json_str,</if>
            icreated_time,
            <if test="createdUserId != null">icreated_user_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="iid != null">
                #{iid},
            </if>
            <if test="srcScriptUuid != null and srcScriptUuid != ''">#{srcScriptUuid},</if>
            <if test="publishInfoId != null">#{publishInfoId},</if>
            <if test="scriptJsonStr != null">#{scriptJsonStr},</if>
            ${@com.ideal.common.util.DbUtils@getCurrentTime()},
            <if test="createdUserId != null">#{createdUserId},</if>
         </trim>
    </insert>

    <update id="updateScriptInfoJsonById" parameterType="com.ideal.script.model.entity.ItsmProductInfo">
        update ieai_script_itsm_product_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="scriptJsonStr != null">iscript_json_str = #{scriptJsonStr},</if>
        </trim>
        where iid = #{iid}
    </update>

</mapper>