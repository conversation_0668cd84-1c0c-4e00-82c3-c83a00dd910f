<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.TaskRuntimeStdoutMapper">

    <resultMap type="com.ideal.script.model.entity.TaskRuntimeStdout" id="TaskRuntimeStdoutResult">
            <result property="iid" column="iid"/>
            <result property="itaskInstanceId" column="itask_instance_id"/>
            <result property="iruntimeId" column="iruntime_id"/>
            <result property="istdout" column="istdout"/>
            <result property="istderror" column="istderror"/>
            <result property="icreateTime" column="icreate_time"/>
    </resultMap>

    <sql id="selectTaskRuntimeStdoutVo">
        select iid, itask_instance_id, iruntime_id, istdout, istderror, icreate_time
        from ieai_script_agent_stdout
    </sql>

    <select id="selectStdoutByTaskRuntimeId" parameterType="Long"
            resultMap="TaskRuntimeStdoutResult">
            <include refid="selectTaskRuntimeStdoutVo"/>
            where iruntime_id = #{id}
    </select>

    <insert id="insert" parameterType="com.ideal.script.model.entity.TaskRuntimeStdout" useGeneratedKeys="true"
            keyProperty="iid">
        insert into ieai_script_agent_stdout
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="iid != null">
                iid,
            </if>
            <if test="itaskInstanceId != null">itask_instance_id,</if>
            <if test="iruntimeId != null">iruntime_id,</if>
            <if test="istdout != null">istdout,</if>
            <if test="istderror != null">istderror,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="iid != null">
                #{iid},
            </if>
            <if test="itaskInstanceId != null">#{itaskInstanceId},</if>
            <if test="iruntimeId != null">#{iruntimeId},</if>
            <if test="istdout != null">#{istdout},</if>
            <if test="istderror != null">#{istderror},</if>
        </trim>
    </insert>

    <update id="updateByRunTimeId" parameterType="com.ideal.script.model.entity.TaskRuntimeStdout">
        update ieai_script_agent_stdout
        <trim prefix="SET" suffixOverrides=",">
            <if test="istdout != null">istdout = #{istdout},</if>
            <if test="istderror != null">istderror = #{istderror},</if>
        </trim>
        where iruntime_id = #{iruntimeId}
    </update>
</mapper>