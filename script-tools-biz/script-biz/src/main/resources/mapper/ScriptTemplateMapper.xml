<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.ScriptTemplateMapper">


    <resultMap type="com.ideal.script.model.entity.ScriptTemplate" id="ScriptTemplateResult">
        <result property="id" column="iid"/>
        <result property="name" column="iname"/>
        <result property="scriptType" column="iscriptType"/>
        <result property="content" column="icontent"/>
        <result property="creatorId" column="icreator_id"/>
        <result property="creatorName" column="icreator_name"/>
        <result property="updatorId" column="iupdator_id"/>
        <result property="updatorName" column="iupdator_name"/>
        <result property="createTime" column="icreate_time"/>
        <result property="updateTime" column="iupdate_time"/>
    </resultMap>

    <insert id="insertScriptTemplate" useGeneratedKeys="true" keyProperty="id">
        insert into ieai_script_edit_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                iid,
            </if>
            <if test="name != null and name != ''">iname,</if>
            <if test="scriptType != null">iscript_type,</if>
            <if test="content != null">icontent,</if>
            <if test="creatorId != null">icreator_id,
            </if>
            <if test="creatorName != null">icreator_name,
            </if>
            <if test="updatorId != null">iupdator_id,
            </if>
            <if test="updatorName != null">iupdator_name,
            </if>
            icreate_time,
            iupdate_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="scriptType != null">#{scriptType},</if>
            <if test="content != null">#{content},</if>
            <if test="creatorId != null">#{creatorId},
            </if>
            <if test="creatorName != null">#{creatorName},
            </if>
            <if test="updatorId != null">#{updatorId},
            </if>
            <if test="updatorName != null">#{updatorName},
            </if>
            ${@com.ideal.common.util.DbUtils@getCurrentTime()},
            ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
    </insert>

    <update id="updateScriptTemplate">
        update ieai_script_edit_template
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">iname = #{name},</if>
            <if test="scriptType != null">iscript_type = #{scriptType},</if>
            <if test="content != null">icontent = #{content},</if>
            <if test="creatorId != null">icreator_id =
                #{creatorId},
            </if>
            <if test="creatorName != null">icreator_name =
                #{creatorName},
            </if>
            <if test="updatorId != null">iupdator_id =
                #{updatorId},
            </if>
            <if test="updatorName != null">iupdator_name =
                #{updatorName},
            </if>
            iupdate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
        where iid = #{id}
    </update>


    <delete id="deleteScriptTemplateByIds">
        delete from  ieai_script_edit_template where iid in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <sql id="selectScriptTemplateVo">
        select iid, iname, iscript_type, icontent, icreator_id, icreator_name, iupdator_id, iupdator_name, icreate_time, iupdate_time
        from ieai_script_edit_template
    </sql>

    <select id="selectScriptTemplateList" resultMap="ScriptTemplateResult">
        <include refid="selectScriptTemplateVo"/>
        <where>
            <if test="id != null ">
                and iid = #{id}
            </if>
            <if test="name != null ">
                <bind name="name" value="'%'+ name + '%'"/>
                and iname like #{name}
            </if>
            <if test="scriptType != null ">
                and iscript_type = #{scriptType}
            </if>
            <if test="content != null ">
                and icontent = #{content}
            </if>
            <if test="creatorId != null ">
                and icreator_id = #{creatorId}
            </if>
            <if test="creatorName != null  and creatorName != ''">
                <bind name="creatorName" value="'%' + creatorName + '%'"/>
                and icreator_name like  #{creatorName}
            </if>
            <if test="updatorId != null ">
                and iupdator_id = #{updatorId}
            </if>
            <if test="updatorName != null  and updatorName != ''">
                <bind name="updatorName" value="'%' + updatorName + '%'"/>
                and iupdator_name like #{updatorName}
            </if>
        </where>
    </select>
    <select id="getScriptTemplateDetail" resultMap="ScriptTemplateResult">
        <include refid="selectScriptTemplateVo"/>
        where iid = #{id}
    </select>
    <select id="getScriptTemplateByName" resultMap="ScriptTemplateResult">
        <include refid="selectScriptTemplateVo"/>
        where iname = #{name};
    </select>


</mapper>