<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.AttachmentMapper">

    <resultMap type="com.ideal.script.model.entity.Attachment" id="AttachmentResult">
        <result property="id" column="iid"/>
        <result property="srcScriptUuid" column="isrc_script_uuid"/>
        <result property="name" column="iname"/>
        <result property="size" column="isize"/>
        <result property="uploadtime" column="iuploadtime"/>
        <result property="contents" column="icontents"/>
    </resultMap>
    <resultMap id="selectAttachmentInfo" type="com.ideal.script.model.entity.Attachment">
        <result property="id" column="iid"/>
        <result property="srcScriptUuid" column="isrc_script_uuid"/>
        <result property="name" column="iname"/>
        <result property="size" column="isize"/>
        <result property="uploadtime" column="iuploadtime"/>
        <result property="contents" column="icontents"/>
    </resultMap>

    <resultMap id="selectAttachmentListMap" type="com.ideal.script.model.entity.Attachment">
        <result property="name" column="iname"/>
        <result property="contents" column="icontents"/>
    </resultMap>

    <sql id="selectAttachmentVo">
        select iid, isrc_script_uuid, iname, isize, iuploadtime, icontents
        from ieai_script_attachment
    </sql>

    <select id="selectAttachmentList" parameterType="com.ideal.script.model.entity.Attachment"
            resultMap="AttachmentResult">
        <include refid="selectAttachmentVo"/>
        <where>
            <if test="srcScriptUuid != null  and srcScriptUuid != ''">and isrc_script_uuid = #{srcScriptUuid}</if>
            <if test="name != null  and name != ''">and iname like concat('%', #{name}, '%')</if>
            <if test="size != null ">and isize = #{size}</if>
            <if test="uploadtime != null ">and iuploadtime = #{uploadtime}</if>
            <if test="contents != null  and contents != ''">and icontents = #{contents}</if>
        </where>
    </select>

    <select id="selectAttachmentById" parameterType="Long" resultMap="AttachmentResult">
        <include refid="selectAttachmentVo"/>
        where iid = #{id}
    </select>

    <insert id="insertAttachment" parameterType="com.ideal.script.model.entity.Attachment" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_script_attachment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                iid,
            </if>
            <if test="srcScriptUuid != null">isrc_script_uuid,</if>
            <if test="name != null">iname,</if>
            <if test="size != null">isize,</if>
            iuploadtime,
            <if test="contents != null">icontents,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="srcScriptUuid != null">#{srcScriptUuid},</if>
            <if test="name != null">#{name},</if>
            <if test="size != null">#{size},</if>
            ${@com.ideal.common.util.DbUtils@getCurrentTime()},
            <if test="contents != null">#{contents},</if>
        </trim>
    </insert>

    <update id="updateAttachment" parameterType="com.ideal.script.model.entity.Attachment">
        update ieai_script_attachment
        <trim prefix="SET" suffixOverrides=",">
            <if test="srcScriptUuid != null">isrc_script_uuid = #{srcScriptUuid},</if>
            <if test="name != null">iname = #{name},</if>
            <if test="size != null">isize = #{size},</if>
            iuploadtime = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
            <if test="contents != null">icontents = #{contents},</if>
        </trim>
        where iid = #{id}
    </update>

    <update id="updateScriptUuid" parameterType="String">
        update ieai_script_attachment
        <trim prefix="SET" suffixOverrides=",">
            <if test="scriptUuid != null">isrc_script_uuid = #{scriptUuid},</if>
        </trim>
        where isrc_script_uuid = #{scriptUuid}
    </update>

    <delete id="deleteAttachmentById" parameterType="Long">
        delete
        from ieai_script_attachment
        where iid = #{id}
    </delete>

    <delete id="deleteAttachmentByIds" parameterType="String">
        delete from ieai_script_attachment where iid in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteAttachmentByIdAndUuid">
        delete from ieai_script_attachment
        where isrc_script_uuid = #{uuid} and iid not in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteAttachmentByScriptUuids" parameterType="String">
        delete from ieai_script_attachment where isrc_script_uuid in
        <foreach item="uuid" collection="array" open="(" separator="," close=")">
            #{uuid}
        </foreach>
    </delete>

    <insert id="copyAttachment" parameterType="Long">
        insert into ieai_script_attachment(isrc_script_uuid, iname, isize, iuploadtime, icontents) (select
        isrc_script_uuid, iname, isize, iuploadtime,
        icontents from ieai_script_attachment where isrc_script_uuid is not null and iid in
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>)
    </insert>

    <delete id="deleteAttachmentByUuid">
        delete from ieai_script_attachment
        where isrc_script_uuid =
        <if test="oldUuid != null">
            #{oldUuid}
        </if>
    </delete>

    <select id="getAttachmentByUuid" resultMap="selectAttachmentInfo">
        select * from ieai_script_attachment
        where isrc_script_uuid =
        <if test="uuid != null">
            #{uuid}
        </if>
    </select>

    <select id="getAttachmentListForDownload" parameterType="String" resultMap="selectAttachmentListMap">
        select iname, icontents
        from ieai_script_attachment
        where isrc_script_uuid = #{uuid}

    </select>
    <select id="selectAttachmentByIds" resultType="com.ideal.script.model.entity.Attachment" resultMap="AttachmentResult">
        <include refid="selectAttachmentVo"/>
        where iid in
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </select>
</mapper>