<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.InfoMapper">

    <resultMap type="com.ideal.script.model.entity.Info" id="InfoResult">
        <result property="id"    column="iid"    />
        <result property="uniqueUuid"    column="iunique_uuid"    />
        <result property="scriptNameZh"    column="iscript_name_zh"    />
        <result property="scriptName"    column="iscript_name"    />
        <result property="scriptType"    column="iscript_type"    />
        <result property="execuser"    column="iexecuser"    />
        <result property="editState"    column="iedit_state"    />
        <result property="deleted"    column="ideleted"    />
        <result property="scriptLabel"    column="iscript_label"    />
        <result property="categoryId"    column="icategory_id"    />
        <result property="checkBeforeExec"    column="icheck_before_exec"    />
        <result property="share"    column="ishare"    />
        <result property="whiteCommand"    column="iwhite_command"    />
        <result property="visibleType"    column="ivisible_type"    />
        <result property="platform"    column="iplatform"    />
        <result property="femscript"    column="ifemscript"    />
        <result property="creatorId"    column="icreator_id"    />
        <result property="creatorName"    column="icreator_name"    />
        <result property="updatorId"    column="iupdator_id"    />
        <result property="updatorName"    column="iupdator_name"    />
        <result property="createTime"    column="icreate_time"    />
        <result property="updateTime"    column="iupdate_time"    />
        <result property="scriptSource"    column="iscript_source"    />
        <result property="categoryPath" column="icategory_path"/>
        <result property="orgCode" column="isys_org_code"/>
    </resultMap>


    <sql id="selectInfoVo">
        select iid, iunique_uuid, iscript_name_zh, iscript_name, iscript_type, iexecuser, iedit_state, ideleted, iscript_label, icategory_id, icheck_before_exec, ishare, iwhite_command, ivisible_type, iplatform, ifemscript, icreator_id, icreator_name, iupdator_id, iupdator_name, icreate_time, iupdate_time,iscript_source,icategory_path,isys_org_code from ieai_script_info
    </sql>

    <select id="selectInfoList" parameterType="com.ideal.script.model.entity.Info" resultMap="InfoResult">
        <include refid="selectInfoVo"/>
        <where>  
            <if test="uniqueUuid != null  and uniqueUuid != ''"> and iunique_uuid = #{uniqueUuid}</if>
            <if test="scriptNameZh != null  and scriptNameZh != ''"> and iscript_name_zh = #{scriptNameZh}</if>
            <if test="scriptName != null  and scriptName != ''"> and iscript_name like concat('%', #{scriptName}, '%')</if>
            <if test="scriptType != null  and scriptType != ''"> and iscript_type = #{scriptType}</if>
            <if test="execuser != null  and execuser != ''"> and iexecuser = #{execuser}</if>
            <if test="editState != null "> and iedit_state = #{editState}</if>
            <if test="deleted != null "> and ideleted = #{deleted}</if>
            <if test="scriptLabel != null  and scriptLabel != ''"> and iscript_label = #{scriptLabel}</if>
            <if test="categoryId != null "> and icategory_id = #{categoryId}</if>
            <if test="checkBeforeExec != null "> and icheck_before_exec = #{checkBeforeExec}</if>
            <if test="share != null "> and ishare = #{share}</if>
            <if test="whiteCommand != null "> and iwhite_command = #{whiteCommand}</if>
            <if test="visibleType != null "> and ivisible_type = #{visibleType}</if>
            <if test="platform != null  and platform != ''"> and iplatform = #{platform}</if>
            <if test="femscript != null "> and ifemscript = #{femscript}</if>
            <if test="creatorId != null "> and icreator_id = #{creatorId}</if>
            <if test="creatorName != null  and creatorName != ''"> and icreator_name like concat('%', #{creatorName}, '%')</if>
            <if test="updatorId != null "> and iupdator_id = #{updatorId}</if>
            <if test="updatorName != null  and updatorName != ''"> and iupdator_name like concat('%', #{updatorName}, '%')</if>
            <if test="categoryPath != null and categoryPath != '' and escapedLikeCategoryPath == null">
                <bind name="categoryPath" value="categoryPath + '/' +'%'"/>
                and icategory_path like #{categoryPath}
            </if>
            <if test="escapedLikeCategoryPath != null and escapedLikeCategoryPath != ''">
                <bind name="escapedLikeCategoryPath" value="escapedLikeCategoryPath + '/' +'%'"/>
                and icategory_path like #{escapedLikeCategoryPath}
            </if>
            <if test="categoryPath != null and categoryPath != '' ">
                <bind name="exactCategoryPath" value="categoryPath"/>
                or icategory_path = #{exactCategoryPath}
            </if>
        </where>
    </select>
    
    <select id="selectInfoById" parameterType="Long" resultMap="InfoResult">
        select i.iid, iunique_uuid, iscript_name_zh, iscript_name, iscript_type, iexecuser, iedit_state, ideleted, iscript_label, icategory_id, icheck_before_exec, ishare, iwhite_command, ivisible_type, iplatform, ifemscript, i.icreator_id, i.icreator_name, i.iupdator_id, i.iupdator_name, i.icreate_time, i.iupdate_time,p.icodevalue from ieai_script_info i
        left join ieai_platform_code p on i.iplatform = p.icodevalue
        where i.iid = #{id}
    </select>

    <select id="getScriptInfo" parameterType="Long" resultMap="InfoResult">
        <include refid="selectInfoVo"/>
        <where>
            <if test="scriptInfoQueryDto.scriptName != null and scriptInfoQueryDto.scriptName != '' ">
                and iscript_name = #{scriptInfoQueryDto.scriptName}
            </if>
            <if test="scriptInfoQueryDto.scriptNameZh != null and scriptInfoQueryDto.scriptNameZh != '' ">
                and iscript_name_zh = #{scriptInfoQueryDto.scriptNameZh}
            </if>
            <if test="scriptInfoQueryDto.infoUniqueUuid != null and scriptInfoQueryDto.infoUniqueUuid != '' ">
                and iunique_uuid = #{scriptInfoQueryDto.infoUniqueUuid}
            </if>
            <if test="scriptInfoQueryDto.scriptInfoId != null and scriptInfoQueryDto.scriptInfoId != '' ">
                and iid = #{scriptInfoQueryDto.scriptInfoId}
            </if>
        </where>
    </select>

    <select id="selectInfoByIds" parameterType="Long" resultMap="InfoResult">
        <include refid="selectInfoVo"/>
        where iid in
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectMaxVersionInfos" resultMap="InfoResult">
        <include refid="selectInfoVo"/>
        where iid in
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
        
    <insert id="insertInfo" parameterType="com.ideal.script.model.entity.Info" useGeneratedKeys="true" keyProperty="id">
        insert into ieai_script_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                iid,
            </if>
            <if test="uniqueUuid != null">iunique_uuid,</if>
            <if test="scriptNameZh != null">iscript_name_zh,</if>
            <if test="scriptName != null">iscript_name,</if>
            <if test="scriptType != null">iscript_type,</if>
            <if test="execuser != null">iexecuser,</if>
            <if test="editState != null">iedit_state,</if>
            <if test="deleted != null">ideleted,</if>
            <if test="scriptLabel != null">iscript_label,</if>
            <if test="categoryId != null">icategory_id,</if>
            <if test="checkBeforeExec != null">icheck_before_exec,</if>
            <if test="share != null">ishare,</if>
            <if test="whiteCommand != null">iwhite_command,</if>
            <if test="visibleType != null">ivisible_type,</if>
            <if test="platform != null">iplatform,</if>
            <if test="femscript != null">ifemscript,</if>
            <if test="orgCode != null">isys_org_code,</if>
            <if test="creatorId != null">icreator_id,</if>
            <if test="creatorName != null">icreator_name,</if>
            <if test="updatorId != null">iupdator_id,</if>
            <if test="updatorName != null">iupdator_name,</if>
            <if test="scriptSource != null">iscript_source,</if>
            <if test="categoryPath != null">icategory_path,</if>
            icreate_time,
            iupdate_time,
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="uniqueUuid != null">#{uniqueUuid},</if>
            <if test="scriptNameZh != null">#{scriptNameZh},</if>
            <if test="scriptName != null">#{scriptName},</if>
            <if test="scriptType != null">#{scriptType},</if>
            <if test="execuser != null">#{execuser},</if>
            <if test="editState != null">#{editState},</if>
            <if test="deleted != null">#{deleted},</if>
            <if test="scriptLabel != null">#{scriptLabel},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="checkBeforeExec != null">#{checkBeforeExec},</if>
            <if test="share != null">#{share},</if>
            <if test="whiteCommand != null">#{whiteCommand},</if>
            <if test="visibleType != null">#{visibleType},</if>
            <if test="platform != null">#{platform},</if>
            <if test="femscript != null">#{femscript},</if>
            <if test="orgCode != null">#{orgCode},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="creatorName != null">#{creatorName},</if>
            <if test="updatorId != null">#{updatorId},</if>
            <if test="updatorName != null">#{updatorName},</if>
            <if test="scriptSource != null">#{scriptSource},</if>
            <if test="categoryPath != null">#{categoryPath},</if>
            <choose>
                <when test="createTime != null">
                    #{createTime},
                </when>
                <otherwise>
                    ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                </otherwise>
            </choose>
            <choose>
                <when test="updateTime != null">
                    #{updateTime}
                </when>
                <otherwise>
                    ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                </otherwise>
            </choose>
         </trim>
    </insert>

    <update id="updateInfo" parameterType="com.ideal.script.model.entity.Info">
        update ieai_script_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="uniqueUuid != null">iunique_uuid = #{uniqueUuid},</if>
            <if test="scriptNameZh != null">iscript_name_zh = #{scriptNameZh},</if>
            <if test="scriptName != null">iscript_name = #{scriptName},</if>
            <if test="scriptType != null">iscript_type = #{scriptType},</if>
            <if test="execuser != null">iexecuser = #{execuser},</if>
            <if test="editState != null">iedit_state = #{editState},</if>
            <if test="deleted != null">ideleted = #{deleted},</if>
            <if test="scriptLabel != null">iscript_label = #{scriptLabel},</if>
            <if test="categoryId != null">icategory_id = #{categoryId},</if>
            <if test="checkBeforeExec != null">icheck_before_exec = #{checkBeforeExec},</if>
            <if test="share != null">ishare = #{share},</if>
            <if test="whiteCommand != null">iwhite_command = #{whiteCommand},</if>
            <if test="visibleType != null">ivisible_type = #{visibleType},</if>
            <if test="platform != null">iplatform = #{platform},</if>
            <if test="femscript != null">ifemscript = #{femscript},</if>
            <if test="creatorId != null">icreator_id = #{creatorId},</if>
            <if test="creatorName != null">icreator_name = #{creatorName},</if>
            <if test="updatorId != null">iupdator_id = #{updatorId},</if>
            <if test="updatorName != null">iupdator_name = #{updatorName},</if>
            <if test="scriptSource != null">iscript_source = #{scriptSource},</if>
            <if test="categoryPath != null and categoryPath != ''">icategory_path = #{categoryPath},</if>
            iupdate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
        where iid = #{id}
    </update>
    <update id="updateInfoByUniqueUuid">
        update ieai_script_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="uniqueUuid != null">iunique_uuid = #{uniqueUuid},</if>
            <if test="scriptNameZh != null">iscript_name_zh = #{scriptNameZh},</if>
            <if test="scriptName != null">iscript_name = #{scriptName},</if>
            <if test="scriptType != null">iscript_type = #{scriptType},</if>
            <if test="execuser != null">iexecuser = #{execuser},</if>
            <if test="editState != null">iedit_state = #{editState},</if>
            <if test="deleted != null">ideleted = #{deleted},</if>
            <if test="scriptLabel != null">iscript_label = #{scriptLabel},</if>
            <if test="categoryId != null">icategory_id = #{categoryId},</if>
            <if test="checkBeforeExec != null">icheck_before_exec = #{checkBeforeExec},</if>
            <if test="share != null">ishare = #{share},</if>
            <if test="whiteCommand != null">iwhite_command = #{whiteCommand},</if>
            <if test="visibleType != null">ivisible_type = #{visibleType},</if>
            <if test="platform != null">iplatform = #{platform},</if>
            <if test="femscript != null">ifemscript = #{femscript},</if>
            <if test="creatorId != null">icreator_id = #{creatorId},</if>
            <if test="creatorName != null">icreator_name = #{creatorName},</if>
            <if test="updatorId != null">iupdator_id = #{updatorId},</if>
            <if test="updatorName != null">iupdator_name = #{updatorName},</if>
            icreate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
            iupdate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
        where iunique_uuid = #{uniqueUuid}
    </update>

    <update id="updateInfosByUuid">
        update ieai_script_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="Info.scriptNameZh != null">iscript_name_zh = #{Info.scriptNameZh},</if>
            <if test="Info.scriptName != null">iscript_name = #{Info.scriptName},</if>
            <if test="Info.scriptType != null">iscript_type = #{Info.scriptType},</if>
            <if test="Info.execuser != null">iexecuser = #{Info.execuser},</if>
            <if test="Info.editState != null">iedit_state = #{Info.editState},</if>
            <if test="Info.deleted != null">ideleted = #{Info.deleted},</if>
            <if test="Info.scriptLabel != null">iscript_label = #{Info.scriptLabel},</if>
            <if test="Info.categoryId != null">icategory_id = #{Info.categoryId},</if>
            <if test="Info.checkBeforeExec != null">icheck_before_exec = #{Info.checkBeforeExec},</if>
            <if test="Info.share != null">ishare = #{Info.share},</if>
            <if test="Info.whiteCommand != null">iwhite_command = #{Info.whiteCommand},</if>
            <if test="Info.visibleType != null">ivisible_type = #{Info.visibleType},</if>
            <if test="Info.platform != null">iplatform = #{Info.platform},</if>
            <if test="Info.femscript != null">ifemscript = #{Info.femscript},</if>
            <if test="Info.creatorId != null">icreator_id = #{Info.creatorId},</if>
            <if test="Info.creatorName != null">icreator_name = #{Info.creatorName},</if>
            <if test="Info.updatorId != null">iupdator_id = #{Info.updatorId},</if>
            <if test="Info.updatorName != null">iupdator_name = #{Info.updatorName},</if>
            <if test="Info.scriptSource != null">iscript_source = #{Info.scriptSource},</if>
            <if test="Info.orgCode != null">isys_org_code = #{Info.orgCode},</if>
            icreate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
            iupdate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
        where iunique_uuid in
        <foreach collection="uuids" item="uuid" separator="," open="(" close=")">
            #{uuid}
        </foreach>
    </update>

    <delete id="deleteInfoById" parameterType="Long">
        delete from ieai_script_info where iid = #{id}
    </delete>

    <delete id="deleteInfoByIds" parameterType="Long">
        delete from ieai_script_info where iid in 
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
     <select id="selectInfoListApi" resultMap="InfoResult">
        <include refid="selectInfoVo"/>
        <where>  
            <if test="scriptId != null  and scriptId != ''"> and iid = #{scriptId}</if>
            <if test="scriptName != null  and scriptName != ''"> and iscript_name = #{scriptName}</if>
            <if test="excludeScriptId != null and excludeScriptId != '' and excludeScriptId.size()>0">
                and iid not in
                <foreach collection="excludeScriptId" item="scriptId" open="(" separator="," close=")">
                    #{scriptId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getDefaultVersionIdsByScriptId" resultType="java.lang.Long">
        select distinct(iid) from ieai_script_info_version where is_default = 1 and iversion is not null and (ideleted =
        0 or ideleted is null) and iinfo_unique_uuid in
        (select distinct(iinfo_unique_uuid) from ieai_script_info_version
        <where>
            and iid in
            <foreach collection="scriptIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
        )
    </select>


    <select id="getSameScriptNameCount" parameterType="String" resultType="Integer">
        select count(*) from ieai_script_info where iscript_name = #{scriptName} AND ideleted = 0
    </select>


    <select id="selectInfoByUniqueUuid"  resultMap="InfoResult">
        <include refid="selectInfoVo"/>
          where iunique_uuid = #{uniqueUuid}
    </select>

    <select id="selectInfoByScriptUuid"  resultMap="InfoResult">
        <include refid="selectInfoVo"/>
        where iunique_uuid = (select iinfo_unique_uuid from ieai_script_info_version where isrc_script_uuid = #{scriptUuid})
    </select>

    <select id="validScriptNameZhCountExist" resultType="java.lang.Boolean">
        select
            exists (
                select
                    1
                from
                    ieai_script_info
                where
                    iscript_name_zh = #{scriptNameZh}
            )
     </select>
    <select id="selectInfoByScriptName" resultMap="InfoResult">
        <include refid="selectInfoVo"/>
        where iscript_name = #{scriptName}
        and ideleted = 0
    </select>
    <select id="selectInfoByScriptNameZh" resultMap="InfoResult">
        <include refid="selectInfoVo"/>
        where iscript_name_zh = #{scriptNameZh}
        and ideleted = 0
    </select>
    <select id="getUniqueUuidByIds" resultType="java.lang.String">
        select iunique_uuid  from ieai_script_info where iid in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="scriptState == 'edit'">and iedit_state = 0</if>
        <if test="scriptState == 'publish'">and iedit_state = 1 </if>
    </select>
    <select id="getLabelList" resultType="java.lang.String">
        select iscript_label from ieai_script_info where iscript_label is not null and iscript_label != ''
    </select>
    <select id="getSameScriptNameZhCount" resultType="java.lang.Integer">
        select count(*) from ieai_script_info where iscript_name_zh = #{scriptNameZh} AND ideleted = 0
    </select>


    <select id="getMaxVersinByUniqueUuid" resultType="java.lang.String">
        select max(iversion) from ieai_script_info_version where iinfo_unique_uuid = #{uniqueUuid}
    </select>

    <select id="getScriptCategoryIconList" resultType="com.ideal.script.dto.ScriptCategoryIconDto">
        select
            i.icategory_path as categoryPath,
            v.isrc_script_uuid as scriptSrcUuid
        from
            ieai_script_info_version v
            join ieai_script_info i on v.iinfo_unique_uuid = i.iunique_uuid
        where
            v.isrc_script_uuid in
            <foreach collection="srcScriptUuids" item="srcScriptUuid" open="(" separator="," close=")">
                #{srcScriptUuid}
            </foreach>
    </select>
</mapper>