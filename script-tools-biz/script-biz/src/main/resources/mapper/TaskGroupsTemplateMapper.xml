<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.TaskGroupsTemplateMapper">

    <resultMap type="com.ideal.script.model.entity.TaskGroups" id="TaskGroupsResult">
            <result property="id" column="iid"/>
            <result property="scriptTaskId" column="iscript_task_id"/>
            <result property="sysmComputerGroupId" column="isysm_computer_group_id"/>
            <result property="cpname" column="icpname"/>
            <result property="createTime" column="icreate_time"/>
    </resultMap>

    <sql id="selectTaskGroupsVo">
        select iid, iscript_task_id, isysm_computer_group_id, icpname, icreate_time
        from ieai_script_task_groups_temp
    </sql>

    <insert id="insertTaskGroups" parameterType="com.ideal.script.model.entity.TaskGroups" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_script_task_groups_temp
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">
                        iid,
                    </if>
                    <if test="scriptTaskId != null">iscript_task_id,
                    </if>
                    <if test="sysmComputerGroupId != null">isysm_computer_group_id,
                    </if>
                    <if test="cpname != null">icpname,
                    </if>
                    icreate_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">
                        #{id},
                    </if>
                    <if test="scriptTaskId != null">#{scriptTaskId},
                    </if>
                    <if test="sysmComputerGroupId != null">#{sysmComputerGroupId},
                    </if>
                    <if test="cpname != null">#{cpname},
                    </if>
                    ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
    </insert>

    <select id="selectTaskGroupsByTaskId" parameterType="Long"
            resultMap="TaskGroupsResult">
        <include refid="selectTaskGroupsVo"/>
        where
        <if test="taskId != null">
            iscript_task_id = #{taskId}
        </if>
    </select>

    <delete id="deleteByTaskId">
        delete from ieai_script_task_groups_temp where iscript_task_id = #{taskId}
    </delete>
</mapper>