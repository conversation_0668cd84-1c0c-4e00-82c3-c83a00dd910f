<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.TaskScheduleMapper">

    <resultMap type="com.ideal.script.model.entity.TaskScheduleEntity" id="TaskScheduleResult">
            <result property="id" column="iid"/>
            <result property="scriptTaskId" column="iscript_task_id"/>
            <result property="scheduleId" column="ischedule_id"/>
            <result property="state" column="istate"/>
            <result property="createTime" column="icreate_time"/>
            <result property="updateTime" column="iupdate_time"/>
            <result property="type" column="itype"/>
    </resultMap>

    <sql id="selectTaskSchedule">
        select iid, iscript_task_id, ischedule_id, istate, icreate_time, iupdate_time, itype
        from ieai_script_task_schedule
    </sql>

    <select id="selectTaskScheduleList" parameterType="com.ideal.script.model.entity.TaskScheduleEntity" resultMap="TaskScheduleResult">
        <include refid="selectTaskSchedule"/>
        <where>
                        <if test="scriptTaskId != null ">
                            and iscript_task_id = #{scriptTaskId}
                        </if>
                        <if test="scheduleId != null ">
                            and ischedule_id = #{scheduleId}
                        </if>
                        <if test="state != null ">
                            and istate = #{state}
                        </if>
                        <if test="createTime != null ">
                            and icreate_time = #{createTime}
                        </if>
                        <if test="updateTime != null ">
                            and iupdate_time = #{updateTime}
                        </if>
                        <if test="type != null ">
                            and itype = #{type}
                        </if>
        </where>
    </select>

    <select id="selectTaskScheduleById" parameterType="Long"
            resultMap="TaskScheduleResult">
            <include refid="selectTaskSchedule"/>
            where iid = #{id}
    </select>

    <select id="selectTaskScheduleByTaskId" parameterType="Long"
            resultMap="TaskScheduleResult">
            <include refid="selectTaskSchedule"/>
            where iscript_task_id = #{scriptTaskId}
    </select>

    <insert id="insertTaskSchedule" parameterType="com.ideal.script.model.entity.TaskScheduleEntity" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_script_task_schedule
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">iid,
                    </if>
                    <if test="scriptTaskId != null">iscript_task_id,
                    </if>
                    <if test="scheduleId != null">ischedule_id,
                    </if>
                    <if test="state != null">istate,
                    </if>
                    <if test="createTime != null">icreate_time,
                    </if>
                    <if test="updateTime != null">iupdate_time,
                    </if>
                    <if test="type != null">itype,
                    </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},
                    </if>
                    <if test="scriptTaskId != null">#{scriptTaskId},
                    </if>
                    <if test="scheduleId != null">#{scheduleId},
                    </if>
                    <if test="state != null">#{state},
                    </if>
                    <if test="createTime != null">#{createTime},
                    </if>
                    <if test="updateTime != null">#{updateTime},
                    </if>
                    <if test="type != null">#{type},
                    </if>
        </trim>
    </insert>

    <update id="updateTaskSchedule" parameterType="com.ideal.script.model.entity.TaskScheduleEntity">
        update ieai_script_task_schedule
        <trim prefix="SET" suffixOverrides=",">
                    <if test="scriptTaskId != null">iscript_task_id =
                        #{scriptTaskId},
                    </if>
                    <if test="scheduleId != null">ischedule_id =
                        #{scheduleId},
                    </if>
                    <if test="state != null">istate =
                        #{state},
                    </if>
                    <if test="createTime != null">icreate_time =
                        #{createTime},
                    </if>
                    <if test="updateTime != null">iupdate_time =
                        #{updateTime},
                    </if>
                    <if test="type != null">itype =
                        #{type},
                    </if>
        </trim>
        where iid = #{id}
    </update>

    <delete id="deleteTaskScheduleById" parameterType="Long">
        delete
        from ieai_script_task_schedule where iid = #{id}
    </delete>

    <delete id="deleteTaskScheduleByIds" parameterType="String">
        delete from ieai_script_task_schedule where iid in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <update id="updateTaskScheduleByScheduleId">
         update ieai_script_task_schedule
        <trim prefix="SET" suffixOverrides=",">
                    <if test="scriptTaskId != null">iscript_task_id =
                        #{scriptTaskId},
                    </if>
                    <if test="scheduleId != null">ischedule_id =
                        #{scheduleId},
                    </if>
                    <if test="state != null">istate =
                        #{state},
                    </if>
                    <if test="createTime != null">icreate_time =
                        #{createTime},
                    </if>
                    <if test="updateTime != null">iupdate_time =
                        #{updateTime},
                    </if>
                    <if test="type != null">itype =
                        #{type},
                    </if>
        </trim>
        where ischedule_id = #{scheduleId}
    </update>
</mapper>