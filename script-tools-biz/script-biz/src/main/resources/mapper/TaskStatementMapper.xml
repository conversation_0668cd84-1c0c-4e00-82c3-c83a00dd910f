<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.TaskStatementMapper">

    <resultMap type="com.ideal.script.model.bean.TaskStatementBean" id="TaskStatementResult">
        <result property="scriptNameZh" column="iscript_name_zh"/>
        <result property="scriptName" column="iscript_name"/>
        <result property="categoryPath" column="icategory_path"/>
        <result property="creatorName" column="icreator_name"/>
        <result property="startUser" column="istart_user"/>
        <result property="auditUser" column="iaudit_user"/>
        <result property="agentIp" column="iagent_ip"/>
        <result property="centerName" column="icenter_name"/>
        <result property="startTime" column="istart_time"/>
        <result property="taskId" column="task_id"/>
        <result property="instanceId" column="instance_id"/>
        <result property="agentPort" column="iagent_port"/>
    </resultMap>

    <select id="selectTaskStatementPage" resultMap="TaskStatementResult">
        select
        ins.iagent_ip,
        isai.icenter_name,
        task.istart_user,
        task.iscript_name_zh,
        task.iscript_name,
        task.icreator_name,
        task.icategory_path,
        isar.iaudit_user,
        ins.istart_time,
        task.iid as task_id,
        ins.iid as instance_id,
        ins.iagent_port
        from
        (
        select
        isti.iid ,
        isti.iscript_task_id ,
        istr.itask_instance_id ,
        istr.iagent_ip ,
        istr.iagent_port,
        isti.istart_time
        from
        ieai_script_task_instance isti
        join ieai_script_task_runtime istr on
        isti.iid = istr.itask_instance_id
        where
        isti.istatus in (20, 60, 70)
        <if test="startTimeRange != null and startTimeRange.size() == 2">
            and isti.istart_time >= #{startTimeRange[0]}
            and isti.istart_time  &lt;= #{startTimeRange[1]}
        </if>
        order by
        isti.istatus,
        isti.icreate_time
        ) ins
        join ieai_script_agent_info isai on
        isai.iagent_ip = ins.iagent_ip
        and ins.iagent_port = isai.iagent_port
        join
        (
        select
        ist.istart_user,
        isi.iscript_name_zh,
        isi.iscript_name,
        isi.icreator_name,
        isi.icategory_path,
        ist.iid
        from
        ieai_script_task ist
        join ieai_script_info_version isiv on
        ist.isrc_script_uuid = isiv.isrc_script_uuid
        join ieai_script_info isi on
        isi.iunique_uuid = isiv.iinfo_unique_uuid
        <where>
            <if test="scriptNameZh != null">
                <bind name="scriptNameZh" value="'%' + scriptNameZh + '%'"/>
                and isi.iscript_name_zh like #{scriptNameZh}
            </if>
            <if test="scriptName != null">
                <bind name="scriptName" value="'%' + scriptName + '%'"/>
                and isi.iscript_name like #{scriptName}
            </if>
            <if test="creatorName != null">
                <bind name="creatorName" value="'%' + creatorName + '%'"/>
                and isi.icreator_name like #{creatorName}
            </if>
            <if test="startUser != null">
                <bind name="startUser" value="'%' + startUser + '%'"/>
                and ist.istart_user like #{startUser}
            </if>
            <if test="categoryPath != null and categoryPath != '' and escapedLikeCategoryPath == null">
                <bind name="categoryPath" value="categoryPath + '/' +'%'"/>
                and (isi.icategory_path like #{categoryPath}
            </if>
            <if test="escapedLikeCategoryPath != null and escapedLikeCategoryPath != ''">
                <bind name="escapedLikeCategoryPath" value="escapedLikeCategoryPath + '/' +'%'"/>
                and (isi.icategory_path like #{escapedLikeCategoryPath}
            </if>
            <if test="categoryPath != null and categoryPath != '' ">
                <bind name="exactCategoryPath" value="categoryPath"/>
                or isi.icategory_path = #{exactCategoryPath})
            </if>
        </where>
        ) task
        on
        task.iid = ins.iscript_task_id
        left join ieai_script_audit_relation isar on
        isar.iscript_task_id = ins.iscript_task_id

        <where>
            <if test="auditUser != null">
                and isar.iaudit_user = #{auditUser}
            </if>
            <if test="agentIp">
                <bind name="agentIp" value="'%' + agentIp + '%'"/>
                and ins.iagent_ip like #{agentIp}
            </if>
            <if test="centerName">
                <bind name="centerName" value="'%' + centerName + '%'"/>
                and isai.icenter_name like #{centerName}
            </if>
        </where>

    </select>
</mapper>