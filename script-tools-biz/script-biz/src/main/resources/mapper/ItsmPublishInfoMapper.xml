<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.ItsmPublishInfoMapper">
    
    <resultMap type="com.ideal.script.model.entity.ItsmPublishScript" id="dataResult">
        <result property="iid"    column="iid"    />
        <result property="itsmProductUrl"    column="itsm_product_url"    />
        <result property="auditState"    column="iaudit_state"    />
        <result property="productId"    column="iproduct_id"    />
        <result property="createdTime"    column="icreated_time"    />
        <result property="createdUserId"    column="icreated_user_id"    />
        <result property="zipFileContent"    column="izip_file_content"    />
    </resultMap>

    <sql id="selectItsmPublishInfoVo">
        select iid, itsm_product_url, iproduct_id, icreated_time, icreated_user_id, izip_file_content from ieai_script_itsm_publish_info
    </sql>

    <select id="getAuditingDataBySrcScriptUuid" parameterType="String" resultMap="dataResult">
        SELECT
            p.iid,
            p.itsm_product_url,
            pr.iaudit_state,
            p.iproduct_id,
            p.icreated_time,
            p.icreated_user_id
        FROM
            ieai_script_itsm_publish_info p,
            ieai_script_itsm_product_info i,
            ieai_script_to_product pr
        WHERE
            p.iid = i.ipublish_info_id
          AND pr.iid = p.iproduct_id
          AND i.isrc_script_uuid = #{srcScriptUuid}
          AND pr.iaudit_state = 2
    </select>

    <select id="getDetailByOrderNumber" parameterType="java.lang.String" resultMap="dataResult">
        SELECT
            a.iid,
            a.itsm_product_url,
            a.iproduct_id,
            a.icreated_time,
            a.icreated_user_id,
            a.izip_file_content
        FROM
            ieai_script_itsm_publish_info a
                JOIN ieai_script_to_product b
        WHERE
            a.iproduct_id = b.iid
          AND b.iorder_number = #{orderNumber}
    </select>

    <insert id="insertItsmPublishInfo" parameterType="map" useGeneratedKeys="true" keyProperty="iid">
        insert into ieai_script_itsm_publish_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="iid != null">iid,</if>
            <if test="itsmProductUrl != null and itsmProductUrl != ''">itsm_product_url,</if>
<!--            <if test="auditState != null">iaudit_state,</if>-->
            <if test="productId != null">iproduct_id,</if>
<!--            <if test="orderNumber != null and orderNumber != ''">iorder_number,</if>-->
            <if test="zipFileContent != null">izip_file_content,</if>
            <if test="fileName != null and fileName != ''">ifile_name,</if>
            icreated_time,
            <if test="createdUserId != null">icreated_user_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="iid != null">#{iid},</if>
            <if test="itsmProductUrl != null and itsmProductUrl != ''">#{itsmProductUrl},</if>
<!--            <if test="auditState != null">#{auditState},</if>-->
            <if test="productId != null">#{productId},</if>
<!--            <if test="orderNumber != null and orderNumber != ''">#{orderNumber},</if>-->
            <if test="zipFileContent != null">#{zipFileContent},</if>
            <if test="fileName != null and fileName != ''">#{fileName},</if>
            ${@com.ideal.common.util.DbUtils@getCurrentTime()},
            <if test="createdUserId != null">#{createdUserId},</if>
        </trim>
    </insert>

    <delete id="deleteByIid" parameterType="java.lang.Long">
            delete from ieai_script_itsm_publish_info where iid = #{iid}
    </delete>

</mapper>