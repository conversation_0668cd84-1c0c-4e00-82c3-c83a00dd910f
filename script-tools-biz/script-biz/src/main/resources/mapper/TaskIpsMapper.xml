<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.TaskIpsMapper">

    <resultMap type="com.ideal.script.model.entity.TaskIps" id="TaskIpsResult">
            <result property="id" column="iid"/>
            <result property="scriptTaskId" column="iscript_task_id"/>
            <result property="sysmComputerGroupId" column="isysm_computer_group_id"/>
            <result property="scriptAgentinfoId" column="iscript_agentinfo_id"/>
            <result property="execUserName" column="iexec_user_name"/>
            <result property="operid" column="ioperid"/>
            <result property="alreadyimpFlag" column="ialreadyimp_flag"/>
            <result property="startType" column="istart_type"/>
            <result property="createTime" column="icreate_time"/>
    </resultMap>

    <sql id="selectTaskIpsVo">
        select iid, iscript_task_id, isysm_computer_group_id, iscript_agentinfo_id, iexec_user_name, ioperid, ialreadyimp_flag, istart_type, icreate_time
        from ieai_script_task_ips
    </sql>

    <select id="selectTaskIpsList" parameterType="com.ideal.script.model.entity.TaskIps" resultMap="TaskIpsResult">
        <include refid="selectTaskIpsVo"/>
        <where>
                        <if test="scriptTaskId != null ">
                            and iscript_task_id = #{scriptTaskId}
                        </if>
                        <if test="sysmComputerGroupId != null ">
                            and isysm_computer_group_id = #{sysmComputerGroupId}
                        </if>
                        <if test="scriptAgentinfoId != null ">
                            and iscript_agentinfo_id = #{scriptAgentinfoId}
                        </if>
                        <if test="execUserName != null  and execUserName != ''">
                            and iexec_user_name like concat('%', #{execUserName}, '%')
                        </if>
                        <if test="operid != null ">
                            and ioperid = #{operid}
                        </if>
                        <if test="alreadyimpFlag != null ">
                            and ialreadyimp_flag = #{alreadyimpFlag}
                        </if>
                        <if test="startType != null ">
                            and istart_type = #{startType}
                        </if>
        </where>
    </select>

    <select id="selectTaskIpsById" parameterType="Long"
            resultMap="TaskIpsResult">
            <include refid="selectTaskIpsVo"/>
            where iid = #{id}
    </select>

    <insert id="insertTaskIps" parameterType="com.ideal.script.model.entity.TaskIps" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_script_task_ips
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">
                        iid,
                    </if>
                    <if test="scriptTaskId != null">iscript_task_id,
                    </if>
                    <if test="sysmComputerGroupId != null">isysm_computer_group_id,
                    </if>
                    <if test="scriptAgentinfoId != null">iscript_agentinfo_id,
                    </if>
                    <if test="execUserName != null">iexec_user_name,
                    </if>
                    <if test="operid != null">ioperid,
                    </if>
                    <if test="alreadyimpFlag != null">ialreadyimp_flag,
                    </if>
                    <if test="startType != null">istart_type,
                    </if>
                    icreate_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">
                        #{id},
                    </if>
                    <if test="scriptTaskId != null">#{scriptTaskId},
                    </if>
                    <if test="sysmComputerGroupId != null">#{sysmComputerGroupId},
                    </if>
                    <if test="scriptAgentinfoId != null">#{scriptAgentinfoId},
                    </if>
                    <if test="execUserName != null">#{execUserName},
                    </if>
                    <if test="operid != null">#{operid},
                    </if>
                    <if test="alreadyimpFlag != null">#{alreadyimpFlag},
                    </if>
                    <if test="startType != null">#{startType},
                    </if>
                    ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
    </insert>

    <update id="updateTaskIps" parameterType="com.ideal.script.model.entity.TaskIps">
        update ieai_script_task_ips
        <trim prefix="SET" suffixOverrides=",">
                    <if test="scriptTaskId != null">iscript_task_id =
                        #{scriptTaskId},
                    </if>
                    <if test="sysmComputerGroupId != null">isysm_computer_group_id =
                        #{sysmComputerGroupId},
                    </if>
                    <if test="scriptAgentinfoId != null">iscript_agentinfo_id =
                        #{scriptAgentinfoId},
                    </if>
                    <if test="execUserName != null">iexec_user_name =
                        #{execUserName},
                    </if>
                    <if test="operid != null">ioperid =
                        #{operid},
                    </if>
                    <if test="alreadyimpFlag != null">ialreadyimp_flag =
                        #{alreadyimpFlag},
                    </if>
                    <if test="startType != null">istart_type =
                        #{startType},
                    </if>
        </trim>
        where iid = #{id}
    </update>



    <delete id="deleteTaskIpsById" parameterType="Long">
        delete
        from ieai_script_task_ips where iid = #{id}
    </delete>

    <delete id="deleteTaskIpsByTaskId" parameterType="Long">
        delete
        from ieai_script_task_ips where iscript_task_id = #{taskId}
    </delete>

    <delete id="deleteTaskIpsByIds" parameterType="String">
        delete from ieai_script_task_ips where iid in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 查询脚本任务指定运行的agent总数 -->
    <select id="getTotalAgentCountForTask" resultType="java.lang.Integer">
        select count(iid) from ieai_script_task_ips where iscript_task_id=#{scriptTaskId}
    </select>

    <!--查询脚本任务绑定的Agent中未运行的agent总数-->
    <select id="getUnexecutedAgentCountForTask" resultType="java.lang.Integer">
         select count(iid) from ieai_script_task_ips where iscript_task_id=#{scriptTaskId}
         and ialreadyimp_flag = 0
    </select>

    <!--获取当前任务在分批执行过程中，当前的最大执行批次-->
    <select id="getMaxOperIdForTask" resultType="java.lang.Integer">
        select  max(ioperid) from ieai_script_task_ips  where iscript_task_id=#{scriptTaskId}
    </select>

    <!--更新任务-->
    <update id="updateIpsStatusAndIncrementOperId">
        update ieai_script_task_ips set ialreadyimp_flag= 1 , ioperid = #{executionBatch} where iid in
          <foreach item="item" collection="taskIpIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <!--获取所选任务指定服务器运行的基础信息-->
     <resultMap type="com.ideal.script.model.bean.TaskIpsAgentResultBean" id="TaskIpsAgentResult">
            <result property="scriptTaskIpsId" column="iscript_task_ips_id"/>
            <result property="scriptAgentInfoId" column="iscript_agentinfo_id"/>
            <result property="execUserName" column="iexec_user_name"/>
            <result property="sysmAgentInfoId" column="isysm_agent_info_id"/>
            <result property="agentIp" column="iagent_ip"/>
            <result property="agentPort" column="iagent_port"/>
    </resultMap>

    <select id="getTaskIpsInfo" resultMap="TaskIpsAgentResult">
       SELECT
            a.iid iscript_task_ips_id,
            a.iscript_agentinfo_id,
            a.iexec_user_name,
            b.isysm_agent_info_id,
            b.iagent_ip,
            b.iagent_port
        FROM
            ieai_script_task_ips a
            join ieai_script_agent_info b
        on a.iscript_agentinfo_id = b.iid
        where a.iscript_task_id=#{taskId}
         and a.iid in
        <foreach item="item" collection="taskIpIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!--根据agent实例运行Id获取基础信息-->
     <select id="getTaskIpsInfoByRuntimeId" resultMap="TaskIpsAgentResult">
        SELECT
            a.iid iscript_task_ips_id,
            a.iscript_agentinfo_id,
            a.iexec_user_name,
            b.isysm_agent_info_id,
            b.iagent_ip,
            b.iagent_port
        FROM
            ieai_script_task_ips a
        JOIN ieai_script_agent_info b ON a.iscript_agentinfo_id = b.iid
        WHERE
            a.iid = (
                SELECT
                    iscript_task_ips_id
                FROM
                    ieai_script_task_runtime
                WHERE
                    iid = #{taskRuntimeId}
            )
     </select>



     <select id="getBindAllTaskIpsInfo" resultMap="TaskIpsAgentResult">
       SELECT
            a.iid iscript_task_ips_id,
            a.iscript_agentinfo_id,
            a.iexec_user_name,
            b.isysm_agent_info_id,
            b.iagent_ip,
            b.iagent_port
        FROM
            ieai_script_task_ips a
            join ieai_script_agent_info b
        on a.iscript_agentinfo_id = b.iid
        where a.iscript_task_id =#{scriptTaskId}
    </select>


    <select id="getBindExecTaskIpsInfo" resultMap="TaskIpsAgentResult">
        SELECT
            a.iid iscript_task_ips_id,
            a.iscript_agentinfo_id,
            a.iexec_user_name,
            b.isysm_agent_info_id,
            b.iagent_ip,
            b.iagent_port
        FROM
            ieai_script_task_ips a
                join ieai_script_agent_info b
                     on a.iscript_agentinfo_id = b.iid
        where a.iscript_task_id =#{scriptTaskId} and a.ialreadyimp_flag = 0
    </select>


    <select id="getNotFirstBatchIpsId" resultMap="TaskIpsAgentResult">
        SELECT
            a.iid iscript_task_ips_id,
            a.iscript_agentinfo_id,
            a.iexec_user_name,
            b.isysm_agent_info_id,
            b.iagent_ip,
            b.iagent_port
        FROM
            ieai_script_task_ips a
                join ieai_script_agent_info b
                     on a.iscript_agentinfo_id = b.iid
        where a.iscript_task_id =#{scriptTaskId} and a.ialreadyimp_flag = 0 and a.iid not in
        <foreach item="item" collection="ipsIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


</mapper>