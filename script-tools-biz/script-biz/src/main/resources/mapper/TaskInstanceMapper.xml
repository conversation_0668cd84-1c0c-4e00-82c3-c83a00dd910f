<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.TaskInstanceMapper">

    <resultMap type="com.ideal.script.model.entity.TaskInstance" id="TaskInstanceResult">
            <result property="id" column="iid"/>
            <result property="scriptTaskId" column="iscript_task_id"/>
            <result property="srcScriptUuid" column="isrc_script_uuid"/>
            <result property="status" column="istatus"/>
            <result property="startTime" column="istart_time"/>
            <result property="endTime" column="iend_time"/>
            <result property="runNums" column="irun_nums"/>
            <result property="serverNum" column="iserver_num"/>
            <result property="taskScheduler" column="itask_scheduler"/>
            <result property="taskCron" column="itask_cron"/>
            <result property="partExec" column="ipart_exec"/>
            <result property="ignore" column="iignore"/>
            <result property="startType" column="istart_type"/>
            <result property="createTime" column="icreate_time"/>
            <result property="callerTaskId" column="icaller_task_id"/>
    </resultMap>

    <sql id="selectTaskInstanceVo">
        select iid, iscript_task_id, isrc_script_uuid, istatus, istart_time, iend_time, irun_nums, iserver_num, itask_scheduler, itask_cron, ipart_exec, iignore, istart_type, icreate_time,icaller_task_id
        from ieai_script_task_instance
    </sql>

    <select id="selectTaskInstanceList" parameterType="com.ideal.script.model.entity.TaskInstance" resultMap="TaskInstanceResult">
        <include refid="selectTaskInstanceVo"/>
        <where>
                        <if test="scriptTaskId != null ">
                            and iscript_task_id = #{scriptTaskId}
                        </if>
                        <if test="srcScriptUuid != null  and srcScriptUuid != ''">
                            and isrc_script_uuid = #{srcScriptUuid}
                        </if>
                        <if test="status != null ">
                            and istatus = #{status}
                        </if>
                        <if test="startTime != null ">
                            and istart_time = #{startTime}
                        </if>
                        <if test="endTime != null ">
                            and iend_time = #{endTime}
                        </if>
                        <if test="runNums != null ">
                            and irun_nums = #{runNums}
                        </if>
                        <if test="serverNum != null ">
                            and iserver_num = #{serverNum}
                        </if>
                        <if test="taskScheduler != null ">
                            and itask_scheduler = #{taskScheduler}
                        </if>
                        <if test="taskCron != null  and taskCron != ''">
                            and itask_cron = #{taskCron}
                        </if>
                        <if test="partExec != null ">
                            and ipart_exec = #{partExec}
                        </if>
                        <if test="ignore != null ">
                            and iignore = #{ignore}
                        </if>
                        <if test="startType != null ">
                            and istart_type = #{startType}
                        </if>
        </where>
    </select>

    <select id="selectTaskInstanceById" parameterType="Long"
            resultMap="TaskInstanceResult">
            <include refid="selectTaskInstanceVo"/>
            where iid = #{id}
    </select>

    <select id="selectIpsCount" parameterType="Long" resultType="java.lang.Long">
        select count(iid) from ieai_script_task_ips where iscript_task_id = (select iscript_task_id from ieai_script_task_instance where iid = #{id})
    </select>

    <select id="selectTaskInstanceIdsById" parameterType="Long" resultType="java.lang.Long">
        select iid from ieai_script_task_instance where iid in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectTaskInstanceByTaskId" parameterType="Long"
            resultMap="TaskInstanceResult">
        <include refid="selectTaskInstanceVo"/>
        where iscript_task_id = #{id}
    </select>

    <select id="getTaskInstanceByRuntimeId" parameterType="Long"
            resultMap="TaskInstanceResult">
        <include refid="selectTaskInstanceVo"/>
        where iid = (select itask_instance_id from ieai_script_task_runtime where iid = #{runtimeId})
    </select>

    <select id="getTaskInstanceByTaskInfoId" parameterType="Long"
            resultMap="TaskInstanceResult">
        <include refid="selectTaskInstanceVo"/>
        where iscript_task_id = #{taskInfoId}
    </select>

    <insert id="insertTaskInstance" parameterType="com.ideal.script.model.entity.TaskInstance" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_script_task_instance
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">
                        iid,
                    </if>
                    <if test="scriptTaskId != null">iscript_task_id,
                    </if>
                    <if test="srcScriptUuid != null">isrc_script_uuid,
                    </if>
                    <if test="status != null">istatus,
                    </if>
                    istart_time,
                    <if test="runNums != null">irun_nums,
                    </if>
                    <if test="serverNum != null">iserver_num,
                    </if>
                    <if test="taskScheduler != null">itask_scheduler,
                    </if>
                    <if test="taskCron != null">itask_cron,
                    </if>
                    <if test="partExec != null">ipart_exec,
                    </if>
                    <if test="ignore != null">iignore,
                    </if>
                    <if test="startType != null">istart_type,
                    </if>
                    <if test="callerTaskId != null">icaller_task_id,
                    </if>
                    icreate_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="id != null">
                            #{id},
                        </if>
                        <if test="scriptTaskId != null">#{scriptTaskId},</if>
                        <if test="srcScriptUuid != null">#{srcScriptUuid},</if>
                        <if test="status != null">#{status},</if>
                        ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                        <if test="runNums != null">#{runNums},</if>
                        <if test="serverNum != null">#{serverNum},</if>
                        <if test="taskScheduler != null">#{taskScheduler},</if>
                        <if test="taskCron != null">#{taskCron},</if>
                        <if test="partExec != null">#{partExec},</if>
                        <if test="ignore != null">#{ignore},</if>
                        <if test="startType != null">#{startType},</if>
                        <if test="callerTaskId != null">#{callerTaskId},</if>
                        ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
    </insert>

    <update id="updateTaskInstance" parameterType="com.ideal.script.model.entity.TaskInstance">
        update ieai_script_task_instance
        <trim prefix="SET" suffixOverrides=",">
                        <if test="scriptTaskId != null">iscript_task_id = #{scriptTaskId},</if>
                        <if test="srcScriptUuid != null">isrc_script_uuid = #{srcScriptUuid},</if>
                        <if test="runNums != null">irun_nums = #{runNums},</if>
                        <if test="serverNum != null">iserver_num = #{serverNum},</if>
                        <if test="taskScheduler != null">itask_scheduler = #{taskScheduler},</if>
                        <if test="taskCron != null">itask_cron = #{taskCron},</if>
                        <if test="partExec != null">ipart_exec = #{partExec},</if>
                        <if test="ignore != null">iignore = #{ignore},</if>
                        <if test="startType != null">istart_type = #{startType},</if>
                         <if test="status != null "> istatus = #{status}, </if>
        </trim>
        where iid = #{id}
    </update>

    <delete id="deleteTaskInstanceById" parameterType="Long">
        delete
        from ieai_script_task_instance where iid = #{id}
    </delete>

    <delete id="deleteTaskInstanceByIds" parameterType="String">
        delete from ieai_script_task_instance where iid in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateServerNum">
        update ieai_script_task_instance set iserver_num = iserver_num -1
        where iid = #{taskInstanceId}
        and istatus not in
         <foreach item="state" collection="notInStates" open="(" separator="," close=")">
            #{state}
        </foreach>

    </update>



    <select id="getStatusSummary" parameterType="java.lang.Long" resultType="java.util.HashMap">
        select
        istate,count(1) as counts
        from ieai_script_task_runtime
        where itask_instance_id= #{taskInstanceId} group by istate
    </select>

    <update id="updateState" >
        update ieai_script_task_instance set istatus = #{state},
        iend_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()}
        where iid = #{taskInstanceId}
        <!-- and istaus != ${partialRunning}-->
        and istatus not in
        <foreach item="state" collection="excludeStates" open="(" separator="," close=")">
            #{state}
        </foreach>
    </update>

    <update id="updateBatchTaskState" >
        update ieai_script_task_instance set istatus = #{state},
        iend_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()}
        where iid in
        <foreach item="id" collection="taskInstanceId" open="(" separator="," close=")">
            #{id}
        </foreach>
        and istatus not in
        <foreach item="state" collection="excludeStates" open="(" separator="," close=")">
            #{state}
        </foreach>
    </update>

    <!--这个字段新的应该没用了，老的V8里面以前的作业，这个是作业下运行的节点的个数-->
    <update id="updateRunNum">
      update ieai_script_task_instance set irun_nums = irun_nums -1
        where iid = #{taskInstanceId}
    </update>

    <select id="updateEndTime">
      update ieai_script_task_instance set iend_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()}
        where iid = #{taskInstanceId}
        and istatus not in
         <foreach item="state" collection="notInStates" open="(" separator="," close=")">
            #{state}
        </foreach>
    </select>

    <select id="selectRunningTaskByScriptTaskId" parameterType="Long" resultType="Long">
        select iid from ieai_script_task_instance
        <where>
            iscript_task_id = #{scriptTaskId}
            and istatus in (10, 11, 30, 50)
        </where>
    </select>

    <select id="selectTaskInstanceByIds" parameterType="Long" resultMap="TaskInstanceResult">
        <include refid="selectTaskInstanceVo"/>
        <where>
            <foreach collection="taskInstanceIds" close=")" open="iid in (" separator="," item="id">
                #{id}
            </foreach>
        </where>

    </select>
</mapper>