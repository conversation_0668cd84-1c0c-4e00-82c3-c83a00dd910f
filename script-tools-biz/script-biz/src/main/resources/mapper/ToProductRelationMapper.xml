<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.ToProductRelationMapper">

    <resultMap type="com.ideal.script.model.entity.ToProductRelationEntity" id="ToProductRelationResult">
            <result property="id" column="iid"/>
            <result property="scriptToproductId" column="iscript_toproduct_id"/>
            <result property="srcScriptUuid" column="isrc_script_uuid"/>
            <result property="createTime" column="icreate_time"/>
    </resultMap>

    <sql id="selectToProductRelation">
        select iid, iscript_toproduct_id, isrc_script_uuid, icreate_time
        from ieai_script_to_product_relation
    </sql>

    <select id="selectToProductRelationList" parameterType="com.ideal.script.model.entity.ToProductRelationEntity" resultMap="ToProductRelationResult">
        <include refid="selectToProductRelation"/>
        <where>
                        <if test="scriptToproductId != null ">
                            and iscript_toproduct_id = #{scriptToproductId}
                        </if>
                        <if test="srcScriptUuid != null  and srcScriptUuid != ''">
                            and isrc_script_uuid = #{srcScriptUuid}
                        </if>
                        <if test="createTime != null ">
                            and icreate_time = #{createTime}
                        </if>
        </where>
    </select>

    <select id="selectToProductRelationById" parameterType="Long"
            resultMap="ToProductRelationResult">
            <include refid="selectToProductRelation"/>
            where iid = #{id}
    </select>

    <insert id="insertToProductRelation" parameterType="com.ideal.script.model.entity.ToProductRelationEntity" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_script_to_product_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">iid,
                    </if>
                    <if test="scriptToproductId != null">iscript_toproduct_id,
                    </if>
                    <if test="srcScriptUuid != null">isrc_script_uuid,
                    </if>
                    <if test="createTime != null">icreate_time,
                    </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},
                    </if>
                    <if test="scriptToproductId != null">#{scriptToproductId},
                    </if>
                    <if test="srcScriptUuid != null">#{srcScriptUuid},
                    </if>
                    <if test="createTime != null">#{createTime},
                    </if>
        </trim>
    </insert>

    <update id="updateToProductRelation" parameterType="com.ideal.script.model.entity.ToProductRelationEntity">
        update ieai_script_to_product_relation
        <trim prefix="SET" suffixOverrides=",">
                    <if test="scriptToproductId != null">iscript_toproduct_id =
                        #{scriptToproductId},
                    </if>
                    <if test="srcScriptUuid != null">isrc_script_uuid =
                        #{srcScriptUuid},
                    </if>
                    <if test="createTime != null">icreate_time =
                        #{createTime},
                    </if>
        </trim>
        where iid = #{id}
    </update>

    <delete id="deleteToProductRelationById" parameterType="Long">
        delete
        from ieai_script_to_product_relation where iid = #{id}
    </delete>

    <delete id="deleteToProductRelationByIds" parameterType="String">
        delete from ieai_script_to_product_relation where iid in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>