<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.PlatformMapper">

    <resultMap type="com.ideal.script.model.entity.Platform" id="PlatformResult">
        <result property="id" column="iid"/>
        <result property="name" column="iname"/>
        <result property="codevalue" column="icodevalue"/>
    </resultMap>

    <select id="selectPlatformList" resultMap="PlatformResult">
        select distinct iname ,icodevalue , iid from ieai_platform_code
        <if test="gdswitch != '1'">
            where iname &lt;&gt; '全部'
        </if>
        order by iid ;
    </select>
</mapper>