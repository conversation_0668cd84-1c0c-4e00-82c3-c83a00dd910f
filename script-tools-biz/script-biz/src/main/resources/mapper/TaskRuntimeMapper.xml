<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.TaskRuntimeMapper">

    <resultMap type="com.ideal.script.model.entity.TaskRuntime" id="TaskRuntimeResult">
            <result property="id" column="iid"/>
            <result property="scriptTaskId" column="iscript_task_id"/>
            <result property="taskInstanceId" column="itask_instance_id"/>
            <result property="srcScriptUuid" column="isrc_script_uuid"/>
            <result property="state" column="istate"/>
            <result property="scriptName" column="iscript_name"/>
            <result property="agentIp" column="iagent_ip"/>
            <result property="agentPort" column="iagent_port"/>
            <result property="execUser" column="iexec_user"/>
            <result property="providerIp" column="iprovider_ip"/>
            <result property="providerPort" column="iprovider_port"/>
            <result property="startTime" column="istart_time"/>
            <result property="endTime" column="iend_time"/>
            <result property="elapsedTime" column="ielapsed_time"/>
            <result property="expectLastline" column="iexpect_lastline"/>
            <result property="expectType" column="iexpect_type"/>
            <result property="timeout" column="itimeout"/>
            <result property="timeoutValue" column="itimeout_value"/>
            <result property="startType" column="istart_type"/>
            <result property="createTime" column="icreate_time"/>
            <result property="agentTaskId" column="iagent_task_id"/>
            <result property="scriptTaskIpsId" column="iscript_task_ips_id"/>
            <result property="bizId" column="ibiz_id"/>
            <result property="currentTime" column="currentTime"/>
    </resultMap>

    <sql id="selectTaskRuntimeVo">
        select iid, iscript_task_id, itask_instance_id, isrc_script_uuid, istate, iscript_name, iagent_ip, iagent_port, iexec_user, iprovider_ip, iprovider_port, istart_time, iend_time, ielapsed_time, iexpect_lastline, iexpect_type, itimeout, itimeout_value, istart_type, icreate_time,iagent_task_id,iscript_task_ips_id,ibiz_id,${@com.ideal.common.util.DbUtils@getCurrentTime()} as currentTime
        from ieai_script_task_runtime
    </sql>

    <select id="selectTaskRuntimeList" parameterType="com.ideal.script.model.entity.TaskRuntime" resultMap="TaskRuntimeResult">
        <include refid="selectTaskRuntimeVo"/>
        <where>
                        <if test="scriptTaskId != null ">
                            and iscript_task_id = #{scriptTaskId}
                        </if>
                        <if test="taskInstanceId != null ">
                            and itask_instance_id = #{taskInstanceId}
                        </if>
                        <if test="srcScriptUuid != null  and srcScriptUuid != ''">
                            and isrc_script_uuid = #{srcScriptUuid}
                        </if>
                        <if test="state != null ">
                            and istate = #{state}
                        </if>
                        <if test="scriptName != null  and scriptName != ''">
                            and iscript_name like concat('%', #{scriptName}, '%')
                        </if>

                        <if test="agentIp != null  and agentIp != ''">
                            and iagent_ip like concat('%', #{agentIp}, '%')
                        </if>

                        <if test="agentPort != null ">
                            and iagent_port = #{agentPort}
                        </if>
                        <if test="execUser != null  and execUser != ''">
                            and iexec_user = #{execUser}
                        </if>
                        <if test="providerIp != null  and providerIp != ''">
                            and iprovider_ip = #{providerIp}
                        </if>
                        <if test="providerPort != null ">
                            and iprovider_port = #{providerPort}
                        </if>

                        <if test="startTime != null">
                            and istart_time &gt;= #{startTime}
                        </if>
                        <!-- 添加新的结束时间条件 -->
                        <if test="endTime != null">
                            and iend_time &lt;= #{endTime}
                        </if>


                        <if test="elapsedTime != null  and elapsedTime != ''">
                            and ielapsed_time = #{elapsedTime}
                        </if>
                        <if test="expectLastline != null  and expectLastline != ''">
                            and iexpect_lastline = #{expectLastline}
                        </if>
                        <if test="expectType != null ">
                            and iexpect_type = #{expectType}
                        </if>
                        <if test="timeout != null ">
                            and itimeout = #{timeout}
                        </if>
                        <if test="timeoutValue != null ">
                            and itimeout_value = #{timeoutValue}
                        </if>
                        <if test="startType != null ">
                            and istart_type = #{startType}
                        </if>
                        <if test="agentTaskId != null ">
                            and iagent_task_id = #{agentTaskId}
                        </if>
                        <if test="scriptTaskIpsId != null ">
                            and iscript_task_ips_id = #{scriptTaskIpsId}
                        </if>
        </where>
    </select>

    <select id="selectErrorRuntimeList" parameterType="com.ideal.script.model.entity.TaskRuntime" resultMap="TaskRuntimeResult">
        <include refid="selectTaskRuntimeVo"/>
        <where>
            <if test="scriptTaskId != null ">
                and iscript_task_id = #{scriptTaskId}
            </if>
            and (istate = 30 or istate = 10 or istate = 11)
        </where>
    </select>

    <select id="selectTaskRuntimeById" parameterType="Long"
            resultMap="TaskRuntimeResult">
            <include refid="selectTaskRuntimeVo"/>
            where iid = #{id}
    </select>

    <select id="selectSchedulerTagByTaskRunTimeId" parameterType="Long" resultType="java.lang.Integer">
        select itask_scheduler from ieai_script_task where iid = (select iscript_task_id from ieai_script_task_runtime where iid = #{id})
    </select>

    <insert id="insertTaskRuntime" parameterType="com.ideal.script.model.entity.TaskRuntime" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_script_task_runtime
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">
                        iid,
                    </if>
                    <if test="scriptTaskId != null">iscript_task_id,
                    </if>
                    <if test="taskInstanceId != null">itask_instance_id,
                    </if>
                    <if test="srcScriptUuid != null">isrc_script_uuid,
                    </if>
                    <if test="state != null">istate,
                    </if>
                    <if test="scriptName != null">iscript_name,
                    </if>
                    <if test="agentIp != null">iagent_ip,
                    </if>
                    <if test="agentPort != null">iagent_port,
                    </if>
                    <if test="execUser != null">iexec_user,
                    </if>
                    <if test="providerIp != null">iprovider_ip,
                    </if>
                    <if test="providerPort != null">iprovider_port,
                    </if>
                    istart_time,
                    <if test="endTime != null">iend_time,
                    </if>
                    <if test="elapsedTime != null">ielapsed_time,
                    </if>
                    <if test="expectLastline != null">iexpect_lastline,
                    </if>
                    <if test="expectType != null">iexpect_type,
                    </if>
                    <if test="timeout != null">itimeout,
                    </if>
                    <if test="timeoutValue != null">itimeout_value,
                    </if>
                    <if test="startType != null">istart_type,
                    </if>
                    icreate_time,
                    <if test="agentTaskId != null">iagent_task_id,</if>
                    <if test="scriptTaskIpsId != null">iscript_task_ips_id,</if>
                    <if test="bizId != null">ibiz_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="id != null">
                        #{id},
                        </if>
                        <if test="scriptTaskId != null">#{scriptTaskId},</if>
                        <if test="taskInstanceId != null">#{taskInstanceId},</if>
                        <if test="srcScriptUuid != null">#{srcScriptUuid},</if>
                        <if test="state != null">#{state},</if>
                        <if test="scriptName != null">#{scriptName},</if>
                        <if test="agentIp != null">#{agentIp},</if>
                        <if test="agentPort != null">#{agentPort},</if>
                        <if test="execUser != null">#{execUser},</if>
                        <if test="providerIp != null">#{providerIp},</if>
                        <if test="providerPort != null">#{providerPort},</if>
                            ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                        <if test="endTime != null">#{endTime},</if>
                        <if test="elapsedTime != null">#{elapsedTime},</if>
                        <if test="expectLastline != null">#{expectLastline},</if>
                        <if test="expectType != null">#{expectType},</if>
                        <if test="timeout != null">#{timeout},</if>
                        <if test="timeoutValue != null">#{timeoutValue},</if>
                        <if test="startType != null">#{startType},</if>
                            ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                        <if test="agentTaskId != null">#{agentTaskId},</if>
                        <if test="scriptTaskIpsId != null">#{scriptTaskIpsId},</if>
                        <if test="bizId != null">#{bizId},</if>
        </trim>
    </insert>

    <update id="updateTaskRuntime" parameterType="com.ideal.script.model.entity.TaskRuntime">
        update ieai_script_task_runtime
        <trim prefix="SET" suffixOverrides=",">
                        <if test="scriptTaskId != null">iscript_task_id = #{scriptTaskId},</if>
                        <if test="taskInstanceId != null">itask_instance_id = #{taskInstanceId},</if>
                        <if test="srcScriptUuid != null">isrc_script_uuid = #{srcScriptUuid},</if>
                        <if test="state != null">istate = #{state},</if>
                        <if test="scriptName != null">iscript_name = #{scriptName},</if>
                        <if test="agentIp != null">iagent_ip = #{agentIp},</if>
                        <if test="agentPort != null">iagent_port = #{agentPort},</if>
                        <if test="execUser != null">iexec_user = #{execUser},</if>
                        <if test="providerIp != null">iprovider_ip = #{providerIp},</if>
                        <if test="providerPort != null">iprovider_port = #{providerPort},</if>
                        <choose>
                            <when test="retry != null and retry.equals(true)">
                                istart_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                                iend_time = null,
                                ielapsed_time = null,
                            </when>
                            <otherwise>
                                <if test="elapsedTime != null">ielapsed_time = #{elapsedTime},</if>
                            </otherwise>
                        </choose>
                        <if test="expectLastline != null">iexpect_lastline = #{expectLastline},</if>
                        <if test="expectType != null">iexpect_type = #{expectType},</if>
                        <if test="timeout != null">itimeout = #{timeout},</if>
                        <if test="timeoutValue != null">itimeout_value = #{timeoutValue},</if>
                        <if test="startType != null">istart_type = #{startType},</if>
                        <if test="agentTaskId != null">iagent_task_id = #{agentTaskId},</if>
                        <if test="scriptTaskIpsId != null">iscript_task_ips_id = #{scriptTaskIpsId},</if>
                        <if test="bizId != null">ibiz_id = #{bizId},</if>
        </trim>
        where iid = #{id}
    </update>


    <delete id="deleteTaskRuntimeById" parameterType="Long">
        delete
        from ieai_script_task_runtime where iid = #{id}
    </delete>

    <delete id="deleteTaskRuntimeByIds" parameterType="String">
        delete from ieai_script_task_runtime where iid in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!--更新agent运行实例状态-->
    <update id="updateTaskRuntimeState" parameterType="java.util.Map">
          update ieai_script_task_runtime
          set istate = #{status},
            iend_time = #{endTime},
            ielapsed_time = #{elapsedTime}
            where iid = #{taskRuntimeId}
            and istate not in
            <foreach item="id" collection="notInStates" open="(" separator="," close=")">
                #{id}
            </foreach>

    </update>

    <select id="getTaskRuntimeStartTime" parameterType="java.util.Map" resultType="com.ideal.script.model.entity.TaskRuntime">
        select istart_time AS startTime, ${@com.ideal.common.util.DbUtils@getCurrentTime()} AS endTime from ieai_script_task_runtime where iid = #{taskRuntimeId}
    </select>

    <select id="getTaskRuntime" parameterType="java.util.Map" resultMap="TaskRuntimeResult">
        select * from ieai_script_task_runtime where itask_instance_id = (select iid from ieai_script_task_instance where icaller_task_id = #{callerTaskId}) and iagent_ip = #{agentIp} and iagent_port = #{agentPort}
    </select>

    <select id="getTaskRuntimeByInstanceId" parameterType="java.lang.Long" resultMap="TaskRuntimeResult">
        <include refid="selectTaskRuntimeVo"/>
        where itask_instance_id = #{taskInstanceId}
    </select>

    <select id="selectCountByTaskInstanceId" resultType="java.lang.Integer">
        SELECT
            count(iid) AS counts
        FROM
            ieai_script_task_runtime
        WHERE
            itask_instance_id = (
                SELECT
                    a.itask_instance_id
                FROM
                    ieai_script_task_runtime a
                WHERE
                    a.iid = #{id}
            )
        AND istate = 30
        AND iid &lt;&gt; #{id}
    </select>

    <select id="getRunningAgentInstanceCount" resultType="java.lang.Integer">
        SELECT
            count(iid) AS counts
        FROM
            ieai_script_task_runtime
        WHERE
            itask_instance_id = (
                SELECT
                    a.itask_instance_id
                FROM
                    ieai_script_task_runtime a
                WHERE
                    a.iid = #{id}
            )
        AND istate = 10
    </select>


    <resultMap type="com.ideal.script.model.bean.TaskRunTimeBindAgentBean" id="TaskRunTimeBindAgentBean">
            <result property="taskRuntimeId" column="itask_runtime_Id"/>
            <result property="taskIpsId" column="iscript_task_ips_id"/>
            <result property="sysmAgentInfoId" column="isysm_agent_info_id"/>
            <result property="agentIp" column="iagent_ip"/>
            <result property="agentPort" column="iagent_port"/>
    </resultMap>

    <select id="getBindAgentForTaskRuntime"  resultMap="TaskRunTimeBindAgentBean">
        SELECT distinct
            a.iid as itask_runtime_Id,
            a.iscript_task_ips_id,
            c.isysm_agent_info_id,
            c.iagent_ip,
            c.iagent_port
        FROM
            ieai_script_task_runtime a,
            ieai_script_task_ips b,
            ieai_script_agent_info c
        WHERE
            a.iscript_task_ips_id = b.iid
        AND b.iscript_agentinfo_id = c.iid
        AND a.iid=#{taskRuntimeId}
    </select>
    <select id="selectTaskRunTimeByTaskId" resultMap="TaskRuntimeResult">
        select * from ieai_script_task_runtime
        <where>
            <if test="scriptTaskId != null">
                iscript_task_id = #{scriptTaskId}
            </if>
        </where>
    </select>

    <select id="selectRuntimeIdsByAgentAddressAndTaskInstanceId" resultType="java.lang.Long">
        SELECT DISTINCT
            a.iid
        FROM
            ieai_script_task_runtime a,
            ieai_script_task_ips b,
            ieai_script_agent_info c
        WHERE
            a.iscript_task_ips_id = b.iid
          AND b.iscript_agentinfo_id = c.iid
          and a.itask_instance_id = #{taskInstanceId}
          AND CONCAT( c.iagent_ip, ':', c.iagent_port ) IN
        <foreach item="agentAddress" collection="agentAddressList" open="(" separator="," close=")">
            #{agentAddress}
        </foreach>
    </select>


</mapper>