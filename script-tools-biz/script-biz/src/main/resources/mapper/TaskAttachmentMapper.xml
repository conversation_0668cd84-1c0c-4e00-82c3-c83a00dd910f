<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.TaskAttachmentMapper">

    <resultMap type="com.ideal.script.model.entity.TaskAttachment" id="TaskAttachmentResult">
            <result property="id" column="iid"/>
            <result property="scriptTaskId" column="iscript_task_id"/>
            <result property="name" column="iname"/>
            <result property="size" column="isize"/>
            <result property="uploadTime" column="iupload_time"/>
            <result property="contents" column="icontents"/>
    </resultMap>

    <sql id="selectTaskAttachmentVo">
        select iid, iscript_task_id, iname, isize, iupload_time, icontents
        from ieai_script_task_attachment
    </sql>

    <select id="selectTaskAttachmentList" parameterType="com.ideal.script.model.entity.TaskAttachment" resultMap="TaskAttachmentResult">
        <include refid="selectTaskAttachmentVo"/>
        <where>
                        <if test="scriptTaskId != null ">
                            and iscript_task_id = #{scriptTaskId}
                        </if>
                        <if test="name != null  and name != ''">
                            and iname like concat('%', #{name}, '%')
                        </if>
                        <if test="size != null ">
                            and isize = #{size}
                        </if>
                        <if test="uploadTime != null ">
                            and iupload_time = #{uploadTime}
                        </if>
                        <if test="contents != null  and contents != ''">
                            and icontents = #{contents}
                        </if>
        </where>
    </select>

    <select id="selectTaskAttachmentById" parameterType="Long"
            resultMap="TaskAttachmentResult">
            <include refid="selectTaskAttachmentVo"/>
            where iid = #{id}
    </select>

    <select id="selectTaskAttachmentByServiceId"  parameterType="Long"
            resultMap="TaskAttachmentResult">
        <include refid="selectTaskAttachmentVo"/>
        <where>
            <if test="serviceId != null">
                iscript_task_id in (
                select
                iscript_task_id
                from
                ieai_script_audit_relation
                where
                iid = #{serviceId}
                )
            </if>
            <if test="serviceId == null and taskId != null">
                iscript_task_id = #{taskId}
            </if>
        </where>

    </select>

    <select id="selectTaskAttachmentNoContentByServiceId"  parameterType="Long"
            resultMap="TaskAttachmentResult">
        select iid, iscript_task_id, iname, isize, iupload_time
        from ieai_script_task_attachment
        <where>
            <if test="serviceId != null">
                iscript_task_id in (
                select
                iscript_task_id
                from
                ieai_script_audit_relation
                where
                iid = #{serviceId}
                )
            </if>
            <if test="serviceId == null and taskId != null">
                iscript_task_id = #{taskId}
            </if>
        </where>

    </select>

    <insert id="insertTaskAttachment" parameterType="com.ideal.script.model.entity.TaskAttachment">
        insert into ieai_script_task_attachment
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">iid,
                    </if>
                    <if test="scriptTaskId != null">iscript_task_id,
                    </if>
                    <if test="name != null">iname,
                    </if>
                    <if test="size != null">isize,
                    </if>
                    iupload_time,
                    <if test="contents != null">icontents,
                    </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},
                    </if>
                    <if test="scriptTaskId != null">#{scriptTaskId},
                    </if>
                    <if test="name != null">#{name},
                    </if>
                    <if test="size != null">#{size},
                    </if>
                    ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                    <if test="contents != null">#{contents},
                    </if>
        </trim>
    </insert>

    <update id="updateTaskAttachment" parameterType="com.ideal.script.model.entity.TaskAttachment">
        update ieai_script_task_attachment
        <trim prefix="SET" suffixOverrides=",">
                    <if test="scriptTaskId != null">iscript_task_id =
                        #{scriptTaskId},
                    </if>
                    <if test="name != null">iname =
                        #{name},
                    </if>
                    <if test="size != null">isize =
                        #{size},
                    </if>
                    iupload_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                    <if test="contents != null">icontents =
                        #{contents},
                    </if>
        </trim>
        where iid = #{id}
    </update>

    <delete id="deleteTaskAttachmentById" parameterType="Long">
        delete
        from ieai_script_task_attachment where iid = #{id}
    </delete>

    <delete id="deleteTaskAttachmentByIds">
        delete from ieai_script_task_attachment where iid in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        and iscript_task_id = 0
    </delete>

    <select id="getIdsByTaskId" resultType="long">
        select iid from ieai_script_task_attachment where iscript_task_id = #{taskId}
    </select>

    <update id="updateTaskIdEmptyByTaskId">
        update ieai_script_task_attachment set iscript_task_id = 0 where iscript_task_id = #{taskId}
    </update>
</mapper>