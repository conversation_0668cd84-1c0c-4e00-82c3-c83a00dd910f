<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.TaskParamsMapper">

    <resultMap type="com.ideal.script.model.entity.TaskParams" id="TaskParamsResult">
            <result property="id" column="iid"/>
            <result property="scriptTaskId" column="iscript_task_id"/>
            <result property="scriptParameterCheckId" column="iscript_parameter_check_id"/>
            <result property="scriptParameterManagerId" column="iscript_parameter_manager_id"/>
            <result property="type" column="itype"/>
            <result property="value" column="ivalue"/>
            <result property="desc" column="idesc"/>
            <result property="order" column="iorder"/>
            <result property="startType" column="istart_type"/>
            <result property="createTime" column="icreate_time"/>
    </resultMap>


    <resultMap type="com.ideal.script.model.bean.TaskHisParamsBean" id="TaskHisParamsResult">
            <result property="srcScriptUuid" column="isrc_script_uuid"/>
            <result property="taskParamsId" column="itask_param_id"/>
            <result property="type" column="itype"/>
            <result property="value" column="ivalue"/>
            <result property="desc" column="idesc"/>
            <result property="order" column="iorder"/>
    </resultMap>

    <!--查询历史参数-->
    <select id="selectHisParam" parameterType="com.ideal.script.model.bean.TaskHisParamsBean" resultMap="TaskHisParamsResult">
        select t.isrc_script_uuid,p.iid as itask_param_id,p.itype,p.ivalue,p.idesc,p.iorder from ieai_script_task_params p,
            ieai_script_task t
            where p.iscript_task_id = t.iid
        and t.isrc_script_uuid= #{srcScriptUuid}
    </select>


    <sql id="selectTaskParamsVo">
        select iid, iscript_task_id, iscript_parameter_check_id, iscript_parameter_manager_id, itype, ivalue, idesc, iorder, istart_type, icreate_time
        from ieai_script_task_params
    </sql>

    <select id="selectTaskParamsList" parameterType="com.ideal.script.model.entity.TaskParams" resultMap="TaskParamsResult">
        <include refid="selectTaskParamsVo"/>
        <where>
                        <if test="scriptTaskId != null ">
                            and iscript_task_id = #{scriptTaskId}
                        </if>
                        <if test="scriptParameterCheckId != null ">
                            and iscript_parameter_check_id = #{scriptParameterCheckId}
                        </if>
                        <if test="scriptParameterManagerId != null ">
                            and iscript_parameter_manager_id = #{scriptParameterManagerId}
                        </if>
                        <if test="type != null  and type != ''">
                            and itype = #{type}
                        </if>
                        <if test="value != null  and value != ''">
                            and ivalue = #{value}
                        </if>
                        <if test="desc != null  and desc != ''">
                            and idesc = #{desc}
                        </if>
                        <if test="order != null ">
                            and iorder = #{order}
                        </if>
                        <if test="startType != null ">
                            and istart_type = #{startType}
                        </if>
        </where>
        order by iorder
    </select>

    <select id="selectTaskParamsById" parameterType="Long"
            resultMap="TaskParamsResult">
            <include refid="selectTaskParamsVo"/>
            where iid = #{id}
    </select>

    <select id="selectTaskParamsByServiceId" parameterType="Long"
            resultMap="TaskParamsResult">
            <include refid="selectTaskParamsVo"/>
            <where>
                <if test="serviceId != null">
                    iscript_task_id in (
                    select
                    iscript_task_id
                    from
                    ieai_script_audit_relation
                    where
                    iid = #{serviceId}
                    )
                </if>
                <if test="serviceId == null and taskId != null">
                    iscript_task_id = #{taskId}
                </if>
            </where>

     </select>

    <insert id="insertTaskParams" parameterType="com.ideal.script.model.entity.TaskParams" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_script_task_params
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">
                        iid,
                    </if>
                    <if test="scriptTaskId != null">iscript_task_id,
                    </if>
                    <if test="scriptParameterCheckId != null">iscript_parameter_check_id,
                    </if>
                    <if test="scriptParameterManagerId != null">iscript_parameter_manager_id,
                    </if>
                    <if test="type != null">itype,
                    </if>
                    <if test="value != null">ivalue,
                    </if>
                    <if test="desc != null">idesc,
                    </if>
                    <if test="order != null">iorder,
                    </if>
                    <if test="startType != null">istart_type,
                    </if>
                    icreate_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">
                        #{id},
                    </if>
                    <if test="scriptTaskId != null">#{scriptTaskId},
                    </if>
                    <if test="scriptParameterCheckId != null">#{scriptParameterCheckId},
                    </if>
                    <if test="scriptParameterManagerId != null">#{scriptParameterManagerId},
                    </if>
                    <if test="type != null">#{type},
                    </if>
                    <if test="value != null">#{value},
                    </if>
                    <if test="desc != null">#{desc},
                    </if>
                    <if test="order != null">#{order},
                    </if>
                    <if test="startType != null">#{startType},
                    </if>
                    ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
    </insert>

    <update id="updateTaskParams" parameterType="com.ideal.script.model.entity.TaskParams">
        update ieai_script_task_params
        <trim prefix="SET" suffixOverrides=",">
                    <if test="scriptTaskId != null">iscript_task_id =
                        #{scriptTaskId},
                    </if>
                    <if test="scriptParameterCheckId != null">iscript_parameter_check_id =
                        #{scriptParameterCheckId},
                    </if>
                    <if test="scriptParameterManagerId != null">iscript_parameter_manager_id =
                        #{scriptParameterManagerId},
                    </if>
                    <if test="type != null">itype =
                        #{type},
                    </if>
                    <if test="value != null">ivalue =
                        #{value},
                    </if>
                    <if test="desc != null">idesc =
                        #{desc},
                    </if>
                    <if test="order != null">iorder =
                        #{order},
                    </if>
                    <if test="startType != null">istart_type =
                        #{startType},
                    </if>
        </trim>
        where iid = #{id}
    </update>

    <delete id="deleteTaskParamsById" parameterType="Long">
        delete
        from ieai_script_task_params where iid = #{id}
    </delete>

    <delete id="deleteTaskParamsByTaskId" parameterType="Long">
        delete
        from ieai_script_task_params where iscript_task_id = #{taskId}
    </delete>


    <delete id="deleteTaskParamsByIds" parameterType="String">
        delete from ieai_script_task_params where iid in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>