<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.AttachmentEphemeralMapper">

    <resultMap type="com.ideal.script.model.entity.Attachment" id="AttachmentResult">
        <result property="id" column="iid"/>
        <result property="srcScriptUuid" column="isrc_script_uuid"/>
        <result property="name" column="iname"/>
        <result property="size" column="isize"/>
        <result property="uploadtime" column="iuploadtime"/>
        <result property="contents" column="icontents"/>
    </resultMap>

    <sql id="selectAttachmentVo">
        select iid, isrc_script_uuid, iname, isize, iuploadtime, icontents
        from ieai_script_attachment_ephemeral
    </sql>

    <insert id="insertAttachment" parameterType="com.ideal.script.model.entity.Attachment" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_script_attachment_ephemeral
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                iid,
            </if>
            <if test="srcScriptUuid != null">isrc_script_uuid,</if>
            <if test="name != null">iname,</if>
            <if test="size != null">isize,</if>
            iuploadtime,
            <if test="contents != null">icontents,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="srcScriptUuid != null">#{srcScriptUuid},</if>
            <if test="name != null">#{name},</if>
            <if test="size != null">#{size},</if>
            ${@com.ideal.common.util.DbUtils@getCurrentTime()},
            <if test="contents != null">#{contents},</if>
        </trim>
    </insert>

    <delete id="deleteAttachmentById" parameterType="Long">
        delete
        from ieai_script_attachment_ephemeral
        where iid = #{id}
    </delete>

    <select id="selectAttachmentByIds" resultType="com.ideal.script.model.entity.Attachment" resultMap="AttachmentResult">
        <include refid="selectAttachmentVo"/>
        where iid in
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </select>
</mapper>