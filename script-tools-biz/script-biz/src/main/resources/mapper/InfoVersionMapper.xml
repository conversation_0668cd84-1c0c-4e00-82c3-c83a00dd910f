<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.InfoVersionMapper">
    
    <resultMap type="com.ideal.script.model.entity.InfoVersion" id="InfoVersionResult">
        <result property="id"    column="iid"    />
        <result property="infoUniqueUuid"    column="iinfo_unique_uuid"    />
        <result property="srcScriptUuid"    column="isrc_script_uuid"    />
        <result property="version"    column="iversion"    />
        <result property="editState"    column="iedit_state"    />
        <result property="useState"    column="iuse_state"    />
        <result property="deleted"    column="ideleted"    />
        <result property="isDefault"    column="is_default"    />
        <result property="description"    column="idescription"    />
        <result property="timeout"    column="itimeout"    />
        <result property="expectType"    column="iexpect_type"    />
        <result property="expectLastline"    column="iexpect_lastline"    />
        <result property="paramflag"    column="iparamflag"    />
        <result property="creatorId"    column="icreator_id"    />
        <result property="creatorName"    column="icreator_name"    />
        <result property="updatorId"    column="iupdator_id"    />
        <result property="updatorName"    column="iupdator_name"    />
        <result property="createTime"    column="icreate_time"    />
        <result property="updateTime"    column="iupdate_time"    />
        <result property="level"    column="ilevel"    />
        <result property="upgradeType"    column="iupgrade_type"    />
    </resultMap>

    <sql id="selectInfoVersionVo">
        select iid, iinfo_unique_uuid, isrc_script_uuid, iversion, iedit_state, iuse_state, ideleted, is_default, idescription, itimeout, iexpect_type, iexpect_lastline, iparamflag, icreator_id, icreator_name, iupdator_id, iupdator_name, icreate_time, iupdate_time,ilevel,iupgrade_type from ieai_script_info_version
    </sql>

    <select id="selectInfoVersionList" parameterType="com.ideal.script.model.entity.InfoVersion" resultMap="InfoVersionResult">
        <include refid="selectInfoVersionVo"/>
        <where>  
            <if test="infoUniqueUuid != null  and infoUniqueUuid != ''"> and iinfo_unique_uuid = #{infoUniqueUuid}</if>
            <if test="srcScriptUuid != null  and srcScriptUuid != ''"> and isrc_script_uuid = #{srcScriptUuid}</if>
            <if test="version != null  and version != ''"> and iversion = #{version}</if>
            <if test="editState != null "> and iedit_state = #{editState}</if>
            <if test="useState != null "> and iuse_state = #{useState}</if>
            <if test="deleted != null "> and ideleted = #{deleted}</if>
            <if test="isDefault != null "> and is_default = #{isDefault}</if>
            <if test="description != null  and description != ''"> and idescription = #{description}</if>
            <if test="timeout != null "> and itimeout = #{timeout}</if>
            <if test="expectType != null "> and iexpect_type = #{expectType}</if>
            <if test="expectLastline != null  and expectLastline != ''"> and iexpect_lastline = #{expectLastline}</if>
            <if test="paramflag != null "> and iparamflag = #{paramflag}</if>
            <if test="creatorId != null "> and icreator_id = #{creatorId}</if>
            <if test="creatorName != null  and creatorName != ''"> and icreator_name like concat('%', #{creatorName}, '%')</if>
            <if test="updatorId != null "> and iupdator_id = #{updatorId}</if>
            <if test="updatorName != null  and updatorName != ''"> and iupdator_name like concat('%', #{updatorName}, '%')</if>
        </where>
    </select>
    
    <select id="selectInfoVersionById" parameterType="Long" resultMap="InfoVersionResult">
        <include refid="selectInfoVersionVo"/>
        where iid = #{id}
    </select>

    <select id="selectPublishInfoVersionById" parameterType="Long" resultMap="InfoVersionResult">
        <include refid="selectInfoVersionVo"/>
        where iid = #{id} and iedit_state = 1
    </select>

    <select id="selectInfoVersionByIds" parameterType="Long" resultMap="InfoVersionResult">
        <include refid="selectInfoVersionVo"/>
        where iid in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectLastInfoVersionByInfoUuid"  resultMap="InfoVersionResult">
        <include refid="selectInfoVersionVo"/>
        where iid = (select iid from ieai_script_info_version where iinfo_unique_uuid = #{uuid}
         <if test="editState == 1">and  is_default = 1</if>
        <if test="editState == null or editState == 0">and iedit_state = 0 </if>
        <if test="editState == 2">and iedit_state = 2 </if>
        AND (ideleted = 0 or ideleted is null)
        )

    </select>
        
    <insert id="insertInfoVersion" parameterType="com.ideal.script.model.entity.InfoVersion" useGeneratedKeys="true" keyProperty="id">
        insert into ieai_script_info_version
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                iid,
            </if>
            <if test="infoUniqueUuid != null">iinfo_unique_uuid,</if>
            <if test="srcScriptUuid != null">isrc_script_uuid,</if>
            <if test="version != null">iversion,</if>
            <if test="editState != null">iedit_state,</if>
            <if test="useState != null">iuse_state,</if>
            <if test="deleted != null">ideleted,</if>
            <if test="isDefault != null">is_default,</if>
            <if test="description != null">idescription,</if>
            <if test="timeout != null">itimeout,</if>
            <if test="expectType != null">iexpect_type,</if>
            <if test="expectLastline != null">iexpect_lastline,</if>
            <if test="paramflag != null">iparamflag,</if>
            <if test="level != null">ilevel,</if>
            <if test="creatorId != null">icreator_id,</if>
            <if test="creatorName != null">icreator_name,</if>
            <if test="updatorId != null">iupdator_id,</if>
            <if test="updatorName != null">iupdator_name,</if>
            <if test="upgradeType != null">iupgrade_type,</if>
            icreate_time,
            iupdate_time,
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="infoUniqueUuid != null">#{infoUniqueUuid},</if>
            <if test="srcScriptUuid != null">#{srcScriptUuid},</if>
            <if test="version != null">#{version},</if>
            <if test="editState != null">#{editState},</if>
            <if test="useState != null">#{useState},</if>
            <if test="deleted != null">#{deleted},</if>
            <if test="isDefault != null">#{isDefault},</if>
            <if test="description != null">#{description},</if>
            <if test="timeout != null">#{timeout},</if>
            <if test="expectType != null">#{expectType},</if>
            <if test="expectLastline != null">#{expectLastline},</if>
            <if test="paramflag != null">#{paramflag},</if>
            <if test="level != null">#{level},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="creatorName != null">#{creatorName},</if>
            <if test="updatorId != null">#{updatorId},</if>
            <if test="updatorName != null">#{updatorName},</if>
            <if test="upgradeType != null">#{upgradeType},</if>
            <choose>
                <when test="createTime != null">
                    #{createTime},
                </when>
                <otherwise>
                    ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                </otherwise>
            </choose>
            <choose>
                <when test="updateTime != null">
                    #{updateTime}
                </when>
                <otherwise>
                    ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                </otherwise>
            </choose>
         </trim>
    </insert>

    <update id="updateInfoVersion" parameterType="com.ideal.script.model.entity.InfoVersion">
        update ieai_script_info_version
        <trim prefix="SET" suffixOverrides=",">
            <if test="infoUniqueUuid != null">iinfo_unique_uuid = #{infoUniqueUuid},</if>
            <if test="srcScriptUuid != null">isrc_script_uuid = #{srcScriptUuid},</if>
            <if test="version != null">iversion = #{version},</if>
            <if test="editState != null">iedit_state = #{editState},</if>
            <if test="useState != null">iuse_state = #{useState},</if>
            <if test="deleted != null">ideleted = #{deleted},</if>
            <if test="isDefault != null">is_default = #{isDefault},</if>
            <if test="description != null">idescription = #{description},</if>
            <if test="timeout != null">itimeout = #{timeout},</if>
            <if test="expectType != null">iexpect_type = #{expectType},</if>
            <if test="level != null">ilevel = #{level},</if>
            <if test="expectLastline != null">iexpect_lastline = #{expectLastline},</if>
            <if test="paramflag != null">iparamflag = #{paramflag},</if>
            <if test="creatorId != null">icreator_id = #{creatorId},</if>
            <if test="creatorName != null">icreator_name = #{creatorName},</if>
            <if test="updatorId != null">iupdator_id = #{updatorId},</if>
            <if test="updatorName != null">iupdator_name = #{updatorName},</if>
            <if test="upgradeType != null">iupgrade_type = #{upgradeType},</if>
            iupdate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
        where iid = #{id}
    </update>

    <update id="updateInfoVersionBySrcUuid" parameterType="com.ideal.script.model.entity.InfoVersion">
        update ieai_script_info_version
        <trim prefix="SET" suffixOverrides=",">
            <if test="version != null">iversion = #{version},</if>
            <if test="editState != null">iedit_state = #{editState},</if>
            <if test="useState != null">iuse_state = #{useState},</if>
            <if test="deleted != null">ideleted = #{deleted},</if>
            <if test="isDefault != null">is_default = #{isDefault},</if>
            <if test="description != null">idescription = #{description},</if>
            <if test="timeout != null">itimeout = #{timeout},</if>
            <if test="expectType != null">iexpect_type = #{expectType},</if>
            <if test="expectLastline != null">iexpect_lastline = #{expectLastline},</if>
            <if test="paramflag != null">iparamflag = #{paramflag},</if>
            <if test="creatorId != null">icreator_id = #{creatorId},</if>
            <if test="creatorName != null">icreator_name = #{creatorName},</if>
            <if test="updatorId != null">iupdator_id = #{updatorId},</if>
            <if test="updatorName != null">iupdator_name = #{updatorName},</if>
            <!--<if test="upgradeType != null">iupgrade_type = #{upgradeType},</if>-->
            iupdate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
        where isrc_script_uuid = #{srcScriptUuid}
    </update>

    <update id="updateInfoVersionByInfoUuid" parameterType="com.ideal.script.model.entity.InfoVersion">
        update ieai_script_info_version
        <trim prefix="SET" suffixOverrides=",">
            <if test="InfoVersion.version != null">iversion = #{InfoVersion.version},</if>
            <if test="InfoVersion.editState != null">iedit_state = #{InfoVersion.editState},</if>
            <if test="InfoVersion.useState != null">iuse_state = #{InfoVersion.useState},</if>
            <if test="InfoVersion.deleted != null">ideleted = #{InfoVersion.deleted},</if>
            <if test="InfoVersion.isDefault != null">is_default = #{InfoVersion.isDefault},</if>
            <if test="InfoVersion.description != null">idescription = #{InfoVersion.description},</if>
            <if test="InfoVersion.timeout != null">itimeout = #{InfoVersion.timeout},</if>
            <if test="InfoVersion.expectType != null">iexpect_type = #{InfoVersion.expectType},</if>
            <if test="InfoVersion.expectLastline != null">iexpect_lastline = #{InfoVersion.expectLastline},</if>
            <if test="InfoVersion.paramflag != null">iparamflag = #{InfoVersion.paramflag},</if>
            <if test="InfoVersion.creatorId != null">icreator_id = #{InfoVersion.creatorId},</if>
            <if test="InfoVersion.creatorName != null">icreator_name = #{InfoVersion.creatorName},</if>
            <if test="InfoVersion.updatorId != null">iupdator_id = #{InfoVersion.updatorId},</if>
            <if test="InfoVersion.updatorName != null">iupdator_name = #{InfoVersion.updatorName},</if>
            <if test="InfoVersion.upgradeType != null">iupgrade_type = #{InfoVersion.upgradeType},</if>
            icreate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
            iupdate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
        where iinfo_unique_uuid in
        <foreach collection="uuids" item="uuid" separator="," open="(" close=")">
            #{uuid}
        </foreach>
    </update>

    <update id="updateInfoVersionDefaultValue" parameterType="com.ideal.script.model.entity.InfoVersion">
        update ieai_script_info_version
        <trim prefix="SET" suffixOverrides=",">
            <if test="isDefault != null">is_default = #{isDefault}</if>
        </trim>
        where iinfo_unique_uuid = #{infoUniqueUuid} and iversion is not null
    </update>
    <update id="disableOldVersionByUuid">
        update ieai_script_info_version
        <trim prefix="SET" suffixOverrides=",">
            iuse_state = 0
        </trim>
        where isrc_script_uuid in
        <foreach item="currentVersionUuid" collection="versionUuids" open="(" separator="," close=")">
            #{currentVersionUuid}
        </foreach>
        and isrc_script_uuid != #{versionUuid}
    </update>
    <update id="updateDefaultVersion">
        update ieai_script_info_version
        <trim prefix="SET" suffixOverrides=",">
            is_default = 1
        </trim>
        where iid = #{lastId}
    </update>
    <update id="batchUpdateInfoVersion">
        update ieai_script_info_version
        <trim prefix="SET" suffixOverrides=",">
            <if test="infoUniqueUuid != null">iinfo_unique_uuid = #{infoUniqueUuid},</if>
            <if test="srcScriptUuid != null">isrc_script_uuid = #{srcScriptUuid},</if>
            <if test="version != null">iversion = #{version},</if>
            <if test="editState != null">iedit_state = #{editState},</if>
            <if test="useState != null">iuse_state = #{useState},</if>
            <if test="deleted != null">ideleted = #{deleted},</if>
            <if test="isDefault != null">is_default = #{isDefault},</if>
            <if test="description != null">idescription = #{description},</if>
            <if test="timeout != null">itimeout = #{timeout},</if>
            <if test="expectType != null">iexpect_type = #{expectType},</if>
            <if test="level != null">ilevel = #{level},</if>
            <if test="expectLastline != null">iexpect_lastline = #{expectLastline},</if>
            <if test="paramflag != null">iparamflag = #{paramflag},</if>
            <if test="creatorId != null">icreator_id = #{creatorId},</if>
            <if test="creatorName != null">icreator_name = #{creatorName},</if>
            <if test="updatorId != null">iupdator_id = #{updatorId},</if>
            <if test="updatorName != null">iupdator_name = #{updatorName},</if>
            <if test="upgradeType != null">iupgrade_type = #{upgradeType},</if>
            icreate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
            iupdate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
        where iid in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
             #{id}
        </foreach>
    </update>

    <delete id="deleteInfoVersionById" parameterType="Long">
        delete from ieai_script_info_version where iid = #{id}
    </delete>

    <delete id="deleteInfoVersionByIds">
        delete from ieai_script_info_version where iid in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteInfoVersionByUuid">
        delete from ieai_script_info_version where isrc_script_uuid = #{versionUuid}
    </delete>

    <select id="countPublishVersion" parameterType="String" resultType="Integer">
        select count(*) from ieai_script_info_version where iinfo_unique_uuid = #{uniqueUuid} and iedit_state = 1
    </select>


    <!--任务申请列表-->
      <resultMap type="com.ideal.script.model.bean.TaskApplyBean" id="TaskApplyResult">
            <result property="scriptInfoId" column="iscript_info_id"/>
            <result property="uniqueUuid" column="iunique_uuid"/>
            <result property="scriptNameZh" column="iscript_name_zh"/>
            <result property="scriptName" column="iscript_name"/>
            <result property="scriptType" column="iscript_type"/>
            <result property="execuser" column="iexecuser"/>
            <result property="scriptLabel" column="iscript_label"/>
            <result property="categoryId" column="icategory_id"/>
            <result property="checkBeforeExec" column="icheck_before_exec"/>
            <result property="level" column="ilevel"/>
            <result property="platform" column="iplatform"/>
            <result property="scriptInfoVersionId" column="iscript_info_version_id"/>
            <result property="srcScriptUuid" column="isrc_script_uuid"/>
            <result property="version" column="iversion"/>
            <result property="creatorName" column="icreator_name"/>
            <result property="expectLastline" column="iexpect_lastline"/>
            <result property="expectType" column="iexpect_type"/>
            <result property="updateTime" column="iupdate_time"/>
    </resultMap>

     <!--任务申请列表（现在只查询了所有任务脚本任务（没有带共享给当前登录用户的脚本，也没有查询同组用户创建的脚本）)-->
     <select id="selectTaskApplyList" parameterType="com.ideal.script.model.bean.TaskApplyBean" resultMap="TaskApplyResult">
         select * from ( (select
         a.iid as iscript_info_id,
         a.iunique_uuid,
         a.iscript_name_zh,
         a.iscript_name,
         a.iscript_type,
         a.iexecuser,
         a.iscript_label,
         a.icategory_id,
         (select iname from ieai_script_category c where c.iid = a.icategory_id) as iscript_category_name,
         b.icreator_id,
         b.icreator_name,
         b.iversion,
         b.isrc_script_uuid,
         b.ilevel,
         b.iid as iscript_info_version_id,
         a.iplatform,
         b.iupdate_time,
         table1.taskCount
         from ieai_script_info a
         join ieai_script_info_version b
         on a.iunique_uuid=b.iinfo_unique_uuid
         left join (select isrc_script_uuid,count(isrc_script_uuid) as taskCount from ieai_script_task ist where
         ist.icreate_time >= #{taskCreatePreTime} and ist.istart_user = #{startUser} group by isrc_script_uuid
         ) table1
         on table1.isrc_script_uuid = b.isrc_script_uuid
         <if test="keyword != null">
             left join ieai_script_info_version_text t on b.isrc_script_uuid = t.isrc_script_uuid
         </if>
         <where>
             b.iedit_state = 1
             and b.ideleted = 0
             and b.iuse_state =1
             and a.iscript_type != 'sql'
             and (a.iscript_source != 1 or a.iscript_source is null)
             <if test="scriptType != null and scriptType != ''">
                 and a.iscript_type = #{scriptType}
             </if>
             <if test="keyword != null">
                 <bind name="keyword" value="'%' + keyword.toUpperCase() + '%'"/>
                 and (UPPER(a.iscript_name_zh) like UPPER(#{keyword})
                 or UPPER(a.iscript_name) like UPPER(#{keyword})
                 or UPPER(t.icontent) like UPPER(#{keyword}))
             </if>
             <if test="level != null ">and ilevel = #{level}</if>
             <if test="platform != null  and platform != ''">
                 <bind name="platform" value="'%' + platform + '%'"/>
                 and a.iplatform like #{platform}
             </if>
             <if test="scriptNameZh != null ">
                 <bind name="scriptNameZh" value="'%' + scriptNameZh + '%'"/>
                 and a.iscript_name_zh like #{scriptNameZh}
             </if>
             <if test="scriptName != null  and scriptName != ''">
                 <bind name="scriptName" value="'%' + scriptName + '%'"/>
                 and a.iscript_name like #{scriptName}
             </if>
             <if test="categoryId != null and categoryIdList != null and categoryIdList.size !=0 and !roleFlag">
                 and a.icategory_id in
                 <foreach collection="categoryIdList" item="categoryIdValue" open="(" close=")" separator=",">
                     #{categoryIdValue}
                 </foreach>
             </if>
             <if test="getDefault != null">
                 <if test="getDefault.size > 0">
                     and is_default = 1
                </if>
             </if>
             <if test="scriptLabel != null">
                 <bind name="scriptLabel" value="'%' + scriptLabel + '%'"/>
                 and a.iscript_label like #{scriptLabel}
             </if>
             <if test="sysOrgCode != null and sysOrgCode != '' and !superUser and !roleFlag and !dutyApply ">
                 <bind name="sysOrgCode" value="sysOrgCode + '%'"/>
                 and ( a.isys_org_code like #{sysOrgCode}
                 <if test="orgCategoryPath != null and orgCategoryPath.size != 0">or
                     <foreach collection="orgCategoryPath" item="path" separator="OR">
                        a.icategory_path like #{path}
                     </foreach>
                 </if>
                 )
             </if>

             <if test="roleFlag and orgCategoryPath != null and orgCategoryPath.size != 0 and !dutyApply">
                and (
                 <foreach collection="orgCategoryPath" item="path" separator=" OR ">
                    a.icategory_path like #{path}
                </foreach>
                 <!--如果不是值班任务申请，并且走了角色权限，那么这里查询绑定到当前角色分类的脚本与角色下所有用户的脚本-->
                 <if test="roleFlag and !dutyApply and userIdList != null and userIdList.size !=0 ">
                     <!--所属角色下其它用户创建的脚本-->
                     <if test="userIdList != null and userIdList.size !=0 ">
                         or a.icreator_id in
                         <foreach collection="userIdList" item="userId" open="(" close=")" separator=",">
                             #{userId}
                         </foreach>
                     </if>
                 </if>
                 )
                 <if test="categoryId != null">
                    and (
                     <foreach collection="orgCategoryPath" item="path" separator=" OR ">
                         a.icategory_path like #{path}
                     </foreach>
                     )
                 </if>
             </if>
          <!--


          tor_id in
            <foreach collection="userIdList" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
          -->
         </where>
         )

         UNION

         (select
         a.iid as iscript_info_id,
         a.iunique_uuid,
         a.iscript_name_zh,
         a.iscript_name,
         a.iscript_type,
         a.iexecuser,
         a.iscript_label,
         a.icategory_id,
         (select iname from ieai_script_category c where c.iid = a.icategory_id) as iscript_category_name,
         b.icreator_id,
         b.icreator_name,
         b.iversion,
         b.isrc_script_uuid,
         b.ilevel,
         b.iid as iscript_info_version_id,
         a.iplatform,
         b.iupdate_time,
         table1.taskCount
         from ieai_script_info a
         JOIN ieai_script_info_version b ON a.iunique_uuid = b.iinfo_unique_uuid
         LEFT JOIN (
         SELECT
         isrc_script_uuid,
         COUNT(isrc_script_uuid) AS taskCount
         FROM
         ieai_script_task ist
         WHERE
         ist.icreate_time >= #{taskCreatePreTime} and ist.istart_user = #{startUser}
         GROUP BY
         isrc_script_uuid
         ) table1 ON table1.isrc_script_uuid = b.isrc_script_uuid
         JOIN ieai_script_share_relation s ON a.iid = s.iscript_info_id
         <if test="keyword != null">
             left join ieai_script_info_version_text t on b.isrc_script_uuid = t.isrc_script_uuid
         </if>
         <where>
             (
             <!--如果是走角色权限，那么需要查询共享给自己的，共享给所有人的，共享给当前用户所属角色的脚本-->
             <if test="roleFlag">
                 <bind name="userIdStr" value="'' + userId + ''"/>
                 (s.ishare_type = 0 AND s.ishare_object_id = #{userIdStr} )
                 <bind name="allStr" value="'' + -1 + ''"/>
                 OR (s.ishare_type = 2 AND s.ishare_object_id = #{allStr} )
                 <if test="roleIdList != null and roleIdList.size > 0 ">
                     OR (s.ishare_type = 3 AND s.ishare_object_id in
                         <foreach collection="roleIdList" item="roleId" open="(" close=")" separator=",">
                             <bind name="roleIdValue" value="'' + roleId + ''"/>
                             #{roleIdValue}
                         </foreach>
                     )
                 </if>
             </if>
             <!--如果走的不是角色权限，那么需要查询共享给自己的，共享给所有人的，共享给当前用户所属部门的脚本-->
             <if test="!roleFlag">
                 <bind name="userIdStr" value="'' + userId + ''"/>
                 (s.ishare_type = 0 AND s.ishare_object_id = #{userIdStr} )
                 <if test="sysOrgCode != null and sysOrgCode != '' and !superUser">
                     <bind name="departmentStr" value="'' +department + '%' + ''"/>
                     OR (s.ishare_type = 1 AND  s.ishare_object_id like #{departmentStr})
                 </if>
                 <bind name="allStr" value="'' + -1 + ''"/>
                 OR (s.ishare_type = 2 AND s.ishare_object_id = #{allStr} )
             </if>
             )
             <if test="scriptType != null and scriptType != ''">
                 and a.iscript_type = #{scriptType}
             </if>
             and b.ideleted = 0
             and b.iedit_state = 1
             and b.iuse_state =1
             and a.iscript_type != 'sql'
             and (a.iscript_source != 1 or a.iscript_source is null)
             <if test="keyword != null">
                 <bind name="keyword" value="'%' + keyword.toUpperCase() + '%'"/>
                 and (UPPER(a.iscript_name_zh) like UPPER(#{keyword})
                 or UPPER(a.iscript_name) like UPPER(#{keyword})
                 or UPPER(t.icontent) like UPPER(#{keyword}))
             </if>
             <if test="level != null ">and ilevel = #{level}</if>
             <if test="platform != null  and platform != ''">
                 <bind name="platform" value="'%' + platform + '%'"/>
                 and a.iplatform like #{platform}
             </if>
             <if test="scriptNameZh != null ">
                 <bind name="scriptNameZh" value="'%' + scriptNameZh + '%'"/>
                 and a.iscript_name_zh like #{scriptNameZh}
             </if>
             <if test="scriptName != null  and scriptName != ''">
                 <bind name="scriptName" value="'%' + scriptName + '%'"/>
                 and a.iscript_name like #{scriptName}
             </if>
             <if test="categoryId != null and categoryIdList != null and categoryIdList.size != 0">
                 and a.icategory_id in
                 <foreach collection="categoryIdList" item="categoryIdValue" open="(" close=")" separator=",">
                     #{categoryIdValue}
                 </foreach>
             </if>
             <if test="getDefault != null">
                 <if test="getDefault.size > 0">
                     and is_default = 1
                 </if>
             </if>
             <if test="scriptLabel != null">
                 <bind name="scriptLabel" value="'%' + scriptLabel + '%'"/>
                 and a.iscript_label like #{scriptLabel}
             </if>
             <!--


             tor_id in
               <foreach collection="userIdList" item="userId" open="(" close=")" separator=",">
                   #{userId}
               </foreach>
             -->
         </where>
         )) applydata

         order by CASE WHEN applydata.taskCount IS NULL THEN 1 ELSE 0 END, applydata.taskCount desc,applydata.iscript_info_version_id desc, applydata.iversion desc
     </select>

     <resultMap type="com.ideal.script.model.bean.TaskAuthorityBean" id="TaskAuthorityResult">
            <result property="scriptInfoId" column="iscript_info_id"/>
            <result property="uniqueUuid" column="iunique_uuid"/>
            <result property="scriptNameZh" column="iscript_name_zh"/>
            <result property="scriptName" column="iscript_name"/>
            <result property="scriptType" column="iscript_type"/>
            <result property="execuser" column="iexecuser"/>
            <result property="scriptLabel" column="iscript_label"/>
            <result property="categoryId" column="icategory_id"/>
            <result property="checkBeforeExec" column="icheck_before_exec"/>
            <result property="share" column="ishare"/>
            <result property="whiteCommand" column="iwhite_command"/>
            <result property="visibleType" column="ivisible_type"/>
            <result property="level" column="ilevel"/>
            <result property="platform" column="iplatform"/>
            <result property="femscript" column="isemscript"/>
            <result property="scriptInfoVersionId" column="iscript_info_version_id"/>
            <result property="srcScriptUuid" column="isrc_script_uuid"/>
            <result property="version" column="iversion"/>
            <result property="useState" column="iuse_state"/>
            <result property="sDefault" column="is_default"/>
            <result property="timeout" column="itimeout"/>
            <result property="expectType" column="iexpect_type"/>
            <result property="expectLastline" column="iexpectlastline"/>
            <result property="paramflag" column="iparamflag"/>
            <result property="creatorName" column="icreator_name"/>
            <result property="updatorId" column="iupdator_id"/>
            <result property="updatorName" column="iupdator_name"/>
            <result property="createTime" column="icreate_time"/>
            <result property="updateTime" column="iupdate_time"/>
    </resultMap>

    <!--任务权限列 表现在只查询了所有任务脚本任务（没有带共享给当前登录用户的脚本，也没有查询同组用户创建的脚本） -->
     <select id="selectTaskAuthorityList" resultType="com.ideal.script.model.bean.TaskAuthorityBean" resultMap="TaskAuthorityResult">
        select
                a.iid as  iscript_info_id,
                b.iid as iscript_info_version_id,
                a.iunique_uuid,
                a.iscript_name_zh,
                a.iscript_name,
                a.iscript_type,
                a.iexecuser,
                a.iscript_label,
                a.icategory_id,
                (select iname from ieai_script_category c where c.iid = a.icategory_id) as iscript_category_name,
                a.icreator_id,
                a.icreator_name,
                b.iversion,
                b.ilevel,
                a.iplatform,
                b.iuse_state
             from ieai_script_info a
             join ieai_script_info_version b
             on a.iunique_uuid=b.iinfo_unique_uuid
              <where>
                b.iedit_state = 1
                <if test="version != null  and version != ''"> and b.iversion = #{version}</if>
                <if test="scriptCategoryName != null "> and iscript_category_name = #{scriptCategoryName}</if>
                <if test="level != null "> and ilevel = #{level}</if>
                <if test="platform != null  and platform != ''"> and a.iplatform like concat('%', #{platform}, '%')</if>
                <if test="scriptNameZh != null "> and a.iscript_name_zh like concat('%', trim(#{scriptNameZh}), '%')</if>
                <if test="scriptName != null  and scriptName != ''"> and a.iscript_name like concat('%', trim(#{scriptName}), '%')</if>
                <if test="scriptType != null  and scriptType != ''"> and a.iscript_type like concat('%', trim(#{scriptType}), '%')</if>
              </where>
             order by b.iid desc, b.iversion desc
     </select>

    <select id="getScriptIdsByUuids" resultType="java.lang.Long">
        select iid from ieai_script_info_version
        where isrc_script_uuid in
        <foreach item="versionUuid" collection="versionUuid" open="(" separator="," close=")">
            #{versionUuid}
        </foreach>
    </select>

     <!--根据isrcscriptuuid查询脚本任务信息-->
     <select id="selectInfoVersionBysrcScriptUuid" resultType="com.ideal.script.model.entity.InfoVersion"  resultMap="InfoVersionResult">
        <include refid="selectInfoVersionVo"/>
        where isrc_script_uuid = #{srcScriptUuid}
     </select>

    <select id="selectDefaultInfoVersionBysrcScriptUuid" resultType="com.ideal.script.model.entity.InfoVersion"  resultMap="InfoVersionResult">
        <include refid="selectInfoVersionVo"/>
        where isrc_script_uuid =
            (select
                isrc_script_uuid
             from
                ieai_script_info_version
             where
                is_default = 1
                and iinfo_unique_uuid =
                    (
                     select iinfo_unique_uuid
                     from ieai_script_info_version
                     where isrc_script_uuid = #{srcScriptUuid}
                    )
            )
    </select>

     <!--根据本版表uuid获取脚本类型（sh、python...)-->
     <select id="getScriptTypeBySrcScriptUuid" resultType="java.lang.String">
        SELECT
            a.iscript_type
        FROM
            ieai_script_info a,
            ieai_script_info_version b
        WHERE
            a.iunique_uuid = b.iinfo_unique_uuid
        AND b.isrc_script_uuid = #{srcScriptUuid}
     </select>

      <select id="isInWhiteList" resultType="java.lang.Boolean">
       select
            case when count(iid) > 0 then true else false end as isInWhiteList
        FROM
           ieai_script_info_version
        WHERE
            ilevel = 0
          and iid=#{id}
    </select>

     <select id="validSrcScriptUuidExist" resultType="java.lang.Boolean">
        select
            exists (
                select
                    1
                from
                    ieai_script_info_version
                where
                    isrc_script_uuid = #{srcScriptUuid}
            )
     </select>

    <select id="getInfoVersionList" resultMap="TaskApplyResult">
        select
        a.iid as iscript_info_id,
        a.iunique_uuid,
        a.iscript_name_zh,
        a.iscript_name,
        a.iscript_type,
        a.iexecuser,
        a.iscript_label,
        a.icategory_id,
        b.icreator_name,
        b.iversion,
        b.isrc_script_uuid,
        b.iid as iscript_info_version_id,
        b.iexpect_type,
        b.iexpect_lastline,
        a.iplatform,
        a.iscript_source
        from ieai_script_info a
        join ieai_script_info_version b
        on a.iunique_uuid=b.iinfo_unique_uuid
        <where>

            b.iuse_state =1
            AND b.ideleted = 0
            <if test="draftFlag == null || !draftFlag">
                and b.iedit_state = 1
            </if>
            <if test="scriptInfoVersionId != null ">
                and b.iid = #{scriptInfoVersionId}
            </if>
            <if test="platform != null  and platform != ''">
                and a.iplatform like concat('%', #{platform}, '%')
            </if>
            <if test="scriptNameZh != null ">
                and a.iscript_name_zh like concat('%', #{scriptNameZh}, '%')
            </if>
            <if test="scriptName != null  and scriptName != ''">
                and a.iscript_name like concat('%', #{scriptName}, '%')
            </if>
            <if test="scriptType != null  and scriptType != ''">
                and a.iscript_type= #{scriptType}
            </if>
            <if test="categoryId != null ">
                and a.icategory_id = #{categoryId}
            </if>
            <if test="scriptLabel != null  and scriptLabel != ''">
                and a.iscript_label= #{scriptLabel}
            </if>
            <if test="(allVersions != null and !allVersions) and (draftFlag == null or !draftFlag)">
                and b.is_default = 1
            </if>
            <if test="draftFlag != null and draftFlag and allVersions != null and !allVersions">
                AND (
                ( EXISTS ( SELECT 1 FROM ieai_script_info_version v WHERE v.iinfo_unique_uuid = a.iunique_uuid AND iversion IS NULL ) AND b.iedit_state != 1 )
                OR
                (
                NOT EXISTS ( SELECT 1 FROM ieai_script_info_version v WHERE v.iinfo_unique_uuid = a.iunique_uuid AND iversion IS NULL )
                AND b.is_default = 1
                )
                )
            </if>
            <if test="(allVersions == null or allVersions) and excludeDefault != null and excludeDefault">
                and b.is_default = 0
            </if>
            <if test="excludeVersionIdList != null and excludeVersionIdList != '' and excludeVersionIdList.size()>0">
                and b. iid not in
                <foreach collection="excludeVersionIdList" item="scriptInfoVersionId" open="(" separator="," close=")">
                    #{scriptInfoVersionId}
                </foreach>
            </if>
            <if test="scriptSource != null">
                AND iscript_source = #{scriptSource}
            </if>
            <if test="infoUniqueUuid != null and infoUniqueUuid != ''">
                AND b.iinfo_unique_uuid = #{infoUniqueUuid}
            </if>
        </where>
        order by a.iscript_name desc, b.iversion desc
    </select>

    <select id="getInfoDefaultVersion" resultMap="TaskApplyResult">
        select
        a.iid as  iscript_info_id,
        a.iunique_uuid,
        a.iscript_name_zh,
        a.iscript_name,
        a.iscript_type,
        a.iexecuser,
        a.iscript_label,
        a.icategory_id,
        b.icreator_name,
        b.iversion,
        b.isrc_script_uuid,
        b.iid as iscript_info_version_id,
        a.iplatform
        from ieai_script_info a
        join ieai_script_info_version b
        on a.iunique_uuid=b.iinfo_unique_uuid
        <where>
            b.iedit_state = 1
            and b.iuse_state =1
            and b.is_default = 1
            <if test="scriptInfoVersionId != null "> and b.iid = #{scriptInfoVersionId}</if>
            <if test="platform != null  and platform != ''"> and a.iplatform = #{platform}</if>
            <if test="scriptNameZh != null "> and a.iscript_name_zh = #{scriptNameZh}</if>
            <if test="scriptName != null  and scriptName != ''"> and a.iscript_name = #{scriptName}</if>
            <if test="scriptType != null  and scriptType != ''"> and a.iscript_type = #{scriptType} </if>
            <if test="categoryId != null "> and a.icategory_id = #{categoryId}</if>
            <if test="scriptLabel != null  and scriptLabel != ''"> and a.iscript_label= #{scriptLabel} </if>
            <if test="srcScriptUuid != null  and srcScriptUuid != ''"> and b.isrc_script_uuid= #{srcScriptUuid} </if>

        </where>
    </select>
    <select id="countVersion" resultType="java.lang.Integer">
        <if test="id != null">
            SELECT count(1)
            FROM ieai_script_info_version
            WHERE iinfo_unique_uuid IN
            (SELECT iinfo_unique_uuid FROM ieai_script_info_version WHERE iid = #{id})
            AND ideleted = 0;
        </if>
    </select>
    <select id="selectDefaultInfoVersionByIds" resultMap="InfoVersionResult">
        <include refid="selectInfoVersionVo"/>
        where iid in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
    <select id="getLastVersionByUuid" resultType="java.lang.Long">
        select max(iid) from ieai_script_info_version where iinfo_unique_uuid = #{uuid} and ideleted = 0 and iuse_state = 1 AND iversion is not null
    </select>
    <select id="getUuidById" resultType="java.lang.String">
        select iinfo_unique_uuid from ieai_script_info_version where iid = #{id}
    </select>
    <select id="getAllVersionUuidByUuid" resultType="java.lang.String">
        select isrc_script_uuid
        from ieai_script_info_version
        where iinfo_unique_uuid =
              (select iinfo_unique_uuid from ieai_script_info_version where isrc_script_uuid = #{versionUuid})
    </select>

    <select id="checkExistRunTask" resultType="java.lang.Integer">
        select
            case
                when exists (
                    select 1
                    from ieai_script_info_version v
                    join ieai_script_task_instance i on v.isrc_script_uuid = i.isrc_script_uuid
                    where v.iid = #{scriptInfoVersionId}
                    and i.istatus in (10,11)
                ) then 1
                else 0
            end as result;
    </select>

    <select id="getInfoVersion" resultMap="TaskApplyResult">
        select
        a.iid as  iscript_info_id,
        a.iunique_uuid,
        a.iscript_name_zh,
        a.iscript_name,
        a.iscript_type,
        a.iexecuser,
        a.iscript_label,
        a.icategory_id,
        b.icreator_name,
        b.iversion,
        b.isrc_script_uuid,
        b.iid as iscript_info_version_id,
        a.iplatform
        from ieai_script_info a
        join ieai_script_info_version b
        on a.iunique_uuid=b.iinfo_unique_uuid
        <where>
            b.iuse_state =1
            <if test="draftFlag == null || !draftFlag"> and b.iedit_state = 1 </if>
            <if test="scriptInfoVersionId != null "> and b.iid = #{scriptInfoVersionId}</if>
            <if test="platform != null  and platform != ''"> and a.iplatform = #{platform}</if>
            <if test="scriptNameZh != null "> and a.iscript_name_zh = #{scriptNameZh}</if>
            <if test="scriptName != null  and scriptName != ''"> and a.iscript_name = #{scriptName}</if>
            <if test="scriptType != null  and scriptType != ''"> and a.iscript_type = #{scriptType} </if>
            <if test="categoryId != null "> and a.icategory_id = #{categoryId}</if>
            <if test="scriptLabel != null  and scriptLabel != ''"> and a.iscript_label= #{scriptLabel} </if>
            <if test="srcScriptUuid != null  and srcScriptUuid != ''"> and b.isrc_script_uuid= #{srcScriptUuid} </if>
        </where>
    </select>
    <select id="selectInfoVersionListForEdit" resultMap="InfoVersionResult">
        select * from ieai_script_info_version where iinfo_unique_uuid in
        <foreach collection="uniqueUuidList" item="uuid" open="(" separator="," close=")">
            #{uuid}
        </foreach>
          and ideleted = 0
          and (iversion is null or iversion = '')
    </select>
    <select id="selectInfoVersionListForDefault" resultMap="InfoVersionResult">
        select * from ieai_script_info_version where iinfo_unique_uuid in
        <foreach collection="uniqueUuidList" item="uuid" open="(" separator="," close=")">
            #{uuid}
        </foreach>
        and ideleted = 0
        and is_default = 1
    </select>
    <select id="selectLastEnableInfoVersion" resultMap="InfoVersionResult">
        <include refid="selectInfoVersionVo"/>
        <where>
            iinfo_unique_uuid = #{uniqueUuid} and iversion = (
            select MAX(iversion) from ieai_script_info_version where iinfo_unique_uuid = #{uniqueUuid} and ideleted = 0  and iuse_state = 1
            )
        </where>
    </select>
    <select id="selectDefaultInfoVersionByUuid" resultMap="InfoVersionResult">
        <include refid="selectInfoVersionVo"/>
        <where>
            iinfo_unique_uuid = #{uniqueUuid} and is_default = 1
        </where>
    </select>
    <select id="getCountVersionForPublish" resultType="java.lang.Integer">
        <if test="uniqueUuid != null">
            SELECT count(1)
            FROM ieai_script_info_version
            WHERE iinfo_unique_uuid = #{uniqueUuid}
            AND iversion is not null;
        </if>
    </select>
    <select id="getLastInfoVersionByIds" resultMap="InfoVersionResult">
        <include refid="selectInfoVersionVo"/>
        <where>
            iid = ( SELECT max( iid ) FROM ieai_script_info_version WHERE iinfo_unique_uuid = #{uniqueUuid} AND iid IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
         AND ideleted = 0 );
        </where>
    </select>
    <select id="getInfoDefaultVersionByUniqueUuid" resultMap="InfoVersionResult">
        <include refid="selectInfoVersionVo"/>
        <where>
            iinfo_unique_uuid  = #{uniqueUuid} and is_default = 1
        </where>
    </select>

    <select id="getTaskCountByVersionUuid" resultType="java.lang.Integer">
        select itask_count from ieai_script_exectime where isrc_script_uuid = #{versionUuid}
    </select>

    <select id="getScriptInfoByAuditRelationId" resultType="java.lang.Long">
        select
            a.icategory_id
        from
            ieai_script_info a
                left join
                ieai_script_info_version b
                    on a.iunique_uuid = b.iinfo_unique_uuid
                left join ieai_script_audit_relation c
                    on b.isrc_script_uuid = c.isrc_script_uuid
        where c.iid = #{relationId}
    </select>

    <select id="selectMaxInfoVersionByInfoUniqueUuid" resultMap="InfoVersionResult">
        <include refid="selectInfoVersionVo"/>
        where
            iedit_state = 1
            and iinfo_unique_uuid = #{infoUniqueUuid}
    </select>

    <select id="getAllVersionByUniqueUuid" resultType="java.lang.String" parameterType="java.lang.String">
        select iversion from ieai_script_info_version where iinfo_unique_uuid = #{uniqueUuid}
    </select>

<!--auto generated by MybatisCodeHelper on 2025-06-13-->
    <select id="selectIdBySrcScriptUuid" resultType="java.lang.Long">
        select iid
        from ieai_script_info_version
        where isrc_script_uuid=#{srcScriptUuid}
    </select>
</mapper>