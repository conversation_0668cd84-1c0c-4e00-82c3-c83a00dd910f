<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.BindFuncVarMapper">

    <resultMap type="com.ideal.script.model.entity.BindFuncVar" id="BindFuncVarResult">
            <result property="id" column="iid"/>
            <result property="srcScriptUuid" column="isrc_script_uuid"/>
            <result property="bindObjId" column="ibind_obj_id"/>
            <result property="bindType" column="ibind_type"/>
            <result property="objName" column="iobj_name"/>
    </resultMap>
    <resultMap id="selectBindFuncVarInfo" type="com.ideal.script.model.entity.BindFuncVar">
        <result property="srcScriptUuid" column="isrc_script_uuid"/>
        <result property="bindObjId" column="ibind_obj_id"/>
        <result property="bindType" column="ibind_type"/>
        <result property="objName" column="iobj_name"/>
    </resultMap>

    <sql id="selectBindFuncVarVo">
        select iid, isrc_script_uuid, ibind_obj_id, ibind_type, iobj_name
        from ieai_script_bind_func_var
    </sql>

    <select id="selectBindFuncVarList" parameterType="com.ideal.script.model.entity.BindFuncVar" resultMap="BindFuncVarResult">
        <include refid="selectBindFuncVarVo"/>
        <where>
                        <if test="srcScriptUuid != null  and srcScriptUuid != ''">
                            and isrc_script_uuid = #{srcScriptUuid}
                        </if>
                        <if test="bindObjId != null ">
                            and ibind_obj_id = #{bindObjId}
                        </if>
                        <if test="bindType != null ">
                            and ibind_type = #{bindType}
                        </if>
                        <if test="objName != null  and objName != ''">
                            and iobj_name like concat('%', #{objName}, '%')
                        </if>
        </where>
    </select>

    <select id="selectBindObjIdList" parameterType="com.ideal.script.model.entity.BindFuncVar" resultType="Long">
        SELECT ibind_obj_id FROM ieai_script_bind_func_var
        <where>
            <if test="srcScriptUuid != null  and srcScriptUuid != ''">
                and isrc_script_uuid = #{srcScriptUuid}
            </if>
            <if test="bindType != null ">
                and ibind_type = #{bindType}
            </if>
        </where>
    </select>

    <select id="selectBindFuncVarById" parameterType="Long"
            resultMap="BindFuncVarResult">
    </select>

    <insert id="insertBindFuncVar" parameterType="com.ideal.script.model.entity.BindFuncVar">
        insert into ieai_script_bind_func_var
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">iid,
                    </if>
                    <if test="srcScriptUuid != null and srcScriptUuid != ''">isrc_script_uuid,
                    </if>
                    <if test="bindObjId != null">ibind_obj_id,
                    </if>
                    <if test="bindType != null">ibind_type,
                    </if>
                    <if test="objName != null and objName != ''">iobj_name,
                    </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},
                    </if>
                    <if test="srcScriptUuid != null and srcScriptUuid != ''">#{srcScriptUuid},
                    </if>
                    <if test="bindObjId != null">#{bindObjId},
                    </if>
                    <if test="bindType != null">#{bindType},
                    </if>
                    <if test="objName != null and objName != ''">#{objName},
                    </if>
        </trim>
    </insert>

    <update id="updateBindFuncVar" parameterType="com.ideal.script.model.entity.BindFuncVar">
        update ieai_script_bind_func_var
        <trim prefix="SET" suffixOverrides=",">
                    <if test="srcScriptUuid != null and srcScriptUuid != ''">isrc_script_uuid =
                        #{srcScriptUuid},
                    </if>
                    <if test="bindObjId != null">ibind_obj_id =
                        #{bindObjId},
                    </if>
                    <if test="bindType != null">ibind_type =
                        #{bindType},
                    </if>
                    <if test="objName != null and objName != ''">iobj_name =
                        #{objName},
                    </if>
        </trim>
        where iid = #{id}
    </update>

    <delete id="deleteBindFuncVarById" parameterType="Long">
        delete
        from ieai_script_bind_func_var where iid = #{id}
    </delete>

    <delete id="deleteBindFuncVarByIds" parameterType="String">
        delete from ieai_script_bind_func_var where iid in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteBindFuncVarByScriptUuid" parameterType="String">
        delete from ieai_script_bind_func_var where isrc_script_uuid = #{scriptUuid}
    </delete>

    <select id="getBindFuncVarByUuid" resultMap="selectBindFuncVarInfo">
        select * from ieai_script_bind_func_var
        where isrc_script_uuid =
        <if test="uuid != null">
            #{uuid}
        </if>
    </select>

    <delete id="deleteBindFuncVarByUuid">
        delete from ieai_script_bind_func_var
        where isrc_script_uuid =
        <if test="oldUuid != null">
            #{oldUuid}
        </if>
    </delete>
</mapper>