<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.ParameterManagerMapper">
    
    <resultMap type="com.ideal.script.model.entity.ParameterManager" id="ParameterManagerResult">
        <result property="id"    column="iid"    />
        <result property="paramName"    column="iparam_name"    />
        <result property="paramValue"    column="iparam_value"    />
        <result property="paramDesc"    column="iparam_desc"    />
        <result property="scope"    column="iscope"    />
        <result property="creatorId"    column="icreator_id"    />
        <result property="creatorName"    column="icreator_name"    />
        <result property="updatorId"    column="iupdator_id"    />
        <result property="updatorName"    column="iupdator_name"    />
        <result property="createTime"    column="icreate_time"    />
        <result property="updateTime"    column="iupdate_time"    />
    </resultMap>

    <sql id="selectParameterManagerVo">
        select iid, iparam_name, iparam_value, iparam_desc, iscope, icreator_id, icreator_name, iupdator_id, iupdator_name, icreate_time, iupdate_time from ieai_script_parameter_manager
    </sql>

    <select id="selectParameterManagerList" parameterType="com.ideal.script.model.entity.ParameterManager" resultMap="ParameterManagerResult">
        <include refid="selectParameterManagerVo"/>
        <where>  
            <if test="paramName != null  and paramName != ''"> and iparam_name like concat('%', #{paramName}, '%')</if>
            <if test="paramValue != null  and paramValue != ''"> and iparam_value = #{paramValue}</if>
            <if test="paramDesc != null  and paramDesc != ''"> and iparam_desc = #{paramDesc}</if>
            <if test="scope != null  and scope != ''"> and iscope = #{scope}</if>
            <if test="creatorId != null "> and icreator_id = #{creatorId}</if>
            <if test="creatorName != null  and creatorName != ''"> and icreator_name like concat('%', #{creatorName}, '%')</if>
            <if test="updatorId != null "> and iupdator_id = #{updatorId}</if>
            <if test="updatorName != null  and updatorName != ''"> and iupdator_name like concat('%', #{updatorName}, '%')</if>
        </where>
        order by iid desc
    </select>

    <select id="selectSaveNameList" parameterType="com.ideal.script.model.entity.ParameterManager" resultMap="ParameterManagerResult">
        <include refid="selectParameterManagerVo"/>
        <where>
            <if test="paramName != null  and paramName != ''"> and iparam_name = #{paramName}</if>
            <if test="paramValue != null  and paramValue != ''"> and iparam_value = #{paramValue}</if>
            <if test="paramDesc != null  and paramDesc != ''"> and iparam_desc = #{paramDesc}</if>
            <if test="scope != null  and scope != ''"> and iscope = #{scope}</if>
            <if test="creatorId != null "> and icreator_id = #{creatorId}</if>
            <if test="creatorName != null  and creatorName != ''"> and icreator_name like concat('%', #{creatorName}, '%')</if>
            <if test="updatorId != null "> and iupdator_id = #{updatorId}</if>
            <if test="updatorName != null  and updatorName != ''"> and iupdator_name like concat('%', #{updatorName}, '%')</if>
        </where>
        order by iid desc
    </select>
    
    <select id="selectParameterManagerById" parameterType="Long" resultMap="ParameterManagerResult">
        <include refid="selectParameterManagerVo"/>
        where iid = #{id}
    </select>
        
    <insert id="insertParameterManager" parameterType="com.ideal.script.model.entity.ParameterManager" useGeneratedKeys="true" keyProperty="id">
        insert into ieai_script_parameter_manager
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                iid,
            </if>
            <if test="paramName != null">iparam_name,</if>
            <if test="paramValue != null">iparam_value,</if>
            <if test="paramDesc != null">iparam_desc,</if>
            <if test="scope != null">iscope,</if>
            <if test="creatorId != null">icreator_id,</if>
            <if test="creatorName != null">icreator_name,</if>
            <if test="updatorId != null">iupdator_id,</if>
            <if test="updatorName != null">iupdator_name,</if>
            icreate_time,
            iupdate_time,
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="paramName != null">#{paramName},</if>
            <if test="paramValue != null">#{paramValue},</if>
            <if test="paramDesc != null">#{paramDesc},</if>
            <if test="scope != null">#{scope},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="creatorName != null">#{creatorName},</if>
            <if test="updatorId != null">#{updatorId},</if>
            <if test="updatorName != null">#{updatorName},</if>
            ${@com.ideal.common.util.DbUtils@getCurrentTime()},
            ${@com.ideal.common.util.DbUtils@getCurrentTime()},
         </trim>
    </insert>

    <update id="updateParameterManager" parameterType="com.ideal.script.model.entity.ParameterManager">
        update ieai_script_parameter_manager
        <trim prefix="SET" suffixOverrides=",">
            <if test="paramName != null">iparam_name = #{paramName},</if>
            <if test="paramValue != null">iparam_value = #{paramValue},</if>
            <if test="paramDesc != null">iparam_desc = #{paramDesc},</if>
            <if test="scope != null">iscope = #{scope},</if>
            <if test="creatorId != null">icreator_id = #{creatorId},</if>
            <if test="creatorName != null">icreator_name = #{creatorName},</if>
            <if test="updatorId != null">iupdator_id = #{updatorId},</if>
            <if test="updatorName != null">iupdator_name = #{updatorName},</if>
            icreate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
            iupdate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
        where iid = #{id}
    </update>

    <delete id="deleteParameterManagerById" parameterType="Long">
        delete from ieai_script_parameter_manager where iid = #{id}
    </delete>

    <delete id="deleteParameterManagerByIds" parameterType="String">
        delete from ieai_script_parameter_manager where iid in 
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectParameterManagerForScriptEdit" resultMap="ParameterManagerResult">
        SELECT iid, iparam_name, iparam_value FROM ieai_script_parameter_manager
    </select>
    <select id="validParamterCheckExist" resultType="java.lang.Boolean">
        select
            exists (
                select
                    1
                from
                    ieai_script_parameter_manager
                where
                    iparam_name = #{paramName}
            )
    </select>
    <select id="selectParameterManagerByName" resultMap="ParameterManagerResult">
        <include refid="selectParameterManagerVo"/>
        where iparam_name=#{paramName}
    </select>
    <select id="checkIdsExist" resultType="java.lang.Long">
        select iid from ieai_script_parameter_manager
        where iid in
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>