<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.StatementMapper">
    <resultMap type="com.ideal.script.model.bean.StatementBean" id="StatementResult">
        <result property="id" column="iid"/>
        <result property="infoId" column="iinfo_id"/>
        <result property="infoVersionId" column="iinfo_version_id"/>
        <result property="scriptNameZh" column="iscript_name_zh"/>
        <result property="scriptName" column="iscript_name"/>
        <result property="categoryPath" column="icategory_path"/>
        <result property="defaultVersion" column="idefault_version"/>
        <result property="taskCount" column="itask_count"/>
        <result property="successRate" column="isuccess_rate"/>
        <result property="unmodifyDay" column="iunmodify_day"/>
        <result property="confirmState" column="iconfirm_state"/>
        <result property="createTime" column="icreate_time"/>
        <result property="scanningTime" column="iscanning_time"/>
        <result property="confirmTime" column="iconfirm_time"/>
        <result property="confirmorId" column="iconfirmor_id"/>
        <result property="confirmorName" column="iconfirmor_name"/>
        <result property="infoUpdateTime" column="iinfo_updatetime"/>
        <result property="creatorId" column="icreator_id"/>
        <result property="creatorName" column="icreator_name"/>
    </resultMap>


    <sql id="selectScriptStatementVo">
        select iid,iinfo_id,iinfo_version_id,iscript_name_zh,iscript_name,icategory_path,idefault_version,itask_count,isuccess_rate,iunmodify_day,iconfirm_state,icreate_time,iscanning_time,iconfirm_time,iconfirmor_id,iconfirmor_name,iinfo_updatetime,icreator_id,icreator_name
        from ieai_script_stale
    </sql>
    <update id="updateScriptStatementByIds">
        update ieai_script_stale
        <trim prefix="SET" suffixOverrides=",">
            <if test="statementBean.infoVersionId != null">iinfo_version_id = #{statementBean.infoVersionId},</if>
            <if test="statementBean.scriptNameZh != null">iscript_name_zh = #{statementBean.scriptNameZh},</if>
            <if test="statementBean.scriptName != null">iscript_name = #{statementBean.scriptName},</if>
            <if test="statementBean.categoryPath != null">icategory_path = #{statementBean.categoryPath},</if>
            <if test="statementBean.defaultVersion != null">idefault_version = #{statementBean.defaultVersion},</if>
            <if test="statementBean.taskCount != null">itask_count = #{statementBean.taskCount},</if>
            <if test="statementBean.successRate != null">isuccess_rate = #{statementBean.successRate},</if>
            <if test="statementBean.unmodifyDay != null">iunmodify_day = #{statementBean.unmodifyDay},</if>
            <if test="statementBean.confirmState != null">iconfirm_state = #{statementBean.confirmState},</if>
            <if test="statementBean.createTime != null">icreate_time = #{statementBean.createTime},</if>
            <if test="statementBean.scanningTime != null">iscanning_time = #{statementBean.scanningTime},</if>
            <if test="statementBean.confirmState == 1">
                iconfirm_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
            </if>
            <if test="statementBean.confirmorId != null">iconfirmor_id = #{statementBean.confirmorId},</if>
            <if test="statementBean.confirmorName != null">iconfirmor_name = #{statementBean.confirmorName},</if>
            <if test="statementBean.infoUpdateTime != null">iinfo_updatetime = #{statementBean.infoUpdateTime},</if>
            <if test="statementBean.creatorId != null">icreator_id = #{statementBean.creatorId},</if>
            <if test="statementBean.creatorName != null">icreator_name = #{statementBean.creatorName},</if>
        </trim>
        where iinfo_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <select id="selectScriptStatementPage" resultMap="StatementResult">
        <include refid="selectScriptStatementVo"/>
        <where>
            <if test="scriptNameZh != null">
                <bind name="scriptNameZh" value="'%' + scriptNameZh + '%'"/>
                and iscript_name_zh like #{scriptNameZh}
            </if>
            <if test="scriptName != null">
                <bind name="scriptName" value="'%' + scriptName + '%'"/>
                and iscript_name like #{scriptName}
            </if>
            <if test="confirmState != null">
                and iconfirm_state = #{confirmState}
            </if>
            <if test="categoryPath != null and categoryPath != '' and escapedLikeCategoryPath == null">
                <bind name="categoryPath" value="categoryPath + '/' +'%'"/>
                and (icategory_path like #{categoryPath}
            </if>
            <if test="escapedLikeCategoryPath != null and escapedLikeCategoryPath != ''">
                <bind name="escapedLikeCategoryPath" value="escapedLikeCategoryPath + '/' +'%'"/>
                and (icategory_path like #{escapedLikeCategoryPath}
            </if>
            <if test="categoryPath != null and categoryPath != '' ">
                <bind name="exactCategoryPath" value="categoryPath"/>
                or icategory_path = #{exactCategoryPath})
            </if>
        </where>

    </select>
    <select id="selectScriptStatementByIds" resultMap="StatementResult">
        <include refid="selectScriptStatementVo"/>
        <where>
            <if test="ids != null and ids.size() != 0">
                and iid in
                <foreach item="id" collection="ids" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getScriptStatementByInfoId" resultMap="StatementResult">
        <include refid="selectScriptStatementVo"/>
        where iinfo_id = #{infoId}
    </select>
</mapper>