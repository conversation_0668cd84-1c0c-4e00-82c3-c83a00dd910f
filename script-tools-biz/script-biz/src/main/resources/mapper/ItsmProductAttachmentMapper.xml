<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.ItsmProductAttachmentMapper">
    
    <resultMap type="com.ideal.script.model.entity.ItsmProductAttachment" id="dataResult">
        <result property="iid"    column="iid"    />
        <result property="srcScriptUuid"    column="isrc_script_uuid"    />
        <result property="attachmentContent"    column="iattachment_content" />
        <result property="attachmentName"    column="iattachment_name" />
        <result property="attachmentSize"    column="iattachment_size" />
    </resultMap>

    <delete id="deleteByPublishInfoId" parameterType="Long">
        delete
        from ieai_script_itsm_product_attachment where ipublish_info_id = #{publishInfoId}
    </delete>

    <insert id="insert" parameterType="com.ideal.script.model.entity.ItsmProductAttachment" useGeneratedKeys="true" keyProperty="iid">
        insert into ieai_script_itsm_product_attachment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="iid != null">
                iid,
            </if>
            <if test="srcScriptUuid != null and srcScriptUuid != ''">isrc_script_uuid,</if>
            <if test="publishInfoId != null">ipublish_info_id,</if>
            <if test="itsmProductInfoId != null">iitsm_product_info_id,</if>
            <if test="attachmentContent != null">iattachment_content,</if>
            <if test="attachmentSize != null">iattachment_size,</if>
            <if test="attachmentName != null and attachmentName != ''">iattachment_name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="iid != null">
                #{iid},
            </if>
            <if test="srcScriptUuid != null and srcScriptUuid != ''">#{srcScriptUuid},</if>
            <if test="publishInfoId != null and publishInfoId != ''">#{publishInfoId},</if>
            <if test="itsmProductInfoId != null">#{itsmProductInfoId},</if>
            <if test="attachmentContent != null">#{attachmentContent},</if>
            <if test="attachmentSize != null">#{attachmentSize},</if>
            <if test="attachmentName != null and attachmentName != ''">#{attachmentName},</if>
        </trim>
    </insert>

    <select id="selectAttachmentById" parameterType="java.lang.Long" resultMap="dataResult">
        select iid, isrc_script_uuid, iattachment_name, iattachment_size, iattachment_content
        from ieai_script_itsm_product_attachment where iid = #{attachmentId}
    </select>

</mapper>