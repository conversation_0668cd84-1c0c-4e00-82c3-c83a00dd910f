<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.IssuerecordMapper">
    <cache eviction="LRU" flushInterval="3000" size="2048" readOnly="true"/>
    <resultMap type="com.ideal.script.model.entity.IssuerecordEntity" id="IssuerecordResult">
            <result property="id" column="iid"/>
            <result property="srcScriptUuid" column="isrc_script_uuid"/>
            <result property="agentIp" column="iagent_ip"/>
            <result property="agentPort" column="iagent_port"/>
            <result property="status" column="istatus"/>
            <result property="message" column="imessage"/>
            <result property="sendPath" column="isend_path"/>
            <result property="chmod" column="ichmod"/>
            <result property="userPermission" column="iuser_permission"/>
            <result property="groupPermission" column="igroup_permission"/>
            <result property="sendUserId" column="isend_user_id"/>
            <result property="sendUserName" column="isend_user_name"/>
            <result property="sendTime" column="isend_time"/>
            <result property="batchNumber" column="ibatch_number"/>
            <result property="bizId" column="ibiz_id"/>
            <result property="scriptName" column="iscript_name"/>
            <result property="scriptNameZh" column="iscript_name_zh"/>
    </resultMap>

    <sql id="selectIssuerecord">
        select iid, isrc_script_uuid, iagent_ip, iagent_port, istatus, imessage, isend_path, ichmod, iuser_permission, igroup_permission, isend_user_id, isend_user_name, isend_time,ibatch_number,ibiz_id
        from ieai_script_issuerecord
    </sql>


    <select id="selectIssuerecordList_COUNT" parameterType="com.ideal.script.model.bean.IssuerecordBean" resultType="long" useCache="true">
        select count(1)
        from (SELECT a.iid,
        a.isrc_script_uuid,
        a.iagent_ip,
        a.iagent_port,
        a.istatus,
        a.imessage,
        a.isend_path,
        a.ichmod,
        a.iuser_permission,
        a.igroup_permission,
        a.isend_user_id,
        a.isend_user_name,
        a.isend_time,
        a.ibatch_number,
        a.ibiz_id,
        b.iinfo_unique_uuid
        FROM ieai_script_issuerecord a,ieai_script_info_version b
        WHERE a.isrc_script_uuid = b.isrc_script_uuid
        <if test="status != null ">
            and a.istatus = #{status}
        </if>
        <if test="batchNumber != null and batchNumber != ''">
            and a.ibatch_number = #{batchNumber}
        </if>
        <if test="sendTimeStart != null ">
            and a.isend_time >= #{sendTimeStart}
        </if>
        <if test="sendTimeEnd != null ">
            AND a.isend_time &lt; #{sendTimeEnd}
        </if>
        order by a.iid desc) as temp
        left join ieai_script_info c on c.iunique_uuid = temp.iinfo_unique_uuid
        <where>
            <if test="scriptName != null and scriptName != ''">
                <bind name="scriptName" value="'%' + scriptName + '%'"/>
                and c.iscript_name like #{scriptName}
            </if>
            <if test="scriptNameZh != null and scriptNameZh != ''">
                <bind name="scriptNameZh" value="'%' + scriptNameZh + '%'"/>
                and c.iscript_name_zh like #{scriptNameZh}
            </if>
        </where>
    </select>



    <select id="selectIssuerecordList" parameterType="com.ideal.script.model.bean.IssuerecordBean" resultMap="IssuerecordResult" useCache="false">
        select temp.*,
            c.iscript_name,
            c.iscript_name_zh
            from (SELECT a.iid,
                a.isrc_script_uuid,
                a.iagent_ip,
                a.iagent_port,
                a.istatus,
                a.imessage,
                a.isend_path,
                a.ichmod,
                a.iuser_permission,
                a.igroup_permission,
                a.isend_user_id,
                a.isend_user_name,
                a.isend_time,
                a.ibatch_number,
                a.ibiz_id,
                b.iinfo_unique_uuid
        FROM ieai_script_issuerecord a,ieai_script_info_version b
        WHERE a.isrc_script_uuid = b.isrc_script_uuid
        <if test="status != null ">
            and a.istatus = #{status}
        </if>
        <if test="batchNumber != null and batchNumber != ''">
            and a.ibatch_number = #{batchNumber}
        </if>
        <if test="sendTimeStart != null ">
            and a.isend_time >= #{sendTimeStart}
        </if>
        <if test="sendTimeEnd != null ">
            AND a.isend_time &lt; #{sendTimeEnd}
        </if>
        order by a.iid desc) as temp
        left join ieai_script_info c on c.iunique_uuid = temp.iinfo_unique_uuid
        <where>
            <if test="scriptName != null and scriptName != ''">
                <bind name="scriptName" value="'%' + scriptName + '%'"/>
                and c.iscript_name like #{scriptName}
            </if>
            <if test="scriptNameZh != null and scriptNameZh != ''">
                <bind name="scriptNameZh" value="'%' + scriptNameZh + '%'"/>
                and c.iscript_name_zh like #{scriptNameZh}
            </if>
        </where>
    </select>

    <select id="selectIssuerecordById" parameterType="Long"
            resultMap="IssuerecordResult" useCache="false">
            <include refid="selectIssuerecord"/>
            where iid = #{id}
    </select>

    <insert id="insertIssuerecord" parameterType="com.ideal.script.model.entity.IssuerecordEntity" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_script_issuerecord
        <trim prefix="(" suffix=")" suffixOverrides=",">
                  <if test="id != null">
                        iid,
                    </if>
                    <if test="srcScriptUuid != null">isrc_script_uuid,
                    </if>
                    <if test="agentIp != null">iagent_ip,
                    </if>
                    <if test="agentPort != null">iagent_port,
                    </if>
                    <if test="status != null">istatus,
                    </if>
                    <if test="message != null">imessage,
                    </if>
                    <if test="sendPath != null">isend_path,
                    </if>
                    <if test="chmod != null">ichmod,
                    </if>
                    <if test="userPermission != null">iuser_permission,
                    </if>
                    <if test="groupPermission != null">igroup_permission,
                    </if>
                    <if test="sendUserId != null">isend_user_id,
                    </if>
                    <if test="sendUserName != null">isend_user_name,
                    </if>
                    isend_time,
                    <if test="batchNumber != null">ibatch_number,
                    </if>
                    <if test="bizId != null">ibiz_id,
                    </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">
                        #{id},
                    </if>
                    <if test="srcScriptUuid != null">#{srcScriptUuid},
                    </if>
                    <if test="agentIp != null">#{agentIp},
                    </if>
                    <if test="agentPort != null">#{agentPort},
                    </if>
                    <if test="status != null">#{status},
                    </if>
                    <if test="message != null">#{message},
                    </if>
                    <if test="sendPath != null">#{sendPath},
                    </if>
                    <if test="chmod != null">#{chmod},
                    </if>
                    <if test="userPermission != null">#{userPermission},
                    </if>
                    <if test="groupPermission != null">#{groupPermission},
                    </if>
                    <if test="sendUserId != null">#{sendUserId},
                    </if>
                    <if test="sendUserName != null">#{sendUserName},
                    </if>
                     ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                     <if test="batchNumber != null">#{batchNumber},
                    </if>
                      <if test="bizId != null">#{bizId},
                    </if>
        </trim>
    </insert>

    <update id="updateIssuerecord" parameterType="com.ideal.script.model.entity.IssuerecordEntity">
        update ieai_script_issuerecord
        <trim prefix="SET" suffixOverrides=",">
                    <if test="srcScriptUuid != null">isrc_script_uuid =
                        #{srcScriptUuid},
                    </if>
                    <if test="agentIp != null">iagent_ip =
                        #{agentIp},
                    </if>
                    <if test="agentPort != null">iagent_port =
                        #{agentPort},
                    </if>
                    <if test="status != null">istatus =
                        #{status},
                    </if>
                    <if test="message != null">imessage =
                        #{message},
                    </if>
                    <if test="sendPath != null">isend_path =
                        #{sendPath},
                    </if>
                    <if test="chmod != null">ichmod =
                        #{chmod},
                    </if>
                    <if test="userPermission != null">iuser_permission =
                        #{userPermission},
                    </if>
                    <if test="groupPermission != null">igroup_permission =
                        #{groupPermission},
                    </if>
                    <if test="sendUserId != null">isend_user_id =
                        #{sendUserId},
                    </if>
                    <if test="sendUserName != null">isend_user_name =
                        #{sendUserName},
                    </if>
                    <if test="sendTime != null">isend_time =
                        #{sendTime},
                    </if>
                    <if test="batchNumber != null">ibatch_number =
                        #{batchNumber},
                    </if>
                    <if test="bizId != null">ibiz_id =
                        #{bizId},
                    </if>
        </trim>
        where iid = #{id}
    </update>

    <delete id="deleteIssuerecordById" parameterType="Long">
        delete
        from ieai_script_issuerecord where iid = #{id}
    </delete>

    <delete id="deleteIssuerecordByIds" parameterType="String">
        delete from ieai_script_issuerecord where iid in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateIssuerecordByBatchNumber">
        update ieai_script_issuerecord
        set istatus=#{status}
        where ibatch_number = #{batchNumber}
          and iagent_ip = #{agentIp}
          and iagent_port = #{agentPort}
    </update>
</mapper>