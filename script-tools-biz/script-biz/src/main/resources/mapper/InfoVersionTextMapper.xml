<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.InfoVersionTextMapper">
    
    <resultMap type="com.ideal.script.model.entity.InfoVersionText" id="InfoVersionTextResult">
        <result property="id"    column="iid"    />
        <result property="srcScriptUuid"    column="isrc_script_uuid"    />
        <result property="content"    column="icontent"    />
        <result property="creatorId"    column="icreator_id"    />
        <result property="creatorName"    column="icreator_name"    />
        <result property="createTime"    column="icreate_time"    />
    </resultMap>

    <sql id="selectInfoVersionTextVo">
        select iid, isrc_script_uuid, icontent, icreator_id, icreator_name, icreate_time from ieai_script_info_version_text
    </sql>

    <select id="selectInfoVersionTextList" parameterType="com.ideal.script.model.entity.InfoVersionText" resultMap="InfoVersionTextResult">
        <include refid="selectInfoVersionTextVo"/>
        <where>  
            <if test="srcScriptUuid != null  and srcScriptUuid != ''"> and isrc_script_uuid = #{srcScriptUuid}</if>
            <if test="content != null  and content != ''"> and icontent = #{content}</if>
            <if test="creatorId != null "> and icreator_id = #{creatorId}</if>
            <if test="creatorName != null  and creatorName != ''"> and icreator_name like concat('%', #{creatorName}, '%')</if>
        </where>
    </select>
    
    <select id="selectInfoVersionTextById" parameterType="Long" resultMap="InfoVersionTextResult">
        <include refid="selectInfoVersionTextVo"/>
        where iid = #{id}
    </select>
        
    <insert id="insertInfoVersionText" parameterType="com.ideal.script.model.entity.InfoVersionText" useGeneratedKeys="true" keyProperty="id">
        insert into ieai_script_info_version_text
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                iid,
            </if>
            <if test="srcScriptUuid != null">isrc_script_uuid,</if>
            <if test="content != null">icontent,</if>
            <if test="creatorId != null">icreator_id,</if>
            <if test="creatorName != null">icreator_name,</if>
            icreate_time,
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="srcScriptUuid != null">#{srcScriptUuid},</if>
            <if test="content != null">#{content},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="creatorName != null">#{creatorName},</if>
            ${@com.ideal.common.util.DbUtils@getCurrentTime()},
         </trim>
    </insert>

    <update id="updateInfoVersionText" parameterType="com.ideal.script.model.entity.InfoVersionText">
        update ieai_script_info_version_text
        <trim prefix="SET" suffixOverrides=",">
            <if test="srcScriptUuid != null">isrc_script_uuid = #{srcScriptUuid},</if>
            <if test="content != null">icontent = #{content},</if>
            <if test="creatorId != null">icreator_id = #{creatorId},</if>
            <if test="creatorName != null">icreator_name = #{creatorName},</if>
            icreate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
        where iid = #{id}
    </update>

    <update id="updateInfoVersionTextByScriptUuid" parameterType="com.ideal.script.model.entity.InfoVersionText">
        update ieai_script_info_version_text
        <trim prefix="SET" suffixOverrides=",">
            <if test="content != null">icontent = #{content},</if>
            <if test="creatorId != null">icreator_id = #{creatorId},</if>
            <if test="creatorName != null">icreator_name = #{creatorName},</if>
            icreate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
        where isrc_script_uuid = #{srcScriptUuid}
    </update>

    <delete id="deleteInfoVersionTextById" parameterType="Long">
        delete from ieai_script_info_version_text where iid = #{id}
    </delete>

    <delete id="deleteInfoVersionTextByIds" parameterType="String">
        delete from ieai_script_info_version_text where iid in 
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteInfoVersionTextByScriptUuids" parameterType="String">
        delete from ieai_script_info_version_text where isrc_script_uuid in
        <foreach item="uuid" collection="array" open="(" separator="," close=")">
            #{uuid}
        </foreach>
    </delete>

    <select id="selectInfoVersionTextByScriptId" parameterType="com.ideal.script.model.entity.InfoVersionText" resultMap="InfoVersionTextResult">
        SELECT t2.icontent FROM ieai_script_info_version t,ieai_script_info t1, ieai_script_info_version_text t2 WHERE t.iinfo_unique_uuid = t1.iunique_uuid 
        AND t.isrc_script_uuid = t2.isrc_script_uuid AND t1.iid = #{scriptId}
    </select>

    <select id="selectInfoVersionTextByScriptUuid" parameterType="String" resultMap="InfoVersionTextResult">
        <include refid="selectInfoVersionTextVo"/>
        <where>
            <if test="srcScriptUuid != null  and srcScriptUuid != ''"> and isrc_script_uuid = #{srcScriptUuid}</if>
        </where>
    </select>
</mapper>