<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.ExectimeMapper">

    <resultMap type="com.ideal.script.model.entity.Exectime" id="ExectimeResult">
            <result property="id" column="iid"/>
            <result property="successTimes" column="isuccess_times"/>
            <result property="totalTimes" column="itotal_times"/>
            <result property="srcScriptUuid" column="isrc_script_uuid"/>
            <result property="taskCount" column="itask_count"/>
            <result property="createTime" column="icreate_time"/>
            <result property="successRate" column="isuccess_rate"/>
    </resultMap>

    <sql id="selectExectimeVo">
        select iid, isuccess_times, itotal_times, isrc_script_uuid, itask_count, icreate_time
        from ieai_script_exectime
    </sql>

    <select id="selectExectimeList" parameterType="com.ideal.script.model.entity.Exectime" resultMap="ExectimeResult">
        <include refid="selectExectimeVo"/>
        <where>
                        <if test="successTimes != null ">
                            and isuccess_times = #{successTimes}
                        </if>
                        <if test="totalTimes != null ">
                            and itotal_times = #{totalTimes}
                        </if>
                        <if test="srcScriptUuid != null  and srcScriptUuid != ''">
                            and isrc_script_uuid = #{srcScriptUuid}
                        </if>
                        <if test="taskCount != null ">
                            and itask_count = #{taskCount}
                        </if>
        </where>
    </select>

    <select id="selectExectimeById" parameterType="Long"
            resultMap="ExectimeResult">
            <include refid="selectExectimeVo"/>
            where iid = #{id}
    </select>

    <select id="getTotalAndSuccessRate" resultMap="ExectimeResult">
        select
            iid,
            isuccess_times,
            itotal_times,
            itask_count,
            isrc_script_uuid,
            CONCAT(
                case
                    when itotal_times = 0 then '0'
                    else CAST(ROUND(isuccess_times * 100 / itotal_times) as char)
                end, '%') as isuccess_rate
        from
            ieai_script_exectime
        where isrc_script_uuid=#{srcScriptUuid}
    </select>

    <insert id="insertExectime" parameterType="com.ideal.script.model.entity.Exectime">
        insert into ieai_script_exectime
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">iid,
                    </if>
                    <if test="successTimes != null">isuccess_times,
                    </if>
                    <if test="totalTimes != null">itotal_times,
                    </if>
                    <if test="srcScriptUuid != null">isrc_script_uuid,
                    </if>
                    <if test="taskCount != null">itask_count,
                    </if>
                    icreate_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                        <if test="id != null">#{id},</if>
                        <if test="successTimes != null">#{successTimes},</if>
                        <if test="totalTimes != null">#{totalTimes},</if>
                        <if test="srcScriptUuid != null">#{srcScriptUuid},</if>
                        <if test="taskCount != null">#{taskCount},</if>
                        ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
    </insert>

    <update id="updateExectime" parameterType="com.ideal.script.model.entity.Exectime">
        update ieai_script_exectime
        <trim prefix="SET" suffixOverrides=",">
                        <if test="successTimes != null">isuccess_times = #{successTimes},</if>
                        <if test="totalTimes != null">itotal_times = #{totalTimes},</if>
                        <if test="srcScriptUuid != null">isrc_script_uuid = #{srcScriptUuid},</if>
                        <if test="taskCount != null">itask_count = #{taskCount},</if>
                        icreate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
        where iid = #{id}
    </update>

    <delete id="deleteExectimeById" parameterType="Long">
        delete
        from ieai_script_exectime where iid = #{id}
    </delete>

    <delete id="deleteExectimeByIds" parameterType="String">
        delete from ieai_script_exectime where iid in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <!--更新执行次数-->
    <update id="updateScriptExectime" parameterType="map">
        <choose>
            <when test="type == 1">
                update ieai_script_exectime set isuccess_times = isuccess_times + 1, itotal_times = itotal_times + 1 where isrc_script_uuid = #{srcScriptUuid};
            </when>
            <when test="type == 2">
                update ieai_script_exectime set itotal_times = itotal_times + 1 where isrc_script_uuid = #{srcScriptUuid};
            </when>
            <when test="type == 3">
                update ieai_script_exectime set itask_count = itask_count + 1 where isrc_script_uuid = #{srcScriptUuid};
            </when>
        </choose>
    </update>

</mapper>