<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.StaleScriptMapper">

    <resultMap id="base" type="com.ideal.script.model.entity.Stale">
        <id property="id" column="iid"></id>
        <result property="confirmState" column="iconfirm_state"></result>
        <result property="confirmTime" column="iconfirm_time"></result>
    </resultMap>

    <select id="getData" parameterType="date" resultMap="com.ideal.script.mapper.InfoVersionMapper.InfoVersionResult">
        select a.iid, b.iinfo_unique_uuid, b.isrc_script_uuid, b.iupdate_time from ieai_script_info a, ieai_script_info_version b
        where a.iunique_uuid = b.iinfo_unique_uuid and b.is_default = 1
    </select>

    <select id="getStaleData" resultMap="base">
        select iid, iconfirm_state, iconfirm_time from ieai_script_stale where iinfo_id = #{id}
    </select>

    <delete id="deleteStaleByInfoId">
        delete from ieai_script_stale where iinfo_id = #{id}
    </delete>

    <update id="updateStaleData">
        update ieai_script_stale set iscanning_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()}, iunmodify_day = #{day} where iid = #{id}
    </update>

    <insert id="insertStaleData" parameterType="com.ideal.script.model.entity.Stale">
        insert into ieai_script_stale(iid, iinfo_id, iinfo_version_id, iscript_name_zh, iscript_name, icategory_path, idefault_version, itask_count, isuccess_rate, iunmodify_day, iinfo_updatetime, icreator_id, icreator_name)
        values (#{id}, #{infoId}, #{infoVersionId}, #{scriptNameZh}, #{scriptName}, #{categoryPath}, #{defaultVersion}, #{taskCount}, #{successRate}, #{unmodifyDay}, #{infoUpdatetime}, #{creatorId}, #{creatorName})
    </insert>
</mapper>