<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.TaskAttachmentTemplateMapper">

    <resultMap type="com.ideal.script.model.entity.TaskAttachment" id="TaskAttachmentResult">
        <result property="id" column="iid"/>
        <result property="scriptTaskId" column="iscript_task_id"/>
        <result property="name" column="iname"/>
        <result property="size" column="isize"/>
        <result property="uploadTime" column="iupload_time"/>
        <result property="contents" column="icontents"/>
    </resultMap>

    <sql id="selectTaskAttachmentVo">
        select iid, iscript_task_id, iname, isize, iupload_time, icontents
        from ieai_script_task_attachment_temp
    </sql>

    <insert id="insertTaskAttachment" parameterType="com.ideal.script.model.entity.TaskAttachment">
        insert into ieai_script_task_attachment_temp
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">iid,
            </if>
            <if test="scriptTaskId != null">iscript_task_id,
            </if>
            <if test="name != null">iname,
            </if>
            <if test="size != null">isize,
            </if>
            iupload_time,
            <if test="contents != null">icontents,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},
            </if>
            <if test="scriptTaskId != null">#{scriptTaskId},
            </if>
            <if test="name != null">#{name},
            </if>
            <if test="size != null">#{size},
            </if>
            ${@com.ideal.common.util.DbUtils@getCurrentTime()},
            <if test="contents != null">#{contents},
            </if>
        </trim>
    </insert>


    <select id="selectTaskAttachmentByTaskId"  parameterType="Long"
            resultMap="TaskAttachmentResult">
        select iid, iscript_task_id, iname, isize,icontents, iupload_time
        from ieai_script_task_attachment_temp
        <where>
            <if test="taskId != null">
                iscript_task_id = #{taskId}
            </if>
        </where>
    </select>

    <delete id="deleteByTaskId">
        delete from ieai_script_task_attachment_temp where iscript_task_id = #{taskId}
    </delete>

    <delete id="deleteById">
        delete from ieai_script_task_attachment_temp where iid = #{id}
    </delete>

    <update id="batchUpdateByIds" parameterType="com.ideal.script.model.entity.Attachment">
        update ieai_script_task_attachment_temp set iscript_task_id = #{taskId}
        where iid in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectAttachmentById" parameterType="Long" resultMap="TaskAttachmentResult">
        select iid, iname, isize, icontents
        from ieai_script_task_attachment_temp where iid = #{id}
    </select>
</mapper>