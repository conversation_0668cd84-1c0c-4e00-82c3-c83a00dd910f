<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.ScriptReferrerInfoMapper">

    <resultMap type="com.ideal.script.model.entity.ScriptReferrerInfo" id="DataResult">
        <result property="iid" column="iid"/>
        <result property="sceneName" column="scene_name"/>
        <result property="flowName" column="flow_name"/>
        <result property="flowVersion" column="flow_version"/>
        <result property="referrerName" column="referrer_name"/>
        <result property="referrerBizId" column="referrer_biz_id"/>
        <result property="scriptSrcUuidSingle" column="script_src_uuid"/>
        <result property="createdTime" column="created_time"/>
        <result property="scriptName" column="iscript_name"/>
        <result property="scriptNameZh" column="iscript_name_zh"/>
        <result property="scriptVersion" column="iversion"/>
    </resultMap>

    <select id="selectPageList" parameterType="com.ideal.script.model.entity.ScriptReferrerInfo" resultMap="DataResult">
        SELECT
        a.scene_name,
        a.flow_name,
        a.flow_version,
        a.referrer_name,
        b.iscript_name,
        b.iscript_name_zh,
        c.iversion
        FROM
        ieai_script_referrer_info a
        LEFT JOIN ieai_script_info_version c ON a.script_src_uuid = c.isrc_script_uuid
        LEFT JOIN ieai_script_info b ON  b.iunique_uuid = c.iinfo_unique_uuid
        <where>
            AND b.iunique_uuid = #{uniqueUuid}
            <if test="flowVersion != null and flowVersion != ''">AND a.flow_version like concat('%', #{flowVersion}, '%') </if>
            <if test="referrerName != null and referrerName != ''">AND a.referrer_name like concat('%', #{referrerName}, '%')</if>
            <if test="flowName != null and flowName != ''">AND a.flow_name like concat('%', #{flowName}, '%')</if>
            <if test="sceneName != null and sceneName != ''">AND a.scene_name like concat('%', #{sceneName}, '%')</if>
            <if test="scriptVersion != null and scriptVersion != ''">AND c.iversion like concat('%', #{scriptVersion}, '%')</if>
        </where>
    </select>

    <delete id="deleteByReferrerBizId" parameterType="com.ideal.script.model.entity.ScriptReferrerInfo">
        delete from ieai_script_referrer_info where referrer_biz_id = #{referrerBizId}
    </delete>

    <insert id="insertReferrerInfo" parameterType="com.ideal.script.model.entity.ScriptReferrerInfo" useGeneratedKeys="true" keyProperty="iid">
        insert into ieai_script_referrer_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="iid != null">
                iid,
            </if>
            <if test="sceneName != null and sceneName != ''">scene_name,</if>
            <if test="flowName != null and flowName != ''">flow_name,</if>
            <if test="flowVersion != null and flowVersion != ''">flow_version,</if>
            <if test="referrerName != null and referrerName != ''">referrer_name,</if>
            <if test="referrerBizId != null">referrer_biz_id,</if>
            <if test="scriptSrcUuidSingle != null and scriptSrcUuidSingle != ''">script_src_uuid,</if>
            created_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="iid != null">
                #{iid},
            </if>
            <if test="sceneName != null and sceneName != ''">#{sceneName},</if>
            <if test="flowName != null and flowName != ''">#{flowName},</if>
            <if test="flowVersion != null and flowVersion != ''">#{flowVersion},</if>
            <if test="referrerName != null and referrerName != ''">#{referrerName},</if>
            <if test="referrerBizId != null">#{referrerBizId},</if>
            <if test="scriptSrcUuidSingle != null and scriptSrcUuidSingle != ''">#{scriptSrcUuidSingle},</if>
            ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
    </insert>
</mapper>