<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.TaskIpsTemplateMapper">

    <resultMap type="com.ideal.script.model.entity.AgentInfo" id="TaskIpsResult">
        <result property="id" column="iid"/>
        <result property="sysmAgentInfoId" column="isysm_agent_info_id"/>
        <result property="agentIp" column="iagent_ip"/>
        <result property="agentName" column="iagent_name"/>
        <result property="agentPort" column="iagent_port"/>
        <result property="deviceName" column="idevice_name"/>
        <result property="osName" column="ios_name"/>
    </resultMap>

    <insert id="insertTaskIps" parameterType="com.ideal.script.model.entity.AgentInfo" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_script_task_ips_temp
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">
                        iid,
                    </if>
                    <if test="scriptTaskId != null">iscript_task_id,
                    </if>
                    <if test="scriptAgentinfoId != null">iscript_agentinfo_id,
                    </if>
                    icreate_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">
                        #{id},
                    </if>
                    <if test="scriptTaskId != null">#{scriptTaskId},
                    </if>
                    <if test="scriptAgentinfoId != null">#{scriptAgentinfoId},
                    </if>
                    ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
    </insert>

    <select id="selectAgentInfoByTaskId" parameterType="java.lang.Long" resultMap="TaskIpsResult">
        select * from ieai_script_agent_info where iid in ( select iscript_agentinfo_id from ieai_script_task_ips_temp where iscript_task_id = #{taskId} )
    </select>

    <delete id="deleteByTaskId">
        delete from ieai_script_task_ips_temp where iscript_task_id = #{taskId}
    </delete>

</mapper>