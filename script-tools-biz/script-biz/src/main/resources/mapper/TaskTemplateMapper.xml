<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.TaskTemplateMapper">

    <resultMap type="com.ideal.script.model.bean.TaskCloneBean" id="TaskTemplateResult">
        <result property="iid" column="iid"/>
        <result property="taskName" column="itask_name"/>
        <result property="scriptNameZh" column="iscript_name_zh"/>
        <result property="scriptName" column="iscript_name"/>
        <result property="scriptLabel" column="iscript_label"/>
        <result property="categoryId" column="icategory_id"/>
        <result property="level" column="ilevel"/>
        <result property="platform" column="iplatform"/>
        <result property="version" column="iversion"/>
        <result property="creatorName" column="icreator_name"/>
        <result property="updateTime" column="iupdate_time"/>
        <result property="scriptInfoId" column="script_info_id"/>
        <result property="uniqueUuid" column="iunique_uuid"/>
        <result property="scriptType" column="iscript_type"/>
        <result property="execuser" column="iexecuser"/>
        <result property="scriptInfoVersionId" column="script_info_version_id"/>
        <result property="srcScriptUuid" column="isrc_script_uuid"/>
        <result property="eachNum" column="ieach_num"/>
        <result property="execDesc" column="ipublish_desc"/>
        <result property="createTime" column="icreate_time"/>
        <result property="taskType" column="itask_type"/>
        <result property="timeout" column="itimeout" />
        <result property="driveMode" column="idrive_mode" />
        <result property="auditorId" column="iauditor_id"/>
        <result property="taskScheduler" column="itask_scheduler"/>
        <result property="taskTime" column="itask_time"/>
        <result property="taskCron" column="itask_cron"/>
        <result property="maxVersion" column="max_version"/>
    </resultMap>

    <insert id="insertTaskTemplate" parameterType="com.ideal.script.model.entity.Task" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_script_task_temp
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                iid,
            </if>
            <if test="srcScriptUuid != null">isrc_script_uuid,
            </if>
            <if test="taskName != null">itask_name,
            </if>
            <if test="eachNum != null">ieach_num,
            </if>
            <if test="execUser != null">iexec_user,
            </if>
            <if test="taskScheduler != null">itask_scheduler,
            </if>
            <if test="taskCron != null">itask_cron,
            </if>
            <if test="taskTime != null">itask_time,
            </if>
            <if test="publishDesc != null">ipublish_desc,
            </if>
            <if test="startUser != null and startUser != ''">istart_user,
            </if>
            <if test="timeout != null">itimeout,
            </if>
            <if test="driveMode != null">idrive_mode,
            </if>
            <if test="startType != null">istart_type,
            </if>
            <if test="readyToExecute != null">iready_to_execute,
            </if>
            <if test="creatorId != null">icreator_id,
            </if>
            <if test="creatorName != null">icreator_name,
            </if>
            <if test="updatorId != null">iupdator_id,
            </if>
            <if test="updatorName != null">iupdator_name,
            </if>
            icreate_time,
            iupdate_time,
            <if test="scriptTaskSource != null">iscript_task_source,
            </if>
            <if test="taskType != null">itask_type,
            </if>
            <if test="auditorId != null">iauditor_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="srcScriptUuid != null">#{srcScriptUuid},
            </if>
            <if test="taskName != null">#{taskName},
            </if>
            <if test="eachNum != null">#{eachNum},
            </if>
            <if test="execUser != null">#{execUser},
            </if>
            <if test="taskScheduler != null">#{taskScheduler},
            </if>
            <if test="taskCron != null">#{taskCron},
            </if>
            <if test="taskTime != null">#{taskTime},
            </if>
            <if test="publishDesc != null">#{publishDesc},
            </if>
            <if test="startUser != null and startUser != ''">#{startUser},
            </if>
            <if test="timeout != null">#{timeout},
            </if>
            <if test="driveMode != null">#{driveMode},
            </if>
            <if test="startType != null">#{startType},
            </if>
            <if test="readyToExecute != null">#{readyToExecute},
            </if>
            <if test="creatorId != null">#{creatorId},
            </if>
            <if test="creatorName != null">#{creatorName},
            </if>
            <if test="updatorId != null">#{updatorId},
            </if>
            <if test="updatorName != null">#{updatorName},
            </if>
            ${@com.ideal.common.util.DbUtils@getCurrentTime()},
            ${@com.ideal.common.util.DbUtils@getCurrentTime()},
            <if test="scriptTaskSource != null">#{scriptTaskSource},
            </if>
            <if test="taskType != null">#{taskType},
            </if>
            <if test="auditorId != null">#{auditorId},
            </if>
        </trim>
    </insert>

    <select id="selectTaskTemplateList" resultMap="TaskTemplateResult">
        select a.iid,
        a.itask_name,
        a.ieach_num,
        a.ipublish_desc,
        a.icreate_time,
        a.itask_type,
        a.iauditor_id,
        a.itask_scheduler,
        a.itask_time,
        a.itask_cron,
        b.iscript_name_zh,
        b.iscript_name,
        b.icategory_id,
        b.iscript_label,
        b.iplatform,
        b.icreator_name,
        b.iid as script_info_id,
        b.iunique_uuid,
        b.iscript_type,
        b.iexecuser,
        c.iversion,
        c.ilevel,
        c.iid as script_info_version_id,
        c.isrc_script_uuid,
        a.itimeout,
        a.idrive_mode,
        (select max(m.iversion) from ieai_script_info_version m where m.iinfo_unique_uuid = b.iunique_uuid) as max_version
        from ieai_script_task_temp a
        left join ieai_script_info_version c
        on a.isrc_script_uuid = c.isrc_script_uuid
        left join ieai_script_info b
        on c.iinfo_unique_uuid = b.iunique_uuid
        <where>
            <!--查询当前登录用户所属角色下的所有用户的脚本-->
            <if test="params.roleFlag and params.userIdList != null and params.userIdList.size != 0">
                and (b.icreator_id in
                <foreach item="userId" collection="params.userIdList" open="(" separator="," close=")">
                    #{userId}
                </foreach>
                <if test="params.orgCategoryPath != null and params.orgCategoryPath.size != 0">
                    or (
                    <foreach collection="params.orgCategoryPath" item="path" separator="OR">
                        b.icategory_path like #{path}
                    </foreach>
                    )
                </if>
                    )
            </if>
            <if test="!params.roleFlag">
                <if test="orgCode != null and orgCode != '' ">
                    <bind name="orgCode" value="orgCode + '%'"/>
                    and (b.isys_org_code like #{orgCode}
                    <if test="params.orgCategoryPath != null and params.orgCategoryPath.size != 0">
                        or (
                        <foreach collection="params.orgCategoryPath" item="path" separator="OR">
                            b.icategory_path like #{path}
                        </foreach>
                        )
                    </if>
                        )
                </if>
            </if>
            <if test="params.scriptNameZh != null and params.scriptNameZh != '' ">
                <bind name="params.scriptNameZh" value="'%' + params.scriptNameZh + '%'" />
                and b.iscript_name_zh like #{params.scriptNameZh}
            </if>
            <if test="params.scriptName != null and params.scriptName != '' ">
                <bind name="params.scriptName" value="'%' + params.scriptName + '%'" />
                and b.iscript_name like #{params.scriptName}
            </if>
            <if test="params.scriptLabel != null and params.scriptLabel != '' ">
                <bind name="params.scriptLabel" value="'%' + params.scriptLabel + '%' " />
                and b.iscript_label like #{params.scriptLabel}
            </if>
            <if test="params.taskName != null and params.taskName != '' ">
                <bind name="params.taskName" value="'%' + params.taskName + '%' " />
                and a.itask_name like #{params.taskName}
            </if>
            <if test="params.categoryId != null and params.categoryId != '' "> and b.icategory_id = #{params.categoryId} </if>
            <if test="params.platform != null and params.platform != '' "> and b.iplatform = #{params.platform} </if>
            <if test="params.level != null and params.level != '' "> and c.ilevel = #{params.level} </if>
            <if test="params.taskType != null "> and a.itask_type = #{params.taskType} </if>
        </where>
        order by a.icreate_time desc
    </select>

    <select id="selectTaskTemplateById" parameterType="com.ideal.script.model.entity.Task" resultMap="TaskTemplateResult">
        SELECT temp.*, ver.ilevel, ver.iid AS script_info_version_id, info.icategory_id
        FROM (SELECT *
              FROM ieai_script_task_temp
              WHERE iid = #{taskTemplateId}) temp
                 JOIN ieai_script_info_version ver ON temp.isrc_script_uuid = ver.isrc_script_uuid
                 JOIN ieai_script_info info ON ver.iinfo_unique_uuid = info.iunique_uuid
    </select>

    <select id="selectTaskTemplateByTaskName" parameterType="java.lang.String" resultMap="TaskTemplateResult">
        select * from ieai_script_task_temp where itask_name = #{taskName}
    </select>

    <delete id="deleteById">
        delete from ieai_script_task_temp where iid = #{iid}
    </delete>

    <update id="updateById" parameterType="com.ideal.script.model.entity.Task">
        update ieai_script_task_TEMP
        <trim prefix="SET" suffixOverrides=",">
            <if test="srcScriptUuid != null">isrc_script_uuid =
                #{srcScriptUuid},
            </if>
            <if test="taskName != null">itask_name =
                #{taskName},
            </if>
            <if test="eachNum != null">ieach_num =
                #{eachNum},
            </if>
            <if test="execUser != null">iexec_user =
                #{execUser},
            </if>
            <if test="taskScheduler != null">itask_scheduler =
                #{taskScheduler},
            </if>
            <if test="taskTime != null">itask_time =
                #{taskTime},
            </if>
            <if test="taskCron != null">itask_cron =
                #{taskCron},
            </if>
            <if test="publishDesc != null">ipublish_desc =
                #{publishDesc},
            </if>
            <if test="startUser != null and startUser != ''">istart_user =
                #{startUser},
            </if>
            <if test="timeout != null">itimeout =
                #{timeout},
            </if>
            <if test="driveMode != null">idrive_mode =
                #{driveMode},
            </if>
            <if test="startType != null">istart_type =
                #{startType},
            </if>
            <if test="readyToExecute != null">iready_to_execute =
                #{readyToExecute},
            </if>
            <if test="creatorId != null">icreator_id =
                #{creatorId},
            </if>
            <if test="creatorName != null">icreator_name =
                #{creatorName},
            </if>
            <if test="updatorId != null">iupdator_id =
                #{updatorId},
            </if>
            <if test="updatorName != null">iupdator_name =
                #{updatorName},
            </if>
            iupdate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
            <if test="scriptTaskSource != null">iscript_task_source =
                #{scriptTaskSource},
            </if>
            <if test="auditorId != null">iauditor_id =
                #{auditorId},
            </if>
        </trim>
        where iid = #{id}
    </update>
</mapper>
