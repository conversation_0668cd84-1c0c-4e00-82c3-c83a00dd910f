package com.ideal.script.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.script.common.constant.enums.BindFuncVarTypeEnum;
import com.ideal.script.dto.BindFuncVarDto;
import com.ideal.script.dto.ScriptInfoDto;
import com.ideal.script.mapper.BindFuncVarMapper;
import com.ideal.script.model.entity.BindFuncVar;
import com.ideal.script.model.entity.InfoVersion;
import com.ideal.script.service.IBindFuncVarService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 【函数变量绑定关系】Service业务层处理
 *
 * <AUTHOR>
 */
@Transactional(rollbackFor = Exception.class)
@Service
public class BindFuncVarServiceImpl implements IBindFuncVarService {

    private final BindFuncVarMapper bindFuncVarMapper;

    public BindFuncVarServiceImpl(BindFuncVarMapper bindFuncVarMapper) {
        this.bindFuncVarMapper = bindFuncVarMapper;
    }


    /**
     * 查询【函数变量信息】
     *
     * @param id 【函数变量】主键
     * @return 【函数变量绑定关系Dto】
     */
    @Override
    public BindFuncVarDto selectBindFuncVarById(Long id) {
        BindFuncVar bindFuncVar = bindFuncVarMapper.selectBindFuncVarById(id);
        return BeanUtils.copy(bindFuncVar,BindFuncVarDto.class);
    }

    /**
     * 查询【函数/变量绑定关系】列表
     *
     * @param bindFuncVarDto 【函数/变量Dto】
     * @return 【集合】
     */
    @Override
    public PageInfo<BindFuncVarDto> selectBindFuncVarList(BindFuncVarDto bindFuncVarDto, int pageNum, int pageSize) {
        PageMethod.startPage(pageNum, pageSize);
        List<BindFuncVar> bindFuncVarList = new ArrayList<>();
        if (null != bindFuncVarDto) {
            BindFuncVar bindFuncVar = BeanUtils.copy(bindFuncVarDto,BindFuncVar.class);
            bindFuncVarList = bindFuncVarMapper.selectBindFuncVarList(bindFuncVar);
        }
        return PageDataUtil.toDtoPage(bindFuncVarList,BindFuncVarDto.class);
    }

    /**
     * 新增【函数/变量绑定关系】
     *
     * @param bindFuncVarDto 【函数/变量Dto】
     */
    @Override
    public void insertBindFuncVar(BindFuncVarDto bindFuncVarDto) {
        BindFuncVar bindFuncVar = BeanUtils.copy(bindFuncVarDto,BindFuncVar.class);
        bindFuncVarMapper.insertBindFuncVar(bindFuncVar);
    }

    /**
     * 修改【函数/变量绑定关系】
     *
     * @param bindFuncVarDto 【函数/变量Dto】
     */
    @Override
    public void updateBindFuncVar(BindFuncVarDto bindFuncVarDto) {
        BindFuncVar bindFuncVar = BeanUtils.copy(bindFuncVarDto,BindFuncVar.class);
        bindFuncVarMapper.updateBindFuncVar(bindFuncVar);
    }

    /**
     * 批量删除【函数/变量绑定关系】
     *
     * @param ids 需要删除的【函数/变量id】主键
     */
    @Override
    public void deleteBindFuncVarByIds(Long[] ids) {
        bindFuncVarMapper.deleteBindFuncVarByIds(ids);
    }

    /**
     * 删除【函数/变量绑定关系】信息
     *
     * @param id 【函数/变量id】主键
     * @return 结果
     */
    @Override
    public int deleteBindFuncVarById(Long id) {
        return bindFuncVarMapper.deleteBindFuncVarById(id);
    }

    /**
     * 保存函数、变量绑定关系
     *
     * @param scriptInfoDto dto
     */

    @Transactional(rollbackFor = Exception.class)
    public void createBindFuncVars(ScriptInfoDto scriptInfoDto) {
        // 变量
        if(scriptInfoDto.getScriptVersionDto() != null && scriptInfoDto.getScriptVersionDto().getVariableList() != null) {
            for (int i = 0; i < scriptInfoDto.getScriptVersionDto().getVariableList().length; i++) {
                BindFuncVar bindFuncVar = new BindFuncVar();
                bindFuncVar.setSrcScriptUuid(scriptInfoDto.getScriptVersionDto().getSrcScriptUuid());
                bindFuncVar.setBindType(BindFuncVarTypeEnum.VARIABLE.getType());
                bindFuncVar.setBindObjId(scriptInfoDto.getScriptVersionDto().getVariableList()[i].getBindObjId());
                bindFuncVar.setObjName(scriptInfoDto.getScriptVersionDto().getVariableList()[i].getObjName());
                bindFuncVarMapper.insertBindFuncVar(bindFuncVar);
            }
        }
        // 函数
        if(scriptInfoDto.getScriptVersionDto() != null && scriptInfoDto.getScriptVersionDto().getFunctionList() != null) {
            for (int i = 0; i < scriptInfoDto.getScriptVersionDto().getFunctionList().length; i++) {
                BindFuncVar bindFuncVar = new BindFuncVar();
                bindFuncVar.setSrcScriptUuid(scriptInfoDto.getScriptVersionDto().getSrcScriptUuid());
                bindFuncVar.setBindType(BindFuncVarTypeEnum.FUNCTION.getType());
                bindFuncVar.setBindObjId(scriptInfoDto.getScriptVersionDto().getFunctionList()[i].getBindObjId());
                bindFuncVar.setObjName(scriptInfoDto.getScriptVersionDto().getFunctionList()[i].getObjName());
                bindFuncVarMapper.insertBindFuncVar(bindFuncVar);
            }
        }
    }

    /**
     *获取函数、变量绑定关系信息
     * @param infoVersion   脚本版本内容信息
     * @param bindType  绑定类型 1-变量，2-函数
     * @return  函数变量绑定关系数组
     */
    public BindFuncVarDto[] getBindFuncVarDtos(InfoVersion infoVersion, Integer bindType) {
        BindFuncVar bindFuncVar = new BindFuncVar();
        bindFuncVar.setSrcScriptUuid(infoVersion.getSrcScriptUuid());
        bindFuncVar.setBindType(bindType);
        List<BindFuncVar> bindFuncVarList = bindFuncVarMapper.selectBindFuncVarList(bindFuncVar);
        BindFuncVarDto[] bindFuncVarDtos = new BindFuncVarDto[bindFuncVarList.size()];
        for (int i = 0; i < bindFuncVarList.size(); i++) {
            BindFuncVarDto bindFuncVarDto = BeanUtils.copy(bindFuncVarList.get(i), BindFuncVarDto.class);
            bindFuncVarDtos[i] = bindFuncVarDto;
        }
        return bindFuncVarDtos;
    }
}
