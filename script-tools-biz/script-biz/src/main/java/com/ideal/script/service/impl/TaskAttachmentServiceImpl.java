package com.ideal.script.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.util.BatchDataUtil;
import com.ideal.script.common.util.FileSizeValidUtil;
import com.ideal.script.dto.AttachmentDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.TaskAttachmentMapper;
import com.ideal.script.model.dto.ScriptExecAuditDto;
import com.ideal.script.model.dto.TaskAttachmentDto;
import com.ideal.script.model.dto.TaskDto;
import com.ideal.script.model.entity.TaskAttachment;
import com.ideal.script.service.IAttachmentService;
import com.ideal.script.service.ITaskAttachmentService;
import org.apache.ibatis.session.SqlSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 脚本任务附件附Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class TaskAttachmentServiceImpl implements ITaskAttachmentService {
    private final Logger logger = LoggerFactory.getLogger(TaskAttachmentServiceImpl.class);
    private final TaskAttachmentMapper taskAttachmentMapper;
    private final BatchDataUtil batchDataUtil;
    private final FileSizeValidUtil fileSizeValidUtil;
    private final RedisTemplate<String, String> redisTemplate;
    public TaskAttachmentServiceImpl(TaskAttachmentMapper taskAttachmentMapper, BatchDataUtil batchDataUtil, IAttachmentService attachmentService, FileSizeValidUtil fileSizeValidUtil, RedisTemplate<String, String> redisTemplate) {
        this.taskAttachmentMapper = taskAttachmentMapper;
        this.batchDataUtil = batchDataUtil;
        this.fileSizeValidUtil = fileSizeValidUtil;
        this.redisTemplate = redisTemplate;
    }

    /**
     * 查询脚本任务附件附
     *
     * @param id 脚本任务附件附主键
     * @return 脚本任务附件附
     */
    @Override
    public TaskAttachmentDto selectTaskAttachmentById(Long id) {
        return BeanUtils.copy(taskAttachmentMapper.selectTaskAttachmentById(id), TaskAttachmentDto.class);
    }

    /**
     * 查询脚本任务附件附列表
     *
     * @param taskAttachmentDto 脚本任务附件附
     * @return 脚本任务附件附
     */
    @Override
    public PageInfo<TaskAttachmentDto> selectTaskAttachmentList(TaskAttachmentDto taskAttachmentDto, int pageNum, int pageSize) {
        PageMethod.startPage(pageNum, pageSize);
        List<TaskAttachment> taskAttachmentList = new ArrayList<>();
        if (null != taskAttachmentDto) {
            TaskAttachment taskAttachment = BeanUtils.copy(taskAttachmentDto, TaskAttachment.class);
            taskAttachmentList = taskAttachmentMapper.selectTaskAttachmentList(taskAttachment);
        }
        return PageDataUtil.toDtoPage(taskAttachmentList, TaskAttachmentDto.class);

    }

    /**
     * 新增脚本任务附件附
     *
     * @param taskAttachmentDto 脚本任务附件附
     * @return 结果
     */
    @Override
    public int insertTaskAttachment(TaskAttachmentDto taskAttachmentDto) {
        TaskAttachment taskAttachment = BeanUtils.copy(taskAttachmentDto, TaskAttachment.class);
        return taskAttachmentMapper.insertTaskAttachment(taskAttachment);
    }

    /**
     * 修改脚本任务附件附
     *
     * @param taskAttachmentDto 脚本任务附件附
     * @return 结果
     */
    @Override
    public int updateTaskAttachment(TaskAttachmentDto taskAttachmentDto) {
        TaskAttachment taskAttachment = BeanUtils.copy(taskAttachmentDto, TaskAttachment.class);
        return taskAttachmentMapper.updateTaskAttachment(taskAttachment);
    }

    /**
     * 批量删除脚本任务附件附
     *
     * @param ids 需要删除的脚本任务附件附主键
     * @return 结果
     */
    @Override
    public int deleteTaskAttachmentByIds(Long[] ids) {
        return taskAttachmentMapper.deleteTaskAttachmentByIds(ids);
    }

    /**
     * 删除脚本任务附件附信息
     *
     * @param id 脚本任务附件附主键
     * @return 结果
     */
    @Override
    public int deleteTaskAttachmentById(Long id) {
        return taskAttachmentMapper.deleteTaskAttachmentById(id);
    }

    /**
     * 双人复核业务详情页面-查询任务绑定的附件
     *
     * @param serviceId 业务主键
     * @return List<TaskAttachmentDto>
     */
    @Override
    public List<TaskAttachmentDto> selectTaskAttachmentByServiceId(Long serviceId,Long taskId) {
        List<TaskAttachment> selectTaskAttachmentList = taskAttachmentMapper.selectTaskAttachmentByServiceId(serviceId,taskId);
        return BeanUtils.copy(selectTaskAttachmentList, TaskAttachmentDto.class);
    }

    @Override
    public List<TaskAttachmentDto> selectTaskAttachmentNoContentByServiceId(Long serviceId,Long taskId) {
        List<TaskAttachment> selectTaskAttachmentList = taskAttachmentMapper.selectTaskAttachmentNoContentByServiceId(serviceId,taskId);
        return BeanUtils.copy(selectTaskAttachmentList, TaskAttachmentDto.class);
    }


    /**
     * 存储附件信息
     *
     * @param scriptExecAuditDto 任务申请提交审核Dto
     * @param taskInfo           任务申请提交任务时的脚本任务对象
     * @param sqlSession         ​SqlSession​ 对象
     * <AUTHOR>
     */
    @Override
    public void saveTaskAttachement(ScriptExecAuditDto scriptExecAuditDto, TaskDto taskInfo, SqlSession sqlSession) throws ScriptException {
        List<AttachmentDto> scriptTempAttachments = scriptExecAuditDto.getScriptTempAttachments();
        List<TaskAttachmentDto> taskAttachmentDtoList = new ArrayList<>();

        if (null != scriptTempAttachments) {
            for (AttachmentDto taskAttachmentDto : scriptTempAttachments) {
                TaskAttachmentDto taskAttachmentDto1 = new TaskAttachmentDto();
                taskAttachmentDto1.setId(taskAttachmentDto.getId());
                taskAttachmentDto1.setScriptTaskId(taskInfo != null ? taskInfo.getId() : null);
                taskAttachmentDtoList.add(taskAttachmentDto1);
            }
        }
        if (!taskAttachmentDtoList.isEmpty()) {
            TaskAttachmentMapper taskAttachmentSession = sqlSession.getMapper(TaskAttachmentMapper.class);
            batchDataUtil.batchData(TaskAttachmentMapper.class, BeanUtils.copy(taskAttachmentDtoList, TaskAttachment.class), TaskAttachment.class, "updateTaskAttachment", sqlSession, taskAttachmentSession);
            String[] removeIds = taskAttachmentDtoList.stream().map(d -> String.valueOf(d.getId())).toArray(String[]::new);
            redisTemplate.opsForSet().remove(Constants.REDIS_SCRIPT_TEMP_ATTACHMENT, (Object[]) removeIds);
            logger.info("任务申请 redis成功上传临时附件： {}", removeIds);
        }
    }

    @Override
    public TaskAttachmentDto uploadAttachment(TaskAttachmentDto taskAttachmentDto) throws IOException, ScriptException {
        logger.info("上传临时附件，附件名为: {}, 字节数: {}", taskAttachmentDto.getName(), taskAttachmentDto.getSize());
        TaskAttachment taskAttachment = BeanUtils.copy(taskAttachmentDto, TaskAttachment.class);
        //验证文件大小
        fileSizeValidUtil.validateFileSize(taskAttachment.getSize());
        taskAttachment.setScriptTaskId(0L);
        taskAttachmentMapper.insertTaskAttachment(taskAttachment);

        // redis 缓存临时附件id
        redisTemplate.opsForSet().add(Constants.REDIS_SCRIPT_TEMP_ATTACHMENT, String.valueOf(taskAttachment.getId()));
        logger.info("上传临时附件，redis缓存临时附件ID： {}", taskAttachment.getId());

        return BeanUtils.copy(taskAttachment, TaskAttachmentDto.class);
    }

    @Override
    public List<Long> getIdsByTaskId(Long taskId) {
        return taskAttachmentMapper.getIdsByTaskId(taskId);
    }

    public int updateTaskIdEmptyByTaskId(Long taskId) {
        return taskAttachmentMapper.updateTaskIdEmptyByTaskId(taskId);
    }

    @Transactional(readOnly = false, rollbackFor = Exception.class)
    @Override
    public int cleanAttachment() {
        // 从redis中获取 临时附件id
        Set<String> members = redisTemplate.opsForSet().members(Constants.REDIS_SCRIPT_TEMP_ATTACHMENT);
        if (CollectionUtils.isEmpty(members)) {
            return 0;
        }

        List<Long> idList = members.stream().map(Long::valueOf).collect(Collectors.toList());
        // 删除数据库中临时附件id
        deleteTaskAttachmentByIds(idList.toArray(new Long[0]));
        // redis中删除 已删除的临时文件id

        Object[] idArray = members.toArray();
        redisTemplate.opsForSet().remove(Constants.REDIS_SCRIPT_TEMP_ATTACHMENT, idArray);
        return idList.size();
    }
}
