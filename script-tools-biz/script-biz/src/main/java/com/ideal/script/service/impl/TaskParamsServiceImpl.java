package com.ideal.script.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.EncryptUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.common.util.BatchDataUtil;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.TaskParamsMapper;
import com.ideal.script.model.dto.*;
import com.ideal.script.model.entity.TaskParams;
import com.ideal.script.service.IParameterCheckService;
import com.ideal.script.service.IParameterService;
import com.ideal.script.service.ITaskParamsService;
import org.apache.ibatis.session.SqlSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 关联关系，包含任务执行的参数信息Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class TaskParamsServiceImpl implements ITaskParamsService {
    private static final Logger logger = LoggerFactory.getLogger(TaskParamsServiceImpl.class);

    private final BatchDataUtil batchDataUtil;
    private final TaskParamsMapper taskParamsMapper;
    private final IParameterService parameterService;
    private final IParameterCheckService parameterCheckService;

    public TaskParamsServiceImpl(BatchDataUtil batchDataUtil, TaskParamsMapper taskParamsMapper, IParameterService parameterService, IParameterCheckService parameterCheckService) {
        this.batchDataUtil = batchDataUtil;
        this.taskParamsMapper = taskParamsMapper;
        this.parameterService = parameterService;
        this.parameterCheckService = parameterCheckService;
    }

    /**
     * 查询关联关系，包含任务执行的参数信息
     *
     * @param id 关联关系，包含任务执行的参数信息主键
     * @return 关联关系，包含任务执行的参数信息
     */
    @Override
    public TaskParamsDto selectTaskParamsById(Long id) {
        return BeanUtils.copy(taskParamsMapper.selectTaskParamsById(id), TaskParamsDto.class);
    }

    /**
     * 查询关联关系，包含任务执行的参数信息列表
     *
     * @param taskParamsDto 关联关系，包含任务执行的参数信息
     * @return 关联关系，包含任务执行的参数信息
     */
    @Override
    public PageInfo<TaskParamsDto> selectTaskParamsList(TaskParamsDto taskParamsDto, int pageNum, int pageSize) {
        PageMethod.startPage(pageNum, pageSize);
        List<TaskParams> taskParamsList = new ArrayList<>();
        if (null != taskParamsDto) {
            TaskParams taskParams = BeanUtils.copy(taskParamsDto, TaskParams.class);
            taskParamsList = taskParamsMapper.selectTaskParamsList(taskParams);
        }
        return PageDataUtil.toDtoPage(taskParamsList, TaskParamsDto.class);

    }

    /**
     * 新增关联关系，包含任务执行的参数信息
     *
     * @param taskParamsDto 关联关系，包含任务执行的参数信息
     * @return 结果
     */
    @Override
    public int insertTaskParams(TaskParamsDto taskParamsDto) {
        TaskParams taskParams = BeanUtils.copy(taskParamsDto, TaskParams.class);
        return taskParamsMapper.insertTaskParams(taskParams);
    }

    /**
     * 修改关联关系，包含任务执行的参数信息
     *
     * @param taskParamsDto 关联关系，包含任务执行的参数信息
     * @return 结果
     */
    @Override
    public int updateTaskParams(TaskParamsDto taskParamsDto) {
        TaskParams taskParams = BeanUtils.copy(taskParamsDto, TaskParams.class);
        return taskParamsMapper.updateTaskParams(taskParams);
    }

    /**
     * 批量删除关联关系，包含任务执行的参数信息
     *
     * @param ids 需要删除的关联关系，包含任务执行的参数信息主键
     * @return 结果
     */
    @Override
    public int deleteTaskParamsByIds(Long[] ids) {
        return taskParamsMapper.deleteTaskParamsByIds(ids);
    }

    /**
     * 删除关联关系，包含任务执行的参数信息信息
     *
     * @param id 关联关系，包含任务执行的参数信息主键
     * @return 结果
     */
    @Override
    public int deleteTaskParamsById(Long id) {
        return taskParamsMapper.deleteTaskParamsById(id);
    }

    /**
     * 删除关联关系，包含任务执行的参数信息信息
     *
     * @param taskId 任务id
     * @return 结果
     */
    @Override
    public int deleteTaskParamsByTaskId(Long taskId) {
        return taskParamsMapper.deleteTaskParamsByTaskId(taskId);
    }

    /**
     * 获取脚本任务执行的历史参数
     *
     * @param srcScriptUuid 脚本版本uuid
     * @return List<TaskHisParamsDto>
     * <AUTHOR>
     */
    @Override
    public List<TaskHisParamsDto> selectHisParam(String srcScriptUuid) {
        return BeanUtils.copy(taskParamsMapper.selectHisParam(srcScriptUuid), TaskHisParamsDto.class);
    }

    /**
     * 双人复核详情页面-查询参数
     *
     * @param serviceId 业务主键
     * @return List<TaskParamsDto>
     */
    @Override
    public List<TaskParamsDto> selectTaskParamsByServiceId(Long serviceId,Long taskId) {
        List<TaskParams> taskParamsList = taskParamsMapper.selectTaskParamsByServiceId(serviceId,taskId);
        return BeanUtils.copy(taskParamsList, TaskParamsDto.class);
    }

    /**
     * 存储参数信息
     *
     * @param scriptExecAuditDto 任务申请提交审核Dto
     * @param taskInfo           任务申请提交任务时的脚本任务对象
     * @param sqlSession         ​SqlSession​ 对象
     */
    @Override
    public void saveTaskParams(ScriptExecAuditDto scriptExecAuditDto, TaskDto taskInfo, SqlSession sqlSession) throws ScriptException {
        List<ParameterDto> parameterDtoList = scriptExecAuditDto.getParams();
        List<TaskParamsDto> taskParamsDtoList = new ArrayList<>();

        if (null != parameterDtoList) {
            for (ParameterDto parameterDto : parameterDtoList) {
                ParameterDto parameterDto1 = null;
                if(null != parameterDto.getId() && parameterDto.getId() > 0){
                    parameterDto1 = parameterService.selectParameterById(parameterDto.getId());
                }else{
                    parameterDto1 = parameterDto;
                }

                TaskParamsDto taskParamsDto = getTaskParamsDto(taskInfo, parameterDto, parameterDto1);
                taskParamsDtoList.add(taskParamsDto);

            }
        }
        if (!taskParamsDtoList.isEmpty()) {
            TaskExecuteServiceImpl.getTaskParamsMapperSession(taskParamsDtoList, sqlSession, batchDataUtil, logger);
        }
    }

    /**
     * 组织任务执行参数
     * @param taskInfo 任务基本信息
     * @param parameterDto  参数信息Dto
     * @param parameterDto1 参数信息Dto
     * @return  任务执行参数
     */
    private static TaskParamsDto getTaskParamsDto(TaskDto taskInfo, ParameterDto parameterDto, ParameterDto parameterDto1) {
        TaskParamsDto taskParamsDto = new TaskParamsDto();
        taskParamsDto.setId(null);
        taskParamsDto.setScriptTaskId(taskInfo != null ? taskInfo.getId() : null);
        taskParamsDto.setType(parameterDto.getParamType());
        taskParamsDto.setValue(parameterDto.getParamDefaultValue());
        taskParamsDto.setDesc(parameterDto.getParamDesc());
        taskParamsDto.setOrder(parameterDto.getParamOrder());
        taskParamsDto.setScriptParameterCheckId(parameterDto1.getParamCheckIid());
        taskParamsDto.setScriptParameterManagerId(parameterDto1.getScriptParameterManagerId());
        taskParamsDto.setStartType(Enums.BusinessType.SCRIPT_SERVICE.getValue());
        return taskParamsDto;
    }

    /**
     * 校验参数是否符合表达式规则
     *
     * @param parameterDtoList 参数列表
     * @return {@link String } 返回不符合规则的值
     */
    @Override
    public String validateParameterList(List<ParameterDto> parameterDtoList) throws ScriptException {
        List<String> validationMessages = new ArrayList<>();

        for (ParameterDto parameterDto : parameterDtoList) {
            String paramDefaultValue = parameterDto.getParamDefaultValue();
            //如果是加密的参数替换为解密的数据
            if(Objects.equals(parameterDto.getParamType(), "Cipher")){
                paramDefaultValue = EncryptUtils.sm4Decrypt(parameterDto.getParamDefaultValue());
            }
            Long paramCheckId = parameterDto.getParamCheckIid();
            if(null!=paramCheckId){
                // 根据 paramCheckIid 获取校验规则表达式，这里假设有一个方法叫 getValidationExpression 根据参数获取对应的校验表达式
                ParameterCheckDto parameterCheckDto = parameterCheckService.selectParameterCheckById(paramCheckId);

                // 使用取回的表达式对 paramDefaultValue 进行校验
                if (!validateParam(paramDefaultValue, parameterCheckDto.getCheckRule())) {
                    validationMessages.add("参数 " + paramDefaultValue + " 校验未通过，规则 "+parameterCheckDto.getCheckRule());
                }
            }
        }
        if(!validationMessages.isEmpty()){
            return String.join(", ", validationMessages);
        }else{
            return null;
        }

    }

    private boolean validateParam(String paramValue, String checkRule) throws ScriptException {
        // 使用正则表达式 Pattern 编译给定的校验规则
        Pattern pattern = null;
        try {
            pattern = Pattern.compile(checkRule);
        } catch (Exception e) {
            logger.error("请检查参数引用的校验规则的正则表达式是否正确！",e);
            throw new ScriptException("parameter.validation.message");
        }

        // 使用 Matcher 对象进行匹配
        Matcher matcher = pattern.matcher(paramValue);

        // 判断是否和给定的校验规则匹配
        return matcher.find();
    }

}
