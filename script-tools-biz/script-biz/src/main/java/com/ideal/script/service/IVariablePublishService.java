package com.ideal.script.service;

import com.ideal.script.model.dto.VarAndFuncForEditDto;
import com.ideal.script.model.dto.VariablePublishDto;
import com.github.pagehelper.PageInfo;
/**
 * 变量库基础信息发布Service接口
 * 
 * <AUTHOR>
 */
 public interface IVariablePublishService
{
    /**
     * 查询变量库基础信息发布
     * 
     * @param id 变量库基础信息发布主键
     * @return 变量库基础信息发布
     */
     VariablePublishDto selectVariablePublishById(Long id);

    /**
     * 查询变量库基础信息发布列表
     * 
     * @param variablePublishDto 变量库基础信息发布
     * @param pageNum 变量库基础信息发布
     * @param pageSize 变量库基础信息发布
     * @return 变量库基础信息发布集合
     */
     PageInfo<VariablePublishDto> selectVariablePublishList(VariablePublishDto variablePublishDto, int pageNum, int pageSize);

    /**
     * 新增变量库基础信息发布
     *
     * @param variablePublishDto 变量库基础信息发布
     */
     void insertVariableP(VariablePublishDto variablePublishDto);

    /**
     * 修改变量库基础信息发布
     *
     * @param variablePublishDto 变量库基础信息发布
     */
     void updateVariableP(VariablePublishDto variablePublishDto);

    /**
     * 批量删除变量库基础信息发布
     *
     * @param ids 需要删除的变量库基础信息发布主键集合
     */
     void deleteVariablePublishByIds(Long[] ids);

    /**
     * 删除变量库基础信息发布信息
     * 
     * @param id 变量库基础信息发布主键
     * @return 结果
     */
     int deleteVariablePublishById(Long id);

    /**
     * 查询
     * @param varAndFuncForEditDto  函数、变量绑定关系dto
     * @param pageNum   起始页
     * @param pageSize  每页大小
     * @return  结果
     */
    PageInfo<VariablePublishDto> selectVariablePublishListForEdit(VarAndFuncForEditDto varAndFuncForEditDto, int pageNum, int pageSize);
}
