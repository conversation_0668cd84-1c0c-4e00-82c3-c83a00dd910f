package com.ideal.script.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.script.mapper.VariablePublishMapper;
import com.ideal.script.model.dto.VarAndFuncForEditDto;
import com.ideal.script.model.dto.VariablePublishDto;
import com.ideal.script.model.entity.VarAndFuncForEdit;
import com.ideal.script.model.entity.VariablePublish;
import com.ideal.script.service.IVariablePublishService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 变量库基础信息发布Service业务层处理
 *
 * <AUTHOR>
 */
@Transactional(rollbackFor = Exception.class)
@Service
public class VariablePublishServiceImpl implements IVariablePublishService {

    private final VariablePublishMapper variablePMapper;

    public VariablePublishServiceImpl(VariablePublishMapper variablePublishMapper) {
        this.variablePMapper = variablePublishMapper;
    }

    /**
     * 查询变量库基础信息发布
     *
     * @param id 变量库基础信息发布主键
     * @return 变量库基础信息发布
     */
    @Override
    public VariablePublishDto selectVariablePublishById(Long id) {
        VariablePublish variableP = variablePMapper.selectVariablePublishById(id);
        return BeanUtils.copy(variableP, VariablePublishDto.class);
    }

    /**
     * 查询变量库基础信息发布列表
     *
     * @param variablePublishDto 变量库基础信息发布
     * @return 变量库基础信息发布
     */
    @Override
    public PageInfo<VariablePublishDto> selectVariablePublishList(VariablePublishDto variablePublishDto, int pageNum, int pageSize) {
        PageMethod.startPage(pageNum, pageSize);
        List<VariablePublish> variablePublishList = new ArrayList<>();
        if (null != variablePublishDto) {
            VariablePublish variableP = BeanUtils.copy(variablePublishDto, VariablePublish.class);
            variablePublishList = variablePMapper.selectVariablePublishList(variableP);
        }
        return PageDataUtil.toDtoPage(variablePublishList, VariablePublishDto.class);
    }

    /**
     * 新增变量库基础信息发布
     *
     * @param variablePublishDto 变量库基础信息发布
     */
    @Override
    public void insertVariableP(VariablePublishDto variablePublishDto) {
        VariablePublish variableP = BeanUtils.copy(variablePublishDto, VariablePublish.class);
        variablePMapper.insertVariableP(variableP);
    }

    /**
     * 修改变量库基础信息发布
     *
     * @param variablePublishDto 变量库基础信息发布
     */
    @Override
    public void updateVariableP(VariablePublishDto variablePublishDto) {
        VariablePublish variableP = BeanUtils.copy(variablePublishDto, VariablePublish.class);
        variablePMapper.updateVariableP(variableP);
    }

    /**
     * 批量删除变量库基础信息发布
     *
     * @param ids 需要删除的变量库基础信息发布主键
     */
    @Override
    public void deleteVariablePublishByIds(Long[] ids) {
        variablePMapper.deleteVariablePublishByIds(ids);
    }

    /**
     * 删除变量库基础信息发布信息
     *
     * @param id 变量库基础信息发布主键
     * @return 结果
     */
    @Override
    public int deleteVariablePublishById(Long id) {
        return variablePMapper.deleteVariablePublishById(id);
    }


    @Override
    public PageInfo<VariablePublishDto> selectVariablePublishListForEdit(VarAndFuncForEditDto varAndFuncForEditDto, int pageNum, int pageSize) {
        PageMethod.startPage(pageNum, pageSize);
        List<VariablePublish> variablePublishList = new ArrayList<>();
        if (null != varAndFuncForEditDto) {
            VarAndFuncForEdit varAndFuncForEdit = BeanUtils.copy(varAndFuncForEditDto, VarAndFuncForEdit.class);
            variablePublishList = variablePMapper.selectVariablePublishListForEdit(varAndFuncForEdit);
        }
        return PageDataUtil.toDtoPage(variablePublishList, VariablePublishDto.class);
    }
}
