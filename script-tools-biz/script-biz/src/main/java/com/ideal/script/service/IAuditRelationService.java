package com.ideal.script.service;

import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.AuditRelationDto;
import com.github.pagehelper.PageInfo;
import com.ideal.script.model.entity.AuditRelation;

/**
 * 双人复核与脚本服务化关系Service接口
 * 
 * <AUTHOR>
 */
 public interface IAuditRelationService
{
    /**
     * 查询双人复核与脚本服务化关系
     * 
     * @param id 双人复核与脚本服务化关系主键
     * @return 双人复核与脚本服务化关系
     */
     AuditRelationDto selectAuditRelationById(Long id);

    /**
     * 查询双人复核与脚本服务化关系
     *
     * @param id 双人复核与脚本服务化关系主键
     * @return 双人复核与脚本服务化关系
     */
    AuditRelationDto selectAuditRelationByTaskId(Long taskId);

    /**
     *查询双人复核与脚本服务化关系(通过双人复核主键)
     * @param workItemId    双人复核表主键id
     * @return  双人复核与脚本服务化关系
     */
     AuditRelationDto selectAuditRelationByWorkItemId(Long workItemId);

    /**
     * 查询双人复核与脚本服务化关系列表
     * @param pageNum 当前页
     * @param pageSize 每页条数
     * @param auditRelationDto 双人复核与脚本服务化关系
     * @return 双人复核与脚本服务化关系集合
     */
     PageInfo<AuditRelationDto> selectAuditRelationList(AuditRelationDto auditRelationDto, int pageNum, int pageSize);

    /**
     * 新增双人复核与脚本服务化关系
     * 
     * @param auditRelationDto 双人复核与脚本服务化关系
     * @return 结果
     */
     int insertAuditRelation(AuditRelationDto auditRelationDto);



    /**
     * 功能描述：更新双人复核与脚本服务化关系
     *
     * @param auditRelationDto 双人复核与脚本服务化关系对象
     * @throws ScriptException exception
     * @return int
     */
    int updateAuditRelation(AuditRelationDto auditRelationDto) throws ScriptException;

    /**
     * 功能描述：通过workItemId更新双人复核与脚本服务化关系
     *
     * @param auditRelationDto 双人复核与脚本服务化关系对象
     * @throws ScriptException exception
     * @return int
     */
    int updateAuditRelationByWorkItemId(AuditRelationDto auditRelationDto) throws ScriptException;

    /**
     * 批量删除双人复核与脚本服务化关系
     * 
     * @param ids 需要删除的双人复核与脚本服务化关系主键集合
     * @return 结果
     */
     int deleteAuditRelationByIds(Long[] ids);

    /**
     * 删除双人复核与脚本服务化关系信息
     * 
     * @param id 双人复核与脚本服务化关系主键
     * @return 结果
     */
     int deleteAuditRelationById(Long id);

    /**
     * 根据双人复核id查询脚本id以及版本uuid
     *
     * @param id 双人复核与脚本服务化关系主键
     * @return {@link AuditRelationDto }
     */
     AuditRelationDto selectInfoIdAndSrcScriptUuidByAuditRelationId(Long id);


    /**
     * 根据审核对象查询数据
     * @param auditRelation 审核实体对象
     * @return 审核实体对象
     */
    AuditRelation selectAuditRelationForAudit(AuditRelation auditRelation);

    /**
     * 根据审核对象更新数据
     * @param auditRelation 审核实体对象
     * @return 成功条数
     */
    int updateAuditRelationForAudit(AuditRelation auditRelation);

}
