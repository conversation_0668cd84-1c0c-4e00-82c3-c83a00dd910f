package com.ideal.script.service;

import com.github.pagehelper.PageInfo;
import com.ideal.script.model.dto.ParameterDto;
import com.ideal.script.model.entity.Parameter;
import com.ideal.script.model.entity.TaskParams;

import java.util.List;

/**
 * Service接口
 * 
 * <AUTHOR>
 */
public interface IParameterService 
{
    /**
     * 查询
     * 
     * @param id 主键
     * @return 
     */
     ParameterDto selectParameterById(Long id);

    /**
     * 查询列表
     * 
     * @param parameterDto 
     * @param pageNum 
     * @param pageSize 
     * @return 集合
     */
     PageInfo<ParameterDto> selectParameterList(ParameterDto parameterDto, int pageNum, int pageSize);

    /**
     * 新增
     *
     * @param parameterDto 
     */
     void insertParameter(ParameterDto parameterDto);

    /**
     * 修改
     *
     * @param parameterDto 
     */
     void updateParameter(ParameterDto parameterDto);

    /**
     * 批量删除
     *
     * @param ids 需要删除的主键集合
     */
     void deleteParameterByIds(Long[] ids);

    /**
     * 删除信息
     * 
     * @param id 主键
     * @return 结果
     */
     int deleteParameterById(Long id);
    
    
	/**
	 * 根据脚本id获取脚本参数
	 * @param scriptId  脚本id
	 * @return  结果
	 */
	 List<ParameterDto> selectParameterList(Long scriptId);

    /**
     * 根据版本uuid,获取脚本参数
     * @param srcScriptUuid  版本uuid
     * @return List<Parameter>
     */
    List<Parameter> getParameterByUuid(String srcScriptUuid);

    List<TaskParams> getParameterByTaskId(Long taskId);
}
