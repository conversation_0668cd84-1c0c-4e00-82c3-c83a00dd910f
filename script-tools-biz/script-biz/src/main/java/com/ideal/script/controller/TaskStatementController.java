package com.ideal.script.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.common.util.BeanUtils;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.permission.MenuPermitConstant;
import com.ideal.script.model.dto.TaskStatementDto;
import com.ideal.script.service.ITaskStatementService;
import com.ideal.system.common.component.aop.MethodPermission;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("${app.script-tools-url:}taskStatement")
@MethodPermission(MenuPermitConstant.HISTORY_TASK_STATEMENT_PER)
public class TaskStatementController {


    private final ITaskStatementService taskStatementService;

    public TaskStatementController(ITaskStatementService taskStatementService) {
        this.taskStatementService = taskStatementService;
    }

    @PostMapping("/list")
    public R<PageInfo<TaskStatementDto>> scriptStatementList(@RequestBody TableQueryDto<TaskStatementDto> tableQueryDTO)
    {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, taskStatementService.selectTaskStatementPage(BeanUtils.copy(tableQueryDTO.getQueryParam(),TaskStatementDto.class), tableQueryDTO.getPageNum(),
                tableQueryDTO.getPageSize()), Constants.LIST_SUCCESS);
    }


}
