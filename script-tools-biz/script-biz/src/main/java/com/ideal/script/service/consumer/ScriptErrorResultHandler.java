package com.ideal.script.service.consumer;

import com.ideal.message.center.ISubscriber;
import com.ideal.script.exception.AgentGatewayError;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.service.resulthandler.IScriptResultHandlerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * 监听mq中script-error-result主题中的消息
 *
 * <AUTHOR>
 */
@Component
public class ScriptErrorResultHandler implements ISubscriber {
    private final Logger logger = LoggerFactory.getLogger(ScriptErrorResultHandler.class);

    private final IScriptResultHandlerService scriptResultHandlerService;

    public ScriptErrorResultHandler(IScriptResultHandlerService scriptResultHandlerService) {
        this.scriptResultHandlerService = scriptResultHandlerService;
    }

    @Override
    public void notice(Object messageBody) {
        Optional<AgentGatewayError> exceptionData;
        try {
            if (messageBody instanceof AgentGatewayError) {
                exceptionData = Optional.of((AgentGatewayError) messageBody);
                logger.debug("script-error-result topic receive data:{}", exceptionData);
                Optional<String> bizId = Optional.of(exceptionData.get().getBizId());
                Optional<Long> agentTaskId = Optional.of(exceptionData.get().getTaskId());
                Optional<String> message = Optional.of(exceptionData.get().getMessage());
                scriptResultHandlerService.handleScriptErrorResult(message.get(), bizId.get(), agentTaskId.get());
            } else {
                throw new ScriptException("script-error-result topic receive data type error:" + messageBody.getClass().getTypeName());
            }
        } catch (Exception e) {
            logger.error("Failed to handle the result from script-error-result topic, messageBody:{},ScriptException occurred:", messageBody,e);
        }
    }
}
