package com.ideal.script.service;

import com.ideal.system.common.component.model.CurrentUser;
import com.ideal.script.model.bean.TaskExecuteBean;
import com.ideal.script.model.dto.TaskDto;
import com.github.pagehelper.PageInfo;
import com.ideal.script.model.dto.TaskQueryDto;

import java.util.List;

/**
 * 脚本任务Service接口
 * 
 * <AUTHOR>
 */
 public interface ITaskService
{
    /**
     * 查询脚本任务
     * 
     * @param id 脚本任务主键
     * @return 脚本任务
     */
     TaskDto selectTaskById(Long id);

    /**
     * 查询脚本任务信息
     *
     * @param serviceId taskId
     * @return 脚本任务
     */
     TaskDto selectTaskByServiceId(Long serviceId, Long taskId);
    /**
     * 查询脚本任务列表
     * @param pageNum 当前页
     * @param pageSize 每页条数
     * @param taskDto 脚本任务
     * @return 脚本任务集合
     */
     PageInfo<TaskDto> selectTaskList(TaskDto taskDto, int pageNum, int pageSize);

    /**
     * 新增脚本任务
     * 
     * @param taskDto 脚本任务
     * @return 结果
     */
     int insertTask(TaskDto taskDto);

    /**
     * 修改脚本任务
     * 
     * @param taskDto 脚本任务
     * @return 结果
     */
     int updateTask(TaskDto taskDto);

    /**
     * 批量删除脚本任务
     * 
     * @param ids 需要删除的脚本任务主键集合
     * @return 结果
     */
     int deleteTaskByIds(Long[] ids);

    /**
     * 删除脚本任务信息
     * 
     * @param id 脚本任务主键
     * @return 结果
     */
     int deleteTaskById(Long id);

     /**
      * 待执行任务列表
      * @param currentUser 当前用户
      * @param taskExecuteBean 执行任务对象
      * @return List<TaskExecuteBean>
      */
    List<TaskExecuteBean> selectTaskReadyToExecuteList(TaskExecuteBean taskExecuteBean, CurrentUser currentUser);

    List<TaskExecuteBean> selectTimeTaskList(TaskExecuteBean taskExecuteBean, CurrentUser currentUser);

    /**
     * 运行中任务列表
     * @param taskExecuteBean 执行任务信息
     * @param currentUser 当前用户
     * @return List<TaskExecuteBean>
     */
    List<TaskExecuteBean> selectRunningScriptTasks(TaskExecuteBean taskExecuteBean, CurrentUser currentUser);

    /**
     * 功能描述：查询当前用户可见的正在运行任务列表
     *
     * @param taskExecuteBean  执行任务信息
     * @param currentUser 当前用户
     * @return {@link List }<{@link TaskExecuteBean }>
     */
    List<TaskExecuteBean> selectCompleteScriptTasks(TaskExecuteBean taskExecuteBean, CurrentUser currentUser);

    /**
     * 根本版本uuid查询该版本运行过的历史任务
     * @param queryParam 脚本任务
     * @param pageNum 当前页
     * @param pageSize 每页条数
     * @return 脚本任务集合
     */
    PageInfo<TaskDto>  listTaskHis(TaskQueryDto queryParam, Integer pageNum, Integer pageSize);
}
