package com.ideal.script.controller;

import com.ideal.common.dto.R;
import com.ideal.common.util.BeanUtils;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.permission.MenuPermitConstant;
import com.ideal.script.model.dto.TaskParamsDto;
import com.ideal.script.model.entity.Parameter;
import com.ideal.script.model.entity.TaskParams;
import com.ideal.script.service.IParameterService;
import com.ideal.system.common.component.aop.MethodPermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 我的脚本
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("${app.script-tools-url:}/parameter")
@MethodPermission(MenuPermitConstant.EXECUTION_TASK_PER)
public class ParameterController {
    private static final Logger logger = LoggerFactory.getLogger(ParameterController.class);
    private final IParameterService parameterService;


    public ParameterController(IParameterService parameterService) {
        this.parameterService = parameterService;
    }


    /**
     * 根据版本uuid查询参数
     */
    @GetMapping("/getParameterByUuid")
    public R<Object> getParameterByUuid(@RequestParam(value = "versionUuid") String versionUuid) {
        try {
            List<Parameter> parameterList = parameterService.getParameterByUuid(versionUuid);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, parameterList, Constants.LIST_SUCCESS);
        } catch (Exception e) {
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, e.getMessage());
        }

    }


    /**
     * 根据版本uuid查询参数
     */
    @GetMapping("/getParameterByTaskId")
    public R<Object> getParameterByTaskId(@RequestParam(value = "taskId") Long taskId) {
        try {
            List<TaskParams> parameterList = parameterService.getParameterByTaskId(taskId);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, BeanUtils.copy(parameterList, TaskParamsDto.class), Constants.LIST_SUCCESS);
        } catch (Exception e) {
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, e.getMessage());
        }

    }
}
