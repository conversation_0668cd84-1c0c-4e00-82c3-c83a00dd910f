package com.ideal.script.service.impl;

import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import org.springframework.stereotype.Service;
import com.ideal.script.mapper.ToProductRelationMapper;
import com.ideal.script.model.entity.ToProductRelationEntity;
import com.ideal.script.service.IToProductRelationService;
import com.ideal.script.model.dto.ToProductRelationDto;
import com.ideal.script.model.dto.ToProductRelationQueryDto;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;

import java.util.List;

/**
 * 投产记录关系Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class ToProductRelationServiceImpl implements IToProductRelationService {
    private final  ToProductRelationMapper toProductRelationMapper;

    public ToProductRelationServiceImpl(ToProductRelationMapper toProductRelationMapper) {
        this.toProductRelationMapper = toProductRelationMapper;
    }

    /**
     * 查询投产记录关系
     *
     * @param id 投产记录关系主键
     * @return 投产记录关系
     */
    @Override
    public ToProductRelationDto selectToProductRelationById(Long id) {
        ToProductRelationEntity toProductRelation = toProductRelationMapper.selectToProductRelationById(id);
        return BeanUtils.copy(toProductRelation, ToProductRelationDto.class);
    }

    /**
     * 查询投产记录关系列表
     *
     * @param toProductRelationQueryDto 投产记录关系
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 投产记录关系
     */
    @Override
    public PageInfo<ToProductRelationDto> selectToProductRelationList(ToProductRelationQueryDto toProductRelationQueryDto, Integer pageNum, Integer pageSize) {
        ToProductRelationEntity query = BeanUtils.copy(toProductRelationQueryDto, ToProductRelationEntity.class);
        PageMethod.startPage(pageNum, pageSize);
        List<ToProductRelationEntity> toProductRelationList = toProductRelationMapper.selectToProductRelationList(query);
        return PageDataUtil.toDtoPage(toProductRelationList, ToProductRelationDto.class);
    }

    /**
     * 新增投产记录关系
     *
     * @param toProductRelationDto 投产记录关系
     * @return 结果
     */
    @Override
    public int insertToProductRelation(ToProductRelationDto toProductRelationDto) {
        ToProductRelationEntity toProductRelation = BeanUtils.copy(toProductRelationDto, ToProductRelationEntity.class);
        return toProductRelationMapper.insertToProductRelation(toProductRelation);
    }

    /**
     * 修改投产记录关系
     *
     * @param toProductRelationDto 投产记录关系
     * @return 结果
     */
    @Override
    public int updateToProductRelation(ToProductRelationDto toProductRelationDto) {
        ToProductRelationEntity toProductRelation = BeanUtils.copy(toProductRelationDto, ToProductRelationEntity.class);
        return toProductRelationMapper.updateToProductRelation(toProductRelation);
    }

    /**
     * 批量删除投产记录关系
     *
     * @param ids 需要删除的投产记录关系主键
     * @return 结果
     */
    @Override
    public int deleteToProductRelationByIds(Long[] ids) {
        return toProductRelationMapper.deleteToProductRelationByIds(ids);
    }
}
