package com.ideal.script.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.script.mapper.FunctionpublishMapper;
import com.ideal.script.model.dto.FunctionpublishDto;
import com.ideal.script.model.dto.VarAndFuncForEditDto;
import com.ideal.script.model.entity.Functionpublish;
import com.ideal.script.model.entity.VarAndFuncForEdit;
import com.ideal.script.service.IFunctionpublishService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 函数库基础发布Service业务层处理
 *
 * <AUTHOR>
 */
@Transactional(rollbackFor = Exception.class)
@Service
public class FunctionpublishServiceImpl implements IFunctionpublishService {

    private final FunctionpublishMapper functionpublishMapper;

    public FunctionpublishServiceImpl(FunctionpublishMapper functionpublishMapper) {
        this.functionpublishMapper = functionpublishMapper;
    }

    /**
     * 查询函数库基础发布
     *
     * @param id 函数库基础发布主键
     * @return 函数库基础发布
     */
    @Override
    public FunctionpublishDto selectFunctionpublishById(Long id) {
        Functionpublish functionpublish = functionpublishMapper.selectFunctionpublishById(id);
        return BeanUtils.copy(functionpublish, FunctionpublishDto.class);
    }

    /**
     * 查询函数库基础发布列表
     *
     * @param functionpublishDto 函数库基础发布
     * @return 函数库基础发布
     */
    @Override
    public PageInfo<FunctionpublishDto> selectFunctionpublishList(FunctionpublishDto functionpublishDto, int pageNum, int pageSize) {
        PageMethod.startPage(pageNum, pageSize);
        List<Functionpublish> functionpublishList = new ArrayList<>();
        if (null != functionpublishDto) {
            Functionpublish functionpublish = BeanUtils.copy(functionpublishDto, Functionpublish.class);
            functionpublishList = functionpublishMapper.selectFunctionpublishList(functionpublish);
        }
        return PageDataUtil.toDtoPage(functionpublishList, FunctionpublishDto.class);
    }

    /**
     * 新增函数库基础发布
     *
     * @param functionpublishDto 函数库基础发布
     */
    @Override
    public void insertFunctionpublish(FunctionpublishDto functionpublishDto) {
        Functionpublish functionpublish = BeanUtils.copy(functionpublishDto, Functionpublish.class);
        functionpublishMapper.insertFunctionpublish(functionpublish);
    }

    /**
     * 修改函数库基础发布
     *
     * @param functionpublishDto 函数库基础发布
     */
    @Override
    public void updateFunctionpublish(FunctionpublishDto functionpublishDto) {
        Functionpublish functionpublish = BeanUtils.copy(functionpublishDto, Functionpublish.class);
        functionpublishMapper.updateFunctionpublish(functionpublish);
    }

    /**
     * 批量删除函数库基础发布
     *
     * @param ids 需要删除的函数库基础发布主键
     */
    @Override
    public void deleteFunctionpublishByIds(Long[] ids) {
        functionpublishMapper.deleteFunctionpublishByIds(ids);
    }

    /**
     * 删除函数库基础发布信息
     *
     * @param id 函数库基础发布主键
     * @return 结果
     */
    @Override
    public int deleteFunctionpublishById(Long id) {
        return functionpublishMapper.deleteFunctionpublishById(id);
    }

    /**
     * 查询函数库基础发布列表
     *
     * @param varAndFuncForEditDto 函数库基础发布
     * @return 函数库基础发布
     */
    @Override
    public PageInfo<FunctionpublishDto> selectFunctionpublishListForScriptEdit(VarAndFuncForEditDto varAndFuncForEditDto, int pageNum, int pageSize) {
        PageMethod.startPage(pageNum, pageSize);
        List<Functionpublish> functionpublishList = new ArrayList<>();
        if (null != varAndFuncForEditDto) {
            VarAndFuncForEdit varAndFuncForEdit = BeanUtils.copy(varAndFuncForEditDto, VarAndFuncForEdit.class);
            functionpublishList = functionpublishMapper.selectFunctionpublishListForScriptEdit(varAndFuncForEdit);
        }
        return PageDataUtil.toDtoPage(functionpublishList, FunctionpublishDto.class);
    }
}
