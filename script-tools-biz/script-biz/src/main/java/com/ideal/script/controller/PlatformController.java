package com.ideal.script.controller;

import com.ideal.common.dto.R;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.permission.MenuPermitConstant;
import com.ideal.script.model.dto.PlatformDto;
import com.ideal.script.service.IPlatformService;
import com.ideal.system.common.component.aop.MethodPermission;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("${app.script-tools-url:}/platform")
@MethodPermission(MenuPermitConstant.MY_SCRIPT_OR_TASK_APPLY_OR_TASK_APPLY_DUTY_PER)
public class PlatformController {

    private final IPlatformService platformService;

    public PlatformController(IPlatformService platformService) {
        this.platformService = platformService;
    }


    @PostMapping("/platformList")
    public R<List<PlatformDto>> getScriptPlatformCode() {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, platformService.getScriptPlatformCode(), Constants.LIST_SUCCESS);
    }

}
