package com.ideal.script.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.sc.util.ValidationUtils;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.permission.KeyCommandsBtnPermitConstant;
import com.ideal.script.common.constant.permission.MenuPermitConstant;
import com.ideal.script.common.validation.Create;
import com.ideal.script.common.validation.Update;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.DangerCmdDto;
import com.ideal.script.service.IDangerCmdService;
import com.ideal.system.common.component.aop.MethodPermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ideal.audit.producer.annotation.Auditable;

/**
 * 关键命令(高危命令)Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("${app.script-tools-url:}/cmd")
@MethodPermission(MenuPermitConstant.KEY_COMMANDS_PER)
public class DangerCmdController {
    private static final Logger logger = LoggerFactory.getLogger(DangerCmdController.class);
    private final IDangerCmdService dangerCmdService;


    public DangerCmdController(IDangerCmdService dangerCmdService) {
        this.dangerCmdService = dangerCmdService;
    }

    /**
     * 查询关键命令(高危命令)列表
     */
    @PostMapping("/listDangerCmd")
    public R<PageInfo<DangerCmdDto>> listDangerCmd(@RequestBody TableQueryDto<DangerCmdDto> tableQueryDTO) {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, dangerCmdService.selectDangerCmdList(tableQueryDTO.getQueryParam(), tableQueryDTO.getPageNum(),
                tableQueryDTO.getPageSize()), Constants.LIST_SUCCESS);
    }


    /**
     * 新增关键命令(高危命令)
     */
    @PostMapping("/saveDangerCmd")
    @Auditable("关键命令|新增")
    @MethodPermission(KeyCommandsBtnPermitConstant.SAVE_DANGER_CMD_PER)
    public R<Object> saveDangerCmd(@RequestBody @Validated(Create.class) DangerCmdDto dangerCmdDto) {
        try {
            dangerCmdService.insertDangerCmd(dangerCmdDto);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "", "save.success");
        } catch (ScriptException e) {
            logger.error("saveDangerCmd error", e);
            return ValidationUtils.customFailResult("scriptCmd", e.getMessage());
        }
    }

    /**
     * 修改关键命令(高危命令)
     */
    @PostMapping("/updateDangerCmd")
    @Auditable("关键命令|编辑")
    @MethodPermission(KeyCommandsBtnPermitConstant.UPDATE_DANGER_CMD_PER)
    public R<Object> updateDangerCmd(@RequestBody @Validated(Update.class) DangerCmdDto dangerCmdDto) {
        try {
            dangerCmdService.updateDangerCmd(dangerCmdDto);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "", "update.success");
        } catch (Exception e) {
            logger.error("updateDangerCmd error", e);
            return ValidationUtils.customFailResult("scriptCmd", e.getMessage());
        }
    }

    /**
     * 删除关键命令(高危命令)
     */
    @GetMapping("/removelDangerCmd")
    @Auditable("关键命令|删除")
    @MethodPermission(KeyCommandsBtnPermitConstant.REMOVEL_DANGER_CMD_PER)
    public R<Object> removeDangerCmd(@RequestParam(value = "ids") Long[] ids) {
        dangerCmdService.deleteDangerCmdByIds(ids);
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "remove.success");
    }

}
