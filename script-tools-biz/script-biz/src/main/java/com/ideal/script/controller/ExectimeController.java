package com.ideal.script.controller;

import com.ideal.common.dto.R;
import com.ideal.script.common.constant.permission.MenuPermitConstant;
import com.ideal.script.model.dto.ExectimeDto;
import com.ideal.script.service.IExectimeService;
import com.ideal.system.common.component.aop.MethodPermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 脚本执行次数统计Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("${app.script-tools-url:}/exectime")
@MethodPermission(MenuPermitConstant.TASK_APPLY_PER)
public class ExectimeController {
    private static final Logger logger = LoggerFactory.getLogger(ExectimeController.class);
    private final IExectimeService exectimeService;


    public ExectimeController(IExectimeService exectimeService) {
        this.exectimeService = exectimeService;
    }

    /**
     * 查询脚本使用次数、成功数、总数、成功率
     *
     * @param srcScriptUuid 脚本版本Id
     * @return R<ExectimeDto>
     */
    @GetMapping("/getTotalAndSuccessRate")
    public R<ExectimeDto> getTotalAndSuccessRate(@RequestParam(value = "srcScriptUuid") String srcScriptUuid) {
        ExectimeDto exectimeDto;
        try {
            exectimeDto = exectimeService.getTotalAndSuccessRate(srcScriptUuid);
            return R.ok(exectimeDto);
        } catch (Exception e) {
            return R.fail();
        }
    }
}
