package com.ideal.script.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.agent.gateway.api.AgentOperateApi;
import com.ideal.agent.gateway.model.AgentOperateDto;
import com.ideal.agent.gateway.model.AgentOptionDto;
import com.ideal.agent.gateway.model.AgentSyncOperateResultDto;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.common.util.BatchDataUtil;
import com.ideal.script.dto.AttachmentDto;
import com.ideal.script.dto.ScriptInfoDto;
import com.ideal.script.dto.ScriptVersionDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.IssuerecordMapper;
import com.ideal.script.model.bean.IssuerecordBean;
import com.ideal.script.model.dto.AgentInfoDto;
import com.ideal.script.model.dto.InfoVersionTextDto;
import com.ideal.script.model.dto.IssuerecordDto;
import com.ideal.script.model.dto.IssuerecordQueryDto;
import com.ideal.script.model.entity.IssuerecordEntity;
import com.ideal.script.service.IAttachmentService;
import com.ideal.script.service.IInfoService;
import com.ideal.script.service.IInfoVersionService;
import com.ideal.script.service.IInfoVersionTextService;
import com.ideal.script.service.IIssuerecordService;
import com.ideal.script.service.ITaskExecuteService;
import com.ideal.system.common.component.model.CurrentUser;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 脚本下发的Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class IssuerecordServiceImpl implements IIssuerecordService {
    private static final Logger logger = LoggerFactory.getLogger(IssuerecordServiceImpl.class);

    private final IssuerecordMapper issuerecordMapper;

    private final SqlSessionFactory factory;

    private final BatchDataUtil batchDataUtil;
    private final IInfoVersionService infoVersionService;
    private final IInfoService infoService;
    private final IInfoVersionTextService infoVersionTextService;
    private final AgentOperateApi agentOperateApi;
    private final IAttachmentService attachmentService;
    private final ITaskExecuteService taskExecuteService;

    @Autowired
    public IssuerecordServiceImpl(IssuerecordMapper issuerecordMapper, SqlSessionFactory factory, BatchDataUtil batchDataUtil, IInfoVersionService infoVersionService, IInfoService infoService, IInfoVersionTextService infoVersionTextService, AgentOperateApi agentOperateApi, IAttachmentService attachmentService, ITaskExecuteService taskExecuteService) {
        this.issuerecordMapper = issuerecordMapper;
        this.factory = factory;
        this.batchDataUtil = batchDataUtil;
        this.infoVersionService = infoVersionService;
        this.infoService = infoService;
        this.infoVersionTextService = infoVersionTextService;
        this.agentOperateApi = agentOperateApi;
        this.attachmentService = attachmentService;
        this.taskExecuteService = taskExecuteService;
    }

    /**
     * 查询脚本下发的
     *
     * @param id 脚本下发的主键
     * @return 脚本下发的
     */
    @Override
    public IssuerecordDto selectIssuerecordById(Long id) {
        IssuerecordEntity issuerecord = issuerecordMapper.selectIssuerecordById(id);
        return BeanUtils.copy(issuerecord, IssuerecordDto.class);
    }

    /**
     * 查询脚本下发的列表
     *
     * @param issuerecordQueryDto 脚本下发的
     * @param pageNum             页码
     * @param pageSize            单页长度
     * @return 脚本下发的
     */
    @Override
    public PageInfo<IssuerecordDto> selectIssuerecordList(IssuerecordQueryDto issuerecordQueryDto, Integer pageNum, Integer pageSize) {

        IssuerecordBean query = BeanUtils.copy(issuerecordQueryDto, IssuerecordBean.class);
        query.setSendTimeStart(issuerecordQueryDto.getSendTime());
        Timestamp sendTime = issuerecordQueryDto.getSendTime();
        if(sendTime != null){
            // 将 Timestamp 转换为 LocalDateTime
            LocalDateTime localDateTime = sendTime.toLocalDateTime();
            // 增加一天
            LocalDateTime nextDay = localDateTime.plusDays(1);
            // 将 LocalDateTime 再转换回 Timestamp
            Timestamp sendTimeEnd = Timestamp.valueOf(nextDay);
            query.setSendTimeEnd(sendTimeEnd);
        }
        PageMethod.startPage(pageNum, pageSize);
        List<IssuerecordEntity> issuerecordList = issuerecordMapper.selectIssuerecordList(query);
        return PageDataUtil.toDtoPage(issuerecordList, IssuerecordDto.class);
    }

    /**
     * 新增脚本下发的
     *
     * @param issuerecordDto 脚本下发的
     * @return 结果
     */
    @Override
    public int insertIssuerecord(IssuerecordDto issuerecordDto) {
        IssuerecordEntity issuerecord = BeanUtils.copy(issuerecordDto, IssuerecordEntity.class);
        return issuerecordMapper.insertIssuerecord(issuerecord);
    }

    /**
     * 修改脚本下发的
     *
     * @param issuerecordDto 脚本下发
     * @return 结果
     */
    @Override
    public int updateIssuerecord(IssuerecordDto issuerecordDto) {
        IssuerecordEntity issuerecord = BeanUtils.copy(issuerecordDto, IssuerecordEntity.class);
        return issuerecordMapper.updateIssuerecord(issuerecord);
    }

    /**
     * 根据batchNumber更新脚本下发状态
     *
     * @param issuerecordDto 脚本下发
     * @return int 结果
     */
    @Override
    public int updateIssuerecordByBatchNumber(IssuerecordDto issuerecordDto) {
        IssuerecordEntity issuerecord = BeanUtils.copy(issuerecordDto, IssuerecordEntity.class);
        return issuerecordMapper.updateIssuerecordByBatchNumber(issuerecord);
    }

    /**
     * 批量删除脚本下发的
     *
     * @param ids 需要删除的脚本下发的主键
     * @return 结果
     */
    @Override
    public int deleteIssuerecordByIds(Long[] ids) {
        return issuerecordMapper.deleteIssuerecordByIds(ids);
    }

    /**
     * 脚本下发
     *
     * @param issuerecordQueryDto 脚本下发Dto，包含脚本集合、目标agent集合、下发路径、权限参数、所属用户、所属组
     * @param currentUser         用户
     * @return {@link String }
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String sendScriptToAgents(IssuerecordQueryDto issuerecordQueryDto, CurrentUser currentUser) throws ScriptException {
        // 下发记录存储
        // 获取当前时间的时间戳（毫秒级）
        long timestamp = System.currentTimeMillis();
        // 将时间戳转换为字符串作为批次号
        String batchNumber = String.valueOf(timestamp);
        saveIssuerecord(issuerecordQueryDto, currentUser, batchNumber);
        // 调用管理服务下发脚本
        List<AgentInfoDto> agentInfoDtoList = issuerecordQueryDto.getAgentInfoDtoList();
        String[] srcScriptUuids = issuerecordQueryDto.getSrcScriptUuids();
        List<AgentOperateDto> listAgentOperateDto = new ArrayList<>();
        for (AgentInfoDto agentInfoDto : agentInfoDtoList) {
            String groupPermission = issuerecordQueryDto.getGroupPermission();
            List<Object> params = new ArrayList<>();
            params.add(issuerecordQueryDto.getSendPath());
            // 所属用户
            params.add(issuerecordQueryDto.getUserPermission());
            if (String.valueOf(agentInfoDto.getOsName()).contains("AIX")
                    && "".equals(groupPermission)) {
                groupPermission = "system";
            }
            // 所属组
            params.add(groupPermission);
            // 权限 例：755
            params.add(issuerecordQueryDto.getChmod());
            List<Map<String,Object>> arl = new ArrayList<>();
            if (ArrayUtils.isEmpty(srcScriptUuids)) {
                params.add(null);
            }
            if(agentInfoDto.getOsName() != null){
                params.add(agentInfoDto.getOsName());
            }else{
                params.add("Linux");
            }
            buildParams(srcScriptUuids, params, arl);
            // 将参数列表转换为JSON字符串
            String jsonStr = taskExecuteService.convertObjectToJsonString(params);
            // 创建Agent操作数据传输对象
            AgentOperateDto agentOperateDto = new AgentOperateDto();
            // 设置业务ID

            // 设置操作内容为转换后的JSON字符串
            agentOperateDto.setContent(jsonStr);
            agentOperateDto.setBizId("ScriptSendScriptToAgents-" + batchNumber);
            // 设置RPC方法
            agentOperateDto.setRpcMethod("IEAIAgent.sendScriptToAgents");
            // 设置内容格式化器为空
            agentOperateDto.setContentFormatter("scriptTaskContentFormatter");
            // 不需要二次握手（agent端不需要回调provider端或者server端），此处setDynamicResourcesDTO为空
            agentOperateDto.setDynamicResourcesDTO(null);
            // 权重 默认1
            agentOperateDto.setWeights(1L);
            // 优先级 默认5
            agentOperateDto.setPriority("5");
            AgentOptionDto agentOptionDto = new AgentOptionDto();
            agentOptionDto.setAgentHost(agentInfoDto.getAgentIp());
            agentOptionDto.setAgentPort(agentInfoDto.getAgentPort().longValue());
            agentOperateDto.setAgentOptionDto(agentOptionDto);
            listAgentOperateDto.add(agentOperateDto);
        }
        // 异步
        for (AgentOperateDto agentOperateDto : listAgentOperateDto) {
            IssuerecordDto issuerecordDto = new IssuerecordDto();
            // 选择了多个脚本下发，每个agent会合并下发一次，只需要根据ip、端口号、批次更新即可
            issuerecordDto.setBatchNumber(batchNumber);
            issuerecordDto.setAgentIp(agentOperateDto.getAgentOptionDto().getAgentHost());
            issuerecordDto.setAgentPort(agentOperateDto.getAgentOptionDto().getAgentPort().intValue());
            issuerecordDto.setStatus(Enums.IssuerecordState.EXCEPTION.getValue());
            try {
                AgentSyncOperateResultDto resultDto = agentOperateApi.sendSync(agentOperateDto);
                if (null != resultDto && (boolean) resultDto.getContent()) {
                    logger.info("sendScriptToAgents result,bizId:{},content:{}", resultDto.getBizId(), resultDto.getContent());
                    issuerecordDto.setStatus(Enums.IssuerecordState.SUCCESS.getValue());
                }else{
                    throw new ScriptException("script.send.agent.path.fail");
                }
            } catch (Exception e) {
                logger.error("agentOperateApi send error:", e);
                throw new ScriptException("script.send.agent.path.fail");
            } finally {
                updateIssuerecordByBatchNumber(issuerecordDto);
            }
        }
        return batchNumber;
    }

    /**
     * 构建脚本下发远程执行参数
     *
     * @param srcScriptUuids 版本uuid
     * @param params params
     * @param list  list
     */
    private void buildParams(String[] srcScriptUuids, List<Object> params, List<Map<String, Object>> list) {
        if (ArrayUtils.isNotEmpty(srcScriptUuids)) {

            for (String srcScriptUuid : srcScriptUuids) {
                ScriptVersionDto infoVersionDto = infoVersionService.selectInfoVersionBySrcScriptUuid(srcScriptUuid);
                InfoVersionTextDto infoVersionTextDto = infoVersionTextService.selectInfoVersionTextByScriptUuid(srcScriptUuid);
                ScriptInfoDto infoDto = infoService.selectInfoByUniqueUuid(infoVersionDto.getInfoUniqueUuid());
                if(!"bat".equals(infoDto.getScriptType()) && !"ps1".equals(infoDto.getScriptType())){
                    String contentReplaceStr = infoVersionTextDto.getContent().replaceAll("\r", "\n");
                    infoVersionTextDto.setContent(contentReplaceStr.replaceAll("\n\n","\n"));
                }
                String scriptType = infoVersionService.getScriptTypeBySrcScriptUuid(srcScriptUuid);
                Map<String,Object> scriptHashtable = new HashMap<>(10);
                scriptHashtable.put("command", infoVersionTextDto.getContent());
                scriptHashtable.put("scriptItimeout", -1 + "");
                scriptHashtable.put("iid", infoVersionDto.getId() + "");
                scriptHashtable.put("serviceName", infoDto.getScriptName());
                scriptHashtable.put("servicesType", scriptType);
                scriptHashtable.put("scriptWorkDir", "");

                buildAttachementParam(srcScriptUuid, scriptHashtable);
                list.add(scriptHashtable);
            }
            // 把所有脚本内容加到参数vector中
            params.add(list);
        }
    }

    /**
     * 构建附件信息
     *
     * @param srcScriptUuid 版本uuid
     * @param scriptHashtable scriptHashtable
     * <AUTHOR>
     */
    private void buildAttachementParam(String srcScriptUuid, Map<String, Object> scriptHashtable) {
        List<AttachmentDto> attachmentDtoList = attachmentService.getAttachmentByUuid(srcScriptUuid);
        if (attachmentDtoList != null && !attachmentDtoList.isEmpty()) {
            Object[] obj = new Object[attachmentDtoList.size() * 2];
            int n = 0;
            for (AttachmentDto attachment : attachmentDtoList) {
                if (attachment.getContents() != null) {
                    obj[n] = attachment.getName();
                    n++;
                    byte[] result = attachment.getContents();
                    obj[n] = result;
                    n++;
                }
            }
            scriptHashtable.put("atta", obj);
        } else {
            scriptHashtable.put("atta", new Object[]{});
        }
    }

    /**
     * 存储下发记录数据
     *
     * @param issuerecordQueryDto 下发记录Dto
     * @param currentUser 用户
     * @param batchNumber 批次号
     */
    private void saveIssuerecord(IssuerecordQueryDto issuerecordQueryDto, CurrentUser currentUser, String batchNumber) throws ScriptException {
        List<AgentInfoDto> agentInfoDtoList = issuerecordQueryDto.getAgentInfoDtoList();

        String[] srcScriptUuids = issuerecordQueryDto.getSrcScriptUuids();

        List<IssuerecordDto> issuerecordDtoList = new ArrayList<>();
        if (null == agentInfoDtoList || null == srcScriptUuids) {
            return;
        }
        if (agentInfoDtoList.isEmpty() || srcScriptUuids.length == 0) {
            return;
        }
        for (AgentInfoDto agentInfoDto : agentInfoDtoList) {
            for (String srcScriptUuid : srcScriptUuids) {
                IssuerecordDto issuerecordDto = new IssuerecordDto();
                // 设置 srcScriptUuid、ip、端口 等字段到 issuerecordDto 对象中
                issuerecordDto.setSrcScriptUuid(srcScriptUuid);
                issuerecordDto.setAgentIp(agentInfoDto.getAgentIp());
                issuerecordDto.setAgentPort(agentInfoDto.getAgentPort());
                issuerecordDto.setChmod(issuerecordQueryDto.getChmod());
                issuerecordDto.setGroupPermission(issuerecordQueryDto.getGroupPermission());
                issuerecordDto.setUserPermission(issuerecordQueryDto.getUserPermission());
                issuerecordDto.setSendPath(issuerecordQueryDto.getSendPath());
                issuerecordDto.setSendUserId(currentUser.getId());
                issuerecordDto.setSendUserName(currentUser.getLoginName());
                issuerecordDto.setStatus(Enums.IssuerecordState.RUNNING.getValue());
                issuerecordDto.setBatchNumber(batchNumber);
                String bizId = "agent-script-issuerecord-" + batchNumber + "-" + agentInfoDto.getSysmAgentInfoId();
                issuerecordDto.setBizId(bizId);
                // 将 issuerecordDto 添加到列表中
                issuerecordDtoList.add(issuerecordDto);
            }
        }


        try (SqlSession sqlSession = factory.openSession(ExecutorType.BATCH, false)) {
            try {
                batchDataUtil.batchData(IssuerecordMapper.class, BeanUtils.copy(issuerecordDtoList, IssuerecordEntity.class), IssuerecordEntity.class, "insertIssuerecord", sqlSession, sqlSession.getMapper(IssuerecordMapper.class));

            } catch (ScriptException e) {
                logger.error("save issue record error:", e);
                throw new ScriptException("save.issue.record.storage.error");
            }
        }
    }
}
