package com.ideal.script.service.impl;

import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.mapper.TaskRuntimeStdoutMapper;
import com.ideal.script.model.bean.TaskHandleParam;
import com.ideal.script.model.dto.TaskRuntimeDto;
import com.ideal.script.model.entity.TaskRuntimeStdout;
import com.ideal.script.service.ITaskRuntimeStdoutService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 脚本服务化任务标准输出处理
 * <AUTHOR>
 **/
@Service
public class TaskRuntimeStdoutServiceImpl implements ITaskRuntimeStdoutService {

    private final TaskRuntimeStdoutMapper taskRuntimeStdoutMapper;

    public TaskRuntimeStdoutServiceImpl(TaskRuntimeStdoutMapper taskRuntimeStdoutMapper) {
        this.taskRuntimeStdoutMapper = taskRuntimeStdoutMapper;
    }
    /**
     * 保存标准输出
     * @param taskHandleParam 任务处理参数对象
     * @param bizId 业务id
     * @param taskRuntimeDto agent运行实例对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveRuntimeStdoutStdError(TaskHandleParam taskHandleParam, String bizId, TaskRuntimeDto taskRuntimeDto){
        //执行更新
        if(bizId.contains(Enums.AgentExecRunFlag.RETRY.getValue())){
            //更新标准输出
            TaskRuntimeStdout taskRuntimeStdout = new TaskRuntimeStdout();
            taskRuntimeStdout.setIstdout(taskHandleParam.getIstdout());
            taskRuntimeStdout.setIstderror(taskHandleParam.getIstderror());
            taskRuntimeStdout.setIruntimeId(taskRuntimeDto.getId());
            taskRuntimeStdout.setItaskInstanceId(taskRuntimeDto.getTaskInstanceId());
            taskRuntimeStdoutMapper.updateByRunTimeId(taskRuntimeStdout);
        }else{
            //保存标准输出
            TaskRuntimeStdout taskRuntimeStdout = new TaskRuntimeStdout();
            taskRuntimeStdout.setIstdout(taskHandleParam.getIstdout());
            taskRuntimeStdout.setIstderror(taskHandleParam.getIstderror());
            taskRuntimeStdout.setIruntimeId(taskRuntimeDto.getId());
            taskRuntimeStdout.setItaskInstanceId(taskRuntimeDto.getTaskInstanceId());
            taskRuntimeStdoutMapper.insert(taskRuntimeStdout);
        }
    }
}
