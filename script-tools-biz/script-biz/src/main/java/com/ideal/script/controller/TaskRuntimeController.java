package com.ideal.script.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.permission.MenuPermitConstant;
import com.ideal.script.model.dto.TaskRuntimeDto;
import com.ideal.script.service.ITaskRuntimeService;
import com.ideal.system.common.component.aop.MethodPermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * agent运行实例Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("${app.script-tools-url:}/runtime")
public class TaskRuntimeController {
    private final ITaskRuntimeService taskRuntimeService;
    private static final Logger logger = LoggerFactory.getLogger(TaskRuntimeController.class);


    public TaskRuntimeController(ITaskRuntimeService taskRuntimeService) {
        this.taskRuntimeService = taskRuntimeService;
    }

    /**
     * 查询agent运行实例列表
     * @tags 对外API接口
     */
    @PostMapping("/listTaskRuntime")
    @MethodPermission(MenuPermitConstant.EXECUTION_TASK_OR_SCHEDULED_TASK_OR_MY_SCRIPT_OR_TASK_APPLY_OR_TEMPLATE_TASK_PER)
    public R<PageInfo<TaskRuntimeDto>> listTaskRuntime(@RequestBody TableQueryDto<TaskRuntimeDto> tableQueryDTO) {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, taskRuntimeService.selectTaskRuntimeList(tableQueryDTO.getQueryParam(), tableQueryDTO.getPageNum(),
                tableQueryDTO.getPageSize()), Constants.LIST_SUCCESS);
    }

    /**
     * 获取agent运行结果
     * @tags 对外API接口
     * @param id agent运行实例主键
     * @return {@link R}<{@link Object}> agent输出结果
     */
    @GetMapping("/getOutPutMessage")
    @MethodPermission(MenuPermitConstant.EXECUTION_TASK_OR_SCHEDULED_TASK_OR_MY_SCRIPT_PER)
    public R<Object> getOutPutMessage(@RequestParam(value = "id") Long id) {
        String outPut = "";
        try {
              outPut = taskRuntimeService.getOutPutMessage(id);
        } catch (Exception e) {
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "", "get.out.put.message.fail");
        }
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, outPut,"get.out.put.message.success");
    }

}
