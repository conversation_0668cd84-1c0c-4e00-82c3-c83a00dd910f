package com.ideal.script.service;

import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.ScriptExecAuditDto;
import com.ideal.script.model.dto.TaskDto;
import com.ideal.script.model.dto.TaskIpsDto;
import com.github.pagehelper.PageInfo;
import org.apache.ibatis.session.SqlSession;

/**
 * 任务与agent关系Service接口
 * 
 * <AUTHOR>
 */
 public interface ITaskIpsService
{
    /**
     * 查询任务与agent关系
     * 
     * @param id 任务与agent关系主键
     * @return 任务与agent关系
     */
     TaskIpsDto selectTaskIpsById(Long id);

    /**
     * 查询任务与agent关系列表
     * @param pageNum 当前页
     * @param pageSize 每页条数
     * @param taskIpsDto 任务与agent关系
     * @return 任务与agent关系集合
     */
     PageInfo<TaskIpsDto> selectTaskIpsList(TaskIpsDto taskIpsDto, int pageNum, int pageSize);

    /**
     * 新增任务与agent关系
     * 
     * @param taskIpsDto 任务与agent关系
     * @return 结果
     */
     int insertTaskIps(TaskIpsDto taskIpsDto);

    /**
     * 修改任务与agent关系
     * 
     * @param taskIpsDto 任务与agent关系
     * @return 结果
     */
     int updateTaskIps(TaskIpsDto taskIpsDto);

    /**
     * 批量删除任务与agent关系
     * 
     * @param ids 需要删除的任务与agent关系主键集合
     * @return 结果
     */
     int deleteTaskIpsByIds(Long[] ids);

    /**
     * 删除任务与agent关系信息
     * 
     * @param id 任务与agent关系主键
     * @return 结果
     */
     int deleteTaskIpsById(Long id);

    /**
     * 删除任务与agent关系信息
     *
     * @param taskId 任务id
     * @return 结果
     */
    int deleteTaskIpsByTaskId(Long taskId);

    /**
     * 存储agent信息
     *
     * @param scriptExecAuditDto 任务申请提交审核Dto
     * @param taskInfo           任务申请提交任务时的脚本任务对象
     * @param sqlSession         ​SqlSession​ 对象
     * @throws ScriptException 抛出自定义异常
     */
     void saveTaskIps(ScriptExecAuditDto scriptExecAuditDto, TaskDto taskInfo, SqlSession sqlSession) throws ScriptException;
}
