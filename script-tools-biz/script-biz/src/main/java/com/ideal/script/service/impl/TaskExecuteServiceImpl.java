package com.ideal.script.service.impl;

import cn.idev.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.agent.gateway.api.AgentOperateApi;
import com.ideal.agent.gateway.model.AgentOperateDto;
import com.ideal.agent.gateway.model.AgentOptionDto;
import com.ideal.agent.gateway.model.AgentSyncOperateResultDto;
import com.ideal.agent.management.api.AgentManagementApi;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.EncryptUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.common.util.batch.Batch;
import com.ideal.jobapi.core.apiclient.IdealXxlJobApiUtil;
import com.ideal.sc.util.Base64;
import com.ideal.sc.util.CurrentUserUtil;
import com.ideal.sc.util.ExcelUtil;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.enums.AgentExecuteKey;
import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.common.util.BatchDataUtil;
import com.ideal.script.common.util.CronDateUtils;
import com.ideal.script.common.util.DesUtils;
import com.ideal.script.common.util.TransactionSyncUtil;
import com.ideal.script.config.ScriptBusinessConfig;
import com.ideal.script.dto.AttachmentDto;
import com.ideal.script.dto.RetryScriptInstanceApiDto;
import com.ideal.script.dto.ScriptInfoDto;
import com.ideal.script.dto.ScriptVersionDto;
import com.ideal.script.dto.StopScriptTasksApiDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.AttachmentMapper;
import com.ideal.script.mapper.InfoVersionTextMapper;
import com.ideal.script.mapper.TaskAttachmentMapper;
import com.ideal.script.mapper.TaskInstanceMapper;
import com.ideal.script.mapper.TaskIpsMapper;
import com.ideal.script.mapper.TaskMapper;
import com.ideal.script.mapper.TaskParamsMapper;
import com.ideal.script.mapper.TaskRuntimeMapper;
import com.ideal.script.model.bean.ShellCmdOutput;
import com.ideal.script.model.bean.StartAgentCommonParam;
import com.ideal.script.model.bean.StartAgentParams;
import com.ideal.script.model.bean.TaskExecuteBean;
import com.ideal.script.model.bean.TaskIpsAgentResultBean;
import com.ideal.script.model.bean.TaskRunTimeBindAgentBean;
import com.ideal.script.model.bean.TimeTaskExportBean;
import com.ideal.script.model.bean.UrlReplace;
import com.ideal.script.model.dto.AgentInfoDto;
import com.ideal.script.model.dto.ScheduleJobTaskDto;
import com.ideal.script.model.dto.ScriptExecAuditDto;
import com.ideal.script.model.dto.ScriptStopShellDto;
import com.ideal.script.model.dto.ScriptTestExecutionDto;
import com.ideal.script.model.dto.TaskDto;
import com.ideal.script.model.dto.TaskExecuteDto;
import com.ideal.script.model.dto.TaskExecuteQueryDto;
import com.ideal.script.model.dto.TaskGroupsDto;
import com.ideal.script.model.dto.TaskHisAgentExcelDto;
import com.ideal.script.model.dto.TaskInstanceDto;
import com.ideal.script.model.dto.TaskParamsDto;
import com.ideal.script.model.dto.TaskRuntimeDto;
import com.ideal.script.model.dto.TaskScheduleDto;
import com.ideal.script.model.dto.TaskStartDto;
import com.ideal.script.model.dto.interaction.agent.AdaptorDto;
import com.ideal.script.model.entity.Attachment;
import com.ideal.script.model.entity.InfoVersionText;
import com.ideal.script.model.entity.Task;
import com.ideal.script.model.entity.TaskAttachment;
import com.ideal.script.model.entity.TaskParams;
import com.ideal.script.model.entity.TaskRuntime;
import com.ideal.script.observer.itsm.ItsmScriptTaskResultPush;
import com.ideal.script.service.IAgentInfoService;
import com.ideal.script.service.ICategoryService;
import com.ideal.script.service.IExectimeService;
import com.ideal.script.service.IInfoService;
import com.ideal.script.service.IInfoVersionService;
import com.ideal.script.service.ITaskAttachmentService;
import com.ideal.script.service.ITaskExecuteService;
import com.ideal.script.service.ITaskGroupsService;
import com.ideal.script.service.ITaskInstanceService;
import com.ideal.script.service.ITaskIpsService;
import com.ideal.script.service.ITaskRuntimeService;
import com.ideal.script.service.ITaskScheduleService;
import com.ideal.script.service.ITaskService;
import com.ideal.script.service.JobOperateService;
import com.ideal.snowflake.util.SnowflakeIdWorker;
import com.ideal.system.common.component.model.CurrentUser;
import com.thoughtworks.xstream.XStream;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RMap;
import org.redisson.api.RQueue;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.ObjectOutputStream;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 任务执行实现类
 *
 * <AUTHOR>
 */
@Service
public class TaskExecuteServiceImpl implements ITaskExecuteService, Batch {

    private static final Logger logger = LoggerFactory.getLogger(TaskExecuteServiceImpl.class);

    private final ScriptBusinessConfig scriptBusinessConfig;
    private final RedissonClient redissonClient;
    private final ITaskService taskService;

    private final TaskParamsMapper taskParamsMapper;

    private final TaskIpsMapper taskIpsMapper;

    private final IInfoService infoService;
    private final IInfoVersionService infoVersionService;

    private final ITaskInstanceService taskInstanceService;

    private final SqlSessionFactory factory;

    private final BatchDataUtil batchDataUtil;

    private final InfoVersionTextMapper infoVersionTextMapper;

    private final TaskAttachmentMapper taskAttachmentMapper;

    private final AttachmentMapper attachmentMapper;

    private final AgentOperateApi agentOperateApi;

    private final ITaskRuntimeService taskRuntimeService;

    private final ICategoryService categoryService;
    private final ITaskGroupsService taskGroupsService;

    private final ITaskAttachmentService taskAttachmentService;
    private final ITaskIpsService taskIpsService;

    private final IExectimeService exectimeService;

    private final AgentManagementApi agentManagementApi;

    private final ITaskScheduleService taskScheduleService;

    private final JobOperateService jobOperateService;
    private final TaskMapper taskMapper;
    private final TaskInstanceMapper taskInstanceMapper;
    private final ITaskExecuteService iTaskExecuteService;
    private final ItsmScriptTaskResultPush itsmScriptTaskResultPush;

    private final IAgentInfoService agentInfoService;
    private final RedisTemplate<String, String> redisTemplate;


    private final TaskRuntimeMapper taskRuntimeMapper;

    @Autowired
    public TaskExecuteServiceImpl(ScriptBusinessConfig scriptBusinessConfig, RedissonClient redissonClient, ITaskService taskService, TaskParamsMapper taskParamsMapper, TaskIpsMapper taskIpsMapper, IInfoService infoService, IInfoVersionService infoVersionService, ITaskInstanceService taskInstanceService, SqlSessionFactory factory, BatchDataUtil batchDataUtil, InfoVersionTextMapper infoVersionTextMapper, TaskAttachmentMapper taskAttachmentMapper, AttachmentMapper attachmentMapper, AgentOperateApi agentOperateApi, ITaskRuntimeService taskRuntimeService, ICategoryService categoryService, ITaskGroupsService taskGroupsService, ITaskAttachmentService taskAttachmentService, ITaskIpsService taskIpsService, IExectimeService exectimeService, AgentManagementApi agentManagementApi, ITaskScheduleService taskScheduleService, JobOperateService jobOperateService, TaskMapper taskMapper, TaskInstanceMapper taskInstanceMapper, @Lazy ITaskExecuteService iTaskExecuteService, ItsmScriptTaskResultPush itsmScriptTaskResultPush, IAgentInfoService agentInfoService, @Qualifier("redisTemplate") RedisTemplate redisTemplate, TaskRuntimeMapper taskRuntimeMapper) {
        this.scriptBusinessConfig = scriptBusinessConfig;
        this.redissonClient = redissonClient;
        this.taskService = taskService;
        this.taskParamsMapper = taskParamsMapper;
        this.taskIpsMapper = taskIpsMapper;
        this.infoService = infoService;
        this.infoVersionService = infoVersionService;
        this.taskInstanceService = taskInstanceService;
        this.factory = factory;
        this.batchDataUtil = batchDataUtil;

        this.infoVersionTextMapper = infoVersionTextMapper;
        this.taskAttachmentMapper = taskAttachmentMapper;
        this.attachmentMapper = attachmentMapper;
        this.agentOperateApi = agentOperateApi;
        this.taskRuntimeService = taskRuntimeService;
        this.categoryService = categoryService;
        this.taskGroupsService = taskGroupsService;
        this.taskAttachmentService = taskAttachmentService;
        this.taskIpsService = taskIpsService;
        this.exectimeService = exectimeService;
        this.agentManagementApi = agentManagementApi;
        this.taskScheduleService = taskScheduleService;
        this.jobOperateService = jobOperateService;
        this.taskMapper = taskMapper;
        this.taskInstanceMapper = taskInstanceMapper;
        this.iTaskExecuteService = iTaskExecuteService;
        this.itsmScriptTaskResultPush = itsmScriptTaskResultPush;
        this.agentInfoService = agentInfoService;
        this.redisTemplate = redisTemplate;
        this.taskRuntimeMapper = taskRuntimeMapper;
    }

    /**
     * 待执行任务列表
     *
     * @param taskExecuteQueryDto 执行任务tab页封装的查询条件
     * @param pageNum             页码
     * @param pageSize            分页条数
     * @param currentUser         当前用户
     * @return PageInfo<TaskExecuteDto>
     * <AUTHOR>
     */
    @Override
    public PageInfo<TaskExecuteDto> selectTaskReadyToExecuteList(TaskExecuteQueryDto taskExecuteQueryDto, Integer pageNum, Integer pageSize, CurrentUser currentUser) {

        List<TaskExecuteBean> taskExecuteBeanList = new ArrayList<>();
        if (null != taskExecuteQueryDto) {
            TaskExecuteBean taskExecuteBean = BeanUtils.copy(taskExecuteQueryDto, TaskExecuteBean.class);

            // 设置权限相关信息
            categoryService.setCategoryPermission(taskExecuteBean, currentUser);

            PageMethod.startPage(pageNum, pageSize);
            taskExecuteBeanList = taskService.selectTaskReadyToExecuteList(taskExecuteBean, currentUser);
            buildCategoryName(taskExecuteBeanList);
        }
        return PageDataUtil.toDtoPage(taskExecuteBeanList, TaskExecuteDto.class);
    }

    /**
     * 查询定时、周期维护任务列表
     *
     * @param taskExecuteQueryDto   任务Bean
     * @param pageNum   起始页
     * @param pageSize  每页大小
     * @param currentUser   当前用户
     * @return  任务信息
     */
    @Override
    public PageInfo<TaskExecuteDto> selectTimeTasks(TaskExecuteQueryDto taskExecuteQueryDto, Integer pageNum, Integer pageSize, CurrentUser currentUser) {

        List<TaskExecuteBean> taskExecuteBeanList = new ArrayList<>();
        if (null != taskExecuteQueryDto) {
            // 查询当前分类以及子分类的Id集合
            List<Long> categoryIdLis = categoryService.getAllCategoryIds(taskExecuteQueryDto.getCategoryId());
            TaskExecuteBean taskExecuteBean = BeanUtils.copy(taskExecuteQueryDto, TaskExecuteBean.class);
            taskExecuteBean.setCategoryIdList(categoryIdLis);
            PageMethod.startPage(pageNum, pageSize);
            taskExecuteBeanList = taskService.selectTimeTaskList(taskExecuteBean, currentUser);
            buildCategoryName(taskExecuteBeanList);
        }
        return PageDataUtil.toDtoPage(taskExecuteBeanList, TaskExecuteDto.class);

    }

    /**
     * 定时、周期启停切换
     *
     * @param scriptTaskId  脚本任务id
     * @param state 状态
     * @throws ScriptException  脚本服务化自定义异常
     */
    @Override
    public void timeTaskSwitch(long scriptTaskId, int state) throws ScriptException {
        // 查询需要操作的xxl-job的id
        TaskScheduleDto taskScheduleDto = taskScheduleService.selectTaskScheduleByTaskId(scriptTaskId);
        // 更新数据库中状态(优先更新数据库，xxljob如果调用失败了，回更状态即可)
        Task task = new Task();
        task.setId(scriptTaskId);
        switch (state) {
            case 1:
                task.setReadyToExecute(Enums.ReadyToExecute.READY_TO_EXECUTE.getCode());
                taskMapper.updateTask(task);

                TaskScheduleDto taskSchedule1 = new TaskScheduleDto();
                taskSchedule1.setId(taskScheduleDto.getScheduleId());
                taskSchedule1.setState(Enums.TaskScheduleEnum.TASK_RUNNING.getValue());
                taskScheduleService.updateTaskSchedule(taskSchedule1);
                // 更新xxl-job状态
                try {
                    if (Boolean.FALSE.equals(jobOperateService.startJob(Math.toIntExact(taskScheduleDto.getScheduleId())))) {
                        throw new ScriptException();
                    }
                } catch (Exception e) {
                    logger.error("Scheduled and periodic tasks fail to start", e);
                    throw new ScriptException("script.time.task.start.fail");
                }
                logger.info("timetask taskId: {}, scheduleId: {}, start success", scriptTaskId, taskScheduleDto.getScheduleId());
                break;
            case 2:
                // 当前为停止状态
                task.setReadyToExecute(Enums.ReadyToExecute.NOT_READY_TO_EXECUTE.getCode());
                taskMapper.updateTask(task);
                TaskScheduleDto taskSchedule2 = new TaskScheduleDto();
                taskSchedule2.setId(taskScheduleDto.getScheduleId());
                taskSchedule2.setState(Enums.TaskScheduleEnum.TASK_INIT.getValue());
                taskScheduleService.updateTaskSchedule(taskSchedule2);

                try {
                    if (!jobOperateService.stopJob(Math.toIntExact(taskScheduleDto.getScheduleId()))) {
                        throw new ScriptException();
                    }
                } catch (Exception e) {
                    logger.error("Scheduled and periodic tasks fail to stop", e);
                    throw new ScriptException("script.time.task.stop.fail");
                }
                logger.info("timetask taskId: {}, scheduleId: {}, stop success", scriptTaskId, taskScheduleDto.getScheduleId());
                break;
            default:
                logger.error("Scheduled and periodic tasks state fail matching");
                throw new ScriptException("script.time.task.state.error");
        }
    }

    /**
     * 定时、周期任务终止
     *
     * @param scriptTaskId  脚本任务id
     * @throws ScriptException  脚本服务化自定义异常
     */
    @Transactional(rollbackFor = ScriptException.class)
    @Override
    public void timeTaskKill(long scriptTaskId) throws ScriptException {
        // 查询出xxljob的id
        TaskScheduleDto taskScheduleDto = taskScheduleService.selectTaskScheduleByTaskId(scriptTaskId);
        logger.info("timeTaskSwitch: {}", taskScheduleDto.getScheduleId());

        // task表更新状态
        Task task = new Task();
        task.setId(scriptTaskId);
        task.setReadyToExecute(Enums.ReadyToExecute.CANCELED.getCode());
        taskMapper.updateTask(task);
        taskScheduleService.deleteTaskScheduleByIds(new Long[]{taskScheduleDto.getScheduleId()});

        try {
            IdealXxlJobApiUtil.removeJob(Math.toIntExact(taskScheduleDto.getScheduleId()));
        } catch (NullPointerException e) {
            // 当xxl-job中不存在对应任务报错空指针
            logger.error("Scheduled and periodic tasks fail to kill", e);
        } catch (Exception e) {
            throw new ScriptException("script.time.task.kill.fail");
        }

        // 历史任务发起终止操作
        // 查询出所有的instance的id
        List<Long> instanceIds = taskInstanceMapper.selectRunningTaskByScriptTaskId(scriptTaskId);
        if(instanceIds != null && !instanceIds.isEmpty()){
            iTaskExecuteService.stopTask(instanceIds.toArray(new Long[0]));
        }
    }

    /**
     * 更新定时、周期cron、time
     *
     * @param taskExecuteDto      执行任务信息Dto
     * @throws ScriptException  脚本服务化自定义异常
     */
    @Override
    public void updateTimeTaskCron(TaskExecuteDto taskExecuteDto) throws ScriptException {
        // 定时类型需要转换cron
        if (taskExecuteDto.getTaskScheduler().equals(Enums.TaskScheduler.TIMED.getValue())) {
            taskExecuteDto.setTaskCron(CronDateUtils.getCron(taskExecuteDto.getTaskTime()));
        }

        Task task = new Task();
        task.setId(taskExecuteDto.getScriptTaskId());
        task.setTaskCron(taskExecuteDto.getTaskCron());
        task.setTaskTime(taskExecuteDto.getTaskTime());
        // 更新数据库内数据
        taskMapper.updateTask(task);

        // 查询当前任务详细信息
        TaskDto taskDto = taskService.selectTaskById(taskExecuteDto.getScriptTaskId());
        ScheduleJobTaskDto scheduleJobTaskDto = new ScheduleJobTaskDto();
        scheduleJobTaskDto.setTaskId(taskDto.getId());
        scheduleJobTaskDto.setTaskName(taskDto.getTaskName());
        scheduleJobTaskDto.setCron(taskDto.getTaskCron());
        scheduleJobTaskDto.setCreateName(taskDto.getCreatorName());

        TaskScheduleDto taskScheduleDto = taskScheduleService.selectTaskScheduleByTaskId(taskExecuteDto.getScriptTaskId());

        scheduleJobTaskDto.setCreatorId(taskDto.getCreatorId());
        scheduleJobTaskDto.setScheduleJobId(taskScheduleDto.getScheduleId());
        scheduleJobTaskDto.setStrategyType(taskDto.getTaskScheduler());

        // 更新xxljob上任务
        try {
            if (!jobOperateService.modifyJob(scheduleJobTaskDto)) {
                throw new ScriptException();
            }
        } catch (Exception e) {
            logger.error("Scheduled and periodic tasks fail to update Cron", e);
            throw new ScriptException("");
        }
    }

    /**
     * 启动脚本任务
     *
     * @param taskStartDto 脚本启动封装的参数dto
     * @return Map<String, Object>
     */
    @Override
    public Long scriptTaskStart(TaskStartDto taskStartDto, CurrentUser user) throws ScriptException {

        //如果是重试，则不需要创建redis节点、eachNum等于1
        if(Boolean.TRUE.equals(taskStartDto.getRetry())){
            taskStartDto.setEachNum(1);
            taskStartDto.setFirstBatch(false);
        }

        //根据iunique_uuid获取脚本的英文名字，并用脚本的英文名作为下发到gent端的脚本文件名
        if(StringUtils.isNotBlank(taskStartDto.getInfoUniqueUuid())){
            ScriptInfoDto scriptInfoDto = infoService.selectInfoByUniqueUuid(taskStartDto.getInfoUniqueUuid());
            taskStartDto.setScriptNameEn(scriptInfoDto.getScriptName());
        }

        //判断，每次执行的agent数量不能超过并发数
        if(ObjectUtils.notEqual(taskStartDto.getTaskIps(),null) && (taskStartDto.getTaskIps().length > taskStartDto.getEachNum())){
            throw new ScriptException("error.eachnum.lessthan.agentcont");
        }

        //获取task数据
        TaskDto taskTaskDto = taskService.selectTaskById(taskStartDto.getScriptTaskId());
        //设置脚本任务来源
        taskStartDto.setScriptTaskSource(taskTaskDto.getScriptTaskSource());
        //设置任务描述
        taskStartDto.setPublishDesc(taskTaskDto.getPublishDesc());
        //设置任务名
        taskStartDto.setTaskName(taskTaskDto.getTaskName());
        //任务启动来源
        taskStartDto.setStartType(taskTaskDto.getStartType());
        //第一批任务，需要向redis中写入相关任务数据
        if(taskStartDto.isFirstBatch()){
            //首批直接设置任务实例id，不使用主键自动生成
            taskStartDto.setIscriptTaskInstanceId(SnowflakeIdWorker.generateId());
            //获取所有ips
            List<TaskIpsAgentResultBean> bindExecTaskIpsInfo = taskIpsMapper.getBindExecTaskIpsInfo(taskStartDto.getScriptTaskId());
            //设置原子计数，存储本次任务total的值，1天失效
            RAtomicLong rAtomicLong = redissonClient.getAtomicLong(String.format(Constants.SCRIPT_TASK_EXEC_REDIS_COUNTER_KEY,taskStartDto.getIscriptTaskInstanceId()));
            rAtomicLong.set(bindExecTaskIpsInfo.size());
            rAtomicLong.expire(Duration.ofDays(Constants.EXEC_TASK_TIMEOUT_DAY));
            //第一批任务的时候，创建redis-hash值记录任务相关数据，1天失效
            RMap<String, String> map = redissonClient.getMap(String.format(Constants.SCRIPT_TASK_EXEC_REDIS_KEY,taskStartDto.getIscriptTaskInstanceId()));
            HashMap<String, String> redisMap = new HashMap<>();
            redisMap.put(Constants.TOTAL,String.valueOf(bindExecTaskIpsInfo.size()));
            //并发数
            redisMap.put(Constants.EACH_NUM,taskTaskDto.getEachNum().toString());
            //用户
            redisMap.put(Constants.USER, JSON.toJSONString(user));
            //dto数据
            redisMap.put(Constants.TASK_START_DTO,JSON.toJSONString(taskStartDto));
            //驱动模式（1、按选择执行；2、分批执行；3、忽略异常分批；4、队列模式）
            //未传值默认忽略异常分批模式（接口调用的时候可能为空）
            if(!ObjectUtils.notEqual(taskStartDto.getDriveMode(),null)){
                taskStartDto.setDriveMode(Enums.DriverModel.IGNORE_ERROR_BATCH_EXEC.getValue());
            }
            redisMap.put(Constants.DRIVER_MODE,taskStartDto.getDriveMode().toString());
            map.putAll(redisMap);
            map.expire(Duration.ofDays(Constants.EXEC_TASK_TIMEOUT_DAY));
            taskStartDto.setEachNum(taskTaskDto.getEachNum());
        }else{
            //如果不是首批执行，需要比对一下目前redis中与本次的driveMode
            //如果是选择执行/分批执行  转成  忽略异常分批/队列执行，需要更新redis中的驱动模式数据
            //前端拿到当前执行模式，提交时判断模式是否发生变更，决定传的是true还是false
            //或者不判断，直接更新，但是这样的话每次都需要交互一次redis
            if(Boolean.TRUE.equals(taskStartDto.getRetry()) && taskStartDto.isTransDriveModeFlag()){
                RMap<String, String> map = redissonClient.getMap(String.format(Constants.SCRIPT_TASK_EXEC_REDIS_KEY,taskStartDto.getIscriptTaskInstanceId()));
                map.put(Constants.DRIVER_MODE,taskStartDto.getDriveMode().toString());
            }
        }
        //首批执行任务实例id也有值
        Long taskInstanceId = taskStartDto.getIscriptTaskInstanceId();
        //定时任务查询的ips不需要确定是否都拉起来，只要是本次任务的ips数据都算
        if(taskTaskDto.getTaskScheduler().intValue() == Enums.TaskScheduler.PERIODIC.getValue()){
            List<TaskIpsAgentResultBean> bindAllTaskIpsInfo = taskIpsMapper.getBindAllTaskIpsInfo(taskTaskDto.getId());
            Long [] ipsArray = bindAllTaskIpsInfo.stream()
                    .map(TaskIpsAgentResultBean::getScriptTaskIpsId)
                    .toArray(Long[]::new);
            taskStartDto.setTaskIps(ipsArray);
        }else{
            Long [] taskIpsIds ;
            //如果是队列模式，第一次执行的时候，非首批的ipsId需要存到redis中，后续每次拉起一个agent
            if(taskStartDto.isFirstBatch() && taskStartDto.getDriveMode().equals(Enums.DriverModel.QUEUE_EXEC.getValue())){
                taskIpsIds = queueModeSaveRedisIps(taskStartDto);
            }else if(taskStartDto.isTransDriveModeFlag() && !taskStartDto.isFirstBatch() && taskStartDto.getDriveMode().equals(Enums.DriverModel.QUEUE_EXEC.getValue())){
                //队列非首批执行，直接使用传递过来的ipsId
                taskIpsIds = taskStartDto.getTaskIps();
            }else if(taskStartDto.getDriveMode().equals(Enums.DriverModel.SELECTIVE.getValue())){
                //按选择执行，直接使用传递过来的ipsId
                taskIpsIds = taskStartDto.getTaskIps();
            }else{
                //如果是分批、忽略异常分批，则根据并发数获取agent
                taskIpsIds = getExecTaskIpsInfo(taskStartDto.getScriptTaskId(),taskStartDto.getEachNum());
            }
            taskStartDto.setTaskIps(taskIpsIds);
        }

        StartAgentCommonParam startAgentCommonParam = iTaskExecuteService.updateBatchStartAndCheck(taskStartDto);

        //如果是首批执行，创建实例id，存入实例数据，非首批延用同一个实例id
        if(taskStartDto.isFirstBatch()){
            TaskDto taskDto = new TaskDto();
            taskDto.setId(taskStartDto.getScriptTaskId());
            taskDto.setDriveMode(taskStartDto.getDriveMode());
            taskService.updateTask(taskDto);
        }

        // 获取脚本任务基础信息以及agent执行用户，agent运行参数信息列表
        List<StartAgentParams> agents = getScriptTaskStartList(taskStartDto);
        if (!agents.isEmpty()) {
            taskInstanceId = execScriptTaskStart(taskInstanceId, taskStartDto, startAgentCommonParam, agents, user);
        }
        return taskInstanceId;
    }

    /**
     * 获取接下来要执行的ipsId
     * @param taskStartDto 参数
     * @return 返回id数组
     */
    private Long [] queueModeSaveRedisIps(TaskStartDto taskStartDto){
        //首批agent的ipsId
        Long [] taskIpsIds = getExecTaskIpsInfo(taskStartDto.getScriptTaskId(),taskStartDto.getEachNum());
        //非首批的agentIpsId
        List<Long> saveRedisIpsIds = getNotFirstBatchIpsId(taskStartDto.getScriptTaskId(),taskIpsIds);
        //将后续的ipsId保存到redis中
        RQueue<Long> redisQueue = redissonClient.getQueue(String.format(Constants.SCRIPT_TASK_EXEC_REDIS_QUEUE_KEY,taskStartDto.getIscriptTaskInstanceId()));
        redisQueue.addAll(saveRedisIpsIds);
        redisQueue.expire(Duration.ofDays(Constants.EXEC_TASK_TIMEOUT_DAY));
        return taskIpsIds;
    }

    /**
     * 获取脚本实时输出
     *
     * @param taskRuntimeId taskRuntimeId
     * @return {@link String }
     */
    @Override
    public String getRealTimeOutPutMessage(Long taskRuntimeId) {
        String stdout = "";
        String stderr = "";
        TaskRuntimeDto taskRuntimeDto = taskRuntimeService.selectTaskRuntimeById(taskRuntimeId);
        AgentOperateDto agentOperateDto = new AgentOperateDto();

        List<Object> params = new ArrayList<>();
        params.add(String.valueOf(taskRuntimeDto.getId()));
        params.add(500);
        String bizId = taskRuntimeDto.getBizId();
        agentOperateDto.setBizId(bizId);
        String jsonStr = convertObjectToJsonString(params);
        logger.info("jsonStr:{}", jsonStr);
        // 设置操作内容为转换后的JSON字符串
        agentOperateDto.setContent(jsonStr);
        // 设置RPC方法
        agentOperateDto.setRpcMethod("IEAIAgent.getShellCmdOutputNLine");
        // 设置内容格式化器为空
        agentOperateDto.setContentFormatter(Enums.ContentFormatter.SCRIPT_CONTENT_FORMATTER.getValue());
        // 权重 默认1
        agentOperateDto.setWeights(1L);
        // 优先级 默认5
        agentOperateDto.setPriority("5");
        // 不需要二次握手（agent端不需要回调provider端或者server端），此处setDynamicResourcesDTO为空
        agentOperateDto.setDynamicResourcesDTO(null);
        AgentOptionDto agentOptionDto = new AgentOptionDto();
        agentOptionDto.setAgentHost(taskRuntimeDto.getAgentIp());
        agentOptionDto.setAgentPort(taskRuntimeDto.getAgentPort().longValue());
        agentOperateDto.setAgentOptionDto(agentOptionDto);
        try {
            AgentSyncOperateResultDto agentSyncOperateResultDto = agentOperateApi.sendSync(agentOperateDto);
            String xml = (String) agentSyncOperateResultDto.getContent();
            XStream stream = new XStream();
            stream.allowTypes(new Class[]{ShellCmdOutput.class});
            stream.alias("ShellCmdOutput", ShellCmdOutput.class);
            ShellCmdOutput shellcmdoutput = (ShellCmdOutput) stream.fromXML(xml);

            if (null != shellcmdoutput) {
                stdout += Base64.getFromBase64(shellcmdoutput.getStdOut());

                if (Enums.NullValue.NULL.getValue().equals(stdout)) {
                    stdout = "";
                }
                stderr = Base64.getFromBase64(shellcmdoutput.getStdErr());
                if (null == stderr || Enums.NullValue.NULL.getValue().equals(stderr)) {
                    stderr = "";
                }
            } else {
                stdout = "活动状态：running，获取当前活动输出日志信息异常!";
                stderr = "活动状态：running，获取当前活动输出日志信息异常!";
            }


            logger.info("stdout: {}", stdout);
        } catch (Exception e) {
            logger.error("getRealTimeOutPutMessage  error:", e);
            stderr = "获取输出日志错误！" + e.getMessage();
        }
        UrlReplace urlReplace = new UrlReplace();
        return urlReplace.getContent(stdout) + urlReplace.getContent(stderr);
    }


    /**
     * 功能描述：
     *
     * @param taskInstanceId        脚本任务运行实例表主键
     * @param taskStartDto          任务启动Dto
     * @param startAgentCommonParam 任务启动公共基础参数信息
     * @param agents                启动的agent数据对象
     * @return Long
     */
    private Long execScriptTaskStart(Long taskInstanceId, TaskStartDto taskStartDto, StartAgentCommonParam startAgentCommonParam, List<StartAgentParams> agents, CurrentUser user) throws ScriptException {

        try {

            TaskDto taskDto = taskService.selectTaskById(taskStartDto.getScriptTaskId());
            //首批插入任务实例表
            if (taskStartDto.isFirstBatch()) {
                // 存储ieai_script_task_instance
                TaskInstanceDto taskInstanceDto = new TaskInstanceDto();
                taskInstanceDto.setScriptTaskId(taskStartDto.getScriptTaskId());
                taskInstanceDto.setId(taskInstanceId);
                taskInstanceDto.setSrcScriptUuid(taskStartDto.getSrcScriptUuid());
                taskInstanceDto.setStatus(Enums.TaskInstanceStatus.RUNNING.getValue());
                // 每次流程运行一个脚本任务，默认仅执行一次。在没有之前的作业情况下，此处硬编码为1。
                taskInstanceDto.setRunNums(1);
                taskInstanceDto.setServerNum(Integer.parseInt(String.valueOf(startAgentCommonParam.getTotal())));
                taskInstanceDto.setTaskScheduler(taskDto.getTaskScheduler());
                taskInstanceDto.setTaskCron(taskDto.getTaskCron());
                taskInstanceDto.setPartExec(0);
                taskInstanceDto.setIgnore(0);
                taskInstanceDto.setStartType(taskStartDto.getStartType() == null ? Integer.valueOf(0) : taskStartDto.getStartType());
                taskInstanceDto.setCallerTaskId(taskStartDto.getCallerTaskId());
                taskInstanceService.insertTaskInstance(taskInstanceDto);
                taskInstanceId = taskInstanceDto.getId();

                // 设置任务待执行状态为已执行
                TaskDto taskDto1 = new TaskDto();
                taskDto1.setReadyToExecute(Enums.ReadyToExecute.EXECUTED.getCode());
                taskDto1.setId(taskDto.getId());
                taskDto1.setStartUser(user.getFullName());
                taskService.updateTask(taskDto1);

            }
            //首批更新任务执行次数
            if (taskStartDto.isFirstBatch()) {
                exectimeService.updateScriptExecTime(null, taskDto.getSrcScriptUuid(), 3);
            }

            // 批量存储ieai_script_task_runtime
            List<TaskRuntimeDto> taskRuntimeDtoList = new ArrayList<>();
            if (!(null != taskStartDto.getRetry() && taskStartDto.getRetry())) {
                saveScriptTaskRuntime(taskStartDto, agents, taskInstanceId, taskDto, taskRuntimeDtoList);
            }else{//重试的时候更新bizId
                // TODO 暂时先这里加上更新bizId，不然后面空指针异常，重试得整体重构速度才快
                TaskRuntimeDto runtimeDto = new TaskRuntimeDto();
                runtimeDto.setId(taskStartDto.getRetryRuntimeId());
                runtimeDto.setRetry(true);
                createBizId(taskStartDto, taskDto, agents.get(0));
                runtimeDto.setBizId(agents.get(0).getBizId());
                taskRuntimeService.updateTaskRuntime(runtimeDto);
            }
            // 用于准备发送给 agent 的数据
            prepareAgentData(taskStartDto, agents, startAgentCommonParam, taskDto, user);

        } catch (Exception e) {
            logger.error("execScriptTaskStart Error", e);
            throw new ScriptException("error.exec.script.task.start");
        }
        return taskInstanceId;
    }

    /**
     * 功能描述：准备发送给 agent 的数据
     *
     * @param taskStartDto          任务启动封装参数Dto
     * @param agents                agent对象
     * @param startAgentCommonParam agent启动公共参数对象
     * @param taskDto               脚本任务Dto
     * <AUTHOR>
     */
    private void prepareAgentData(TaskStartDto taskStartDto, List<StartAgentParams> agents, StartAgentCommonParam startAgentCommonParam, TaskDto taskDto, CurrentUser user) {
        // 获取脚本内容
        InfoVersionText infoVersionText = infoVersionTextMapper.selectInfoVersionTextByScriptUuid(taskStartDto.getSrcScriptUuid());
        //\r换行标识linux无法识别，需要转换成\r\n
        if(!"bat".equals(taskStartDto.getScriptType()) && !"ps1".equals(taskStartDto.getScriptType())){
            String contentReplaceStr = infoVersionText.getContent().replaceAll("\r", "\n");
            infoVersionText.setContent(contentReplaceStr.replaceAll("\n\n","\n"));
        }
        String scriptType = infoVersionService.getScriptTypeBySrcScriptUuid(taskStartDto.getSrcScriptUuid());
        Map<String, Object> hashMap = new HashMap<>(10);


        hashMap.put(AgentExecuteKey.AgentHashtableKey.COMMAND.getValue(), infoVersionText.getContent());

        hashMap.put(AgentExecuteKey.AgentHashtableKey.SCRIPT_TIMEOUT.getValue(), taskDto.getTimeout() + "");
        String scriptNameEn = taskStartDto.getScriptNameEn();
        hashMap.put(AgentExecuteKey.AgentHashtableKey.SERVICE_NAME.getValue(), scriptNameEn+"_"+taskStartDto.getSrcScriptUuid());
        hashMap.put(AgentExecuteKey.AgentHashtableKey.SERVICES_TYPE.getValue(), scriptType);
        hashMap.put(AgentExecuteKey.AgentHashtableKey.SCRIPT_WORK_DIR.getValue(), "");

        // 根据脚本uuid获取脚本绑定附件
        Attachment attachmentQuery = new Attachment();
        attachmentQuery.setSrcScriptUuid(taskStartDto.getSrcScriptUuid());
        List<Attachment> attachments = attachmentMapper.selectAttachmentList(attachmentQuery);
        List<TaskAttachment> attachmentDtoList =  new ArrayList<>();
        if( !attachments.isEmpty()) {
            attachmentDtoList.addAll(BeanUtils.copy(attachments, TaskAttachment.class));
        }
        // 根据taskid获取临时附件
        TaskAttachment taskAttachment = new TaskAttachment();
        taskAttachment.setScriptTaskId(taskStartDto.getScriptTaskId());
        List<TaskAttachment> taskAttachments = taskAttachmentMapper.selectTaskAttachmentList(taskAttachment);
        if(CollectionUtils.isNotEmpty(taskAttachments)){
            attachmentDtoList.addAll(taskAttachments);
        }
        if (!attachmentDtoList.isEmpty()) {
            Object[] obj = new Object[attachmentDtoList.size() * 2];
            int n = 0;
            for (TaskAttachment attachment : attachmentDtoList) {
                if (attachment.getContents() != null) {
                    obj[n] = attachment.getName();
                    n++;
                    byte[] result = attachment.getContents();
                    obj[n] = result;
                    n++;
                }
            }
            //如果附件是空值附件，那么这里obj长度只会是初始化的大小并且属性值都为null
            if(obj.length == 2 && obj[0] == null && obj[1] == null) {
                hashMap.put(AgentExecuteKey.AgentHashtableKey.ANNEX_FILE.getValue(), new Object[]{});
            }else{
                hashMap.put(AgentExecuteKey.AgentHashtableKey.ANNEX_FILE.getValue(), obj);

            }
        } else {
            hashMap.put(AgentExecuteKey.AgentHashtableKey.ANNEX_FILE.getValue(), new Object[]{});
        }
        List<AgentOperateDto> listAgentOperateDTO = new ArrayList<>();
        for (StartAgentParams agent : agents) {
            agent.setScriptHashMap(hashMap);
            buildStartAgentParams(taskStartDto, startAgentCommonParam, agent, user);

            List<Object> vector = buildVector(agent);
            generateAgentOperateDto(vector, listAgentOperateDTO, agent);
        }
        //事务提交后发送agentGateway
        TransactionSyncUtil.execute(agentOperateApi::send,null,null,listAgentOperateDTO);
    }

    /**
     * 功能描述：生成biz
     *
     * @param taskStartDto 任务启动封装参数Dto
     * @param taskDto      脚本任务Dto
     * @param agent        agent启动参数对象
     * <AUTHOR>
     */
    private void createBizId(TaskStartDto taskStartDto, TaskDto taskDto, StartAgentParams agent) {
        long taskRuntimeId;
        Boolean retry = taskStartDto.getRetry();
        if (Boolean.TRUE.equals(retry)) {
            taskRuntimeId = taskStartDto.getRetryRuntimeId();
            agent.setTaskRuntimeId(taskRuntimeId);
        } else {
            taskRuntimeId = agent.getTaskRuntimeId();
        }

        String from = "";
        if (null != taskDto.getScriptTaskSource()) {
            // 来自脚本测试
            if (taskDto.getScriptTaskSource().equals(Enums.TaskSource.SCRIPT_TESTING.getValue())) {
                from = Enums.BizIdInfix.SCRIPT_TEST.getValue();
            }
            // 通过任务申请执行的脚本任务
            if (taskDto.getScriptTaskSource().equals(Enums.TaskSource.TASK_APPLICATION.getValue())) {
                from = Enums.BizIdInfix.TASK_EXEC.getValue();
            }
        }
        String bizId = buildBizId(taskRuntimeId, retry, from);
        agent.setBizId(bizId);
    }

    private void buildStartAgentParams(TaskStartDto taskStartDto, StartAgentCommonParam startAgentCommonParam, StartAgentParams agent, CurrentUser user) {
        String agentParam = agent.getAgentParam();
        if (null != agentParam && !agentParam.trim().isEmpty()) {
            agentParam = agentParam.trim();
        }
        if (null != startAgentCommonParam.getParams() && !startAgentCommonParam.getParams().isEmpty()) {
            agentParam = startAgentCommonParam.getParams();
        }
        agent.setAgentParam(agentParam);
        agent.setScriptTaskId(taskStartDto.getScriptTaskId());
        agent.setScriptEnvironment(String.valueOf(Enums.ScriptEnvironment.SCRIPT_ENV_PROD.getValue()));
        agent.setSrcScriptUuid(taskStartDto.getSrcScriptUuid());
        agent.setInfoUniqueUuid(taskStartDto.getInfoUniqueUuid());

        long userId = user.getId();

        agent.setUserId(String.valueOf(userId));
        agent.setScriptInParam(startAgentCommonParam.getScriptInParam());
    }


    /**
     * 功能描述： 生成bizId
     *
     * @param taskRuntimeId agent运行实例id
     * @param retry         是否重试
     * @param from          来源（任务申请或脚本）
     * @return {@link String }
     * <AUTHOR>
     */
    private String buildBizId(long taskRuntimeId, Boolean retry, String from) {
        // 生成bizId
        RAtomicLong counter = redissonClient.getAtomicLong(Enums.AgentExecRunFlag.RETRY.getValue() + taskRuntimeId);
        String bizId;
        // 重试
        if (Boolean.TRUE.equals(retry)) {
            long count = counter.incrementAndGet();
            bizId = Enums.AgentExecRunFlag.RETRY.getValue() + from + count + Enums.Separator.DASH.getValue() + taskRuntimeId;
        } else {
            bizId = Enums.AgentExecRunFlag.START.getValue() + from + taskRuntimeId;
        }
        return bizId;
    }

    /**
     * 功能描述： 存储agent运行实例关联的bizId
     *
     * @param taskRuntimeId agent运行实例id
     * @param bizId         业务主键
     * <AUTHOR>
     */
    private void updateBizId(long taskRuntimeId, String bizId) {
        // 更新bizId
        TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setId(taskRuntimeId);
        taskRuntimeDto.setBizId(bizId);
        taskRuntimeService.updateTaskRuntime(taskRuntimeDto);
    }

    /**
     * 功能描述： 构建List对象
     *
     * @param startAgentParams 启动agent参数
     * @return {@link List }<{@link Object }>
     * <AUTHOR>
     */
    public List<Object> buildVector(StartAgentParams startAgentParams) {
        String agentIp = startAgentParams.getAgentIp();
        Integer agentPort = startAgentParams.getAgentPort();
        long taskRuntimeId = startAgentParams.getTaskRuntimeId();
        String expectLastLine = startAgentParams.getExpectLastLine();
        String errorExpectLastLine = startAgentParams.getErrorExpectLastLine();
        Integer expectType = startAgentParams.getExpectType();
        String serviceType = buildContentForSqlServiceType(startAgentParams);
        ArrayList<Object> params = new ArrayList<>();
        params.add(String.valueOf(taskRuntimeId));
        params.add(scriptBusinessConfig.getAgentGatewayIp());
        params.add(scriptBusinessConfig.getAgentGatewayPort());

        Map<String, Object> table = new HashMap<>(50);
        table.put(AgentExecuteKey.AgentHashtableKey.SCOPE_ID.getValue(), "162865");
        table.put(AgentExecuteKey.AgentHashtableKey.ADAPTOR_CONFIG.getValue(), 1);
        table.put(AgentExecuteKey.AgentHashtableKey.SERVER_HOST.getValue(), scriptBusinessConfig.getAgentGatewayIp());
        table.put(AgentExecuteKey.AgentHashtableKey.SERVER_PORT.getValue(), scriptBusinessConfig.getAgentGatewayPort());
        table.put(AgentExecuteKey.AgentHashtableKey.ACT_STATE_DATA_VERSION.getValue(), -1);
        table.put(AgentExecuteKey.AgentHashtableKey.TIME_OUT.getValue(), "0");
        table.put(AgentExecuteKey.AgentHashtableKey.START_TIME.getValue(), new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((new Date()).getTime()));
        table.put(AgentExecuteKey.AgentHashtableKey.AGENT_HOST.getValue(), agentIp);
        table.put(AgentExecuteKey.AgentHashtableKey.AGENT_PORT.getValue(), agentPort);

        AdaptorDto shellCmdAdaptor = new AdaptorDto();
        if (Enums.ScriptType.SQL.getValue().equals(serviceType)) {
            String shellCmdUuid = agentManagementApi.queryAdaptorUuid("sqladaptor");
            shellCmdAdaptor.setUuid(shellCmdUuid);
            table.put(AgentExecuteKey.AgentHashtableKey.ADAPTOR_DEF_UUID.getValue(), shellCmdAdaptor.getUuid());
            table.put(AgentExecuteKey.AgentHashtableKey.ACT_ID.getValue(), "execsql");
            table.put(AgentExecuteKey.AgentHashtableKey.ADAPTOR_DEF_NAME.getValue(), "sqladaptor");
            table.put(AgentExecuteKey.AgentHashtableKey.ACT_NAME.getValue(), "sqlScriptAct");
            table.put(AgentExecuteKey.AgentHashtableKey.PROJECT_NAME.getValue(), "shell_test_" + taskRuntimeId);
            table.put(AgentExecuteKey.AgentHashtableKey.FLOW_NAME.getValue(), "ExecSql");

        } else {
            // 接口 shellCmdAdaptor.setUuid("cf1197c4-f262-1004-8b17-9272e3c0c036")
            String shellCmdUuid = agentManagementApi.queryAdaptorUuid("shellcmd");
            shellCmdAdaptor.setUuid(shellCmdUuid);
            table.put(AgentExecuteKey.AgentHashtableKey.ADAPTOR_DEF_UUID.getValue(), shellCmdAdaptor.getUuid());
            table.put(AgentExecuteKey.AgentHashtableKey.ACT_ID.getValue(), "script");
            table.put(AgentExecuteKey.AgentHashtableKey.ADAPTOR_DEF_NAME.getValue(), Constants.ADAPTOR_SHELLCMD);
            table.put(AgentExecuteKey.AgentHashtableKey.ACT_NAME.getValue(), "ShellCmd");
            table.put(AgentExecuteKey.AgentHashtableKey.PROJECT_NAME.getValue(), "shell_test_" + taskRuntimeId);
            table.put(AgentExecuteKey.AgentHashtableKey.FLOW_NAME.getValue(), "ShellCmd_" + taskRuntimeId);
        }
        table.put(AgentExecuteKey.AgentHashtableKey.STATUS.getValue(), 2);
        table.put(AgentExecuteKey.AgentHashtableKey.LEVEL_OF_WEIGHT.getValue(), "1");
        table.put(AgentExecuteKey.AgentHashtableKey.ID.getValue(), String.valueOf(taskRuntimeId));
        table.put(AgentExecuteKey.AgentHashtableKey.FLOW_ID.getValue(), "-99");
        table.put(AgentExecuteKey.AgentHashtableKey.FLOW_POOL_NUM.getValue(), "0");
        table.put(AgentExecuteKey.AgentHashtableKey.IS_SAFE_FIRST.getValue(), true);
        table.put(AgentExecuteKey.AgentHashtableKey.LEVEL_OF_PRI.getValue(), "5");
        table.put(Enums.ScriptExpectation.EXPECT_LAST_LINE.getValue(), expectLastLine == null ? "" : expectLastLine);
        // 预期异常结果
        table.put(Enums.ScriptExpectation.ERROR_EXPECT_LAST_LINE.getValue(), errorExpectLastLine == null ? "" : errorExpectLastLine);
        // 判断类型 1：lastLine 2：ret
        table.put(AgentExecuteKey.AgentHashtableKey.EXPECT_TYPE.getValue(), expectType == null ? "" : expectType + "");
        organizeScriptHashtable(startAgentParams);
        table.put(AgentExecuteKey.AgentHashtableKey.INPUT.getValue(), startAgentParams.getScriptHashMap());
        params.add(table);
        return params;
    }


    private String buildContentForSqlServiceType(StartAgentParams startAgentParams) {
        String serviceType = String.valueOf(startAgentParams.getScriptHashMap().get(AgentExecuteKey.AgentScriptHashtableKey.SERVICES_TYPE.getValue()));
        if (Enums.ScriptType.SQL.getValue().equals(serviceType)) {
            String scriptContent = (String) startAgentParams.getScriptHashMap().get(AgentExecuteKey.AgentScriptHashtableKey.COMMAND.getValue());
            scriptContent = getSqlScriptContentWithParams(scriptContent, startAgentParams.getAgentParam());
            startAgentParams.getScriptHashMap().put(AgentExecuteKey.AgentScriptHashtableKey.COMMAND.getValue(), scriptContent);
            startAgentParams.getScriptHashMap().put(AgentExecuteKey.AgentScriptHashtableKey.PM.getValue(), "");
        }
        return serviceType;
    }


    /**
     * 功能描述：组织ScriptHashtable参数
     *
     * <AUTHOR>
     */

    public void organizeScriptHashtable(StartAgentParams startAgentParams) {

        long taskRuntimeId = startAgentParams.getTaskRuntimeId();
        String execUserName = startAgentParams.getExecUserName();

        encryptAgentParamIfNeeded(startAgentParams);
        Integer expectType = startAgentParams.getExpectType();
        makeExpectLastLine(startAgentParams);
        // 判断类型，1：lastLine，2：ret
        startAgentParams.getScriptHashMap().put(AgentExecuteKey.AgentScriptHashtableKey.EXPECT_TYPE.getValue(), expectType.toString());
        startAgentParams.getScriptHashMap().put(AgentExecuteKey.AgentScriptHashtableKey.EXECUTE_USER_NAME.getValue(), execUserName == null ? "" : execUserName);
        startAgentParams.getScriptHashMap().put(AgentExecuteKey.AgentScriptHashtableKey.IS_SHUT_DOWN.getValue(), false);
        startAgentParams.getScriptHashMap().put(AgentExecuteKey.AgentScriptHashtableKey.IS_TEST.getValue(), false);
        startAgentParams.getScriptHashMap().put(AgentExecuteKey.AgentScriptHashtableKey.SCRIPT_INSTANCE_ID.getValue(), String.valueOf(taskRuntimeId));

        //函数、变量，函数使用加密形式传递
        startAgentParams.getScriptHashMap().put(AgentExecuteKey.AgentScriptHashtableKey.ENV_PARAM.getValue(), "");

        startAgentParams.getScriptHashMap().put(AgentExecuteKey.AgentScriptHashtableKey.SCRIPT_INSTANCE_BEAN_ID.getValue(), scriptBusinessConfig.getScriptInstance());
        startAgentParams.getScriptHashMap().put(AgentExecuteKey.AgentScriptHashtableKey.SCRIPT_ADAPTOR_BEAN_ID.getValue(), "scriptAdaptor");
    }

    /**
     * 组织ScriptHashMap中存储的预期结果以及异常结果值
     *
     * @param startAgentParams agent启动参数
     */
    public void makeExpectLastLine(StartAgentParams startAgentParams) {
        String expectLastLine = startAgentParams.getExpectLastLine();
        startAgentParams.getScriptHashMap().put(Enums.ScriptExpectation.EXPECT_LAST_LINE.getValue(), expectLastLine == null ? "" : expectLastLine);
        startAgentParams.getScriptHashMap().put(Enums.ScriptExpectation.ERROR_EXPECT_LAST_LINE.getValue(), "");
    }


    private void encryptAgentParamIfNeeded(StartAgentParams startAgentParams) {
        if (scriptBusinessConfig.isSendScriptParametersDesEnc()) {
            // 自定义密钥 bankCode001参数加密
            DesUtils des = new DesUtils(AgentExecuteKey.DesUtilsKey.SCRIPT_SERVICE_LEEMENZ.getValue());
            try {
                startAgentParams.getScriptHashMap().put(AgentExecuteKey.AgentScriptHashtableKey.PM.getValue(), startAgentParams.getAgentParam() == null ? "" : des.encrypt(startAgentParams.getAgentParam()));
            } catch (IllegalBlockSizeException e) {
                logger.error("IllegalBlockSizeException occur!");
            } catch (BadPaddingException e) {
                logger.error("BadPaddingException occur!");
            }
        } else {
            startAgentParams.getScriptHashMap().put(AgentExecuteKey.AgentScriptHashtableKey.PM.getValue(), startAgentParams.getAgentParam() == null ? "" : startAgentParams.getAgentParam());
        }
    }

    /**
     * 功能描述：准备Agent操作数据
     *
     * @param params              脚本任务参数
     * @param listAgentOperateDto Agent操作数据传输对象的列表
     * @param startAgentParams    agent启动参数
     * <AUTHOR>
     */
    public void generateAgentOperateDto(List<Object> params, List<AgentOperateDto> listAgentOperateDto, StartAgentParams startAgentParams) {
        // 将参数列表转换为JSON字符串
        String jsonStr = convertObjectToJsonString(params);
        // 创建Agent操作数据传输对象
        AgentOperateDto agentOperateDto = new AgentOperateDto();
        // 设置业务ID
        String bizId = startAgentParams.getBizId();
        agentOperateDto.setBizId(bizId);
        // 设置操作内容为转换后的JSON字符串
        agentOperateDto.setContent(jsonStr);
        // 设置RPC方法,是否重试，需要调用agent的不同方法
        if(bizId.contains(Enums.AgentExecRunFlag.RETRY.getValue())){
            agentOperateDto.setRpcMethod(Constants.RETRY_RPC_METHOD);
        }else{
            agentOperateDto.setRpcMethod(Constants.RPC_METHOD);
        }

        // 设置内容格式化器为空
        agentOperateDto.setContentFormatter("scriptTaskContentFormatter");
        // 不需要二次握手（agent端不需要回调provider端或者server端），此处setDynamicResourcesDTO为空
        agentOperateDto.setDynamicResourcesDTO(null);
        // 权重 默认1
        agentOperateDto.setWeights(1L);
        // 优先级 默认5
        agentOperateDto.setPriority("5");
        AgentOptionDto agentOptionDto = new AgentOptionDto();
        agentOptionDto.setAgentHost(startAgentParams.getAgentIp());
        agentOptionDto.setAgentPort(startAgentParams.getAgentPort().longValue());
        agentOperateDto.setAgentOptionDto(agentOptionDto);
        // 将Agent操作数据传输对象添加到列表中
        listAgentOperateDto.add(agentOperateDto);
    }


    /**
     * 功能描述： 将参数对象转换为JSON字符串
     *
     * @param params params 要转换的对象
     * @return {@link String } JSON字符串表示
     * <AUTHOR>
     */
    @Override
    public String convertObjectToJsonString(List<Object> params) {
        String jsonStr = "";
        byte[] serializedMetaData;
        // 将对象序列化为 byte[] 数组
        try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
             ObjectOutputStream objectOutputStream = new ObjectOutputStream(byteArrayOutputStream)) {
            objectOutputStream.writeObject(new ArrayList<>(params));
            objectOutputStream.flush();
            serializedMetaData = byteArrayOutputStream.toByteArray();
            jsonStr = java.util.Base64.getEncoder().encodeToString(serializedMetaData);
        } catch (IOException e) {
            logger.error("convertObjectToJsonString error", e);
        }
        // 使用 Base64 编码将 byte 数组转换为字符串

        return jsonStr;
    }


    public String getSqlScriptContentWithParams(String scriptContent, String param) {
        String res = scriptContent;
        String[] params = param.split(Constants.PARAM_VALUE);
        for (int i = 0; i < params.length; i++) {
            String val = params[i];
            String oldKey = "${" + (i + 1) + "}";
            if (val.contains("'")) {
                val = val.replace("'", "''");
            }
            res = res.replace(oldKey, val);
        }
        return res;
    }

    private void saveScriptTaskRuntime(TaskStartDto taskStartDto, List<StartAgentParams> agents, Long taskInstanceId, TaskDto taskDto, List<TaskRuntimeDto> taskRuntimeDtoList)  {
        String taskName = taskStartDto.getTaskName();
        for (StartAgentParams agent : agents) {
            TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
            //直接获取主键
            long runtimeId = SnowflakeIdWorker.generateId();
            // 从agent中获取需要的数据，并设置到taskRuntimeDto中
            taskRuntimeDto.setId(runtimeId);
            taskRuntimeDto.setAgentIp(agent.getAgentIp());
            taskRuntimeDto.setAgentPort(agent.getAgentPort());
            taskRuntimeDto.setExecUser(agent.getExecUserName());
            taskRuntimeDto.setExpectLastline(agent.getExpectLastLine());
            taskRuntimeDto.setExpectType(agent.getExpectType());

            taskRuntimeDto.setScriptTaskId(taskStartDto.getScriptTaskId());
            taskRuntimeDto.setTaskInstanceId(taskInstanceId);
            taskRuntimeDto.setSrcScriptUuid(taskStartDto.getSrcScriptUuid());
            taskRuntimeDto.setState(Enums.TaskRuntimeState.RUNNING.getValue());
            taskRuntimeDto.setScriptName(taskName);
            taskRuntimeDto.setTimeoutValue(taskDto.getTimeout());
            taskRuntimeDto.setStartType(taskDto.getStartType());
            taskRuntimeDto.setScriptTaskIpsId(agent.getScriptTaskIpsId());
            agent.setTaskRuntimeId(runtimeId);
            //生成bizId 设置到  StartAgentParams 中去
            createBizId(taskStartDto,taskDto, agent);
            taskRuntimeDto.setBizId(agent.getBizId());
            // 将taskRuntimeDto添加到结果列表中
            taskRuntimeDtoList.add(taskRuntimeDto);
        }
        List<TaskRuntime> taskRuntimeList = BeanUtils.copy(taskRuntimeDtoList, TaskRuntime.class);

        batchData(taskRuntimeList, taskRuntimeMapper::insertTaskRuntime);
    }

    public Long[] getBindAllTaskIpsInfo(Long scriptTaskId) {
        List<Long> idList = new ArrayList<>();

        // 执行MyBatis查询任务下绑定的代理信息
        List<TaskIpsAgentResultBean> resultList = taskIpsMapper.getBindAllTaskIpsInfo(scriptTaskId);

        // 提取结果中的a.iid，并添加到List集合中
        for (TaskIpsAgentResultBean result : resultList) {
            idList.add(result.getScriptTaskIpsId());
        }

        // 转换为Long[]数组
        return idList.toArray(new Long[0]);
    }

    /**
     * 根据任务id与并发数查询本次要执行的agent的ipsId
     * @param scriptTaskId
     * @param eachNum
     * @return 本次要执行的agent的ipsId
     */
    public Long[] getExecTaskIpsInfo(Long scriptTaskId,int eachNum) {
        PageMethod.startPage(1, eachNum);
        List<TaskIpsAgentResultBean> resultList = taskIpsMapper.getBindExecTaskIpsInfo(scriptTaskId);

        return resultList.stream()
                .map(TaskIpsAgentResultBean::getScriptTaskIpsId)
                .toArray(Long[]::new);
    }

    /**
     *
     * @param scriptTaskId 任务id
     * @return 首批执行ipsId
     */
    public List<Long> getNotFirstBatchIpsId(Long scriptTaskId,Long [] firstBantchIpsId) {
        List<Long> ipsList = new ArrayList<>();
        List<TaskIpsAgentResultBean> resultList = taskIpsMapper.getNotFirstBatchIpsId(scriptTaskId,firstBantchIpsId);
        for(TaskIpsAgentResultBean taskIpsAgentResultBean : resultList){
            ipsList.add(taskIpsAgentResultBean.getScriptTaskIpsId());
        }
        return ipsList;
    }

    /**
     * 功能描述： 获取任务启动时，任务绑定的agent，运行时的脚本任务基础信息以及agent执行用户，agent运行参数等信息
     *
     * @param taskStartDto 任务启动封装参数Dto
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     */
    public List<StartAgentParams> getScriptTaskStartList(TaskStartDto taskStartDto) {

        List<StartAgentParams> startAgentParamsList = new ArrayList<>();

        TaskDto taskDto = taskService.selectTaskById(taskStartDto.getScriptTaskId());
        taskStartDto.setSrcScriptUuid(taskDto.getSrcScriptUuid());
        taskStartDto.setTaskName(taskDto.getTaskName());
        ScriptVersionDto versionDto = infoVersionService.selectInfoVersionBySrcScriptUuid(taskStartDto.getSrcScriptUuid());
        taskStartDto.setInfoUniqueUuid(versionDto.getInfoUniqueUuid());
        // 获取agent的信息
        List<TaskIpsAgentResultBean> taskIpsAgentResultBeanList = taskIpsMapper.getTaskIpsInfo(taskStartDto.getTaskIps(), taskStartDto.getScriptTaskId());

        // 查询脚本类型scriptType (sh、bat、perl、python、powershell)
        String scriptType = infoVersionService.getScriptTypeBySrcScriptUuid(taskStartDto.getSrcScriptUuid());

        // 查询本次任务的脚本运行参数
        TaskParams taskParams = new TaskParams();
        taskParams.setScriptTaskId(taskStartDto.getScriptTaskId());
        List<TaskParams> taskParamsList = taskParamsMapper.selectTaskParamsList(taskParams);

        List<String> params = new ArrayList<>();
        for (TaskParams taskParam : taskParamsList) {
            //加密参数解密之后使用
            if (Enums.ScriptParamType.CIPHER.getValue().equals(taskParam.getType())) {
                params.add(EncryptUtils.sm4Decrypt(taskParam.getValue()));
            } else {
                params.add(taskParam.getValue());
            }
        }

        if (null != taskIpsAgentResultBeanList && !taskIpsAgentResultBeanList.isEmpty()) {
            for (TaskIpsAgentResultBean taskIpsAgentResultBean : taskIpsAgentResultBeanList) {
                StartAgentParams startAgentParams = new StartAgentParams();
                startAgentParams.setExpectLastLine(versionDto.getExpectLastline());
                startAgentParams.setScriptType(scriptType);
                startAgentParams.setExpectType(versionDto.getExpectType());
                startAgentParams.setExecUserName(taskIpsAgentResultBean.getExecUserName());
                startAgentParams.setScriptAgentInfoId(taskIpsAgentResultBean.getScriptAgentInfoId());
                startAgentParams.setAgentIp(taskIpsAgentResultBean.getAgentIp());
                startAgentParams.setScriptTaskIpsId(taskIpsAgentResultBean.getScriptTaskIpsId());
                startAgentParams.setAgentPort(taskIpsAgentResultBean.getAgentPort());
                startAgentParams.setAgentParam(StringUtils.join(params, Constants.PARAM_VALUE));
                startAgentParams.setScriptWorkDir(null);
                startAgentParams.setDsId(0);
                startAgentParamsList.add(startAgentParams);
            }
        }
        return startAgentParamsList;
    }


    /**
     * 功能描述：将选定的agent标记为已执行状态，并将本次脚本任务的执行批次号加一进行更新
     * 同时返回任务执行相关的信息，包括勾选的AgentId集合以及脚本任务指定运行的agent总数
     *
     * @param taskStartDto 任务启动封装参数Dto
     * <AUTHOR>
     */
    @Override
    public StartAgentCommonParam updateBatchStartAndCheck(TaskStartDto taskStartDto) throws ScriptException {
        StartAgentCommonParam startAgentCommonParam = new StartAgentCommonParam();
        // 并发数
        int eachNum = taskStartDto.getEachNum();
        startAgentCommonParam.setEachNum(eachNum);
        // 脚本任务id
        Long scriptTaskId = taskStartDto.getScriptTaskId();

        // 按照选择执行（勾选的AgentId集合）
        String taskIpIds = StringUtils.join(taskStartDto.getTaskIps(), ",");
        try {

            if (Enums.ScriptType.SQL.getValue().equals(taskStartDto.getScriptType())) {
                startAgentCommonParam = getScriptTaskParams(taskStartDto);
            }
            // 查询脚本任务指定运行的agent总数
            int total = taskIpsMapper.getTotalAgentCountForTask(scriptTaskId);
            startAgentCommonParam.setTotal(total);

            startAgentCommonParam.setTaskIpIds(taskIpIds);

            // 获取当前任务最大执行批次
            int maxExecutionBatch = taskIpsMapper.getMaxOperIdForTask(scriptTaskId);

            // 将选定的agent标记为已执行状态，并将本次脚本任务的执行批次号加一进行更新。
            taskIpsMapper.updateIpsStatusAndIncrementOperId(taskStartDto.getTaskIps(), maxExecutionBatch + 1);

        } catch (Exception e) {
            throw new ScriptException("updateBatchStartAndCheck", e);
        }
        return startAgentCommonParam;

    }

    /**
     * 功能描述： 从TaskStartDto中获取脚本任务参数并返回Map
     *
     * @param taskStartDto 任务启动封装参数Dto
     * @return {@link Map }<{@link String }, {@link String }>  包含脚本任务参数的Map，键params和scriptInParam
     */
    private StartAgentCommonParam getScriptTaskParams(TaskStartDto taskStartDto) {
        StartAgentCommonParam startAgentCommonParam = new StartAgentCommonParam();
        StringBuilder params = new StringBuilder();
        StringBuilder scriptInParam = new StringBuilder();

        TaskParams taskParams = new TaskParams();
        taskParams.setScriptTaskId(taskStartDto.getScriptTaskId());
        List<TaskParams> taskParamsList = taskParamsMapper.selectTaskParamsList(taskParams);

        for (TaskParams taskParam : taskParamsList) {
            String type = taskParam.getType();
            String value = taskParam.getValue();

            // 提取paramType
            String paramType = getTypeFromTaskParam(type);
            // 忽略无效的paramType值
            if (paramType.isEmpty()) {
                continue;
            }
            String fir = determineFir(type);
            if ("0".equals(fir)) {
                handleScriptInParam(scriptInParam, paramType, value);
            }
            buildParamsString(params, fir, paramType, value);
        }
        startAgentCommonParam.setParams(params.toString().trim());
        startAgentCommonParam.setScriptInParam(scriptInParam.toString());

        return startAgentCommonParam;
    }

    /**
     * 功能描述：
     *
     * @param type 参数类型
     * @return {@link String }
     * <AUTHOR>
     */
    private String getTypeFromTaskParam(String type) {
        switch (type.substring(type.indexOf('-') + 1)) {
            case "string":
                return "string";
            case "int":
                return "int";
            case "float":
                return "float";
            default:
                return "";
        }
    }

    /**
     * 功能描述： 根据参数类型判断并返回相应的值。
     *
     * @param type 参数类型字符串
     * @return {@link String } 参数对应的值，如果不匹配则返回空字符串
     * <AUTHOR>
     */
    private String determineFir(String type) {
        if (type.startsWith(Enums.ParameterType.IN.getValue())) {
            return "0";
        } else if (type.startsWith(Enums.ParameterType.OUT.getValue())) {
            return "1";
        } else {
            return "";
        }
    }

    /**
     * 功能描述：处理脚本输入参数，拼接参数类型和值到指定的 StringBuilder 中。
     *
     * @param scriptInParam 用于存储脚本输入参数的 StringBuilder 对象
     * @param paramType     参数类型
     * @param value         参数类型
     * <AUTHOR>
     */
    private void handleScriptInParam(StringBuilder scriptInParam, String paramType, String value) {
        if (scriptInParam.length() == 0) {
            scriptInParam.append(value);
        } else {
            scriptInParam.append(" ").append(paramType);
        }
    }

    private void buildParamsString(StringBuilder params, String fir, String paramType, String value) {
        params.append(fir).append(":").append(paramType).append(":").append(value).append(" ");
    }

    /**
     * 查询当前用户可见的正在的运行任务列表
     *
     * @param pageNum        当前页
     * @param pageSize       分页条数
     * @param currentUser    用户
     * @param taskExecuteDto 查询条件的封装对象，包含页面查询条件属性（适用于"运行中"选项卡）
     * @return PageInfo<TaskExecuteDto>
     * <AUTHOR>
     */
    @Override
    public PageInfo<TaskExecuteDto> listRunningScriptTasks(TaskExecuteQueryDto taskExecuteDto, Integer pageNum, Integer pageSize, CurrentUser currentUser) {

        List<TaskExecuteBean> taskExecuteBeanList = new ArrayList<>();
        if (null != taskExecuteDto) {
            TaskExecuteBean taskExecuteBean = BeanUtils.copy(taskExecuteDto, TaskExecuteBean.class);

            // 设置权限相关信息
            categoryService.setCategoryPermission(taskExecuteBean, currentUser);

            PageMethod.startPage(pageNum, pageSize);
            taskExecuteBeanList = taskService.selectRunningScriptTasks(taskExecuteBean, currentUser);
            buildCategoryName(taskExecuteBeanList);
        }
        return PageDataUtil.toDtoPage(taskExecuteBeanList, TaskExecuteDto.class);
    }

    /**
     * 查询当前用户可见的正在运行任务列表
     *
     * @param taskExecuteQueryDto 查询条件的封装对象，包含页面查询条件属性（适用于"执行历史"选项卡）
     * @param pageNum             当前页
     * @param pageSize            分页条数
     * @param currentUser         用户
     * @return R<PageInfo < TaskExecuteDto>> 包含已完成任务信息的分页结果对象
     * <AUTHOR>
     */
    @Override
    public PageInfo<TaskExecuteDto> listCompleteScriptTasks(TaskExecuteQueryDto taskExecuteQueryDto, Integer pageNum, Integer pageSize, CurrentUser currentUser) {
        List<TaskExecuteBean> taskExecuteBeanList = new ArrayList<>();
        if (null != taskExecuteQueryDto) {
            TaskExecuteBean taskExecuteBean = BeanUtils.copy(taskExecuteQueryDto, TaskExecuteBean.class);

            // 设置权限相关信息
            categoryService.setCategoryPermission(taskExecuteBean, currentUser);

            PageMethod.startPage(pageNum, pageSize);
            taskExecuteBeanList = taskService.selectCompleteScriptTasks(taskExecuteBean, currentUser);
            buildCategoryName(taskExecuteBeanList);
        }
        return PageDataUtil.toDtoPage(taskExecuteBeanList, TaskExecuteDto.class);
    }

    private void buildCategoryName(List<TaskExecuteBean> taskExecuteBeanList) {
        for (TaskExecuteBean taskExecuteBean : taskExecuteBeanList) {
            if (null == taskExecuteBean.getCategoryId()) {
                taskExecuteBean.setScriptCategoryName(null);
            } else {
                String categoryName = categoryService.getCategoryFullPath(taskExecuteBean.getCategoryId());
                taskExecuteBean.setScriptCategoryName(categoryName);
            }

        }
    }

    /**
     * 获取agent实例数据
     * @param retryScriptInstanceApiDto 参数
     * @return agent实例对象
     */
    @Override
    public TaskRuntime getTaskRuntime(RetryScriptInstanceApiDto retryScriptInstanceApiDto){
        return taskRuntimeService.getTaskRuntime(retryScriptInstanceApiDto);
    }

    /**
     * 功能描述：重试agent
     *
     * @param id agent运行实例id
     * <AUTHOR>
     */
    @Override
    @Transactional(rollbackFor = ScriptException.class)
    public void retryScriptServiceShell(Long id, Long taskInstanceId, CurrentUser user) throws ScriptException {

        Integer count = taskRuntimeService.selectCountByTaskInstanceId(id);

        // 更新agent实例为运行中状态
        TaskRuntimeDto runtimeDto = new TaskRuntimeDto();
        runtimeDto.setId(id);
        runtimeDto.setState(Enums.TaskRuntimeState.RUNNING.getValue());
        runtimeDto.setRetry(true);
        taskRuntimeService.updateTaskRuntime(runtimeDto);

        // 更新实例状态
        TaskInstanceDto taskInstanceDto = new TaskInstanceDto();
        taskInstanceDto.setId(taskInstanceId);
        taskInstanceDto.setStatus(count > 0 ? Enums.TaskInstanceStatus.EXCEPTION.getValue() : Enums.TaskInstanceStatus.RUNNING.getValue());
        taskInstanceService.updateTaskInstance(taskInstanceDto);

        // 组装必要的信息，调用启动方法
        TaskRuntimeDto taskRuntimeDto = taskRuntimeService.selectTaskRuntimeById(id);

        TaskStartDto taskStartDto = new TaskStartDto();
        taskStartDto.setScriptTaskId(taskRuntimeDto.getScriptTaskId());
        taskStartDto.setSrcScriptUuid(taskRuntimeDto.getSrcScriptUuid());

        TaskDto taskDto = taskService.selectTaskById(taskRuntimeDto.getScriptTaskId());
        taskStartDto.setTaskName(taskDto.getTaskName());

        String scriptType = infoVersionService.getScriptTypeBySrcScriptUuid(taskRuntimeDto.getSrcScriptUuid());
        taskStartDto.setScriptType(scriptType);

        ScriptVersionDto infoVersionDto = infoVersionService.selectInfoVersionBySrcScriptUuid(taskRuntimeDto.getSrcScriptUuid());
        String infoUniqueUuid = infoVersionDto.getInfoUniqueUuid();
        taskStartDto.setInfoUniqueUuid(infoUniqueUuid);
        taskStartDto.setIscriptTaskInstanceId(taskInstanceId);

        // 声明一个 Long 类型的数组
        Long[] taskIpIds = new Long[1];
        // 将 taskRuntimeDto.getSysmAgentInfoId() 放入数组中
        taskIpIds[0] = taskRuntimeDto.getScriptTaskIpsId();

        taskStartDto.setTaskIps(taskIpIds);
        // 驱动
        taskStartDto.setDriveMode(Enums.DriverModel.SELECTIVE.getValue());

        taskStartDto.setRetry(true);

        taskStartDto.setRetryRuntimeId(id);
        //设置任务来源
        taskStartDto.setScriptTaskSource(taskDto.getScriptTaskSource());
        // 重试启动
        iTaskExecuteService.scriptTaskStart(taskStartDto, user);
    }

    /**
     * 功能描述：
     *
     * @param taskInstanceId 脚本任务运行实例Id
     * @param runTimeIds     agent运行实例id集合
     * <AUTHOR>
     */
    @Override
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public void scriptShellKill(Long taskInstanceId, Long[] runTimeIds) throws ScriptException {
        // 根据runTimeIds获取agent信息
        List<TaskRunTimeBindAgentBean> taskRunTimeBindAgentBeanList = new ArrayList<>();
        for (Long taskRuntimeId : runTimeIds) {
            TaskRunTimeBindAgentBean taskRunTimeBindAgentBean = taskRuntimeService.getBindAgentForTaskRuntime(taskRuntimeId);
            if (null == taskRunTimeBindAgentBean || null == taskRunTimeBindAgentBean.getTaskRuntimeId()) {
                logger.error("No data found for the taskRuntimeId {}", taskRuntimeId);
                throw new ScriptException("no.task.runtime.record.find");
            }
            taskRunTimeBindAgentBeanList.add(taskRunTimeBindAgentBean);

            // 状态计算
            TaskRuntimeDto taskRuntimeDto = taskRuntimeService.selectTaskRuntimeById(taskRuntimeId);
            if (isBoolean(taskRuntimeDto, taskRuntimeDto.getTaskInstanceId())) {
                return;
            }
            //如果此时agent实例为运行状态，需要触发计数器逻辑
            if(taskRuntimeDto.getState().equals(Enums.TaskRuntimeState.RUNNING.getValue())){
                taskRuntimeService.driverNextBatchAgent(taskRuntimeId);
            }
            // 更新状态
            taskRuntimeService.updateExecTimeAndState(taskRuntimeId.toString(), Enums.TaskRuntimeState.TERMINATED.getValue(), taskRuntimeDto);
        }
        //更新任务实例状态
        taskInstanceService.updateTaskInstanceState(taskInstanceId);
        List<AgentOperateDto> listAgentOperateDTO = new ArrayList<>();
        for (TaskRunTimeBindAgentBean taskRunTimeBindAgentBean : taskRunTimeBindAgentBeanList) {
            // 更新bizId
            String killKey = "agent-script-kill-" + taskRunTimeBindAgentBean.getTaskRuntimeId();
            TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
            taskRuntimeDto.setBizId(killKey);
            taskRuntimeDto.setId(taskRunTimeBindAgentBean.getTaskRuntimeId());
            taskRuntimeService.updateTaskRuntime(taskRuntimeDto);


            List<Object> params = new ArrayList<>();
            params.add(String.valueOf(taskRunTimeBindAgentBean.getTaskRuntimeId()));
            String jsonStr = convertObjectToJsonString(params);
            AgentOperateDto agentOperateDto = new AgentOperateDto();
            agentOperateDto.setBizId("agent-script-kill-" + taskRunTimeBindAgentBean.getTaskRuntimeId());
            agentOperateDto.setContent(jsonStr);
            // 权重 默认1
            agentOperateDto.setWeights(1L);
            // 优先级 默认5
            agentOperateDto.setPriority("5");
            agentOperateDto.setRpcMethod("IEAIAgent.stopShellCmdProcess");
            //*************的agentid
            AgentOptionDto agentOptionDto = new AgentOptionDto();
            agentOptionDto.setAgentHost(taskRunTimeBindAgentBean.getAgentIp());
            agentOptionDto.setAgentPort(Long.parseLong(taskRunTimeBindAgentBean.getAgentPort()));
            agentOperateDto.setAgentOptionDto(agentOptionDto);

            agentOperateDto.setContentFormatter("scriptTaskContentFormatter");
            agentOperateDto.setDynamicResourcesDTO(null);
            listAgentOperateDTO.add(agentOperateDto);
        }
        //事务提交后发送agentGateway
        TransactionSyncUtil.execute(agentOperateApi::send,null,null,listAgentOperateDTO);
    }

    /**
     * 脚本测试
     *
     * @param testExecutionDto 脚本测试封装的参数信息
     * @return Long
     */
    @Override
    @Transactional(rollbackFor = ScriptException.class)
    public Long scriptTestExecution(ScriptTestExecutionDto testExecutionDto, CurrentUser user) throws ScriptException {
        // 存储 任务申请一套
        ScriptInfoDto infoDto = infoService.selectInfoById(testExecutionDto.getInfoId());
        if (null == infoDto || infoDto.getId() <= 0) {
            logger.error("no data retrieve the main version information , infoId:{}", testExecutionDto.getInfoId());
            throw new ScriptException("no.data.find.for.script.info");
        }

        // 组装Task信息
        TaskDto taskInfo = getTaskDto(testExecutionDto, infoDto);
        // 任务提交审核，存储脚本任务基础信息
        taskService.insertTask(taskInfo);

        ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        List<AttachmentDto> attachmentDtoList = testExecutionDto.getScriptTempAttachments();
        //设置执行人
        scriptExecAuditDto.setExecuser(testExecutionDto.getExecuser());
        scriptExecAuditDto.setScriptTempAttachments(attachmentDtoList);

        scriptExecAuditDto.setChosedResGroups(testExecutionDto.getChosedResGroups());

        scriptExecAuditDto.setChosedAgentUsers(testExecutionDto.getChosedAgentUsers());

        scriptExecAuditDto.setResGroupFlag(testExecutionDto.getResGroupFlag());
        try (SqlSession sqlSession = factory.openSession(ExecutorType.BATCH, false)) {

            // 存储附件信息
            taskAttachmentService.saveTaskAttachement(scriptExecAuditDto, taskInfo, sqlSession);

            // 存储任务与资源组关系
            taskGroupsService.saveTaskGroups(scriptExecAuditDto, taskInfo, sqlSession);

            // 获取资源组绑定的服务器没写
            List<TaskGroupsDto> taskGroupsDtoList = scriptExecAuditDto.getChosedResGroups();
            Boolean  resGroupFlag = scriptExecAuditDto.getResGroupFlag();
            if (resGroupFlag != null && resGroupFlag) {
                List<AgentInfoDto> agentInfoDtoList = taskGroupsService.retrieveUniqueAgentInfoList(taskGroupsDtoList);
                scriptExecAuditDto.setChosedAgentUsers(agentInfoDtoList);
            }



            // 存储agent信息
            taskIpsService.saveTaskIps(scriptExecAuditDto, taskInfo, sqlSession);
            // 存储参数信息
            saveTaskParams(testExecutionDto.getParams(), taskInfo, sqlSession);
        }

        // 调用执行
        TaskStartDto taskStartDto = new TaskStartDto();
        taskStartDto.setScriptTaskId(taskInfo.getId());
        taskStartDto.setSrcScriptUuid(testExecutionDto.getSrcScriptUuid());
        taskStartDto.setTaskName(taskInfo.getTaskName());
        taskStartDto.setEachNum(100);
        //设置任务来源
        taskStartDto.setScriptTaskSource(taskInfo.getScriptTaskSource());
        String scriptType = infoVersionService.getScriptTypeBySrcScriptUuid(testExecutionDto.getSrcScriptUuid());
        taskStartDto.setScriptType(scriptType);

        ScriptVersionDto infoVersionDto = infoVersionService.selectInfoVersionBySrcScriptUuid(testExecutionDto.getSrcScriptUuid());
        String infoUniqueUuid = infoVersionDto.getInfoUniqueUuid();
        taskStartDto.setInfoUniqueUuid(infoUniqueUuid);

        // 驱动 用agent一次全执行的方式
        taskStartDto.setDriveMode(Enums.DriverModel.BATCH_EXEC.getValue());
        Long[] allIpIds = getBindAllTaskIpsInfo(taskStartDto.getScriptTaskId());
        if (allIpIds != null) {
            taskStartDto.setTaskIps(allIpIds);
            taskStartDto.setEachNum(allIpIds.length);
        }

        return iTaskExecuteService.scriptTaskStart(taskStartDto, user);
    }

    /**
     * 组织脚本任务对象信息
     * @param testExecutionDto  脚本测试信息dto
     * @param infoDto   脚本基本信息Dto
     * @return 脚本任务对象信息
     */
    private static TaskDto getTaskDto(ScriptTestExecutionDto testExecutionDto, ScriptInfoDto infoDto) {
        TaskDto taskInfo = new TaskDto();
        taskInfo.setTaskName(infoDto.getScriptName() + "_脚本测试");
        taskInfo.setEachNum(100);
        taskInfo.setTaskScheduler(Enums.TaskScheduler.TRIGGER.getValue());
        taskInfo.setPublishDesc("脚本测试");
        taskInfo.setType(Enums.Type.NORMAL.getValue());
        taskInfo.setDriveMode(Enums.DriverModel.BATCH_EXEC.getValue());
        taskInfo.setStartType(Enums.BusinessType.SCRIPT_SERVICE.getValue());
        taskInfo.setSrcScriptUuid(testExecutionDto.getSrcScriptUuid());
        // 待执行状态，具备执行能力
        taskInfo.setReadyToExecute(1);
        taskInfo.setTimeout(-1L);
        // 脚本任务来源 1：任务申请  2：脚本测试
        taskInfo.setScriptTaskSource(Enums.TaskSource.SCRIPT_TESTING.getValue());
        return taskInfo;
    }

    /**
     * 启动脚本任务
     *
     * @param taskStartDto 任务启动封装参数Dto
     * @return Long 脚本任务运行实例Id
     */
    @Override
    @Transactional(rollbackFor = ScriptException.class)
    public Long scriptTaskStartFormApply(TaskStartDto taskStartDto, CurrentUser user) throws ScriptException {

        try {
            // 页面触发执行前，删除先试点后执行 redisKey
            redisTemplate.delete("script:checkBefore:" + taskStartDto.getScriptTaskId());
        } catch (Exception e) {
            logger.error("删除先试点后执行redisKey {} 异常", "script:checkBefore:" + taskStartDto.getScriptTaskId(), e);
        }
        return iTaskExecuteService.scriptTaskStart(taskStartDto, user);
    }

    /**
     * 功能描述：略过agent
     *
     * @param taskInstanceId 脚本任务运行实例Id
     * @param runTimeIds     agent运行实例id集合
     * @throws ScriptException 抛出自定义异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void skipScriptShell(Long taskInstanceId, Long[] runTimeIds) throws ScriptException {
        // 更新状态
        for (Long taskRuntimeId : runTimeIds) {
            TaskRuntimeDto taskRuntimeDto = taskRuntimeService.selectTaskRuntimeById(taskRuntimeId);
            if (isBoolean(taskRuntimeDto, taskRuntimeDto.getTaskInstanceId())) {
                return;
            }
            //如果实例状态是运行中，则需要触发计数器逻辑，调用驱动下一批次方法
            if(taskRuntimeDto.getState().equals(Enums.TaskRuntimeState.RUNNING.getValue())){
                taskRuntimeService.driverNextBatchAgent(taskRuntimeId);
            }
            // 更新agent实例状态
            taskRuntimeService.updateExecTimeAndState(taskRuntimeId.toString(), Enums.TaskRuntimeState.SKIP.getValue(), taskRuntimeDto);
        }
        //更新任务实例状态
        taskInstanceService.updateTaskInstanceState(taskInstanceId);
    }

    /**
     * 终止agent实例
     * @param scriptStopShellDto 参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void stopScriptInstanceShell(ScriptStopShellDto scriptStopShellDto) throws ScriptException{
        //判断参数，agent地址与agent实例id至少存在一个值；任务id与任务实例id至少存在一个值
        if(!checkStopParams(scriptStopShellDto)){
            throw new ScriptException("script.stop.agent.instance.params.error");
        }
        //为任务实例id赋值
        Long taskInstanceId;
        if(Objects.nonNull(scriptStopShellDto.getTaskInstanceId())){
            taskInstanceId = scriptStopShellDto.getTaskInstanceId();
        }else{
            //根据任务id获取任务实例id
            TaskInstanceDto taskInstance = taskInstanceService.getTaskInstanceByTaskInfoId(scriptStopShellDto.getTaskId());
            if(Objects.nonNull(taskInstance)){
                taskInstanceId = taskInstance.getId();
            }else{
                throw new ScriptException("script.stop.agent.get.instanceId.error");
            }
        }
        //为agent实例id赋值
        Long [] runTimeIds;
        if(Objects.nonNull(scriptStopShellDto.getRunTimeIds()) && scriptStopShellDto.getRunTimeIds().length > 0){
            runTimeIds = scriptStopShellDto.getRunTimeIds();
        }else{
            //根据agent地址获取对应的agent实例id
            List<Long> runTimeIdList = taskRuntimeService.selectRuntimeIdsByAgentAddressAndTaskInstanceId(Arrays.asList(scriptStopShellDto.getAgentAddress()), taskInstanceId);
            if(Objects.nonNull(runTimeIdList) && !runTimeIdList.isEmpty()){
                runTimeIds = runTimeIdList.toArray(new Long[0]);
            }else{
                throw new ScriptException("script.stop.agent.get.runtimeId.error");
            }
        }
        iTaskExecuteService.scriptShellKill(taskInstanceId, runTimeIds);
    }

    /**
     * 校验终止参数是否正确
     * @param scriptStopShellDto 参数信息
     * @return 参数是否正确标识
     */
    private boolean checkStopParams(ScriptStopShellDto scriptStopShellDto){
        //判断参数，agent地址与agent实例id至少存在一个值；任务id与任务实例id至少存在一个值
        String[] agentAddress = scriptStopShellDto.getAgentAddress();
        Long[] runTimeIds = scriptStopShellDto.getRunTimeIds();
        Long taskId = scriptStopShellDto.getTaskId();
        Long taskInstanceId = scriptStopShellDto.getTaskInstanceId();
        if((Objects.isNull(agentAddress) || agentAddress.length == 0)
            && (Objects.isNull(runTimeIds) || runTimeIds.length == 0)){
            return false;
        }
        if(Objects.isNull(taskId) && Objects.isNull(taskInstanceId)){
            return false;
        }
        return true;
    }


    /**
     * 终止脚本服务化任务
     * @param taskInstanceId 任务实例Id
     */
    @Override
    public StopScriptTasksApiDto stopScriptTaskByTaskInstanceId(List<Long> taskInstanceId) {
        //定义返回值dto与参数
        StopScriptTasksApiDto stopScriptTasksApiDto = new StopScriptTasksApiDto();
        List<Long> successList = new ArrayList<>();
        List<Long> failList = new ArrayList<>();
        //遍历终止任务，将taskInstanceId分别放入不同的list中
        for(Long instanceId : taskInstanceId){
            try {
                iTaskExecuteService.stopTask(new Long[]{instanceId});
                successList.add(instanceId);
            } catch (ScriptException e) {
                failList.add(instanceId);
            }
        }
        //dto赋值并返回结果
        stopScriptTasksApiDto.setSuccessTaskInstanceIdList(successList);
        stopScriptTasksApiDto.setFailTaskInstanceIdList(failList);
        return stopScriptTasksApiDto;
    }

    /**
     * 其它模块的脚本任务终止功能
     * @param stopCallerScriptTaskApiDtos 参数
     * @return 终止成功的agent信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> stopScriptTaskByCallerTaskIdAndAgent(List<RetryScriptInstanceApiDto> stopCallerScriptTaskApiDtos) throws ScriptException{
        List<String> successList = new ArrayList<>();
        //根据入参（调用方任务id、agentIp、agent端口查询agent实例信息）
        for(RetryScriptInstanceApiDto retryScriptInstanceApiDto : stopCallerScriptTaskApiDtos){
            TaskRuntime taskRuntime = iTaskExecuteService.getTaskRuntime(retryScriptInstanceApiDto);
            scriptShellKill(taskRuntime.getTaskInstanceId(), new Long[]{taskRuntime.getId()});
            successList.add(taskRuntime.getAgentIp()+":"+taskRuntime.getAgentPort());
        }
        return successList;
    }

    /**
     * 功能描述：任务终止（取消）
     *
     * @param taskInstanceId 脚本任务运行实例Id
     * @throws ScriptException 抛出自定义通知异常
     */

    /**
     * 终止任务
     * @param taskInstanceIds 任务实例id集合
     * @throws ScriptException 抛出自定义通知异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void stopTask(Long [] taskInstanceIds) throws ScriptException {
        //批量获取实例，如果返回的id集合为空，说明任务已经不存在了，则不再终止相关任务
        List<Long> taskInstanceList = taskInstanceService.selectTaskInstanceIdsById(taskInstanceIds);
        if (taskInstanceList.isEmpty()) {
            logger.error("taskInstanceList is empty taskInstanceIds is:{} ", ObjectUtils.notEqual(taskInstanceIds,null) ? Arrays.toString(taskInstanceIds) : "");
            throw new ScriptException("no.task.instance.record.find");
        }
        Long[] stopTaskInstanceIds = taskInstanceList.toArray(new Long[0]);
        // 发送agent执行终止
        // 根据任务实例id，获取这个任务下需要终止的agent实例Id集合
        for(Long taskInstanceId : stopTaskInstanceIds){
            //根据任务实例id获取对应的所有agent实例对象
            List<TaskRuntimeDto> taskRuntimeList = taskRuntimeService.getTaskRuntimeByInstanceId(taskInstanceId);
            if(taskRuntimeList != null && !taskRuntimeList.isEmpty()){
                //获取所有agent实例id
                Long[] runTimeIds = taskRuntimeList.stream().map(TaskRuntimeDto::getId).toArray(Long[]::new);
                iTaskExecuteService.scriptShellKill(taskInstanceId, runTimeIds);
                //给itsm推送返回结果，任务完成推送itsm
                itsmScriptTaskResultPush.pushMessage(taskRuntimeList.get(0).getScriptTaskId(),"【stop】任务已被强行终止",true);
            }
        }
    }

    /**
     * 判断是否是定时执行的任务
     *
     * @param scriptTaskId 脚本任务Id
     */
    private boolean isTimeTask(Long scriptTaskId) throws ScriptException {
        boolean isTimeTask = false;
        // 判断任务是否是定时任务
        TaskDto taskDto = taskService.selectTaskById(scriptTaskId);
        if (null == taskDto) {
            logger.error("脚本任务不存在！");
            throw new ScriptException("no.task.data.find");
        }
        Integer taskScheduler = taskDto.getTaskScheduler();
        if (taskScheduler.intValue() == Enums.TaskScheduler.PERIODIC.getValue() || taskScheduler.intValue() == Enums.TaskScheduler.TIMED.getValue()) {
            isTimeTask = true;
        }
        return isTimeTask;
    }

    /**
     * 功能描述：agent实例终止
     *
     * @param taskInstanceId 脚本任务运行实例Id
     * @param runTimeIds     agent运行实例id集合
     */
    @Override
    @Transactional(rollbackFor = ScriptException.class)
    public void scriptShellKillByRunTimeIds(Long taskInstanceId, Long[] runTimeIds) throws ScriptException {
        scriptShellKill(taskInstanceId, runTimeIds);
    }

    /**
     * 白名单任务启动
     *
     * @param user          用户
     * @param taskInfo      脚本任务信息
     * @param srcScriptUuid 脚本版本uuid
     * @return Long
     */
    @Override
    @Transactional(rollbackFor = ScriptException.class)
    public Long scriptWhiteTaskStart(CurrentUser user, TaskDto taskInfo, String srcScriptUuid) throws ScriptException {
        TaskStartDto taskStartDto = new TaskStartDto();
        taskStartDto.setScriptTaskId(taskInfo.getId());
        taskStartDto.setSrcScriptUuid(srcScriptUuid);
        taskStartDto.setTaskName(taskInfo.getTaskName());
        String scriptType = infoVersionService.getScriptTypeBySrcScriptUuid(srcScriptUuid);
        taskStartDto.setScriptType(scriptType);
        ScriptVersionDto infoVersionDto = infoVersionService.selectInfoVersionBySrcScriptUuid(srcScriptUuid);
        String infoUniqueUuid = infoVersionDto.getInfoUniqueUuid();
        taskStartDto.setInfoUniqueUuid(infoUniqueUuid);
        taskStartDto.setDriveMode(taskInfo.getDriveMode());
        taskStartDto.setStartType(taskInfo.getStartType());
        taskStartDto.setCallerTaskId(taskInfo.getCallerTaskId());
        taskStartDto.setEachNum(taskInfo.getEachNum());
        return iTaskExecuteService.scriptTaskStart(taskStartDto, user);
    }

    /**
     * 待执行页面取消任务
     *
     * @param scriptTaskId 脚本任务Id
     * <AUTHOR>
     */
    @Override
    public void cancelTask(Long scriptTaskId) throws ScriptException {
        boolean isTimeTask = isTimeTask(scriptTaskId);
        boolean stopFlag = true;
        Long scheduleId = null;
        if (isTimeTask) {
            logger.info("timetask stop job start:");
            TaskScheduleDto taskScheduleDto = taskScheduleService.selectTaskScheduleByTaskId(scriptTaskId);
            logger.info("scheduleId:{}", taskScheduleDto.getScheduleId());
            stopFlag = jobOperateService.stopJob(taskScheduleDto.getScheduleId().intValue());
            scheduleId = taskScheduleDto.getScheduleId();
        }
        if (Boolean.TRUE.equals(stopFlag)) {
            logger.info("timetask stop job success.");
            TaskDto taskDto = new TaskDto();
            taskDto.setId(scriptTaskId);
            taskDto.setReadyToExecute(Enums.ReadyToExecute.CANCELED.getCode());
            taskService.updateTask(taskDto);
        }
        logger.info("cancelTask stopFlag:{},scriptTaskId:{},scheduleId{}", stopFlag, scriptTaskId, scheduleId);

    }

    private boolean isBoolean(TaskRuntimeDto dto, Long id) {
        return null == dto || null == id;
    }

    /**
     * 存储脚本任务参数
     *
     * @param taskParamsDtoList 脚本任务参数集合
     * @param taskInfo          脚本任务对象
     * @param sqlSession        sqlSession
     */
    private void saveTaskParams(List<TaskParamsDto> taskParamsDtoList, TaskDto taskInfo, SqlSession sqlSession) throws ScriptException {
        if (!taskParamsDtoList.isEmpty()) {
            taskParamsDtoList.forEach(dto -> {
                dto.setId(null);
                dto.setScriptTaskId(taskInfo.getId());
                dto.setStartType(Enums.BusinessType.SCRIPT_SERVICE.getValue());
            });
            getTaskParamsMapperSession(taskParamsDtoList, sqlSession, batchDataUtil, logger);
        }
    }

    static void getTaskParamsMapperSession(List<TaskParamsDto> taskParamsDtoList, SqlSession sqlSession, BatchDataUtil batchDataUtil, Logger logger) throws ScriptException {
        TaskParamsMapper taskParamsMapperSession = sqlSession.getMapper(TaskParamsMapper.class);
        try {
            batchDataUtil.batchData(TaskParamsMapper.class, BeanUtils.copy(taskParamsDtoList, TaskParams.class), TaskParams.class, "insertTaskParams", sqlSession, taskParamsMapperSession);
        } catch (ScriptException e) {
            logger.error("saveTaskParams error:", e);
            throw new ScriptException("save.task.params.error");
        }
    }


    @Override
    public void exportExcel(List<Long> ids, HttpServletResponse response) {
        logger.info("定时任务维护开始导出");
        //组织数据
        CurrentUser currentUser = CurrentUserUtil.getCurrentUser();
        List<TaskExecuteBean> taskExecuteBeanList = taskMapper.selectTaskByIds(ids,currentUser);
        //组织分类数据
        buildCategoryName(taskExecuteBeanList);
        //组织agent信息
        buildAgent(taskExecuteBeanList);
        //调用导出
        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMddHHmmss");
        String flag = sf.format(new Date());
        String fileName = "定时任务维护" + flag;
        String sheetName = "定时任务维护";

        for (TaskExecuteBean taskExecuteBean : taskExecuteBeanList) {
            if(taskExecuteBean.getReadyToExecute() == null){
                taskExecuteBean.setReadyToExecute(0);
            }
        }
        List<TimeTaskExportBean> timeTaskExportBeanList = BeanUtils.copy(taskExecuteBeanList, TimeTaskExportBean.class);
        logger.info("查询到的 timeTaskExportBeanList 列表: {}", timeTaskExportBeanList);

        ExcelUtil.writeExcel(response,timeTaskExportBeanList,fileName,sheetName, TimeTaskExportBean.class);

    }

    /**
     * 构建agent信息（定时任务维护导出使用）
     * @param taskExecuteBeanList   列表
     */
    private void buildAgent(List<TaskExecuteBean> taskExecuteBeanList){
        for (TaskExecuteBean taskExecuteBean : taskExecuteBeanList) {
            StringBuilder stringBuilder = new StringBuilder();
            Long scriptTaskId = taskExecuteBean.getScriptTaskId();
            List<AgentInfoDto> agentInfoDtoList = agentInfoService.selectAgentInfoByServiceId(null, scriptTaskId);
            for (AgentInfoDto agentInfoDto : agentInfoDtoList) {
                String agentIp = agentInfoDto.getAgentIp();
                Integer agentPort = agentInfoDto.getAgentPort();
                String agent = agentIp + ":" + agentPort;
                stringBuilder.append(agent);
                stringBuilder.append(",");
            }
            String substring= "";
            if(stringBuilder.length() > 1) {
                substring = stringBuilder.substring(0, stringBuilder.length() - 1);
            }
            taskExecuteBean.setAgent(substring);
        }

    }

    @Override
    public void exportAgentHisExcel(List<Long> taskInstanceIds, HttpServletResponse response) throws IOException {
        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("任务监控执行历史", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

        List<TaskHisAgentExcelDto> taskHisExcelExport = taskMapper.getTaskHisExcelExport(taskInstanceIds);
        // 根据枚举值处理状态
        for (int i = 0; i < taskHisExcelExport.size(); i++) {
            String agentState = taskHisExcelExport.get(i).getAgentState();
            if (StringUtils.isNotEmpty(agentState)) {
                for (Enums.TaskRuntimeState enumConstant : Enums.TaskRuntimeState.class.getEnumConstants()) {
                    if (Integer.parseInt(agentState) == enumConstant.getValue()) {
                        taskHisExcelExport.get(i).setAgentState(enumConstant.getDescription());
                        break;
                    }
                }
            }
        }

        EasyExcel.write(response.getOutputStream()).autoCloseStream(true).sheet("agent执行历史").head(TaskHisAgentExcelDto.class).doWrite(taskHisExcelExport);
    }
}
