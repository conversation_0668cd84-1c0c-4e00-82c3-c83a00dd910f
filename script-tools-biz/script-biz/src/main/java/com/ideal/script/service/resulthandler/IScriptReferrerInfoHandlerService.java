package com.ideal.script.service.resulthandler;

import com.ideal.script.exception.ScriptException;

/**
 * 引用脚本情况mq消费业务处理类
 * <AUTHOR>
 */

public interface IScriptReferrerInfoHandlerService {

    /**
     * 处理各个模块引用脚本信息数据
     * @param message 引用脚本新增、删除数据内容（script-referrer-info）
     * @throws ScriptException 抛出自定义通知异常
     */
    void scriptReferrerInfoHandler(String message) throws ScriptException;
}
