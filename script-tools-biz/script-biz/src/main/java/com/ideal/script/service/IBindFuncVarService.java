package com.ideal.script.service;

import com.github.pagehelper.PageInfo;
import com.ideal.script.dto.BindFuncVarDto;
/**
 * Service接口
 * 
 * <AUTHOR>
 */
 public interface IBindFuncVarService
{
    /**
     * 查询
     * 
     * @param id 主键
     * @return 
     */
     BindFuncVarDto selectBindFuncVarById(Long id);

    /**
     * 查询列表
     * 
     * @param bindFuncVarDto 
     * @param pageNum 
     * @param pageSize 
     * @return 集合
     */
     PageInfo<BindFuncVarDto> selectBindFuncVarList(BindFuncVarDto bindFuncVarDto, int pageNum, int pageSize);

    /**
     * 新增
     *
     * @param bindFuncVarDto 
     */
     void insertBindFuncVar(BindFuncVarDto bindFuncVarDto);

    /**
     * 修改
     *
     * @param bindFuncVarDto 
     */
     void updateBindFuncVar(BindFuncVarDto bindFuncVarDto);

    /**
     * 批量删除
     *
     * @param ids 需要删除的主键集合
     */
     void deleteBindFuncVarByIds(Long[] ids);

    /**
     * 删除信息
     * 
     * @param id 主键
     * @return 结果
     */
     int deleteBindFuncVarById(Long id);
}
