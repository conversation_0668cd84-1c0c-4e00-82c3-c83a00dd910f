package com.ideal.script.service.impl;

import cn.idev.excel.EasyExcel;
import cn.idev.excel.FastExcelFactory;
import cn.idev.excel.read.builder.ExcelReaderBuilder;
import cn.idev.excel.read.builder.ExcelReaderSheetBuilder;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.approval.dto.ApprovalNode;
import com.ideal.approval.dto.AuditorApiDto;
import com.ideal.approval.dto.DoubleCheckApiDto;
import com.ideal.approval.dto.ResultApiDto;
import com.ideal.approval.dto.SmsMessageDto;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.sc.util.CurrentUserUtil;
import com.ideal.sc.util.IpUtils;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.common.constant.enums.ExceptionMessage;
import com.ideal.script.common.util.CronDateUtils;
import com.ideal.script.common.util.StateConverter;
import com.ideal.script.config.ScriptBusinessConfig;
import com.ideal.script.dto.AttachmentDto;
import com.ideal.script.dto.RetryScriptInstanceApiDto;
import com.ideal.script.dto.ScriptInfoDto;
import com.ideal.script.dto.ScriptInfoQueryDto;
import com.ideal.script.dto.ScriptTaskApplyAgentApiDto;
import com.ideal.script.dto.ScriptTaskApplyApiDto;
import com.ideal.script.dto.ScriptVersionDto;
import com.ideal.script.exception.ScheduleJobOperateException;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.exception.SystemException;
import com.ideal.script.mapper.InfoMapper;
import com.ideal.script.mapper.InfoVersionMapper;
import com.ideal.script.mapper.ScriptVersionShareMapper;
import com.ideal.script.model.bean.AuditResultBean;
import com.ideal.script.model.bean.CategoryOrgBean;
import com.ideal.script.model.bean.CategoryRoleBean;
import com.ideal.script.model.bean.OrgBean;
import com.ideal.script.model.bean.TaskApplyBean;
import com.ideal.script.model.bean.UserBean;
import com.ideal.script.model.dto.AgentInfoDto;
import com.ideal.script.model.dto.AuditRelationDto;
import com.ideal.script.model.dto.InfoVersionTextDto;
import com.ideal.script.model.dto.ParameterDto;
import com.ideal.script.model.dto.ScheduleJobTaskDto;
import com.ideal.script.model.dto.ScriptAuditDetailDto;
import com.ideal.script.model.dto.ScriptExecAuditDto;
import com.ideal.script.model.dto.TaskApplyDto;
import com.ideal.script.model.dto.TaskApplyQueryDto;
import com.ideal.script.model.dto.TaskAttachmentDto;
import com.ideal.script.model.dto.TaskDto;
import com.ideal.script.model.dto.TaskGroupsDto;
import com.ideal.script.model.dto.TaskParamsDto;
import com.ideal.script.model.dto.TaskScheduleDto;
import com.ideal.script.model.dto.interaction.agent.UserInfoDto;
import com.ideal.script.model.entity.Info;
import com.ideal.script.model.entity.TaskRuntime;
import com.ideal.script.remotecall.RemoteCall;
import com.ideal.script.service.AuditSource;
import com.ideal.script.service.IAgentInfoService;
import com.ideal.script.service.IAttachmentService;
import com.ideal.script.service.IAuditRelationService;
import com.ideal.script.service.ICategoryService;
import com.ideal.script.service.IInfoService;
import com.ideal.script.service.IInfoVersionService;
import com.ideal.script.service.IInfoVersionTextService;
import com.ideal.script.service.IMyScriptService;
import com.ideal.script.service.ITaskApplyService;
import com.ideal.script.service.ITaskAttachmentService;
import com.ideal.script.service.ITaskExecuteService;
import com.ideal.script.service.ITaskGroupsService;
import com.ideal.script.service.ITaskInstanceService;
import com.ideal.script.service.ITaskIpsService;
import com.ideal.script.service.ITaskParamsService;
import com.ideal.script.service.ITaskScheduleService;
import com.ideal.script.service.ITaskService;
import com.ideal.script.service.JobOperateService;
import com.ideal.script.service.impl.builders.MyScriptServiceScripts;
import com.ideal.system.api.IAgentInfo;
import com.ideal.system.api.IUserInfo;
import com.ideal.system.common.component.model.CurrentUser;
import com.ideal.system.dto.AgentGroupRoleQueryBean;
import com.ideal.system.dto.PermissionUserInfoApiDto;
import com.ideal.system.dto.RoleApiDto;
import com.ideal.system.dto.ServicePermissionApiQueryDto;
import com.ideal.system.dto.SystemAgentInfoApiDto;
import com.ideal.system.dto.UserInfoApiDto;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * 任务申请业务类
 *
 * <AUTHOR>
 */
@Service
public class TaskApplyServiceImpl implements ITaskApplyService {
    private static final Logger logger = LoggerFactory.getLogger(TaskApplyServiceImpl.class);
    private final InfoVersionMapper infoVersionMapper;

    private final ITaskService taskService;

    private final IInfoVersionService infoVersionService;

    private final IInfoVersionTextService infoVersionTextService;
    private final IAuditRelationService auditRelationService;

    private final SqlSessionFactory factory;

    private final IAgentInfoService agentInfoService;
    private final RemoteCall remoteCall;


    private final ICategoryService categoryService;

    private final ITaskAttachmentService taskAttachmentService;

    private final ITaskParamsService taskParamsService;

    private final ITaskGroupsService taskGroupsService;

    private final ITaskExecuteService taskExecuteService;

    private final ITaskIpsService taskIpsService;

    private final JobOperateService jobOperateService;

    private final ITaskScheduleService taskScheduleService;

    private final IUserInfo userInfoApi;

    private final IAttachmentService attachmentService;

    private final ITaskInstanceService taskInstanceService;

    private final ITaskApplyService taskApplyService;

    private final IMyScriptService iMyScriptService;

    private final AuditSource auditSource;
    private final RedisTemplate<String, String> redisTemplate;

    private final ScriptBusinessConfig scriptBusinessConfig;

    private final IUserInfo iUserInfoApi;
    private final IAgentInfo iAgentInfo;

    private final IInfoService infoService;

    private final ScriptVersionShareMapper scriptVersionShareMapper;

    private final InfoMapper infoMapper;
    private final MyScriptServiceScripts myScriptServiceScripts;
    /**
     * 双人复核服务主键
     */
    private final ThreadLocal<Long> taskApplyAuditIdThreadLocal = new ThreadLocal<>();

    @Value("${ideal.customer.name:ideal}")
    private String customerName;

    @Autowired
    public TaskApplyServiceImpl(InfoMapper infoMapper, ScriptVersionShareMapper scriptVersionShareMapper,
                                InfoVersionMapper infoVersionMapper, ITaskService taskService,
                                IInfoVersionService infoVersionService, IInfoVersionTextService infoVersionTextService,
                                IAuditRelationService auditRelationService, SqlSessionFactory factory,
                                IAgentInfoService agentInfoService, RemoteCall remoteCall, ICategoryService categoryService,
                                ITaskAttachmentService taskAttachmentService, ITaskParamsService taskParamsService,
                                ITaskGroupsService taskGroupsService, ITaskExecuteService taskExecuteService,
                                ITaskIpsService taskIpsService, JobOperateService jobOperateService,
                                ITaskScheduleService taskScheduleService, IUserInfo userInfoApi,
                                IAttachmentService attachmentService, ITaskInstanceService taskInstanceService,
                                @Lazy ITaskApplyService taskApplyService, @Lazy IMyScriptService iMyScriptService,
                                @Qualifier("taskApplySource") AuditSource auditSource, RedisTemplate<String, String> redisTemplate,
                                ScriptBusinessConfig scriptBusinessConfig, IUserInfo iUserInfoApi, IAgentInfo iAgentInfo, IInfoService infoService, @Lazy MyScriptServiceScripts myScriptServiceScripts) {
        this.infoMapper = infoMapper;
        this.scriptVersionShareMapper = scriptVersionShareMapper;
        this.infoVersionMapper = infoVersionMapper;
        this.taskService = taskService;
        this.infoVersionService = infoVersionService;
        this.infoVersionTextService = infoVersionTextService;
        this.auditRelationService = auditRelationService;
        this.factory = factory;
        this.agentInfoService = agentInfoService;
        this.remoteCall = remoteCall;
        this.categoryService = categoryService;
        this.taskAttachmentService = taskAttachmentService;
        this.taskParamsService = taskParamsService;
        this.taskGroupsService = taskGroupsService;
        this.taskExecuteService = taskExecuteService;
        this.taskIpsService = taskIpsService;
        this.jobOperateService = jobOperateService;
        this.taskScheduleService = taskScheduleService;
        this.userInfoApi = userInfoApi;
        this.attachmentService = attachmentService;
        this.taskInstanceService = taskInstanceService;
        this.taskApplyService = taskApplyService;
        this.iMyScriptService = iMyScriptService;
        this.auditSource = auditSource;
        this.redisTemplate = redisTemplate;
        this.scriptBusinessConfig = scriptBusinessConfig;
        this.iUserInfoApi = iUserInfoApi;
        this.iAgentInfo = iAgentInfo;
        this.infoService = infoService;
        this.myScriptServiceScripts = myScriptServiceScripts;
    }

    /**
     * 任务申请列表
     *
     * @param taskApplyQueryDto 任务申请Dto
     * @param pageNum           当前页第几页
     * @param pageSize          每页条数
     * @param user              用户
     * @return PageInfo<TaskApplyDto>
     * <AUTHOR>
     */
    @Override
    public PageInfo<TaskApplyDto> selectTaskApplyList(TaskApplyQueryDto taskApplyQueryDto, Integer pageNum, Integer pageSize, CurrentUser user) {

        List<TaskApplyBean> taskApplyBeanList = new ArrayList<>();
        if (null != taskApplyQueryDto) {
            TaskApplyBean taskApplyBean = BeanUtils.copy(taskApplyQueryDto, TaskApplyBean.class);

            // 设置权限相关信息
            categoryService.setCategoryPermission(taskApplyBean, user);


            //获取任务申请周期
            Integer scriptTaskApplyCycle = scriptBusinessConfig.getScriptTaskApplyCycle();
            if(scriptTaskApplyCycle > 30){
                //限制最大不能超过30
                scriptTaskApplyCycle = 30;
            }
            // 获取当前时间
            LocalDateTime currentDateTime = LocalDateTime.now();
            // 在当前时间上减去周期
            LocalDateTime preDateTime = currentDateTime.minusDays(scriptTaskApplyCycle);
            taskApplyBean.setStartUser(user.getFullName());
            taskApplyBean.setTaskCreatePreTime(Timestamp.valueOf(preDateTime));
            //获取共享的脚本（共享给自己的、共享给所有人的、共享给所在部门的，并且共享脚本的所属用户不能跟当前登陆人在同一部门）
            taskApplyBean.setUserId(user.getId());
            taskApplyBean.setDepartment(user.getOrgCode());
            //如果是值班任务申请，将部门置空，查询的sql不会加入部门条件
            if(taskApplyQueryDto.isDutyApply()){
                taskApplyBean.setSysOrgCode("");
            }

            PageMethod.startPage(pageNum, pageSize);
            //获取任务申请数据
            taskApplyBeanList = infoVersionMapper.selectTaskApplyList(taskApplyBean);
            //遍历数据，为分类字段赋值
            for (TaskApplyBean taskApplyBean1 : taskApplyBeanList) {
                if (null == taskApplyBean1.getCategoryId()) {
                    taskApplyBean1.setScriptCategoryName(null);
                } else {
                    String categoryName = categoryService.getCategoryFullPath(taskApplyBean1.getCategoryId());
                    taskApplyBean1.setScriptCategoryName(categoryName);
                }
            }
        }
        return PageDataUtil.toDtoPage(taskApplyBeanList, TaskApplyDto.class);
    }

    /**
     * 任务申请-提交任务审核，dubbo接口使用
     *
     * @param scriptExecAuditDto 任务申请提交审核Dto
     * @return int
     * <AUTHOR>
     */
    @Override
    @Transactional(rollbackFor = ScriptException.class)
    public Long scriptExecAuditing(ScriptExecAuditDto scriptExecAuditDto, CurrentUser user, AuditSource auditSource) throws ScriptException {
        // 数据预处理
        auditSource.preHandle(scriptExecAuditDto);

        TaskDto taskInfo = scriptExecAuditDto.getTaskInfo();
        if (taskInfo == null) {
            logger.error("Cannot proceed with script execution auditing due to empty taskInfo carried during task application submission!");
            throw new ScriptException("error.apply.script.taskInfoEmpty");
        }
        //如果agent并发数为空，设置成配置的值
        taskInfo.setEachNum(Optional.ofNullable(taskInfo.getEachNum()).orElse(scriptBusinessConfig.getTaskEachNumLimit()));

        taskInfo.setScriptTaskSource(Enums.TaskSource.TASK_APPLICATION.getValue());

        // 存储脚本任务基础信息
        boolean isWhite = auditSource.isInWhiteList(scriptExecAuditDto);
        if (isWhite || isTimeTask(scriptExecAuditDto.getTaskInfo())) {
            // 白名单、定时、周期执行人是发起人
            scriptExecAuditDto.getTaskInfo().setStartUser(user.getFullName());
        }
        String srcScriptUuid = saveTask(scriptExecAuditDto, taskInfo, auditSource);
        // 重新审核 参数、agent绑定taskId
        auditSource.bindTaskId(scriptExecAuditDto);
        // 重新审核 预处理临时附件
        auditSource.preHandleAttachment(scriptExecAuditDto.getTaskInfo().getId());

        // 保存任务信息的各项数据，包括存储附件信息、任务与资源组关系、agent信息、参数信息等。
        saveTaskInformation(scriptExecAuditDto, taskInfo);

        //任务来源
        taskInfo.setStartType(scriptExecAuditDto.getStartType());
        //其它任务（其它模块）任务id
        taskInfo.setCallerTaskId(scriptExecAuditDto.getCallerTaskId());

        //如果invokeId不为空，说明为是itsm发起的任务，此时需要将invokeId保存到redis
        if(StringUtils.isNotBlank(scriptExecAuditDto.getInvokeId())){
            redisTemplate.opsForValue().set("itsm_task_"+taskInfo.getId(),scriptExecAuditDto.getInvokeId());
        }

        if (isWhite) {
            // 如果是白名单方式、驱动模式（2 ：全部执行）
            TaskDto taskDto = taskService.selectTaskById(taskInfo.getId());
            if (null == taskDto.getId()) {
                logger.error("not find script task based on the given taskId!");
                throw new ScriptException("error.script.task.not.find");
            }
//            taskInfo.setDriveMode(Enums.DriverModel.BATCH_EXEC.getValue());
            // 判断是否是定时任务
            if (isTimeTask(taskDto)) {
                // 调用定时任务服务
                insertTaskSchedule(taskDto);
            } else {
                // 如果不是定时任务类型的白名单，不对接审核服务，直接启动。
                taskExecuteService.scriptWhiteTaskStart(user, taskInfo, srcScriptUuid);
            }
        } else {
            if (scriptExecAuditDto.getCheckBefore() != null) {
                if (scriptExecAuditDto.getCheckBefore()) {
                    redisTemplate.opsForValue().set("script:checkBefore:" + taskInfo.getId(), "true");
                } else {
                    redisTemplate.delete("script:checkBefore:" + taskInfo.getId());
                }
            }

            // 如果不是白名单，走双人复核服务
            //存储双人复核与脚本服务化关系
            Long auditRelationId = auditSource.getRelationId(scriptExecAuditDto, taskInfo, srcScriptUuid);
            // 提交双人复核
            taskSubmit(scriptExecAuditDto, taskInfo, auditRelationId);
        }
        return taskInfo.getId();
    }


    /**
     * 脚本任务申请web接口使用
     * @param scriptExecAuditDto 脚本任务提交审核Dto
     * @param user 用户
     * @param auditSource 审核源
     * @return 任务表id、双人复核id
     * @throws ScriptException 脚本异常
     */
    @Override
    public Map<String, Long> scriptExecAuditingForWeb(ScriptExecAuditDto scriptExecAuditDto, CurrentUser user, AuditSource auditSource) throws ScriptException {
        HashMap<String, Long> res = new HashMap<>();
        try {
            //调用原有任务申请方法
            Long taskId = taskApplyService.scriptExecAuditing(scriptExecAuditDto, user, auditSource);
            //任务表id
            res.put("taskId", taskId);
            //双人复核服务id
            res.put("auditId", taskApplyAuditIdThreadLocal.get());
        } finally {
            taskApplyAuditIdThreadLocal.remove();
        }
        return res;
    }
    private boolean isTimeTask(TaskDto taskDto) {
        return (null != taskDto && null != taskDto.getTaskScheduler()) && (Enums.TaskScheduler.TIMED.getValue().equals(taskDto.getTaskScheduler()) || Enums.TaskScheduler.PERIODIC.getValue().equals(taskDto.getTaskScheduler()));
    }

    /**
     * 根据审核关系id查询脚本的分类id
     * @param relationId 审核关系数据
     * @return 脚本分类id
     */
    @Override
    public Long getScriptInfoByAuditRelationId(Long relationId){
        return infoVersionMapper.getScriptInfoByAuditRelationId(relationId);
    }

    /**
     * 保存任务信息的各项数据。
     * 包括存储附件信息、任务与资源组关系、agent信息、参数信息等。
     *
     * @param scriptExecAuditDto 脚本任务提交审核Dto
     * @param taskInfo           脚本任务对象
     */

    private void saveTaskInformation(ScriptExecAuditDto scriptExecAuditDto, TaskDto taskInfo) throws ScriptException {
        try (SqlSession sqlSession = factory.openSession(ExecutorType.BATCH, false)) {

            // 存储附件信息
            taskAttachmentService.saveTaskAttachement(scriptExecAuditDto, taskInfo, sqlSession);

            // 存储任务与资源组关系
            taskGroupsService.saveTaskGroups(scriptExecAuditDto, taskInfo, sqlSession);

            validIsEmpty(scriptExecAuditDto);
            // 获取资源组绑定的服务器
            if (null != scriptExecAuditDto.getResGroupFlag() && scriptExecAuditDto.getResGroupFlag()) {
                List<TaskGroupsDto> taskGroupsDtoList = scriptExecAuditDto.getChosedResGroups();
                List<AgentInfoDto> agentInfoDtoList = taskGroupsService.retrieveUniqueAgentInfoList(taskGroupsDtoList);
                scriptExecAuditDto.setChosedAgentUsers(agentInfoDtoList);
            }

            // 存储agent信息
            taskIpsService.saveTaskIps(scriptExecAuditDto, taskInfo, sqlSession);

            // 校验参数
            validParams(scriptExecAuditDto);

            // 存储参数信息
            taskParamsService.saveTaskParams(scriptExecAuditDto, taskInfo, sqlSession);
        }
    }

    /**
     * 校验参数是否匹配正则规则
     *
     * @param scriptExecAuditDto 含有参数集合
     */
    public void validParams(ScriptExecAuditDto scriptExecAuditDto) throws ScriptException {
        List<ParameterDto> parameterDtoList = scriptExecAuditDto.getParams();
        if (null != parameterDtoList) {
            String message = taskParamsService.validateParameterList(parameterDtoList);
            if (null != message) {
                throw new ScriptException(message);
            }
        }
    }

    /**
     * 根据服务器是否来自资源组标识校验
     *
     * @param scriptExecAuditDto 脚本任务提交审核Dto
     */
    public void validIsEmpty(ScriptExecAuditDto scriptExecAuditDto) throws ScriptException {
        if (null != scriptExecAuditDto.getResGroupFlag() && !scriptExecAuditDto.getResGroupFlag()) {
            if (null == scriptExecAuditDto.getChosedAgentUsers()) {
                throw new ScriptException("chosed.agent.users.empty");
            }
            if (scriptExecAuditDto.getChosedAgentUsers().isEmpty()) {
                throw new ScriptException("chosed.agent.users.empty");
            }
        }
        if (null != scriptExecAuditDto.getResGroupFlag() && scriptExecAuditDto.getResGroupFlag()) {
            if (null == scriptExecAuditDto.getChosedResGroups()) {
                throw new ScriptException("error.script.resGroupsEmpty");
            }
            if (scriptExecAuditDto.getChosedResGroups().isEmpty()) {
                throw new ScriptException("error.script.resGroupsEmpty");
            }
        }
    }


    /**
     * 双人复核结果接收，接收审核结果，对脚本任务进行后续操作
     *
     * @param auditResultBean 审核结果
     * @throws ScriptException 抛出自定义通知异常
     */
    @Override

    public void receiveAuditResult(AuditResultBean auditResultBean) throws ScriptException, SystemException {

        try {
            // 更新任务表状态
            AuditRelationDto auditRelationDto = new AuditRelationDto();
            auditRelationDto.setId(auditResultBean.getAuditRelationId());
            auditRelationDto.setState(auditResultBean.getState());
            auditRelationDto.setApprWorkitemId(auditResultBean.getApprWorkitemId());
            auditRelationService.updateAuditRelation(auditRelationDto);

            if (Enums.AuditState.REJECTED.getValue().equals(auditResultBean.getState())) {
                // 打回状态不需要执行下面执行逻辑
                return;
            }

            Long scriptTaskId = auditRelationService.selectAuditRelationById(auditRelationDto.getId()).getScriptTaskId();
            // 脚本任务状态更改成待执行
            TaskDto taskDto = new TaskDto();
            taskDto.setId(scriptTaskId);
            taskDto.setReadyToExecute(1);
            taskService.updateTask(taskDto);


            TaskDto taskInfo = taskService.selectTaskById(taskDto.getId());
            if (null == taskInfo.getId()) {
                logger.error("not find script task based on the given taskId!");
                throw new ScriptException("error.script.task.not.find");
            }

            if (isTimeTask(taskInfo)) {
                taskInfo.setDriveMode(Enums.DriverModel.IGNORE_ERROR_BATCH_EXEC.getValue());
                // 调用定时任务服务
                insertTaskSchedule(taskInfo);
            }

        } catch (ScriptException e) {
            logger.error("receiveAuditResult exception:",e);
            throw new ScriptException("error.receive.audit.information");
        } catch (Exception e) {
            throw new SystemException("Error occurred while updating task and script status in receiveAuditResult:", e);
        }

    }

    /**
     * 脚本类型为定时任务的任务调度
     *
     * @param taskDto 任务Dto
     */
    public void insertTaskSchedule(TaskDto taskDto) {

        ScheduleJobTaskDto scheduleJobTaskDto = new ScheduleJobTaskDto();
        scheduleJobTaskDto.setTaskId(taskDto.getId());
        scheduleJobTaskDto.setTaskName(taskDto.getTaskName());
        scheduleJobTaskDto.setCron(taskDto.getTaskCron());
        scheduleJobTaskDto.setCreateName(taskDto.getCreatorName());

        scheduleJobTaskDto.setCreatorId(taskDto.getCreatorId());
        scheduleJobTaskDto.setScheduleJobId(1L);
        scheduleJobTaskDto.setStrategyType(taskDto.getTaskScheduler());
        try {
            Integer xxJobId = jobOperateService.createAndStartJob(scheduleJobTaskDto);
            TaskScheduleDto taskScheduleDto = new TaskScheduleDto();
            taskScheduleDto.setScheduleId(xxJobId.longValue());
            taskScheduleDto.setScriptTaskId(taskDto.getId());
            taskScheduleDto.setState(Enums.TaskScheduleEnum.TASK_INIT.getValue());
            taskScheduleService.insertTaskSchedule(taskScheduleDto);

            // 更新任务待执行为空，定时任务的数据重新出一个菜单。

        } catch (ScheduleJobOperateException e) {
            logger.error("createAndStartJob occur  ScheduleJobOperateException: taskId:{}", taskDto.getId(), e);
        }
    }


    /**
     * 任务申请时调用双人复核服务（dubbo通信）
     *
     * <AUTHOR>
     */
    private void taskSubmit(ScriptExecAuditDto scriptExecAuditDto, TaskDto taskInfo, Long auditRelationId) throws ScriptException {
        // 调用双人复核服务
        DoubleCheckApiDto doubleCheckDto = new DoubleCheckApiDto();

        doubleCheckDto.setServiceId(auditRelationId);
        doubleCheckDto.setTaskSubject(taskInfo.getTaskName() + "任务通过任务申请功能发起双人复核操作！");
        doubleCheckDto.setDetailUrl("taskApplyAudit");
        CurrentUser currentUser = CurrentUserUtil.getCurrentUser();

        doubleCheckDto.setOriginatorName(currentUser.getFullName());
        doubleCheckDto.setOriginatorId(currentUser.getId());

        List<AuditorApiDto> auditorApiDtoList = new ArrayList<>();
        AuditorApiDto auditorApiDto = new AuditorApiDto();
        auditorApiDto.setAuditorId(scriptExecAuditDto.getAuditUserId());
        auditorApiDto.setAuditorName(scriptExecAuditDto.getAuditUser());
        auditorApiDtoList.add(auditorApiDto);

        ApprovalNode node=new ApprovalNode();
        List <ApprovalNode> nodes = new ArrayList<>();
        node.setNodeIndex(1);
        node.setIsAutoAssign(false);
        node.setAuditorApiDtoList(auditorApiDtoList);
        nodes.add(node);
        doubleCheckDto.setApprovalNodes(nodes);

        // 这个后续换成"script"
        doubleCheckDto.setItemType("script");
        doubleCheckDto.setCallbackUrl("script");
        //是否支持短信审核标识
        doubleCheckDto.setExternalEnabled(scriptBusinessConfig.isTaskApplicationMessageReviewFlag());

        String uuid = "script_" + auditRelationId;

        logger.info("Sending data for double check: serviceId={}, taskSubject={}, originatorName={}, originatorId={}, " +
                        "auditorName={}, auditorId={}, callbackUrl={}, uuid={}",
                taskInfo.getId(), doubleCheckDto.getTaskSubject(), currentUser.getLoginName(), currentUser.getId(),
                scriptExecAuditDto.getAuditUser(), scriptExecAuditDto.getAuditUserId(), doubleCheckDto.getCallbackUrl(), uuid);

        try {
            ResultApiDto resultApiDto = remoteCall.applyForDoubleCheck(uuid, doubleCheckDto);
            if (null != resultApiDto) {
                logger.info("Double check submission result: taskId={}, success={}, message={}",
                        resultApiDto.getTaskId(), resultApiDto.isSuccess(), resultApiDto.getMessage());
                if(!resultApiDto.isSuccess()){
                    logger.error("task audit failure!");
                    throw new ScriptException(ExceptionMessage.DOUBLE_CHECK_SUBMISSION_ERROR.getValue());
                }
                //设置双人复核服务返回的id，第三方接口调用需要这个id进行审核
                taskApplyAuditIdThreadLocal.set(resultApiDto.getTaskId());
            }else {
                logger.error("task audit failure result is null!");
                throw new ScriptException(ExceptionMessage.DOUBLE_CHECK_SUBMISSION_ERROR.getValue());
            }

        } catch (Exception e) {
            Thread.currentThread().interrupt();
            logger.error("Error occurred during double check submission:", e);
            throw new ScriptException(ExceptionMessage.DOUBLE_CHECK_SUBMISSION_ERROR.getValue());
        }

    }

    /**
     * 短信审核相关战术整理
     * @param scriptExecAuditDto 脚本信息
     * @param taskInfo 任务信息
     */
    private void taskApplyForMessageAudit(ScriptExecAuditDto scriptExecAuditDto, TaskDto taskInfo , DoubleCheckApiDto doubleCheckDto) throws ScriptException{
        try{
            //查询脚本名，一级分类，风险级别，发起人电话号
            //任务发起人电话号
            CurrentUser currentUser = CurrentUserUtil.getCurrentUser();
            List<Long> userIdList = new ArrayList<>();
            userIdList.add(currentUser.getId());
            List<UserInfoApiDto> userInfoList = userInfoApi.getUserInfoList(userIdList);
            String telephone = userInfoList.get(0).getTelephone();
            String startUser = userInfoList.get(0).getFullName();
            //脚本名
            ScriptInfoQueryDto scriptInfoQueryDto = new ScriptInfoQueryDto();
            scriptInfoQueryDto.setSrcScriptUuid(taskInfo.getSrcScriptUuid());
            ScriptInfoDto scriptDetail = iMyScriptService.getScriptDetail(scriptInfoQueryDto);
            String category = scriptDetail.getCategoryPath().split("\\\\")[0];
            //获取风险等级中文标识
            String levelStr = getLevelStr(scriptDetail);
            // 获取当前的日期和时间
            LocalDateTime now = LocalDateTime.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String formattedDateTime = now.format(formatter);
            //给审核人发送审核信息
            //短信审核信息，与王雷确认，v9目前没有超时审核机制，暂时仍然写死30分钟
            String auditContent = "任务名称："+taskInfo.getTaskName()+"，脚本名称："+scriptDetail.getScriptName()+"，一级分类："+category+"，风险级别："+levelStr+
                    "，发起人："+startUser+"，发起时间："+formattedDateTime+"，执行描述："+taskInfo.getPublishDesc()+"，" +
                    "确认审批通过请在{timeout}分钟内回复短信【auto+1+{workitemId}+10】。";
            //审批通过后给审核人发的消息
            String afterTaskAuditAuditorContent = "【邮储银行】脚本服务化执行任务【"+taskInfo.getTaskName()+"】授权成功！";
            //审核通过后给任务发起人发送消息
            String afterTaskAuditStarterContent = "【邮储银行】脚本服务化执行任务【"+taskInfo.getTaskName()+"】,"+scriptExecAuditDto.getAuditUser()+"已授权,请操作！";
            //为参数dto赋值
            doubleCheckDto.setCompleteMessageToOriginator(afterTaskAuditStarterContent);
            List<SmsMessageDto> smsMessageDtoList = new ArrayList<>();
            SmsMessageDto smsMessageDto = new SmsMessageDto();
            smsMessageDto.setMessage(auditContent);
            smsMessageDto.setCompleteMessageToAudit(afterTaskAuditAuditorContent);
            smsMessageDtoList.add(smsMessageDto);
            //修改是否发送标识
            doubleCheckDto.getApprovalNodes().get(0).getAuditorApiDtoList().get(0).setSendSms(true);
            doubleCheckDto.getApprovalNodes().get(0).getAuditorApiDtoList().get(0).setSmsMessageDtoList(smsMessageDtoList);
        }catch (Exception e){
            logger.error(ExceptionMessage.BUILD_TASK_APPLY_MESSAGE_ERROR.getValue(),e);
            throw new ScriptException(ExceptionMessage.BUILD_TASK_APPLY_MESSAGE_ERROR.getValue());
        }
    }

    /**
     * 根据level标识获取中文风险等级
     * @param scriptDetail 脚本详情信息
     * @return 返回风险等级中文标注
     */
    private String getLevelStr(ScriptInfoDto scriptDetail) {
        Integer level = scriptDetail.getScriptVersionDto().getLevel();
        String levelStr = "";
        if(Enums.ScriptLevel.HIGH_RISK.getValue() == level.intValue()){
            levelStr = "高级风险";
        }
        if(Enums.ScriptLevel.MEDIUM_RISK.getValue() == level.intValue()){
            levelStr = "中级风险";
        }
        if(Enums.ScriptLevel.LOW_RISK.getValue() == level.intValue()){
            levelStr = "低级风险";
        }
        return levelStr;
    }

    /**
     * 任务提交审核，存储脚本任务基础信息
     *
     * @param scriptExecAuditDto 任务申请提交审核Dto
     * @param taskInfo           任务申请提交任务时的脚本任务对象
     * @return String
     * <AUTHOR>
     */
    private String saveTask(ScriptExecAuditDto scriptExecAuditDto, TaskDto taskInfo, AuditSource auditSource) throws ScriptException {
        if (null != taskInfo && null != taskInfo.getTaskScheduler()) {

            if (taskInfo.getTaskScheduler().equals(Enums.TaskScheduler.PERIODIC.getValue())) {
                // 执行周期
                String taskCron = taskInfo.getTaskCron();
                // 校验执行周期表达式填写是否正确
                boolean isCronOk = CronDateUtils.isValid(taskCron);
                if (!isCronOk) {
                    String cronErrMsg = "执行时间填写错误(提示：0 */1 * * * ?)";
                    logger.error("执行时间填写错误(提示：0 */1 * * * ?)");
                    throw new ScriptException(cronErrMsg);
                }
            } else if (taskInfo.getTaskScheduler().equals(Enums.TaskScheduler.TIMED.getValue())) {
                taskInfo.setTaskCron(CronDateUtils.getCron(taskInfo.getTaskTime()));
            }
        }
        // 脚本版本id
        Long scriptInfoVersionId = scriptExecAuditDto.getScriptInfoVersionId();
        ScriptVersionDto infoVersionDto = infoVersionService.selectInfoVersionById(scriptInfoVersionId);
        String srcScriptUuid = "";
        if (taskInfo != null && null != infoVersionDto) {
            srcScriptUuid = infoVersionDto.getSrcScriptUuid();
            taskInfo.setSrcScriptUuid(srcScriptUuid);
            taskInfo.setStartType(scriptExecAuditDto.getStartType());
            boolean isWhite = auditSource.isInWhiteList(scriptExecAuditDto);
            if (isWhite) {
                taskInfo.setType(Enums.Type.WHITE_LIST.getValue());
                // 脚本任务来源 1：任务申请  2：脚本测试
                taskInfo.setScriptTaskSource(Enums.TaskSource.TASK_APPLICATION.getValue());
                // 待执行状态，具备执行能力
                taskInfo.setReadyToExecute(1);
                taskInfo.setTimeout(scriptExecAuditDto.getTaskInfo().getTimeout());
                // 白名单
                taskInfo.setType(Enums.Type.WHITE_LIST.getValue());
            } else {
                // 普通
                taskInfo.setType(Enums.Type.NORMAL.getValue());
            }
        }
        // 任务提交审核，存储脚本任务基础信息
        auditSource.saveOrUpdateTask(scriptExecAuditDto, taskInfo);
        logger.info("taskId: {}", (taskInfo != null ? taskInfo.getId() : null));
        return srcScriptUuid;
    }

    /**
     * 双人复核-业务详情
     *
     * @param serviceId 业务id，双人复核表主键
     * @return ScriptExecAuditDto
     */
    @Override
    public ScriptAuditDetailDto getAuditDetail(Long serviceId, Long taskId) throws ScriptException {
        ScriptAuditDetailDto scriptAuditDetailDto = new ScriptAuditDetailDto();

        // 脚本任务信息
        TaskDto taskDto = buildTaskInfo(serviceId, taskId);
        scriptAuditDetailDto.setTaskDto(taskDto);

        // 任务绑定附件信息
        List<TaskAttachmentDto> taskAttachmentDtoList = buildTaskAttachment(serviceId,taskId);
        scriptAuditDetailDto.setTaskAttachmentDtoList(taskAttachmentDtoList);

        // 任务绑定参数信息
        List<TaskParamsDto> taskParamsDtoList = buildTaskParam(serviceId,taskId);
        scriptAuditDetailDto.setTaskParamsDtoList(taskParamsDtoList);

        // 任务绑定设备组信息
        List<TaskGroupsDto> taskGroupsDtoList = buildTaskGroup(serviceId,taskId);
        scriptAuditDetailDto.setTaskGroupsDtoList(taskGroupsDtoList);

        // 设备组已被选择时，不再查询服务器数据
        if (taskGroupsDtoList.isEmpty()) {
            // 任务绑定服务器信息
            List<AgentInfoDto> agentInfoDtoList = buildAgent(serviceId, taskId);
            scriptAuditDetailDto.setAgentInfoDtoList(agentInfoDtoList);
        }

        // 获取脚本基本信息
        ScriptInfoQueryDto scriptInfoQueryDto = new ScriptInfoQueryDto();
        scriptInfoQueryDto.setSrcScriptUuid(taskDto.getSrcScriptUuid());
        scriptAuditDetailDto.setScriptInfoDto(iMyScriptService.getScriptDetail(scriptInfoQueryDto));

        // 获取脚本审核关系
        scriptAuditDetailDto.setAuditRelationDto(auditRelationService.selectAuditRelationByTaskId(taskDto.getId()));

        // 构建绑定内容
        scriptAuditDetailDto.setScriptType(infoVersionService.getScriptTypeBySrcScriptUuid(taskDto.getSrcScriptUuid()));
        InfoVersionTextDto infoVersionTextDto = infoVersionTextService.selectInfoVersionTextByScriptUuid(taskDto.getSrcScriptUuid());
        scriptAuditDetailDto.setContent(infoVersionTextDto.getContent());
        return scriptAuditDetailDto;
    }

    /**
     * 任务绑定资源组信息
     *
     * @param serviceId 业务主键
     * @return List<TaskGroupsDto>
     */
    public List<TaskGroupsDto> buildTaskGroup(Long serviceId,Long taskId) {
        return taskGroupsService.selectTaskGroupsByServiceId(serviceId,taskId);
    }

    /**
     * 构建脚本任务绑定服务器信息
     *
     * @param serviceId 业务主键
     * @return List<AgentInfoDto>
     */

    public List<AgentInfoDto> buildAgent(Long serviceId,Long taskId) {
        return agentInfoService.selectAgentInfoByServiceId(serviceId,taskId);
    }

    /**
     * 构建脚本任务绑定参数信息
     *
     * @param serviceId 业务主键-双人复核表主键
     * @return List<TaskParamsDto>
     */

    public List<TaskParamsDto> buildTaskParam(Long serviceId,Long taskId) {
        return taskParamsService.selectTaskParamsByServiceId(serviceId,taskId);
    }

    /**
     * 构建任务附件信息
     *
     * @param serviceId 业务主键-双人复核主键
     * @return List<TaskAttachmentDto>
     */

    public List<TaskAttachmentDto> buildTaskAttachment(Long serviceId,Long taskId) {
        return taskAttachmentService.selectTaskAttachmentNoContentByServiceId(serviceId,taskId);
    }

    /**
     * 构脚本任务信息
     *
     * @param serviceId 业务主键-双人复核主键Id
     * @return TaskDto 返回脚本任务信息
     */
    public TaskDto buildTaskInfo(Long serviceId, Long taskId) {
        TaskDto taskDto = taskService.selectTaskByServiceId(serviceId, taskId);
        taskDto.setCheckBefore(redisTemplate.hasKey("script:checkBefore:" + taskDto.getId()));
        return taskDto;
    }

    /**
     * 接收审核结果并处理
     *
     * @param doubleCheckApiDto 审核结果
     * <AUTHOR>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doubleCheckCallBack(DoubleCheckApiDto doubleCheckApiDto) throws ScriptException, SystemException {
        logger.info("Acceptance of audit results:{}", doubleCheckApiDto);

        if (null != doubleCheckApiDto) {
            logger.info("Audit information：{}", doubleCheckApiDto);
            AuditResultBean auditResultBean = new AuditResultBean();
            auditResultBean.setAuditRelationId(doubleCheckApiDto.getServiceId());
            auditResultBean.setApprWorkitemId(doubleCheckApiDto.getId());
            auditResultBean.setState(StateConverter.convertStatus(doubleCheckApiDto.getApprovalState()));
            receiveAuditResult(auditResultBean);
        } else {
            logger.error("Audit information is empty");
        }
        logger.info("Receive audit results success");

    }

    /**
     * 根据服务权限码获取这个服务权限码所属角色下的所有人信息
     *
     * @param permissionCode 服务权限码
     * @return {@link List }<{@link UserInfoDto }>
     * <AUTHOR>
     */
    @Override
    public List<UserInfoDto> queryUserInfoListByPermissionCode(String permissionCode) {
        List<UserInfoApiDto> userInfoApiDtoList = userInfoApi.queryUserInfoListByPermissionCode(permissionCode);
        return BeanUtils.copy(userInfoApiDtoList, UserInfoDto.class);
    }

    /**
     * 根据userId获取user信息
     * @param userId    用户id
     * @return  用户信息列表
     */
    @Override
    public List<UserInfoDto> getUserByUserId(Long userId) {
        return BeanUtils.copy(userInfoApi.getUserInfoList(Collections.singletonList(userId)), UserInfoDto.class);
    }

    /**
     * 组装dto参数
     * @param scriptTaskApplyDto    脚本信息查询Dto
     * @return  脚本任务提交审核信息
     */
    private ScriptExecAuditDto getScriptExecDto(ScriptTaskApplyApiDto scriptTaskApplyDto,CurrentUser user) {
        //获取入参dto参数
        String scriptUuid = scriptTaskApplyDto.getScriptUuid();
        List<ScriptTaskApplyAgentApiDto> chosedAgentUsers = scriptTaskApplyDto.getChosedAgentUsers();
        //根据用户id查询用户
        List<UserInfoApiDto> userInfoList = userInfoApi.getUserInfoList(Collections.singletonList(user.getId()));
        UserInfoApiDto userInfoApiDto = new UserInfoApiDto();
        if(null != userInfoList && !userInfoList.isEmpty()){
            userInfoApiDto = userInfoList.get(0);
        }
        //根据uuid查询脚本信息
        ScriptVersionDto infoVersionDto = infoVersionService.selectInfoVersionBySrcScriptUuid(scriptUuid);
        //组装agent信息
        List<AgentInfoDto> agentInfoDtoList = new ArrayList<>();
        for(ScriptTaskApplyAgentApiDto scriptTaskApplyAgentApiDto : chosedAgentUsers){
            AgentInfoDto agentInfoDto = new AgentInfoDto();
            agentInfoDto.setAgentPort(Integer.valueOf(scriptTaskApplyAgentApiDto.getAgentPort()));
            agentInfoDto.setAgentIp(scriptTaskApplyAgentApiDto.getAgentIp());
            agentInfoDtoList.add(agentInfoDto);
        }
        //组装附件信息
        List<AttachmentDto> attachmentDtoList = attachmentService.getAttachmentByUuid(scriptUuid);
        //组装taskInfo信息
        TaskDto taskDto = new TaskDto();
        taskDto.setExecUser(scriptTaskApplyDto.getExecuser());
        taskDto.setTaskName(scriptTaskApplyDto.getTaskName());
        taskDto.setTimeout(-1L);
        //组装参数
        List<ParameterDto> parameterDtoList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(scriptTaskApplyDto.getParams())){
            scriptTaskApplyDto.getParams().forEach(parameterValidationDto -> {
                parameterValidationDto.setId(0L);
                parameterDtoList.add(BeanUtils.copy(parameterValidationDto, ParameterDto.class));
            });
        }
        //定义Dto
        ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();
        //agent信息
        scriptExecAuditDto.setChosedAgentUsers(agentInfoDtoList);
        //附件信息
        scriptExecAuditDto.setScriptTempAttachments(attachmentDtoList);
        //taskInfo信息
        scriptExecAuditDto.setTaskInfo(taskDto);
        //任务审核人
        scriptExecAuditDto.setAuditUser(userInfoApiDto.getFullName());
        //任务审核人id
        scriptExecAuditDto.setAuditUserId(userInfoApiDto.getId());
        //执行用户
        scriptExecAuditDto.setExecuser(scriptTaskApplyDto.getExecuser());
        //参数
        scriptExecAuditDto.setParams(parameterDtoList);
        //服务器来源（服务器是否来自资源组，false否，true是，false的时候使用chosedAgentUsers，true的时候使用chosedResGroups）
        scriptExecAuditDto.setResGroupFlag(false);
        //脚本id
        scriptExecAuditDto.setScriptInfoVersionId(infoVersionDto.getId());
        //任务来源
        scriptExecAuditDto.setStartType(scriptTaskApplyDto.getStartType());
        //调用方id
        if(ObjectUtils.notEqual(scriptTaskApplyDto.getCallerTaskId(),null)
                && 2L == scriptTaskApplyDto.getStartType()){
            scriptExecAuditDto.setCallerTaskId(scriptTaskApplyDto.getCallerTaskId());
        }

        return scriptExecAuditDto;
    }

    /**
     * 任务申请接口方法
     * @param scriptTaskApplyDto 任务申请信息
     * @return  Long
     */
    @Override
    public Long scriptTaskApplyApi(ScriptTaskApplyApiDto scriptTaskApplyDto) throws ScriptException {
        Long iid = 0L;
        try {
            CurrentUser user = CurrentUserUtil.getCurrentUser();
            //组装启动数据
            ScriptExecAuditDto scriptExecAuditDto = getScriptExecDto(scriptTaskApplyDto,user);
            // 根据taskInfoId查询taskInstanceId当前版本只有白名单脚本会调到这里
            // 后续可以改为通过脚本uuid判断脚本是否为风险脚本，风险脚本返回taskInfoId，白名单返回TaskInstanceId
            Long scriptExecAuditing = taskApplyService.scriptExecAuditing(scriptExecAuditDto, user, auditSource);
            iid = taskInstanceService.selectTaskInstanceByTaskId(scriptExecAuditing).getId();
        } catch (ScriptException e){
            String errorMessage = "scriptExecAuditing error: " + e.getMessage();
            logger.error(errorMessage, e);
            throw new ScriptException(errorMessage);
        }
        return iid;
    }

    /**
     * agent实例任务重试
     * @param retryScriptInstanceApiDto 参数
     * @throws ScriptException 脚本服务化异常
     */
    @Override
    public void reTryScriptTask(List<RetryScriptInstanceApiDto> retryScriptInstanceApiDto) throws ScriptException {
        for(RetryScriptInstanceApiDto retryScriptInstanceApiDto1 : retryScriptInstanceApiDto){
            //根据其它模块任务id、agentIp、agent端口查询对应的agent实例
            TaskRuntime taskRuntime = taskExecuteService.getTaskRuntime(retryScriptInstanceApiDto1);
            taskExecuteService.retryScriptServiceShell(taskRuntime.getId(), taskRuntime.getTaskInstanceId(), BeanUtils.copy(retryScriptInstanceApiDto1.getCurrentUserDto(),CurrentUser.class));
        }
    }

    /**
     * 删除附近啊
     * @param id 附件id
     */
    @Override
    public void deleteAttachment(Long id) {
        taskAttachmentService.deleteTaskAttachmentById(id);
        redisTemplate.opsForSet().remove(Constants.REDIS_SCRIPT_TEMP_ATTACHMENT, String.valueOf(id));
        logger.info("删除脚本服务化临时附件id: {}", id);
    }

    /**
     * 用户-权限数据查询
     * @param servicePermissionApiQueryDto  查询条件
     * @param categoryId 分类id
     * @return 用户信息
     */
    @Override
    public List<UserInfoApiDto> queryPermissionUserInfoList(ServicePermissionApiQueryDto servicePermissionApiQueryDto ,Long categoryId) {
        List<UserInfoApiDto> userInfoList = new ArrayList<>();
        CurrentUser currentUser = CurrentUserUtil.getCurrentUser();
        //判断，角色权限模式下：脚本分类绑定了审核人展示绑定审核人，绑定审核人后只有绑定的审核人可以审核；未绑定审核人时当前用户所有角色内有专审权限的人可以审核；
        if("userRoleGroup".equals(myScriptServiceScripts.getScriptBusinessConfig().getDataPermissionPolicy())){
            //角色id不为空，说明走了危险命令使用特定角色下有审批权限的用户进行审核，此时根据角色、权限查询用户
            if(ObjectUtils.notEqual(servicePermissionApiQueryDto.getRoleIds(),null)
                    && !servicePermissionApiQueryDto.getRoleIds().isEmpty()){
                servicePermissionApiQueryDto.setOrgManagementFullNames(new ArrayList<>());
                return getUserInfoApiDtos(servicePermissionApiQueryDto, currentUser, iUserInfoApi);
            }
            //根据分类id查询角色分类关系表，找到绑定的所有角色
            List<CategoryRoleBean> categoryRoleRelations = myScriptServiceScripts.getCategoryMapper().getCategoryRoleRelations(categoryId);
            List<Long> roleIds = new ArrayList<>();
            //集合不为空，说明此分类绑定了角色，此时根据角色查询有审核权限的用户
            if(!categoryRoleRelations.isEmpty()){
                //确认绑定角色后是否配置了审核人，如果配置了审核人，将审核人返回，如果未配置，则获取分类绑定的角色下所有有审核权限的用户
                List<UserBean> userBeanList = myScriptServiceScripts.getCategoryMapper().getCategoryUsertRelations(categoryId);
                //根据userid获取审核人并且返回
                if(ObjectUtils.notEqual(userBeanList,null) && !userBeanList.isEmpty()){
                    List<Long> userIds = userBeanList.stream()
                            .map(UserBean::getUserId)
                            .collect(Collectors.toList());
                    servicePermissionApiQueryDto.setUserIds(userIds);
                    List<UserInfoApiDto> userInfoList1 = userInfoApi.getUserInfoList(userIds);
                    List<UserInfoApiDto> userList = new ArrayList<>();
                    //审核人去掉当前登录用户
                    for(UserInfoApiDto userInfo : userInfoList1){
                        if(!userInfo.getId().equals(currentUser.getId())){
                            userList.add(userInfo);
                        }
                    }
                    return BeanUtils.copy(userList,UserInfoApiDto.class);
                }else{
                    //返回角色下的所有有审核权限的用户
                    roleIds = categoryRoleRelations.stream()
                            .map(CategoryRoleBean::getRoleId)
                            .collect(Collectors.toList());
                }
            }else{
                //如果集合为空，说明此分类没有绑定任何角色，此时获取当前用户所有角色中有审核权限的用户
                List<RoleApiDto> roleList = myScriptServiceScripts.getiRole().selectRoleListByLoginName(currentUser.getLoginName());
                roleIds = roleList.stream()
                        .map(RoleApiDto::getId)
                        .collect(Collectors.toList());
            }
            if(!ObjectUtils.notEqual(roleIds,null) || roleIds.isEmpty()){
                return new ArrayList<>();
            }
            //设置角色，获取审核人
            servicePermissionApiQueryDto.setRoleIds(roleIds);
            return getUserInfoApiDtos(servicePermissionApiQueryDto, currentUser, userInfoApi);
        }

        //不走角色权限的时候，走原来部门等逻辑
        //查询当前用户下的分类绑定的用户
        if(!currentUser.getSupervisor() && currentUser.getOrgCode() != null) {
            if(servicePermissionApiQueryDto.getOrgManagementFullNames()!= null && !servicePermissionApiQueryDto.getOrgManagementFullNames().isEmpty()){
                servicePermissionApiQueryDto.setUserLevel(1);
                List<PermissionUserInfoApiDto> permissionUserInfoApiDtoList = iUserInfoApi.queryPermissionUserInfoList(servicePermissionApiQueryDto);
                userInfoList = BeanUtils.copy(permissionUserInfoApiDtoList,UserInfoApiDto.class);
            }else {
                //通过分类查找授权用户列表
                List<UserBean> userBeanList = new ArrayList<>();
                List<Long> categoryList = new ArrayList<>();
                List<Long> orgManagementIdsList = new ArrayList<>();
                if(categoryId != null){
                    categoryList.add(categoryId);
                    userBeanList = categoryService.getUserByCategoryIds(categoryList);
                    //这个分类绑定的其他部门
                    CategoryOrgBean categoryOrgRelations = categoryService.getCategoryOrgRelations(categoryId);
                    List<OrgBean> orgList = categoryOrgRelations.getOrgList();
                    for (OrgBean orgBean : orgList) {
                        orgManagementIdsList.add(orgBean.getOrgId());
                    }
                }
                //调用平台接口查询当前用户部门的部门主管(0是员工，1是主管)
                servicePermissionApiQueryDto.setUserLevel(1);
                orgManagementIdsList.add(currentUser.getOrgId());

                // 使用 HashSet 去重
                Set<Long> uniqueOrgManagementIds = new HashSet<>(orgManagementIdsList);
                orgManagementIdsList.clear();
                orgManagementIdsList.addAll(uniqueOrgManagementIds);

                servicePermissionApiQueryDto.setOrgManagementIds(orgManagementIdsList);
                List<PermissionUserInfoApiDto> permissionUserInfoApiDtoList = iUserInfoApi.queryPermissionUserInfoList(servicePermissionApiQueryDto);
                List<Long> userIdList = getUserIdList(permissionUserInfoApiDtoList, userBeanList);
                if (!userIdList.isEmpty()) {
                    userInfoList = iUserInfoApi.getUserInfoList(userIdList);
                }
            }
        }else{
            if(servicePermissionApiQueryDto.getOrgManagementFullNames()!= null && !servicePermissionApiQueryDto.getOrgManagementFullNames().isEmpty()){
                servicePermissionApiQueryDto.setUserLevel(1);
                List<PermissionUserInfoApiDto> permissionUserInfoApiDtoList = iUserInfoApi.queryPermissionUserInfoList(servicePermissionApiQueryDto);
                userInfoList = BeanUtils.copy(permissionUserInfoApiDtoList,UserInfoApiDto.class);
            }else {
                List<UserInfoDto> userInfoDtoList = queryUserInfoListByPermissionCode(servicePermissionApiQueryDto.getPermissionCodes().get(0));
                userInfoList = BeanUtils.copy(userInfoDtoList, UserInfoApiDto.class);
            }
        }

        return userInfoList;
    }

    /**
     * 获取审核用户并去重
     * @param servicePermissionApiQueryDto 参数对象
     * @param currentUser 当前登录用户
     * @param iUserInfoApi 用户管理接口
     * @return 返回审批用户
     */
    private List<UserInfoApiDto> getUserInfoApiDtos(ServicePermissionApiQueryDto servicePermissionApiQueryDto, CurrentUser currentUser, IUserInfo iUserInfoApi) {
        List<PermissionUserInfoApiDto> permissionUserInfoApiDtoList = iUserInfoApi.queryPermissionUserInfoList(servicePermissionApiQueryDto);
        List<PermissionUserInfoApiDto> userList = new ArrayList<>();
        for(PermissionUserInfoApiDto permissionUser : permissionUserInfoApiDtoList){
            if(!permissionUser.getId().equals(currentUser.getId())){
                userList.add(permissionUser);
            }
        }
        return BeanUtils.copy(userList,UserInfoApiDto.class);
    }

    /**
     * 根据脚本id查询脚本创建人的部门领导
     * @param scriptInfoId 脚本id
     * @return 脚本创建人部门领导
     */
    @Override
    public List<UserInfoApiDto> queryDepartmentUserInfoList(Long scriptInfoId) {
        //根据脚本id查询脚本信息
        Info info = infoMapper.selectInfoById(scriptInfoId);
        //查询当前用户下的分类绑定的用户
        CurrentUser currentUser = CurrentUserUtil.getCurrentUser();
        //获取所有用户
        List<UserInfoApiDto> userInfoList = iUserInfoApi.getUserInfoList(new ArrayList<Long>());
        //获取除本部门外的所有用户
        List<UserInfoApiDto> userInfoApiDtos = iUserInfoApi.queryUserInfoListByOtherOrgManagement(info.getCreatorId());
        // 获取 userInfoList 中不在 userInfoApiDtos 中的数据
        List<UserInfoApiDto> result = userInfoList.stream()
                .filter(user -> user.getLevel() == 1) // 主管
                .filter(user -> !Objects.equals(user.getId(), currentUser.getId())) //去除申请人
                .filter(user -> ObjectUtils.notEqual(user.getOrgId(),null)) //没有部门的人去除掉
                .filter(user -> userInfoApiDtos.stream()
                        .noneMatch(otherUser -> otherUser.getLoginName().equals(user.getLoginName()))) // 原有条件：不在 userInfoApiDtos 中
                .collect(Collectors.toList());
        return result;
    }

    private static List<Long> getUserIdList(List<PermissionUserInfoApiDto> permissionUserInfoApiDtoList, List<UserBean> userBeanList) {
        List<Long> userIdList = new ArrayList<>();
        //查询部门主管之后将两部分的id合并查询所有用户
        if (!permissionUserInfoApiDtoList.isEmpty()) {
            for (PermissionUserInfoApiDto permissionUserInfoApiDto : permissionUserInfoApiDtoList) {
                userIdList.add(permissionUserInfoApiDto.getId());
            }
        }
        if (!userBeanList.isEmpty()) {
            for (UserBean userBean : userBeanList) {
                userIdList.add(userBean.getUserId());
            }
        }
        return userIdList;
    }

    @Override
    public Map<String, Object> importServerExcel(MultipartFile file) throws IOException, ScriptException {
        Map<String, Object> res = new HashMap<>();

        List<String> addressList = new ArrayList<>();
        List<String> errorMessage = new CopyOnWriteArrayList<>();  // excel导入ip和端口消息
        List<String> notExistMessage = new CopyOnWriteArrayList<>(); // 不存在agent消息
        List<AgentInfoDto> agentInfoDtoList = new CopyOnWriteArrayList<>();
        List<Object> firstSheetDataList = new ArrayList<>();

        try (InputStream inputStream = file.getInputStream()) {
            ExcelReaderBuilder excelReaderBuilder = FastExcelFactory.read(inputStream);
            ExcelReaderSheetBuilder firstSheet = excelReaderBuilder.sheet(0);
            firstSheet.autoTrim(true);

            firstSheetDataList = firstSheet.doReadSync();
        }

        // 校验Excel数据
        validateExcelData(firstSheetDataList, addressList, errorMessage);

        // 获取当前用户拥有权限使用的所有脚本服务化服务器
        CurrentUser user = CurrentUserUtil.getCurrentUser();
        List<SystemAgentInfoApiDto> agentInfoList = getLoginUserAgent(user);

        // 构建AgentInfoMap
        Map<String, AgentInfoDto> agentInfoDtoMap = buildAgentInfoMap(agentInfoList);

        // 检查是否存在或有权限、或者agent状态是否正常
        checkAgentInfo(addressList, agentInfoDtoMap, agentInfoDtoList, notExistMessage);
        //返回值去重
        List<AgentInfoDto> distinctList = new ArrayList<>(agentInfoDtoList.stream()
                .collect(Collectors.toMap(AgentInfoDto::getAgentIp, dto -> dto, (existing, replacement) -> existing))
                .values());

        res.put("errorMessage", errorMessage);
        res.put("notExistMessage", notExistMessage);
        res.put("agentInfoDtoList", distinctList);
        return res;
    }

    private void validateExcelData(List<Object> firstSheetDataList, List<String> addressList, List<String> errorMessage) {
        firstSheetDataList.parallelStream().forEach(firstSheetData -> {
            Map<Integer, String> rows = (Map<Integer, String>) firstSheetData;
            if (!IpUtils.isValidIP(rows.get(0))) {
                errorMessage.add("Ip: " + rows.get(0) + ", 未能通过ip校验");
                return;
            }
            //去重
            if(!addressList.contains(rows.get(0))){
                addressList.add(rows.get(0));
            }
        });
    }

    private Map<String, AgentInfoDto> buildAgentInfoMap(List<SystemAgentInfoApiDto> agentInfoDtoPageInfo) {
        Map<String, AgentInfoDto> agentInfoDtoMap = new ConcurrentHashMap<>();
        agentInfoDtoPageInfo.parallelStream().forEach(systemAgentInfoApiDto -> {
            AgentInfoDto agentInfo = taskGroupsService.convertToAgentInfoDto(systemAgentInfoApiDto);
            agentInfoDtoMap.put(agentInfo.getAgentIp(), agentInfo);
        });
        return agentInfoDtoMap;
    }

    private void checkAgentInfo(List<String> addressList, Map<String, AgentInfoDto> agentInfoDtoMap, List<AgentInfoDto> agentInfoDtoList, List<String> notExistMessage) {
        addressList.parallelStream().forEach(address -> {
            AgentInfoDto agentInfoDto = agentInfoDtoMap.get(address);
            if (agentInfoDtoMap.containsKey(address)) {
                //校验agent状态
                if(ObjectUtils.notEqual(agentInfoDto.getAgentState(),null) && agentInfoDto.getAgentState().equals(0)){
                    agentInfoDtoList.add(agentInfoDto);
                }else{
                    notExistMessage.add("Address: " + address + ", 服务器状态不为“正常”状态");
                }
            } else {
                notExistMessage.add("Address: " + address + ", 服务器不存在或者当前用户没有使用权限");
            }
        });
    }

    private boolean isValidPort(String portStr) {
        // 检查字符串是否为空
        if (portStr == null || portStr.trim().isEmpty()) {
            return false;
        }

        try {
            // 将字符串转换为整数
            int port = Integer.parseInt(portStr);
            // 校验端口号范围
            return port >= 0 && port <= 65535;
        } catch (NumberFormatException e) {
            // 如果转换失败，说明不是合法的整数
            return false;
        }
    }

    @Override
    public void downloadImportTemplate(HttpServletResponse response) {
        try {
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("服务器导入模板", "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

            // 设置表头
            List<List<String>> headList = new ArrayList<>();
            headList.add(Collections.singletonList("IP"));

            // 写入 Excel 文件
            EasyExcel.write(response.getOutputStream()).sheet("服务器").head(headList).doWrite(new ArrayList<>());
        } catch (IOException e) {
            logger.error("下载模板失败", e);
        }
    }

    /**
     * 循环获取当前登录用户可以获取的所有agent
     * @param user 当前登录用户
     * @return 所有agent列表
     */
    public List<SystemAgentInfoApiDto> getLoginUserAgent (CurrentUser user){
        AgentGroupRoleQueryBean agentGroupRoleQueryBean = new AgentGroupRoleQueryBean();
        int pageNum = 1;
        agentGroupRoleQueryBean.setUserId(user.getId());
        agentGroupRoleQueryBean.setPageNum(pageNum);
        agentGroupRoleQueryBean.setPageSize(200);
        agentGroupRoleQueryBean.setBusinessType("脚本服务化");
        //此参数为true，结果会返回agent状态
        agentGroupRoleQueryBean.setResultAgentState(true);
        //定义agent集合
        List<SystemAgentInfoApiDto> list = new ArrayList<>();
        //循环调用平台管理接口，获取当前用户可选择的所有agent
        boolean forFlag = true;
        while (forFlag){
            PageInfo<SystemAgentInfoApiDto> pageInfo = iAgentInfo.queryAgentInfoGroupRole(agentGroupRoleQueryBean);
            list.addAll(pageInfo.getList());
            if(pageInfo.isHasNextPage()){
                agentGroupRoleQueryBean.setPageNum(++pageNum);
            }else{
                forFlag = false;
            }
        }
        return list;
    }

}
