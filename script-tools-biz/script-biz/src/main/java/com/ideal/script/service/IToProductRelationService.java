package com.ideal.script.service;

import com.ideal.script.model.dto.ToProductRelationDto;
import com.ideal.script.model.dto.ToProductRelationQueryDto;
import com.github.pagehelper.PageInfo;

/**
 * 投产记录关系Service接口
 *
 * <AUTHOR>
 * @date 2024-04-02
 */
public interface IToProductRelationService {
    /**
     * 查询投产记录关系
     *
     * @param id 投产记录关系主键
     * @return 投产记录关系
     */
    ToProductRelationDto selectToProductRelationById(Long id);

    /**
     * 查询投产记录关系列表
     *
     * @param toProductRelationQueryDto 投产记录关系
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 投产记录关系集合
     */
    PageInfo<ToProductRelationDto> selectToProductRelationList(ToProductRelationQueryDto toProductRelationQueryDto, Integer pageNum, Integer pageSize);

    /**
     * 新增投产记录关系
     *
     * @param toProductRelationDto 投产记录关系
     * @return 结果
     */
    int insertToProductRelation(ToProductRelationDto toProductRelationDto);

    /**
     * 修改投产记录关系
     *
     * @param toProductRelationDto 投产记录关系
     * @return 结果
     */
    int updateToProductRelation(ToProductRelationDto toProductRelationDto);

    /**
     * 批量删除投产记录关系
     *
     * @param ids 需要删除的投产记录关系主键集合
     * @return 结果
     */
    int deleteToProductRelationByIds(Long[] ids);
}
