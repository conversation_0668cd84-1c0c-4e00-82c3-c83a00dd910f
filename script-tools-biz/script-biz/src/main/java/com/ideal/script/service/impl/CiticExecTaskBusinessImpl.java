package com.ideal.script.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.model.bean.TaskHandleParam;
import com.ideal.script.model.dto.TaskInstanceDto;
import com.ideal.script.model.dto.TaskRuntimeDto;
import com.ideal.script.service.*;
import com.ideal.script.service.impl.builders.MyScriptServiceScripts;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * citic银行特有逻辑处理类
 */


@Service("timeTaskScriptTypeService")
public class CiticExecTaskBusinessImpl implements IAfterRuntimeHandlerService {
    private final Logger logger = LoggerFactory.getLogger(CiticExecTaskBusinessImpl.class);

    private final ITaskInstanceService taskInstanceService;

    private final MyScriptServiceScripts myScriptServiceScripts;

    public CiticExecTaskBusinessImpl(ITaskInstanceService taskInstanceService,MyScriptServiceScripts myScriptServiceScripts) {
        this.taskInstanceService = taskInstanceService;
        this.myScriptServiceScripts = myScriptServiceScripts;

    }

    @Async
    @Override
    public void monitorMqToSend(String typeFalg, TaskHandleParam taskHandleParam, TaskRuntimeDto taskRuntimeDto) {
        //开启开关执行推送mq
        if(!myScriptServiceScripts.getScriptBusinessConfig().isMonitorMessageSendMqFlag()){
            return;
        }
        //定时任务启动的脚本任务发送正常推送mq
        TaskInstanceDto taskInstanceByTaskInfoId = taskInstanceService.getTaskInstanceByRuntimeId(taskRuntimeDto.getId());
        if(ObjectUtils.notEqual(taskInstanceByTaskInfoId.getCallerTaskId(),null)){
            try {
                JSONObject jsonObject = new JSONObject();
                //给agent推送成功的场景
                if(Constants.CRONTABS_SEND_RESULT_DEV.equals(typeFalg)){
                    jsonObject.put("runtimeId", taskInstanceByTaskInfoId.getCallerTaskId());
                    jsonObject.put("agentIp", taskRuntimeDto.getAgentIp());
                    jsonObject.put("agentPort", taskRuntimeDto.getAgentPort());
                    myScriptServiceScripts.getiPublisher().apply(Constants.CRONTABS_SEND_RESULT_DEV, jsonObject.toJSONString());
                }

                //消费到了mq标准输出场景
                if(Constants.CRONTABS_EXECUTE_RESULT_DEV.equals(typeFalg)){
                    jsonObject.put("runtimeId", taskInstanceByTaskInfoId.getCallerTaskId());
                    jsonObject.put("agentIp", taskRuntimeDto.getAgentIp());
                    jsonObject.put("agentPort", taskRuntimeDto.getAgentPort());
                    jsonObject.put("ret", "1");
                    jsonObject.put("stdout", taskHandleParam.getIstdout());
                    jsonObject.put("stderr", taskHandleParam.getIstderror());
                    jsonObject.put("lastLine", taskHandleParam.getIlastline());
                    jsonObject.put("isTimeout", false);
                    myScriptServiceScripts.getiPublisher().apply(Constants.CRONTABS_EXECUTE_RESULT_DEV, jsonObject.toJSONString());
                }

                //推送agent失败场景
                if(Constants.CRONTABS_ERROR_RESULT_DEV.equals(typeFalg)){
                    jsonObject.put("runtimeId", taskInstanceByTaskInfoId.getCallerTaskId());
                    jsonObject.put("agentIp", taskRuntimeDto.getAgentIp());
                    jsonObject.put("agentPort", taskRuntimeDto.getAgentPort());
                    myScriptServiceScripts.getiPublisher().apply(Constants.CRONTABS_ERROR_RESULT_DEV, jsonObject.toJSONString());
                }
            }catch (Exception e){
                logger.error("CiticScriptTaskMqSendResult.pushMessage is error : " , e);
            }
        }
    }

}
