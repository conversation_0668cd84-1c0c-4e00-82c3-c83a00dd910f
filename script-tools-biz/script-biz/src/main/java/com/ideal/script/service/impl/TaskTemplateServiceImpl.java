package com.ideal.script.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.common.util.batch.Batch;
import com.ideal.sc.util.CurrentUserUtil;
import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.common.util.FileSizeValidUtil;
import com.ideal.script.dto.AttachmentDto;
import com.ideal.script.dto.AttachmentResponseDto;
import com.ideal.script.dto.AttachmentUploadDto;
import com.ideal.script.dto.ParameterValidationDto;
import com.ideal.script.dto.ScriptInfoDto;
import com.ideal.script.dto.ScriptInfoQueryDto;
import com.ideal.script.dto.ScriptVersionDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.TaskAttachmentMapper;
import com.ideal.script.mapper.TaskAttachmentTemplateMapper;
import com.ideal.script.mapper.TaskGroupsTemplateMapper;
import com.ideal.script.mapper.TaskIpsMapper;
import com.ideal.script.mapper.TaskIpsTemplateMapper;
import com.ideal.script.mapper.TaskParamsMapper;
import com.ideal.script.mapper.TaskParamsTemplateMapper;
import com.ideal.script.mapper.TaskTemplateMapper;
import com.ideal.script.model.bean.TaskCloneBean;
import com.ideal.script.model.bean.TaskExecuteBean;
import com.ideal.script.model.dto.AgentInfoDto;
import com.ideal.script.model.dto.AuditRelationDto;
import com.ideal.script.model.dto.ParameterDto;
import com.ideal.script.model.dto.ScriptExecAuditDto;
import com.ideal.script.model.dto.ScriptTaskApplyResDto;
import com.ideal.script.model.dto.StartCommonTaskDto;
import com.ideal.script.model.dto.TaskApplyQueryDto;
import com.ideal.script.model.dto.TaskAttachmentDto;
import com.ideal.script.model.dto.TaskDto;
import com.ideal.script.model.dto.TaskGroupsDto;
import com.ideal.script.model.dto.TaskStartDto;
import com.ideal.script.model.dto.TaskTemplateDto;
import com.ideal.script.model.dto.interaction.agent.SystemComputerGroupDto;
import com.ideal.script.model.entity.AgentInfo;
import com.ideal.script.model.entity.Task;
import com.ideal.script.model.entity.TaskAttachment;
import com.ideal.script.model.entity.TaskGroups;
import com.ideal.script.model.entity.TaskIps;
import com.ideal.script.model.entity.TaskParams;
import com.ideal.script.service.AuditSource;
import com.ideal.script.service.IAgentInfoService;
import com.ideal.script.service.IAuditRelationService;
import com.ideal.script.service.ICategoryService;
import com.ideal.script.service.IInfoVersionService;
import com.ideal.script.service.IMyScriptService;
import com.ideal.script.service.ITaskApplyService;
import com.ideal.script.service.ITaskGroupsService;
import com.ideal.script.service.ITaskService;
import com.ideal.script.service.ITaskTemplateService;
import com.ideal.script.service.impl.builders.MyScriptServiceScripts;
import com.ideal.system.common.component.model.CurrentUser;
import com.ideal.system.dto.PermissionUserInfoApiDto;
import com.ideal.system.dto.ServicePermissionApiQueryDto;
import com.ideal.system.dto.UserInfoApiDto;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 常用/克隆任务执行实现类
 *
 * <AUTHOR>
 */
@Service
public class TaskTemplateServiceImpl implements ITaskTemplateService, Batch {

    private static final Logger logger = LoggerFactory.getLogger(TaskTemplateServiceImpl.class);

    private final ITaskService taskService;
    private final TaskParamsMapper taskParamsMapper;
    private final TaskIpsMapper taskIpsMapper;
    private final TaskAttachmentMapper taskAttachmentMapper;
    private final ICategoryService categoryService;
    private final ITaskGroupsService taskGroupsService;
    private final TaskTemplateMapper taskTemplateMapper;
    private final TaskAttachmentTemplateMapper taskAttachmentTemplateMapper;
    private final TaskParamsTemplateMapper taskParamsTemplateMapper;
    private final TaskIpsTemplateMapper taskIpsTemplateMapper;
    private final TaskGroupsTemplateMapper taskGroupsTemplateMapper;
    private final IMyScriptService myScriptService;
    private final ITaskApplyService taskApplyService;
    private final AuditSource taskApplySource;
    private final IAgentInfoService agentInfoService;
    private final FileSizeValidUtil fileSizeValidUtil;
    private final IAuditRelationService auditRelationService;
    private final IInfoVersionService infoVersionService;
    private final MyScriptServiceScripts myScriptServiceScripts;
    private final ITaskTemplateService taskTemplateService;
    @Value("${ideal.customer.name:ideal}")
    private String customerName;

    @Autowired
    public TaskTemplateServiceImpl(MyScriptServiceScripts myScriptServiceScripts, IInfoVersionService infoVersionService, IAuditRelationService auditRelationService, FileSizeValidUtil fileSizeValidUtil, IAgentInfoService agentInfoService, @Qualifier("taskApplySource") AuditSource taskApplySource, ITaskApplyService taskApplyService, IMyScriptService myScriptService, TaskGroupsTemplateMapper taskGroupsTemplateMapper, TaskIpsTemplateMapper taskIpsTemplateMapper, TaskParamsTemplateMapper taskParamsTemplateMapper, TaskAttachmentTemplateMapper taskAttachmentTemplateMapper, TaskTemplateMapper taskTemplateMapper, ITaskService taskService, TaskParamsMapper taskParamsMapper, TaskIpsMapper taskIpsMapper, TaskAttachmentMapper taskAttachmentMapper, ICategoryService categoryService, ITaskGroupsService taskGroupsService, @Lazy ITaskTemplateService taskTemplateService) {
        this.infoVersionService = infoVersionService;
        this.myScriptServiceScripts = myScriptServiceScripts;
        this.auditRelationService = auditRelationService;
        this.fileSizeValidUtil = fileSizeValidUtil;
        this.agentInfoService = agentInfoService;
        this.taskApplySource = taskApplySource;
        this.taskApplyService = taskApplyService;
        this.myScriptService = myScriptService;
        this.taskGroupsTemplateMapper = taskGroupsTemplateMapper;
        this.taskIpsTemplateMapper = taskIpsTemplateMapper;
        this.taskParamsTemplateMapper = taskParamsTemplateMapper;
        this.taskAttachmentTemplateMapper = taskAttachmentTemplateMapper;
        this.taskTemplateMapper = taskTemplateMapper;
        this.taskService = taskService;
        this.taskParamsMapper = taskParamsMapper;
        this.taskIpsMapper = taskIpsMapper;
        this.taskAttachmentMapper = taskAttachmentMapper;
        this.categoryService = categoryService;
        this.taskGroupsService = taskGroupsService;
        this.taskTemplateService = taskTemplateService;
    }

    /**
     * 创建克隆任务
     * @param taskStartDto 克隆任务对象（查询使用）
     * @param user 当前系统用户
     * @throws ScriptException 脚本服务化相关异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createCloneTask(TaskStartDto taskStartDto,CurrentUser user) throws ScriptException {
        //校验任务名称是否重复，重复的禁止生成
        if(ObjectUtils.notEqual(taskStartDto,null)
                && ObjectUtils.notEqual(taskStartDto.getTaskName(),null)
                && templateExistByTaskName(taskStartDto.getTaskName())){
            throw new ScriptException("template.script.task.name.exist");
        }
        //通过taskId获取本次任务的信息，包括附件、参数、ips、agentGroup
        if(ObjectUtils.notEqual(taskStartDto,null)
                && ObjectUtils.notEqual(taskStartDto.getScriptTaskId(),null)){
            //查询并保存任务信息
            TaskDto taskDto = taskService.selectTaskById(taskStartDto.getScriptTaskId());
            //审核关系表数据
            AuditRelationDto auditRelationDto = auditRelationService.selectAuditRelationByTaskId(taskDto.getId());
            taskDto.setId(null);
            taskDto.setTaskName(taskStartDto.getTaskName());
            //标记常用任务/克隆任务 创建人
            taskDto.setCreatorId(user.getId());
            Task task = BeanUtils.copy(taskDto, Task.class);
            //任务类型 0常用任务 1克隆任务
            task.setTaskType(taskStartDto.getTaskType());
            if(ObjectUtils.notEqual(auditRelationDto,null)){
                //审核人id
                task.setAuditorId(auditRelationDto.getAuditUserId());
            }
            taskTemplateMapper.insertTaskTemplate(task);
            Long taskId = task.getId();
            //查询并保存附件信息
            TaskAttachment taskAttachment = new TaskAttachment();
            taskAttachment.setScriptTaskId(taskStartDto.getScriptTaskId());
            taskAttachment.setScriptTaskId(taskId);
            List<TaskAttachment> taskAttachments = taskAttachmentMapper.selectTaskAttachmentList(taskAttachment);
            for(TaskAttachment taskAttachment1 : taskAttachments){
                taskAttachment1.setId(null);
                taskAttachment1.setScriptTaskId(taskId);
            }
            this.batchData(taskAttachments , taskAttachmentTemplateMapper::insertTaskAttachment);
            //查询并保存参数信息
            TaskParams taskParams = new TaskParams();
            taskParams.setScriptTaskId(taskStartDto.getScriptTaskId());
            List<TaskParams> taskParamsList = taskParamsMapper.selectTaskParamsList(taskParams);
            for(TaskParams taskParams1 : taskParamsList){
                taskParams1.setId(null);
                taskParams1.setScriptTaskId(taskId);
            }
            this.batchData(taskParamsList , taskParamsTemplateMapper::insertTaskParams);
            //查询并保存ips信息
            TaskIps taskIps = new TaskIps();
            taskIps.setScriptTaskId(taskStartDto.getScriptTaskId());
            List<TaskIps> taskIpsList = taskIpsMapper.selectTaskIpsList(taskIps);
            for(TaskIps taskIps1 : taskIpsList){
                taskIps1.setId(null);
                taskIps1.setScriptTaskId(taskId);
                taskIps1.setAlreadyimpFlag(0);
            }
            this.batchData(taskIpsList , taskIpsTemplateMapper::insertTaskIps);
            //查询并保存设备组信息
            List<TaskGroupsDto> taskGroupsList = taskGroupsService.selectTaskGroupsByServiceId(null, taskStartDto.getScriptTaskId());
            List<TaskGroups> taskGroups = new ArrayList<>();
            for(TaskGroupsDto taskGroupsDto : taskGroupsList){
                taskGroupsDto.setId(null);
                taskGroupsDto.setScriptTaskId(taskId);
                taskGroups.add(BeanUtils.copy(taskGroupsDto, TaskGroups.class));
            }
            this.batchData(taskGroups , taskGroupsTemplateMapper::insertTaskGroups);
        }else{
            //如果查询任务的必要参数为空，返回异常
            logger.error("clone task is not exist!");
            throw new ScriptException("clone.script.task.error");
        }
    }

    /**
     * 判断任务是否存在
     * @param taskName 任务名
     * @return 任务是否存在标识
     */
    private boolean templateExistByTaskName(String taskName){
        List<TaskCloneBean> taskCloneBean = taskTemplateMapper.selectTaskTemplateByTaskName(taskName);
        return ObjectUtils.notEqual(taskCloneBean,null) && !taskCloneBean.isEmpty();
    }

    /**
     * 任务申请页面常用任务直接保存成常用任务
     * @param scriptExecAuditDto 任务参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createCloneTaskFromTaskApply(ScriptExecAuditDto scriptExecAuditDto, CurrentUser user) throws ScriptException{
        //校验任务名称是否重复，重复的禁止生成
        if(ObjectUtils.notEqual(scriptExecAuditDto,null)
                && ObjectUtils.notEqual(scriptExecAuditDto.getTaskInfo(),null)
                &&templateExistByTaskName(scriptExecAuditDto.getTaskInfo().getTaskName())){
            throw new ScriptException("template.script.task.name.exist");
        }

        Long longScriptInfoVersionId = Optional.ofNullable(scriptExecAuditDto.getScriptInfoVersionId()).orElseThrow(() -> new ScriptException("script.info.version.id.not.exist"));
        //根据脚本versionId查询脚本的uuid
        ScriptVersionDto scriptVersionDto = infoVersionService.selectInfoVersionById(longScriptInfoVersionId);
        //保存常用任务数据
        Task task = getTask(scriptExecAuditDto, user, scriptVersionDto);
        taskTemplateMapper.insertTaskTemplate(task);
        //保存task数据后获取id，后续业务表都需要关联这个taskId
        Long taskId = task.getId();
        //更新附件taskId
        if(ObjectUtils.notEqual(scriptExecAuditDto.getScriptTempAttachments(),null)
                && !scriptExecAuditDto.getScriptTempAttachments().isEmpty()){
            Long [] attachmentIds = new Long[scriptExecAuditDto.getScriptTempAttachments().size()];
            int index = 0;
            for(AttachmentDto attachmentDto : scriptExecAuditDto.getScriptTempAttachments()){
                attachmentIds[index++] = attachmentDto.getId();
            }
            taskAttachmentTemplateMapper.batchUpdateByIds(taskId,attachmentIds);
        }
        //保存参数
        if(ObjectUtils.notEqual(scriptExecAuditDto.getParams(),null)
                && !scriptExecAuditDto.getParams().isEmpty()){
            List<TaskParams> taskParamsList = getTaskParams(scriptExecAuditDto, taskId);
            this.batchData(taskParamsList , taskParamsTemplateMapper::insertTaskParams);
        }
        //保存ips信息
        if(ObjectUtils.notEqual(scriptExecAuditDto.getChosedAgentUsers(),null)
                && !scriptExecAuditDto.getChosedAgentUsers().isEmpty()){
            List<TaskIps> taskIpsList = new ArrayList<>();
            for(AgentInfoDto agentInfoDto : scriptExecAuditDto.getChosedAgentUsers()){
                //根据ip端口查询agent信息，为了给agentId赋值
                AgentInfoDto dto = new AgentInfoDto();
                dto.setAgentIp(agentInfoDto.getAgentIp());
                dto.setAgentPort(agentInfoDto.getAgentPort());
                // 检查ieai_script_agent_info表中是否已存在相同的记录(IP+端口)
                boolean exists = agentInfoService.checkAgentInfoExists(agentInfoDto.getAgentIp(), agentInfoDto.getAgentPort().longValue());
                if (!exists) {
                    // 存储ieai_script_agent_info表信息
                    // 脚本服务化插入ip数据以IDGenerate注解为准，重置ID属性
                    agentInfoDto.setId(null);
                    agentInfoService.insertAgentInfo(agentInfoDto);
                }
                AgentInfo agentInfo = agentInfoService.selectAgentInfoByIpAndPort(dto);
                //保存ips数据
                TaskIps taskIps = new TaskIps();
                taskIps.setScriptTaskId(taskId);
                taskIps.setScriptAgentinfoId(agentInfo.getId());
                taskIpsList.add(taskIps);
            }
            this.batchData(taskIpsList , taskIpsTemplateMapper::insertTaskIps);
        }
        //保存设备组信息
        if(ObjectUtils.notEqual(scriptExecAuditDto.getChosedResGroups(),null)
                && !scriptExecAuditDto.getChosedResGroups().isEmpty()){
            List<TaskGroups> taskGroupsList = new ArrayList<>();
            for(TaskGroupsDto taskGroupsDto : scriptExecAuditDto.getChosedResGroups()){
                TaskGroups taskGroups = new TaskGroups();
                taskGroups.setScriptTaskId(taskId);
                taskGroups.setSysmComputerGroupId(taskGroupsDto.getSysmComputerGroupId());
                taskGroups.setCpname(taskGroupsDto.getCpname());
                taskGroupsList.add(taskGroups);
            }
            this.batchData(taskGroupsList , taskGroupsTemplateMapper::insertTaskGroups);
        }

    }

    /**
     * 整合参数信息
     * @param scriptExecAuditDto 前台传递过来的数据
     * @param taskId 任务id
     * @return 返回参数信息
     */
    private List<TaskParams> getTaskParams(ScriptExecAuditDto scriptExecAuditDto, Long taskId) {
        List<TaskParams> taskParamsList = new ArrayList<>();
        for(ParameterDto parameterDto : scriptExecAuditDto.getParams()){
            TaskParams taskParams = new TaskParams();
            taskParams.setScriptTaskId(taskId);
            taskParams.setScriptParameterCheckId(parameterDto.getParamCheckIid());
            taskParams.setScriptParameterManagerId(parameterDto.getScriptParameterManagerId());
            taskParams.setType(parameterDto.getParamType());
            taskParams.setValue(parameterDto.getParamDefaultValue());
            taskParams.setDesc(parameterDto.getParamDesc());
            taskParams.setOrder(parameterDto.getParamOrder());
            taskParams.setStartType(0);
            taskParamsList.add(taskParams);
        }
        return taskParamsList;
    }

    /**
     * 整合task信息
     * @param scriptExecAuditDto 前台传递过来的参数
     * @param user 当前登录用户信息
     * @param scriptVersionDto 脚本信息
     * @return 返回task数据
     */
    private Task getTask(ScriptExecAuditDto scriptExecAuditDto, CurrentUser user, ScriptVersionDto scriptVersionDto) {
        Task task = new Task();
        task.setSrcScriptUuid(scriptVersionDto.getSrcScriptUuid());
        task.setTaskName(scriptExecAuditDto.getTaskInfo().getTaskName());
        task.setEachNum(scriptExecAuditDto.getTaskInfo().getEachNum());
        task.setTaskScheduler(scriptExecAuditDto.getTaskInfo().getTaskScheduler());
        task.setTaskTime(scriptExecAuditDto.getTaskInfo().getTaskTime());
        task.setTaskCron(scriptExecAuditDto.getTaskInfo().getTaskCron());
        task.setPublishDesc(scriptExecAuditDto.getTaskInfo().getPublishDesc());
        task.setStartUser(user.getLoginName());
        task.setDriveMode(scriptExecAuditDto.getTaskInfo().getDriveMode());
        task.setStartType(0);
        task.setCreatorId(user.getId());
        task.setCreatorName(user.getFullName());
        task.setScriptTaskSource(1);
        task.setReadyToExecute(3);
        task.setTaskType(0);
        task.setExecUser(scriptExecAuditDto.getExecuser());
        return task;
    }

    /**
     * 获取克隆任务列表
     * @param taskApplyQueryDto 查询条件dto
     * @param pageNum 页码
     * @param pageSize 分页大小
     * @param user 当前系统登录用户
     * @return 返回克隆任务数据分页信息
     */
    @Override
    public PageInfo<TaskTemplateDto> listCloneTask(TaskApplyQueryDto taskApplyQueryDto, Integer pageNum, Integer pageSize, CurrentUser user) {

        //判断是否执行角色查询权限，注意，值班任务申请时不需要处理，保持获取所有数据
        boolean rolePermission = myScriptService.getRolePermission();
        if(rolePermission && !user.getSupervisor()){
            //根据角色获取持有的共享角色id
            List<Long> idList = categoryService.getShareTypeRoleIdsByLoginName(user.getLoginName());
            if(ObjectUtils.notEqual(idList,null)
                && !idList.isEmpty()){
                //获取角色下的所有用户
                ServicePermissionApiQueryDto servicePermissionApiQueryDto = new ServicePermissionApiQueryDto();
                servicePermissionApiQueryDto.setRoleIds(idList);
                List<PermissionUserInfoApiDto> permissionUserInfoList =  myScriptServiceScripts.getiUserInfo().queryPermissionUserInfoList(servicePermissionApiQueryDto);
                if(ObjectUtils.notEqual(permissionUserInfoList,null) && !permissionUserInfoList.isEmpty()){
                    //用户id赋值
                    List<Long> excludeUserIds = permissionUserInfoList.stream()
                            .map(PermissionUserInfoApiDto::getId)
                            .collect(Collectors.toList());
                    taskApplyQueryDto.setUserIdList(excludeUserIds);
                }
            }
            //常用任务也需要查询自己的数据
            if(!ObjectUtils.notEqual(taskApplyQueryDto.getUserIdList(),null)
                || taskApplyQueryDto.getUserIdList().isEmpty()){
                List<Long> excludeUserIds = new ArrayList<>();
                excludeUserIds.add(user.getId());
                taskApplyQueryDto.setUserIdList(excludeUserIds);
            }
            taskApplyQueryDto.setRoleFlag(true);
            taskApplyQueryDto.setRoleIdList(idList);
        }else{
            //走部门权限
            TaskExecuteBean taskExecuteBean = BeanUtils.copy(taskApplyQueryDto, TaskExecuteBean.class);
            // 设置权限相关信息
            categoryService.setCategoryPermission(taskExecuteBean, user);
            taskApplyQueryDto = BeanUtils.copy(taskExecuteBean, TaskApplyQueryDto.class);
        }
        //分页查询
        PageMethod.startPage(pageNum, pageSize);
        List<TaskCloneBean> taskCloneBeans = taskTemplateMapper.selectTaskTemplateList(taskApplyQueryDto,user.getOrgCode(),user.getId());
        //根据查询的数据获取分类信息
        for (TaskCloneBean taskCloneBean : taskCloneBeans) {
            if (null == taskCloneBean.getCategoryId()) {
                taskCloneBean.setScriptCategoryName(null);
            } else {
                String categoryName = categoryService.getCategoryFullPath(taskCloneBean.getCategoryId());
                taskCloneBean.setScriptCategoryName(categoryName);
            }
        }
        return PageDataUtil.toDtoPage(taskCloneBeans, TaskTemplateDto.class);
    }

    /**
     * 获取脚本基本信息
     * @param scriptInfoQueryDto 请求参数
     * @return 返回脚本基本信息
     */
    @Override
    public ScriptInfoDto getScriptTemplateDetail(ScriptInfoQueryDto scriptInfoQueryDto) throws ScriptException{
        //一、调用获取脚本信息方法获取脚本信息
        ScriptInfoDto scriptDetail = myScriptService.getScriptDetail(scriptInfoQueryDto);
        if(Objects.isNull(scriptDetail) || Objects.isNull(scriptDetail.getScriptVersionDto())){
            throw new ScriptException("script.get.template.detail.error");
        }
        //二、参数、附件、agent等信息从常用任务表里获取
        //根据TaskId获取参数，详情查询到的参数只需把值改成克隆的参数即可
        List<TaskParams> taskParams = taskParamsTemplateMapper.selectTaskParamsByTaskId(scriptInfoQueryDto.getTaskId());
        List<ParameterValidationDto> parameterValidationDtoList = scriptDetail.getScriptVersionDto().getParameterValidationDtoList();
        if(Objects.nonNull(parameterValidationDtoList) && !parameterValidationDtoList.isEmpty()){
            for(ParameterValidationDto parameterValidationDto : parameterValidationDtoList){
                for(TaskParams taskParams1 : taskParams){
                    if(taskParams1.getOrder().intValue() == parameterValidationDto.getParamOrder().intValue()){
                        parameterValidationDto.setParamDefaultValue(taskParams1.getValue());
                        break;
                    }
                }
            }
        }
        return scriptDetail;
    }

    /**
     * 获取执行的agent
     * @param taskId 任务id
     * @return agent集合
     */
    @Override
    public List<AgentInfoDto> getAllChoseAgent(Long taskId){
        return BeanUtils.copy(taskIpsTemplateMapper.selectAgentInfoByTaskId(taskId), AgentInfoDto.class);
    }

    /**
     * 根据taskId获取资源组数据
     * @param taskId 任务id
     * @return 资源组数据
     */
    @Override
    public List<SystemComputerGroupDto> getAllChoseGroup(Long taskId){
        List<SystemComputerGroupDto> groupApiDtoList = new ArrayList<>();
        List<TaskGroups> taskGroups = taskGroupsTemplateMapper.selectTaskGroupsByTaskId(taskId);
        for(TaskGroups taskGroup : taskGroups){
            SystemComputerGroupDto computerGroupApiDto = new SystemComputerGroupDto();
            computerGroupApiDto.setSysmComputerGroupId(taskGroup.getSysmComputerGroupId());
            computerGroupApiDto.setCpname(taskGroup.getCpname());
            groupApiDtoList.add(computerGroupApiDto);
        }
        return groupApiDtoList;
    }

    /**
     * 保存\执行常用任务或者克隆任务
     * @param scriptExecAuditDto 执行参数
     */
    @Override
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public Map<String,Long> execAuditTemplateTask(ScriptExecAuditDto scriptExecAuditDto) throws ScriptException {
        Map<String, Long> res;
        try {
            Long taskId = scriptExecAuditDto.getTaskInfo().getId();
            //先执行任务申请
            CurrentUser user = CurrentUserUtil.getCurrentUser();
            if (null == user) {
                throw new ScriptException("current.user.empty");
            }
            //将数据迁移到ieai_script_task_attament表中
            //1、根据前端传递过来的附件id查询附件
            List<TaskAttachment> taskAttachments = taskAttachmentTemplateMapper.selectTaskAttachmentByTaskId(scriptExecAuditDto.getTaskInfo().getId());
            //2、将本次需要的附件存到ieai_script_task_attachment
            List<TaskAttachment> taskAttachmentList = new ArrayList<>();
            for(TaskAttachment attachment : taskAttachments){
                TaskAttachment taskAttachment = new TaskAttachment();
                taskAttachment.setSize(attachment.getSize());
                taskAttachment.setContents(attachment.getContents());
                taskAttachment.setName(attachment.getName());
                taskAttachment.setScriptTaskId(0L);
                taskAttachmentMapper.insertTaskAttachment(taskAttachment);
                taskAttachmentList.add(taskAttachment);
            }
            //将本次传递的附件替换成刚刚保存到ieai_script_task_attachment表里的数据
            scriptExecAuditDto.setScriptTempAttachments(BeanUtils.copy(taskAttachmentList, AttachmentDto.class));
            //查询本次任务是常用任务还是克隆任务
            TaskCloneBean taskCloneBean = taskTemplateMapper.selectTaskTemplateById(scriptExecAuditDto.getTaskInfo().getId());
            //查询为空，返回不存在提示
            if(Objects.isNull(taskCloneBean)){
                throw new ScriptException("script.template.task.no.exist");
            }
            scriptExecAuditDto.getTaskInfo().setId(null);
            //调用任务申请方法，包含任务id和双人复核服务id，白名单的时候双人复核id为空
            res = taskApplyService.scriptExecAuditingForWeb(scriptExecAuditDto, user, taskApplySource);

            //判断数据类型，如果是克隆任务，任务申请结束就删除，如果是常用任务，不删除任务
            if(taskCloneBean.getTaskType() == 1){
                deleteCloneTask(taskId);
            }
        }catch (ScriptException e){
            throw new ScriptException("script.clone.task.start.error",e);
        }
        return res;
    }

    /**
     * 更新常用/克隆任务
     * @param scriptExecAuditDto 执行参数
     * @return 成功标识
     * @throws ScriptException 脚本服务化异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAuditTemplateTask(ScriptExecAuditDto scriptExecAuditDto) throws ScriptException {
        try {
            //更新task信息，包括任务名称、并发数等
            Long auditorId = scriptExecAuditDto.getAuditUserId();
            scriptExecAuditDto.getTaskInfo().setAuditorId(auditorId);
            taskTemplateMapper.updateById(BeanUtils.copy(scriptExecAuditDto.getTaskInfo(), Task.class));
            //更新ips
            updateChoseAgentIps(scriptExecAuditDto);
            //更新资源组
            updateChoseResGroups(scriptExecAuditDto);
            //更新参数
            updateTaskParams(scriptExecAuditDto);
            //更新附件
            updateAttachment(scriptExecAuditDto);

        }catch (ScriptException e){
            throw new ScriptException("script.update.template.task.error",e);
        }
        return true;
    }

    /**
     * 删除克隆任务
     * @param taskId 任务id
     */
    private void deleteCloneTask(Long taskId){
        //删除ieai_script_task_temp以及其它表相关数据
        taskTemplateMapper.deleteById(taskId);
        taskParamsTemplateMapper.deleteByTaskId(taskId);
        taskAttachmentTemplateMapper.deleteByTaskId(taskId);
        taskGroupsTemplateMapper.deleteByTaskId(taskId);
        taskIpsTemplateMapper.deleteByTaskId(taskId);
    }

    /**
     * 删除常用/克隆任务
     * @param taskId 任务id
     * @throws ScriptException 脚本服务化异常
     */
    @Override
    public void deleteTaskTemplate(Long taskId) throws ScriptException{
        deleteCloneTask(taskId);
    }

    /**
     * 更新ips数据
     * @param scriptExecAuditDto 任务数据信息dto
     */
    private void updateChoseAgentIps(ScriptExecAuditDto scriptExecAuditDto) throws ScriptException{
        try {
            Long taskId = scriptExecAuditDto.getTaskInfo().getId();
            //先删除已经绑定的agent-ips数据
            taskIpsTemplateMapper.deleteByTaskId(taskId);
            //获取前台传过来的agent数据
            List<AgentInfoDto> choseAgentUsers = scriptExecAuditDto.getChosedAgentUsers();
            //根据agentip与端口查询本次选中的agent数据
            List<AgentInfo> agentInfos = agentInfoService.selectAgentInfoByIpAndPort(choseAgentUsers);
            if(!choseAgentUsers.isEmpty()){
                List<TaskIps> saveList = new ArrayList<>();
                //先要删除agent-ips列表数据，然后再新增
                for(AgentInfo agentInfo : agentInfos){
                    TaskIps taskIps = new TaskIps();
                    taskIps.setScriptTaskId(taskId);
                    taskIps.setScriptAgentinfoId(agentInfo.getId());
                    taskIps.setScriptAgentinfoId(agentInfo.getId());
                    saveList.add(taskIps);
                }
                this.batchData(saveList , taskIpsTemplateMapper::insertTaskIps);
            }
        }catch (Exception e){
            throw new ScriptException(e.getMessage());
        }

    }

    /**
     * 更新资源组信息
     * @param scriptExecAuditDto 参数信息
     * @throws ScriptException 脚本服务化异常
     */
    private void updateChoseResGroups(ScriptExecAuditDto scriptExecAuditDto) throws ScriptException{
        try {
            Long taskId = scriptExecAuditDto.getTaskInfo().getId();
            //先删除已经绑定的agent-ips数据
            taskGroupsTemplateMapper.deleteByTaskId(taskId);
            //获取前台传过来的资源组数据
            List<TaskGroupsDto> choseResGroups = scriptExecAuditDto.getChosedResGroups();
            if(!choseResGroups.isEmpty()){
                List<TaskGroups> saveList = new ArrayList<>();
                //先要删除agent-ips列表数据，然后再新增
                for(TaskGroupsDto taskGroupsDto : choseResGroups){
                    TaskGroups taskGroup = new TaskGroups();
                    taskGroup.setScriptTaskId(taskId);
                    taskGroup.setSysmComputerGroupId(taskGroupsDto.getSysmComputerGroupId());
                    taskGroup.setCpname(taskGroupsDto.getCpname());
                    saveList.add(taskGroup);
                }
                this.batchData(saveList , taskGroupsTemplateMapper::insertTaskGroups);
            }
        }catch (Exception e){
            throw new ScriptException(e.getMessage());
        }

    }

    /**
     * 更新参数信息
     * @param scriptExecAuditDto 任务数据信息dto
     */
    private void updateTaskParams(ScriptExecAuditDto scriptExecAuditDto) throws ScriptException{
        try {
            //页面传递过来的参数信息
            List<ParameterDto> params = scriptExecAuditDto.getParams();
            if(!params.isEmpty()){
                //获取当前
                Long taskId = scriptExecAuditDto.getTaskInfo().getId();
                List<TaskParams> taskParams = taskParamsTemplateMapper.selectTaskParamsByTaskId(taskId);
                for(TaskParams taskParams1 : taskParams){
                    for(ParameterDto parameterDto : params){
                        if(taskParams1.getOrder().intValue() == parameterDto.getParamOrder().intValue()){
                            taskParams1.setValue(parameterDto.getParamDefaultValue());
                        }
                    }
                }
                taskParamsTemplateMapper.batchUpdateByIds(taskParams);
            }
        } catch (Exception e){
            throw new ScriptException(e.getMessage());
        }

    }

    /**
     * 更新附件信息
     * @param scriptExecAuditDto 参数信息
     */
    private void updateAttachment(ScriptExecAuditDto scriptExecAuditDto) throws ScriptException{
        try {
            Long taskId = scriptExecAuditDto.getTaskInfo().getId();
            List<AttachmentDto> scriptTempAttachments = scriptExecAuditDto.getScriptTempAttachments();
            if(!scriptTempAttachments.isEmpty()){
                Long [] ids = new Long[scriptTempAttachments.size()];
                int index = 0;
                for(AttachmentDto attachmentDto : scriptTempAttachments){
                    ids[index++] = attachmentDto.getId();
                }
                taskAttachmentTemplateMapper.batchUpdateByIds(taskId,ids);
            }
        }catch (Exception e){
            throw new ScriptException(e.getMessage());
        }
    }

    /**
     * 下载附件
     * @param id 附件id
     * @return 附件对象
     */
    @Override
    public TaskAttachmentDto selectAttachmentById(Long id) {
        return BeanUtils.copy(taskAttachmentTemplateMapper.selectAttachmentById(id), TaskAttachmentDto.class);
    }

    /**
     * 上传附件
     * @param taskAttachmentDto  文件
     * @return 附件信息
     * @throws ScriptException 业务异常
     */
    @Override
    public TaskAttachmentDto uploadAttachment(TaskAttachmentDto taskAttachmentDto) throws ScriptException {
        TaskAttachment taskAttachment = new TaskAttachment();
        taskAttachment.setName(taskAttachmentDto.getName());
        taskAttachment.setSize(taskAttachmentDto.getSize());
        taskAttachment.setContents(taskAttachmentDto.getContents());
        taskAttachment.setScriptTaskId(taskAttachmentDto.getScriptTaskId());
        //验证文件大小
        fileSizeValidUtil.validateFileSize(taskAttachment.getSize());
        taskAttachmentTemplateMapper.insertTaskAttachment(taskAttachment);
        return BeanUtils.copy(taskAttachment, TaskAttachmentDto.class);
    }

    /**
     * 根据常用任务id查询当前常用任务的附件
     * @param scriptTaskId 任务id
     * @return 附件集合
     */
    @Override
    public List<AttachmentUploadDto> getTaskTemplateAttachment(Long scriptTaskId){
        List<TaskAttachment> taskAttachments = taskAttachmentTemplateMapper.selectTaskAttachmentByTaskId(scriptTaskId);
        List<AttachmentUploadDto> attachmentUploadDtoList = new ArrayList<>();
        for(TaskAttachment taskAttachment : taskAttachments){
            AttachmentUploadDto attachmentUploadDto = new AttachmentUploadDto();
            attachmentUploadDto.setId(taskAttachment.getId());
            attachmentUploadDto.setName(taskAttachment.getName());
            attachmentUploadDto.setContents(taskAttachment.getContents());
            attachmentUploadDto.setSize(taskAttachment.getSize());
            AttachmentResponseDto attachmentResponseDto = new AttachmentResponseDto();
            attachmentResponseDto.setId(taskAttachment.getId());
            attachmentUploadDto.setResponse(attachmentResponseDto);
            attachmentUploadDtoList.add(attachmentUploadDto);
        }
        return attachmentUploadDtoList;
    }

    /**
     * 根据id删除附件
     * @param id 附件id
     */
    @Override
    public void deleteAttachmentTemplate(Long id) {
        taskAttachmentTemplateMapper.deleteById(id);
    }

    /**
     * 启动常用任务
     *
     * @param startCommonTaskDto 启动常用任务参数
     * @param user               当前用户
     * @return ScriptTaskApplyResDto 任务id和双人复核服务id
     * @throws ScriptException 脚本服务化异常
     */
    @Override
    public ScriptTaskApplyResDto startCommonTask(StartCommonTaskDto startCommonTaskDto, CurrentUser user) throws ScriptException {
        Long commonTaskId = startCommonTaskDto.getCommonTaskId();
        Long auditUserId = startCommonTaskDto.getAuditUserId();

        // 1. 根据常用任务ID查询任务基本信息、脚本风险级别、脚本分类id
        TaskCloneBean taskCloneBean = Optional.ofNullable(taskTemplateMapper.selectTaskTemplateById(commonTaskId))
                .orElseThrow(() -> new ScriptException("script.template.task.no.exist"));

        // 2.获取脚本风险级别、分类id
        Integer level = Optional.ofNullable(taskCloneBean.getLevel())
                .orElseThrow(() -> new ScriptException("script.level.not.exist"));
        Long categoryId = Optional.ofNullable(taskCloneBean.getCategoryId())
                .orElseThrow(() -> new ScriptException("script.category.not.exist"));

        // 3. 判断脚本类型并进行审核权限校验
        boolean isRiskScript = isRiskScript(level);
        if (isRiskScript) {
            // 风险脚本需要校验审核人ID和权限
            Optional.ofNullable(auditUserId)
                    .orElseThrow(() -> new ScriptException("audit.user.id.required.for.risk.script"));
            // 校验审核人是否有审核权限
            validateAuditPermission(auditUserId, categoryId);
        }

        // 4. 组织ScriptExecAuditDto对象
        ScriptExecAuditDto scriptExecAuditDto = buildScriptExecAuditDto(taskCloneBean, auditUserId, startCommonTaskDto);

        // 5. 调用现有的execAuditTemplateTask方法
        Map<String, Long> stringLongMap = taskTemplateService.execAuditTemplateTask(scriptExecAuditDto);
        ScriptTaskApplyResDto res = new ScriptTaskApplyResDto();
        res.setTaskId(stringLongMap.get("taskId"));
        res.setAuditId(stringLongMap.get("auditId"));
        return res;
    }

    /**
     * 判断是否为风险脚本
     * @param level 脚本风险级别
     * @return true-风险脚本，false-白名单脚本
     */
    private boolean isRiskScript(Integer level) {
        // 根据业务规则判断：0 - 白名单，1 - 高风险级别， 2 - 中风险级别， 3 - 低风险级别
        // 只有level为0时是白名单脚本，其他都是风险脚本
        return Optional.ofNullable(level)
                .map(l -> l != 0)
                .orElse(true); // 如果level为null，默认认为是风险脚本
    }

    /**
     * 校验审核人是否有审核权限
     * @param auditUserId 审核人ID
     * @param categoryId 分类ID
     * @throws ScriptException 权限校验失败异常
     */
    private void validateAuditPermission(Long auditUserId, Long categoryId) throws ScriptException {
        ServicePermissionApiQueryDto queryDto = new ServicePermissionApiQueryDto();
        queryDto.setPermissionCodes(Collections.singletonList("task_apply_approval"));
        List<UserInfoApiDto> permissionUsers = taskApplyService.queryPermissionUserInfoList(queryDto, categoryId);
        boolean hasPermission = permissionUsers.stream()
                                               .anyMatch(user -> user.getId().equals(auditUserId));
        if (!hasPermission) {
            throw new ScriptException("audit.user.no.permission");
        }
    }

    /**
     * 常用任务API启动接口，构建ScriptExecAuditDto对象
     * @param taskCloneBean 常用任务信息
     * @param auditUserId 审核人ID
     * @return ScriptExecAuditDto对象
     * @throws ScriptException 构建异常
     */
    private ScriptExecAuditDto buildScriptExecAuditDto(TaskCloneBean taskCloneBean, Long auditUserId, StartCommonTaskDto startCommonTaskDto) throws ScriptException {
        try {
            ScriptExecAuditDto scriptExecAuditDto = new ScriptExecAuditDto();

            // 设置基本信息
            scriptExecAuditDto.setScriptInfoVersionId(taskCloneBean.getScriptInfoVersionId());
            scriptExecAuditDto.setExecuser(taskCloneBean.getExecuser());
            scriptExecAuditDto.setAuditUserId(auditUserId);
            scriptExecAuditDto.setStartType(Integer.parseInt(Enums.ScriptStartType.SCRIPT.getStartValue())); // 脚本服务化

            // 设置任务信息
            TaskDto taskDto = new TaskDto();
            taskDto.setId(taskCloneBean.getIid());
            taskDto.setTaskName(taskCloneBean.getTaskName());
            taskDto.setEachNum(taskCloneBean.getEachNum());
            taskDto.setDriveMode(taskCloneBean.getDriveMode());
            taskDto.setTimeout((long) taskCloneBean.getTimeout());
            scriptExecAuditDto.setTaskInfo(taskDto);

            // 查询并设置参数信息
            List<TaskParams> taskParamsList = Optional.ofNullable(taskParamsTemplateMapper.selectTaskParamsByTaskId(taskCloneBean.getIid()))
                    .orElse(Collections.emptyList());
            List<ParameterDto> parameterDtos = getParameterDtos(taskParamsList,startCommonTaskDto);
            scriptExecAuditDto.setParams(parameterDtos);

            // 查询IP信息
            List<AgentInfo> agentInfoList = Optional.ofNullable(taskIpsTemplateMapper.selectAgentInfoByTaskId(taskCloneBean.getIid()))
                    .orElse(Collections.emptyList());
            if (!agentInfoList.isEmpty()) {
                // 非设备组任务
                scriptExecAuditDto.setResGroupFlag(false);
                List<AgentInfoDto> agentInfoDtos = BeanUtils.copy(agentInfoList, AgentInfoDto.class);
                scriptExecAuditDto.setChosedAgentUsers(agentInfoDtos);
            } else {
                // 查询设备组信息
                List<TaskGroups> taskGroupsList = Optional.ofNullable(taskGroupsTemplateMapper.selectTaskGroupsByTaskId(taskCloneBean.getIid()))
                        .orElse(Collections.emptyList());

                if (!taskGroupsList.isEmpty()) {
                    scriptExecAuditDto.setResGroupFlag(true);
                    List<TaskGroupsDto> taskGroupsDtos = taskGroupsList.stream()
                            .map(taskGroups -> {
                                TaskGroupsDto taskGroupsDto = new TaskGroupsDto();
                                taskGroupsDto.setSysmComputerGroupId(taskGroups.getSysmComputerGroupId());
                                taskGroupsDto.setCpname(taskGroups.getCpname());
                                return taskGroupsDto;
                            })
                            .collect(Collectors.toList());
                    scriptExecAuditDto.setChosedResGroups(taskGroupsDtos);
                } else {
                    throw new ScriptException("no.agent.for.script.task");
                }
            }

            return scriptExecAuditDto;

        } catch (Exception e) {
            if (e instanceof ScriptException) {
                throw (ScriptException) e;
            }else {
                throw new ScriptException("Exception occurred while starting the common task" ,e);
            }
        }
    }

    /**
     * 常用任务API启动接口，将TaskParams列表转换为ParameterDto列表
     * @param taskParamsList TaskParams列表
     * @return ParameterDto列表
     */
    private static List<ParameterDto> getParameterDtos(List<TaskParams> taskParamsList,StartCommonTaskDto startCommonTaskDto) {
        //如果传递了参数，使用传递的参数，否则使用默认参数
        List<ParameterValidationDto> params = startCommonTaskDto.getParams();
        if(ObjectUtils.notEqual(params,null) && !params.isEmpty()){
            return Optional.of(params)
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(Objects::nonNull)
                    .map(taskParams -> {
                        ParameterDto parameterDto = new ParameterDto();
                        parameterDto.setParamOrder(taskParams.getParamOrder());
                        parameterDto.setParamDefaultValue(taskParams.getParamDefaultValue());
                        parameterDto.setParamType(taskParams.getParamType());
                        parameterDto.setParamDesc(taskParams.getParamDesc());
                        return parameterDto;
                    })
                    .collect(Collectors.toList());
        }else{
            return Optional.ofNullable(taskParamsList)
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(Objects::nonNull)
                    .map(taskParams -> {
                        ParameterDto parameterDto = new ParameterDto();
                        parameterDto.setParamOrder(taskParams.getOrder());
                        parameterDto.setParamDefaultValue(taskParams.getValue());
                        parameterDto.setParamType(taskParams.getType());
                        parameterDto.setParamDesc(taskParams.getDesc());
                        return parameterDto;
                    })
                    .collect(Collectors.toList());
        }

    }
}
