package com.ideal.script.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.common.util.batch.Batch;
import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.common.util.BatchDataUtil;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.TaskIpsMapper;
import com.ideal.script.model.dto.AgentInfoDto;
import com.ideal.script.model.dto.ScriptExecAuditDto;
import com.ideal.script.model.dto.TaskDto;
import com.ideal.script.model.dto.TaskIpsDto;
import com.ideal.script.model.entity.AgentInfo;
import com.ideal.script.model.entity.TaskIps;
import com.ideal.script.service.IAgentInfoService;
import com.ideal.script.service.ITaskIpsService;
import org.apache.ibatis.session.SqlSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 任务与agent关系Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class TaskIpsServiceImpl implements ITaskIpsService, Batch {
    private static final Logger logger = LoggerFactory.getLogger(TaskIpsServiceImpl.class);

    private final TaskIpsMapper taskIpsMapper;
    private final IAgentInfoService agentInfoService;
    private final BatchDataUtil batchDataUtil;
    private final AsyncAgentQueryService asyncAgentQueryService;

    public TaskIpsServiceImpl(TaskIpsMapper taskIpsMapper, IAgentInfoService agentInfoService,
                             BatchDataUtil batchDataUtil, AsyncAgentQueryService asyncAgentQueryService) {
        this.taskIpsMapper = taskIpsMapper;
        this.agentInfoService = agentInfoService;
        this.batchDataUtil = batchDataUtil;
        this.asyncAgentQueryService = asyncAgentQueryService;
    }

    /**
     * 查询任务与agent关系
     *
     * @param id 任务与agent关系主键
     * @return 任务与agent关系
     */
    @Override
    public TaskIpsDto selectTaskIpsById(Long id) {
        return BeanUtils.copy(taskIpsMapper.selectTaskIpsById(id), TaskIpsDto.class);
    }

    /**
     * 查询任务与agent关系列表
     *
     * @param taskIpsDto 任务与agent关系
     * @return 任务与agent关系
     */
    @Override
    public PageInfo<TaskIpsDto> selectTaskIpsList(TaskIpsDto taskIpsDto, int pageNum, int pageSize) {
        PageMethod.startPage(pageNum, pageSize);
        List<TaskIps> taskIpsList = new ArrayList<>();
        if (null != taskIpsDto) {
            TaskIps taskIps = BeanUtils.copy(taskIpsDto, TaskIps.class);
            taskIpsList = taskIpsMapper.selectTaskIpsList(taskIps);
        }
        return PageDataUtil.toDtoPage(taskIpsList, TaskIpsDto.class);

    }

    /**
     * 新增任务与agent关系
     *
     * @param taskIpsDto 任务与agent关系
     * @return 结果
     */
    @Override
    public int insertTaskIps(TaskIpsDto taskIpsDto) {
        TaskIps taskIps = BeanUtils.copy(taskIpsDto, TaskIps.class);
        return taskIpsMapper.insertTaskIps(taskIps);
    }

    /**
     * 修改任务与agent关系
     *
     * @param taskIpsDto 任务与agent关系
     * @return 结果
     */
    @Override
    public int updateTaskIps(TaskIpsDto taskIpsDto) {
        TaskIps taskIps = BeanUtils.copy(taskIpsDto, TaskIps.class);
        return taskIpsMapper.updateTaskIps(taskIps);
    }

    /**
     * 批量删除任务与agent关系
     *
     * @param ids 需要删除的任务与agent关系主键
     * @return 结果
     */
    @Override
    public int deleteTaskIpsByIds(Long[] ids) {
        return taskIpsMapper.deleteTaskIpsByIds(ids);
    }

    /**
     * 删除任务与agent关系信息
     *
     * @param id 任务与agent关系主键
     * @return 结果
     */
    @Override
    public int deleteTaskIpsById(Long id) {
        return taskIpsMapper.deleteTaskIpsById(id);
    }

    /**
     * 删除任务与agent关系信息
     *
     * @param taskId 任务id
     * @return 结果
     */
    @Override
    public int deleteTaskIpsByTaskId(Long taskId) {
        return taskIpsMapper.deleteTaskIpsByTaskId(taskId);
    }


    /**
     * 存储agent信息
     *
     * @param scriptExecAuditDto 任务申请提交审核Dto
     * @param taskInfo           任务申请提交任务时的脚本任务对象
     * @param sqlSession         ​SqlSession​ 对象
     * <AUTHOR>
     */
    @Override
    public void saveTaskIps(ScriptExecAuditDto scriptExecAuditDto, TaskDto taskInfo, SqlSession sqlSession) throws ScriptException {
        List<TaskIps> taskIpsList = buildTaskIpsList(scriptExecAuditDto, taskInfo);

        if (!taskIpsList.isEmpty()) {
            TaskIpsMapper taskIpsMapperSession = sqlSession.getMapper(TaskIpsMapper.class);
            try {
                batchDataUtil.batchData(TaskIpsMapper.class, taskIpsList, TaskIps.class, "insertTaskIps", sqlSession, taskIpsMapperSession);
            } catch (ScriptException ex) {
                logger.error("saveTaskIps error:", ex);
                throw new ScriptException("save.taskIps.error");
            }
        }

    }


    private List<TaskIps> buildTaskIpsList(ScriptExecAuditDto scriptExecAuditDto, TaskDto taskInfo) throws ScriptException {
        List<TaskIps> taskIpsList = new ArrayList<>();
        List<AgentInfoDto> agentUsers = scriptExecAuditDto.getChosedAgentUsers();

        // 早期返回，避免不必要的处理
        if (agentUsers == null || agentUsers.isEmpty()) {
            return taskIpsList;
        }

        // 分批处理，避免Oracle IN子句1000参数限制
        final int BATCH_SIZE = 1000;

        // 步骤1 并发启动所有批次的查询任务
        List<CompletableFuture<List<AgentInfo>>> futures = IntStream.range(0, (agentUsers.size() + BATCH_SIZE - 1) / BATCH_SIZE)
                                                                    .mapToObj(i -> {
                int startIndex = i * BATCH_SIZE;
                int endIndex = Math.min(startIndex + BATCH_SIZE, agentUsers.size());
                List<AgentInfoDto> batchAgents = agentUsers.subList(startIndex, endIndex);
                return asyncAgentQueryService.queryAgentsBatchAsync(batchAgents);
            }).collect(Collectors.toList());

        // 步骤2：等待所有任务完成并合并结果
        try {
            CompletableFuture<Void> allDone = CompletableFuture.allOf(
                    futures.toArray(new CompletableFuture[0])
            );
            allDone.get(); // 等待所有完成
            // 步骤3：获取合并后的结果并处理
            List<AgentInfo> allExistingAgents = futures.stream()
                                                       .map(CompletableFuture::join) // 此时 join 不会阻塞
                                                       .flatMap(List::stream)
                                                       .collect(Collectors.toList());
            // 步骤4：一次性处理所有合并后的结果
            processAllBatchResults(agentUsers, allExistingAgents, scriptExecAuditDto, taskInfo, taskIpsList);

        } catch (InterruptedException | ExecutionException e) {
            logger.error("buildTaskIpsList error:", e);
            throw new ScriptException("buildTaskIpsList error:" + e.getCause().getMessage());
        }

        return taskIpsList;
    }

    /**
     * 一次性处理所有合并后的查询结果
     *
     * @param agentUsers 所有Agent列表
     * @param allExistingAgents 所有批次查询到的已存在Agent列表
     * @param scriptExecAuditDto 脚本任务提交审核Dto
     * @param taskInfo 运行时任务基础信息
     * @param taskIpsList 任务与agent关系列表（用于添加结果）
     */
    private void processAllBatchResults(List<AgentInfoDto> agentUsers,
                                       List<AgentInfo> allExistingAgents,
                                       ScriptExecAuditDto scriptExecAuditDto,
                                       TaskDto taskInfo,
                                       List<TaskIps> taskIpsList) {

        logger.debug("开始处理合并后的查询结果，总Agent数量：{}，查询到已存在：{}",
                    agentUsers.size(), allExistingAgents.size());

        // 步骤1：构建快速查找Map，key为"IP:Port"格式
        Map<String, AgentInfo> existingAgentMap = allExistingAgents.stream()
            .collect(Collectors.toMap(
                agent -> agent.getAgentIp() + ":" + agent.getAgentPort(),
                agent -> agent
            ));

        // 步骤2：找出需要插入的agent（不存在的agent）
        List<AgentInfoDto> agentsToInsert = agentUsers.stream()
            .filter(agent -> !existingAgentMap.containsKey(agent.getAgentIp() + ":" + agent.getAgentPort()))
            .peek(agentInfoDto -> agentInfoDto.setId(null)) // 重置ID属性，以IDGenerate注解为准
            .collect(Collectors.toList());

        // 步骤3：一次性批量插入所有不存在的agent
        if (!agentsToInsert.isEmpty()) {
            logger.debug("需要插入{}个新Agent，开始批量插入", agentsToInsert.size());
            // 使用Batch接口的batchData方法进行批量插入
            this.batchData(agentsToInsert, agentInfoService::insertAgentInfo);
            logger.debug("批量插入完成，插入了{}个新Agent", agentsToInsert.size());
        }

        // 步骤4：为每个agent构建TaskIps对象
        for (AgentInfoDto agentInfoDto : agentUsers) {

            String key = agentInfoDto.getAgentIp() + ":" + agentInfoDto.getAgentPort();
            AgentInfo existingAgent = existingAgentMap.get(key);

            if (existingAgent != null) {
                // 如果agent已存在，设置其ID
                agentInfoDto.setId(existingAgent.getId());
            }
            // 如果是新插入的agent，ID已在insertAgentInfo中设置

            // 构建TaskIps对象（保持原有逻辑）
            TaskIps taskIps = getTaskIps(scriptExecAuditDto, taskInfo, agentInfoDto);
            taskIpsList.add(taskIps);
        }
        logger.debug("合并处理完成，总共生成TaskIps数量：{}", agentUsers.size());
    }

    /**
     * 组织任务与agent关系对象
     * @param scriptExecAuditDto    脚本任务提交审核Dto
     * @param taskInfo  运行时任务基础信息
     * @param agentInfoDto  Agent基本信息
     * @return  任务与agent关系对象信息
     */
    private static TaskIps getTaskIps(ScriptExecAuditDto scriptExecAuditDto, TaskDto taskInfo, AgentInfoDto agentInfoDto) {
        TaskIps taskIps = new TaskIps();
        taskIps.setId(null);
        taskIps.setScriptTaskId(taskInfo != null ? taskInfo.getId() : null);
        taskIps.setScriptAgentinfoId(agentInfoDto.getId());
        taskIps.setExecUserName(agentInfoDto.getExecUserName() == null || ("").equals(agentInfoDto.getExecUserName()) ? scriptExecAuditDto.getExecuser() : agentInfoDto.getExecUserName());
        taskIps.setAlreadyimpFlag(Enums.ExecutionStatus.NOT_EXECUTED.getValue());
        taskIps.setStartType(Enums.BusinessType.SCRIPT_SERVICE.getValue());
        return taskIps;
    }

}
