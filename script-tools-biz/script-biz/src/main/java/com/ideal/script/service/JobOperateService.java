package com.ideal.script.service;

import com.ideal.script.exception.ScheduleJobOperateException;
import com.ideal.script.model.dto.ScheduleJobTaskDto;

/**
 * <AUTHOR>
 */
public interface JobOperateService {

    /**
     * 创建任务
     *
     * @param scheduleJobTaskDto 任务创建实体
     * @throws ScheduleJobOperateException 定时任务处理异常
     * @return xxJob服务对应任务的Id
     */
    Integer createAndStartJob(ScheduleJobTaskDto scheduleJobTaskDto) throws ScheduleJobOperateException;

    /**
     * 修改任务
     *
     * @param scheduleJobTaskDto 任务修改实体
     * @return 操作结果
     */
    boolean modifyJob(ScheduleJobTaskDto scheduleJobTaskDto);

    /**
     * 停止任务
     *
     * @param xxJobTaskId xxjob服务对应任务的Id
     * @return 操作结果
     */
    boolean stopJob(Integer xxJobTaskId);

    /**
     * 启动任务
     *
     * @param xxJobTaskId xxjob服务对应任务的Id
     * @return 操作结果
     */
    Boolean startJob(Integer xxJobTaskId);
}
