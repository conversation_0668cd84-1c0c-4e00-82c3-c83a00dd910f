package com.ideal.script.controller;

import com.ideal.common.dto.R;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.permission.MenuPermitConstant;
import com.ideal.script.dto.ScriptInfoDto;
import com.ideal.script.dto.ScriptVersionDto;
import com.ideal.script.service.IInfoVersionService;
import com.ideal.system.common.component.aop.MethodPermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 【脚本版本信息】Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("${app.script-tools-url:}/version")
@MethodPermission(MenuPermitConstant.MY_SCRIPT_PER)
@Validated
public class InfoVersionController
{
    private final IInfoVersionService infoVersionService;
    private static final Logger logger = LoggerFactory.getLogger(InfoVersionController.class);


    public InfoVersionController(IInfoVersionService infoVersionService){
       this.infoVersionService=infoVersionService;
    }


    /**
     * 根据version表主键id集合获取对应的脚本集合
     * @param ids   版本id集合
     * @return  脚本集合
     */
    @GetMapping(value = "/getInfoVersionInfoList")
    public R<List<ScriptVersionDto>> getInfoVersionInfoList(@RequestParam(value = "ids") Long[] ids)
    {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, infoVersionService.getInfoVersionInfoList(ids), Constants.LIST_SUCCESS);
    }


    /**
     * 根据版本id查询脚本信息
     */
    @GetMapping("/getInfoByVersionUuid")
    @MethodPermission(MenuPermitConstant.MY_SCRIPT_OR_SCHEDULED_TASK_PER)
    public R<ScriptInfoDto> getInfoByVersionUuid (@RequestParam(value = "versionUuid") String versionUuid){

        try {
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, infoVersionService.getInfoByVersionUuid(versionUuid),Constants.LIST_SUCCESS);
        } catch (Exception e) {
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, e.getMessage());
        }
    }

    /**
     * 根据主表id获取版本id
     */
    @GetMapping("/getVersionIdByIds")
    @MethodPermission(MenuPermitConstant.MY_SCRIPT_OR_DOUBLE_CHECK_PER)
    public R<List<Long>> getVersionIdByIds (Long[] ids){

        try {
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, infoVersionService.getVersionIdByIds(ids),Constants.LIST_SUCCESS);
        } catch (Exception e) {
            logger.error("getVersionIdByIds fail",e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, e.getMessage());
        }
    }

    @GetMapping("/getTaskCountByVersionId")
    public R<Integer> getTaskCountByVersionId (@RequestParam(value = "versionId") Long versionId){

        try {
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, infoVersionService.getTaskCountByVersionId(versionId),Constants.LIST_SUCCESS);
        } catch (Exception e) {
            logger.error("getTaskCountByVersionId fail",e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, e.getMessage());
        }
    }

    /**
     * V9迁移常用任务使用
     * @param srcVersionUuid 版本uuid
     * @return 版本id
     */
    @GetMapping("/getScriptVersionInfoId")
    public R<Long> getScriptVersionInfoIds (@NotBlank String  srcVersionUuid){
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, infoVersionService.selectIdBySrcScriptUuid(srcVersionUuid),"查询成功");
    }
}
