package com.ideal.script.service;

import com.github.pagehelper.PageInfo;
import com.ideal.script.dto.AttachmentDto;
import com.ideal.script.dto.ScriptAttachmentTempMegDto;
import com.ideal.script.dto.ScriptFileAttachmentTempApiDto;
import com.ideal.script.exception.ScriptException;

import java.io.IOException;
import java.util.List;

/**
 * Service接口
 *
 * <AUTHOR>
 */
public interface IAttachmentService {
    /**
     * 查询
     *
     * @param id 主键
     * @return 
     */
    AttachmentDto selectAttachmentById(Long id);

    /**
     * 根据id批量查询附件信息
     * @param ids   附件主键id
     * @return  附件信息列表
     */
    List<AttachmentDto> selectAttachmentByIds(Long[] ids);
    /**
     * 查询
     *
     * @param attachmentDto dto
     * @param pageNum       起始页
     * @param pageSize      每页大小
     * @return              pageInfo
     */
    PageInfo<AttachmentDto> selectAttachmentList(AttachmentDto attachmentDto, int pageNum, int pageSize);

    /**
     * 新增
     *
     * @param attachmentDto 
     * @return  结果
     * */
    int insertAttachment(AttachmentDto attachmentDto);

    /**
     * 修改
     *
     * @param attachmentDto 
     */
    void updateAttachment(AttachmentDto attachmentDto);

    /**
     * 批量删除
     *
     * @param ids 需要删除的主键集合
     */
    void deleteAttachmentByIds(Long[] ids);

    /**
     * 删除信息
     *
     * @param id 主键
     * @return 结果
     */
    int deleteAttachmentById(Long id);

    /**
     * 上传附件
     *
     * @param attachmentDto  文件
     * @return      dto
     * @throws IOException exception
     */
    AttachmentDto uploadAttachment(AttachmentDto attachmentDto) throws IOException, ScriptException;

    /**
     * 根据版本srcScriptUuid查询绑定的附件
     * @param srcScriptUuid 版本uuid
     * @return List<AttachmentDto>
     */
    List<AttachmentDto> getAttachmentByUuid(String srcScriptUuid);

    /**
     * 上传临时附件dubbo接口
     * @param scriptFileAttachmentTempList 附件参数
     * @return 上传成功附件id信息
     * @throws ScriptException 脚本服务化异常
     * @throws IOException IO异常
     */
    ScriptAttachmentTempMegDto uploadAttachmentTemp(List<ScriptFileAttachmentTempApiDto> scriptFileAttachmentTempList) throws ScriptException, IOException;

}
