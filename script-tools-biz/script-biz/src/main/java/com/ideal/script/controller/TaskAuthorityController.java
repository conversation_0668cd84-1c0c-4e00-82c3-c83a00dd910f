package com.ideal.script.controller;

import com.ideal.audit.producer.annotation.Auditable;
import com.ideal.common.dto.R;
import com.ideal.sc.util.ValidationUtils;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.permission.MenuPermitConstant;
import com.ideal.script.common.constant.permission.MyScriptBtnPermitConstant;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.ScriptDeleteDto;
import com.ideal.script.model.dto.TaskAuthorityDto;
import com.ideal.script.service.IMyScriptService;
import com.ideal.script.service.ITaskAuthorityService;
import com.ideal.system.common.component.aop.MethodPermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 *  任务权限（任务启用、任务禁用、任务删除）Controller
 * <AUTHOR>
 */
@RestController
@RequestMapping("${app.script-tools-url:}/taskAuthority")
@MethodPermission(MenuPermitConstant.MY_SCRIPT_PER)
public class TaskAuthorityController {
    private static final Logger logger = LoggerFactory.getLogger(TaskAuthorityController.class);

    private final ITaskAuthorityService taskAuthorityService;
    private final IMyScriptService myScriptService;

    public TaskAuthorityController(ITaskAuthorityService taskAuthorityService, IMyScriptService myScriptService) {
        this.taskAuthorityService = taskAuthorityService;
        this.myScriptService = myScriptService;
    }

    /**
    * 任务启用/禁用
    *
    * @param taskAuthorityDto 任务权限Dto
    * @return R<Object>
    */
    @PostMapping("/updateUseState")
    @Auditable("我的脚本|启用禁用")
    @MethodPermission(MyScriptBtnPermitConstant.UPDATE_USE_STATE_PER)
    public R<Object> updateUseState(@RequestBody TaskAuthorityDto taskAuthorityDto)
    {
        try {
            boolean result = taskAuthorityService.updateUseState(taskAuthorityDto);
            if(result){
                return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE,  "update.success");
            }else {
                return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "update.error");
            }
        } catch (ScriptException e) {
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "update.error");
        }
    }

    /**
     * 任务启用/禁用
     *
     * @param taskAuthorityDto 任务权限Dto
     * @return R<Object>
     */
    @PostMapping("/batchUpdateUseState")
    @MethodPermission(MyScriptBtnPermitConstant.UPDATE_USE_STATE_PER)
    public R<Object> batchUpdateUseState(@RequestBody TaskAuthorityDto taskAuthorityDto)
    {
        try {
            boolean result = taskAuthorityService.batchUpdateUseState(taskAuthorityDto);
            if(result){
                return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE,  "update.success");
            }else {
                return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "batchUpdate.error");
            }
        } catch (ScriptException e) {
            logger.error("批量修改任务权限失败!{}",e.getMessage());
            return ValidationUtils.customFailResult("useState",e.getMessage());
        }
    }

    @PostMapping("/deleteScript")
    public R<Object> deleteScript(@RequestBody ScriptDeleteDto scriptDeleteDto) {
        try {
            myScriptService.deleteMyScript(scriptDeleteDto,true);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "删除成功");
        } catch (ScriptException e) {
            return ValidationUtils.customFailResult("deleteScript",e.getMessage());
        } catch (Exception e) {
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "删除失败");
        }
    }


    /**
     * 查看是否有未完成的任务。
     *
     * @param scriptInfoVersionId 版本Id
     * @return {@link R }<{@link Object }>
     */
    @GetMapping("/checkExistRunTask")
    public R<Object> checkExistRunTask(@RequestParam(value = "scriptInfoVersionId") Long scriptInfoVersionId) {
        try {
            boolean isExistRunTask = taskAuthorityService.checkExistRunTask(scriptInfoVersionId);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE,isExistRunTask, "check.exist.run.task success!");
        } catch (ScriptException e) {
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, e.getMessage());
        } catch (Exception e) {
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "check.exist.run.task fail!");
        }
    }

}
