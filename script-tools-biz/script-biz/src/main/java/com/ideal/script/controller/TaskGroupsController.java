package com.ideal.script.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.permission.MenuPermitConstant;
import com.ideal.script.model.dto.AgentInfoDto;
import com.ideal.script.model.dto.AgentInfoQueryDto;
import com.ideal.script.model.dto.TaskGroupsDto;
import com.ideal.script.model.dto.interaction.agent.SystemComputerGroupDto;
import com.ideal.script.model.dto.interaction.agent.SystemComputerGroupQueryDto;
import com.ideal.script.service.ITaskGroupsService;
import com.ideal.system.common.component.aop.MethodPermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Agent设备信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("${app.script-tools-url:}/groups")
@MethodPermission(MenuPermitConstant.EXECUTION_TASK_PER)
public class TaskGroupsController
{
    private final ITaskGroupsService taskGroupsService;
    private static final Logger logger = LoggerFactory.getLogger(TaskGroupsController.class);


    public TaskGroupsController(ITaskGroupsService taskGroupsService){
       this.taskGroupsService=taskGroupsService;
    }
    /**
     * 查询任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉列表
     */
    @PostMapping("/listTaskGroups")
    public R<PageInfo<TaskGroupsDto>> listTaskGroups(@RequestBody TableQueryDto<TaskGroupsDto> tableQueryDTO)
    {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, taskGroupsService.selectTaskGroupsList(tableQueryDTO.getQueryParam(), tableQueryDTO.getPageNum(),
            tableQueryDTO.getPageSize()), Constants.LIST_SUCCESS);
    }
    /**
     * 获取agent分组列表数据
     * @param tableQueryDto 查询条件
     * @return PageInfo<SystemComputerGroupDto>
     */
    @PostMapping("/queryAgentGroupPageList")
    @MethodPermission(MenuPermitConstant.EXECUTION_TASK_OR_TASK_APPLY_OR_TEMPLATE_TASK_PER)
    public R<PageInfo<SystemComputerGroupDto>> queryAgentGroupPageList(@RequestBody TableQueryDto<SystemComputerGroupQueryDto> tableQueryDto ) {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, taskGroupsService.queryAgentGroupPageList(tableQueryDto.getQueryParam(),tableQueryDto .getPageNum(),
                tableQueryDto.getPageSize()), Constants.LIST_SUCCESS);
    }

    /**
     * 查询某个agent分组下绑定的agent信息
     * @param tableQueryDto 查询条件
     * @return PageInfo<SystemComputerGroupDto>
     */
    @PostMapping("/queryAgentPageListByGroupId")
    @MethodPermission(MenuPermitConstant.EXECUTION_TASK_OR_TASK_APPLY_OR_TEMPLATE_TASK_PER)
    public R<PageInfo<AgentInfoDto>> queryAgentPageListByGroupId(@RequestBody TableQueryDto<SystemComputerGroupQueryDto> tableQueryDto ) {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, taskGroupsService.queryAgentPageListByGroupId(tableQueryDto.getQueryParam(),tableQueryDto .getPageNum(),
                tableQueryDto.getPageSize()), Constants.LIST_SUCCESS);
    }

    /**
     * 查询用户角色下所有agent信息(脚本服务化)
     * @tags 对外API接口,兴业
     * @param tableQueryDto 查询条件
     * @return PageInfo<AgentInfoDto>
     */
    @PostMapping("/queryAgentInfoGroupRole")
    @MethodPermission(MenuPermitConstant.EXECUTION_TASK_OR_TASK_APPLY_OR_TEMPLATE_TASK_PER)
    public R<PageInfo<AgentInfoDto>> queryAgentInfoGroupRole(@RequestBody TableQueryDto<AgentInfoQueryDto> tableQueryDto) {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, taskGroupsService.queryAgentInfoGroupRole(tableQueryDto.getQueryParam(), tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()), Constants.LIST_SUCCESS);
    }
}
