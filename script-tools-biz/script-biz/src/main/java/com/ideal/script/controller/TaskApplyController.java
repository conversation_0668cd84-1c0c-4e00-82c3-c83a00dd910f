package com.ideal.script.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.ideal.audit.producer.annotation.Auditable;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.common.util.spring.MessageUtil;
import com.ideal.common.util.spring.SpringUtil;
import com.ideal.sc.CustomerProperty;
import com.ideal.sc.annotation.ScriptAuditingValidate;
import com.ideal.sc.util.CurrentUserUtil;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.permission.MenuPermitConstant;
import com.ideal.script.common.constant.permission.TaskApplyBtnPermitConstant;
import com.ideal.script.common.validation.ExecAuditCreate;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.ScriptAuditDetailDto;
import com.ideal.script.model.dto.ScriptExecAuditDto;
import com.ideal.script.model.dto.TaskApplyDto;
import com.ideal.script.model.dto.TaskApplyQueryDto;
import com.ideal.script.model.dto.interaction.agent.UserInfoDto;
import com.ideal.script.service.AuditSource;
import com.ideal.script.service.ITaskApplyService;
import com.ideal.system.common.component.aop.MethodPermission;
import com.ideal.system.common.component.model.CurrentUser;
import com.ideal.system.dto.PermissionUserInfoApiDto;
import com.ideal.system.dto.ServicePermissionApiQueryDto;
import com.ideal.system.dto.UserInfoApiDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 任务申请
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("${app.script-tools-url:}/taskApply")
@MethodPermission(MenuPermitConstant.TASK_APPLY_PER)
public class TaskApplyController {
    private static final Logger logger = LoggerFactory.getLogger(TaskApplyController.class);

    private final ITaskApplyService taskApplyService;
    private final AuditSource taskApplySource;
    private final AuditSource reAuditSource;


    public TaskApplyController(ITaskApplyService taskApplyService, @Qualifier("taskApplySource") AuditSource taskApplySource, @Qualifier("reAuditSource") AuditSource reAuditSource) {
        this.taskApplyService = taskApplyService;
        this.taskApplySource = taskApplySource;
        this.reAuditSource = reAuditSource;
    }

    /**
     * 查询能申请任务的脚本数据列表
     * 任务申请菜单查询用户可见范围内脚本列表，可以发起任务申请，白名单脚本不需要审核，风险脚本需要审核
     * @tags 对外API接口,兴业
     * @param tableQueryDTO 任务申请Dto
     * @return R<PageInfo < TaskApplyDto>>
     */
    @PostMapping("/listTaskApply")
    @MethodPermission(MenuPermitConstant.TASK_APPLY_OR_TASK_APPLY_DUTY_PER)
    public R<PageInfo<TaskApplyDto>> listTaskApply(@RequestBody TableQueryDto<TaskApplyQueryDto> tableQueryDTO) {
        CurrentUser currentUser = CurrentUserUtil.getCurrentUser();
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, taskApplyService.selectTaskApplyList(tableQueryDTO.getQueryParam(), tableQueryDTO.getPageNum(),
                tableQueryDTO.getPageSize(), currentUser), Constants.LIST_SUCCESS);
    }

    /**
     * 根据服务权限码获取这个服务权限码所属角色下的所有人信息
     *
     * @param permissionCode 服务权限码
     * @return {@link R }<{@link List }<{@link TaskApplyDto }>>
     * <AUTHOR>
     */
    @GetMapping("/queryUserInfoListByPermissionCode")
    @MethodPermission(MenuPermitConstant.TASK_APPLY_OR_TASK_APPLY_DUTY_OR_MY_SCRIPT_OR_SERVICE_ROLLOUT_OR_DOUBLE_CHECK_OR_HISTORY_TASK_STATEMENT_PER)
    public R<List<UserInfoDto>> queryUserInfoListByPermissionCode(@RequestParam String permissionCode) {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, taskApplyService.queryUserInfoListByPermissionCode(permissionCode), Constants.LIST_SUCCESS);
    }

    /**
     * 根据服务权限码获取这个服务权限码所属角色下的所有人信息（根据分类筛选）(下拉选)
     *
     * @param servicePermissionApiQueryDto 查询信息
     * @return {@link R }<{@link List }<{@link PermissionUserInfoApiDto }>>
     */
    @PostMapping("/queryPermissionUserInfoList")
    @MethodPermission(MenuPermitConstant.TASK_APPLY_OR_MY_SCRIPT_OR_DOUBLE_CHECK_PER)
    public R<List<UserInfoApiDto>> queryPermissionUserInfoList(@RequestBody ServicePermissionApiQueryDto servicePermissionApiQueryDto,@RequestParam(required = false)  Long categoryId) {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, taskApplyService.queryPermissionUserInfoList(servicePermissionApiQueryDto,categoryId), Constants.LIST_SUCCESS);
    }

    /**
     * 根据脚本id查询脚本创建人的部门领导
     * @param scriptInfoId 脚本id
     * @return 脚本创建人部门领导
     */
    @PostMapping("/queryDepartmentUserInfoList")
    @MethodPermission(MenuPermitConstant.TASK_APPLY_OR_DOUBLE_CHECK_PER)
    public R<List<UserInfoApiDto>> queryDepartmentUserInfoList(@RequestParam  Long scriptInfoId) {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, taskApplyService.queryDepartmentUserInfoList(scriptInfoId), Constants.LIST_SUCCESS);
    }

    /**
     * 任务申请提交功能
     * 根据传递的脚本版本id、agent信息、脚本参数信息，进行创建任务的接口
     * @tags 对外API接口,兴业
     * @param scriptExecAuditDto 脚本任务执行审核Dto
     * @return R<Object>
     * <AUTHOR>
     */
    @PostMapping("/scriptExecAuditing")
    @Auditable("任务申请|任务申请")
    @MethodPermission(TaskApplyBtnPermitConstant.SCRIPT_EXEC_AUDITING_PER)
    @ScriptAuditingValidate
    public R<Object> scriptExecAuditing(@RequestBody @Validated(ExecAuditCreate.class) ScriptExecAuditDto scriptExecAuditDto) throws ScriptException {
        Map<String, Long> res;
        if(logger.isDebugEnabled()) {
            logger.debug("scriptExecAuditDto报文:{}", JSONObject.toJSONString(scriptExecAuditDto));
        }
        try {
            CurrentUser user = CurrentUserUtil.getCurrentUser();
            if (null == user) {
                throw new ScriptException("current.user.empty");
            }
            res = taskApplyService.scriptExecAuditingForWeb(scriptExecAuditDto, user, taskApplySource);
        } catch (ScriptException e) {
            logger.error("scriptExecAuditing validate error:", e);
            throw new ScriptException(e.getMessage());
        } catch (Exception e){
            logger.error("scriptExecAuditing error:", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "", e.getMessage());
        }
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, res, "scriptExecAuditing.success");

    }


    /**
     * 获取当前登陆人id
     * @return 用户信息
     */
    @PostMapping("/getAuditorUserSelf")
    @MethodPermission(MenuPermitConstant.TASK_APPLY_OR_TEMPLATE_TASK_PER)
    public R<UserInfoDto> getAuditorUserSelf() {
        CurrentUser currentUser = CurrentUserUtil.getCurrentUser();
        UserInfoDto user = taskApplyService.getUserByUserId(currentUser.getId()).get(0);
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, user, "getAuditorUserSelf.success");
    }

    /**
     * 双人复核-业务详情   任务申请-展示历史任务详情信息 共用
     * serviceId和taskId冲突，serviceId为优先查询条件
     * @param serviceId 双人复审关系表id
     * @param taskId    task表id
     * @return ScriptAuditDetailDto
     */
    @GetMapping("/getAuditDetail")
    @MethodPermission(MenuPermitConstant.TASK_APPLY_OR_EXECUTION_TASK_OR_DOUBLE_CHECK_PER)
    public R<ScriptAuditDetailDto> getAuditDetail(@RequestParam(value = "serviceId", required = false) Long serviceId, @RequestParam(value = "taskId", required = false) Long taskId) throws ScriptException {
        return R.ok(taskApplyService.getAuditDetail(serviceId, taskId));
    }

    /**
     * 重新提交审核
     */
    @PostMapping("/reAudit")
    @MethodPermission(MenuPermitConstant.TASK_APPLY_OR_DOUBLE_CHECK_PER)
    public R<?> reAudit(@RequestBody ScriptExecAuditDto scriptExecAuditDto, @RequestParam("taskId") Long taskId) throws ScriptException {
        CurrentUser user = CurrentUserUtil.getCurrentUser();
        scriptExecAuditDto.getTaskInfo().setId(taskId);
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, taskApplyService.scriptExecAuditing(scriptExecAuditDto, user, reAuditSource), MessageUtil.message("scriptExecAuditing.success"));
    }

    /**
     * 根据审核关系id查询脚本的分类id
     * @param relationId 审核关系数据
     * @return 脚本分类id
     */
    @PostMapping("/getScriptInfoByAuditRelationId")
    @MethodPermission(MenuPermitConstant.TASK_APPLY_OR_DOUBLE_CHECK_PER)
    public R<?> reAudit(@RequestParam("relationId") Long relationId) {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, taskApplyService.getScriptInfoByAuditRelationId(relationId), "");
    }

    /**
     * 任务申请 删除临时附件
     * @param id 临时附件id
     */
    @GetMapping("/deleteAttachment")
    @MethodPermission(TaskApplyBtnPermitConstant.SCRIPT_EXEC_AUDITING_PER)
    public R<?> deleteAttachment(@RequestParam("id") Long id) {
        taskApplyService.deleteAttachment(id);
        return R.ok();
    }


    @PostMapping("/importServerExcel")
    public R<?> importServerExcel(@RequestParam("file") MultipartFile file) {
        try {
            Map<String, Object> res = taskApplyService.importServerExcel(file);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, res, MessageUtil.message("import.server.success"));
        } catch (IOException | ScriptException e) {
            logger.error("importServerExcel error:", e);
        }
        return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "", MessageUtil.message("import.server.fail"));
    }

    @GetMapping("/downloadImportTemplate")
    public void downloadImportTemplate(HttpServletResponse response) {
        taskApplyService.downloadImportTemplate(response);
    }

    @GetMapping("/getSource")
    @MethodPermission(MenuPermitConstant.TASK_APPLY_OR_DOUBLE_CHECK_PER)
    public R<String> getSource(){
        String customerName  = SpringUtil.getBean(CustomerProperty.class).getName();
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, customerName, Constants.LIST_SUCCESS);
    }
}
