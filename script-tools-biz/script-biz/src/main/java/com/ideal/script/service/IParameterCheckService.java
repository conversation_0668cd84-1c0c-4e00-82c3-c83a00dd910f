package com.ideal.script.service;

import com.github.pagehelper.PageInfo;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.ParameterCheckDto;

/**
 * <AUTHOR>
 */
public interface IParameterCheckService {

    /**
     * 查询参数校验规则
     *
     * @param id 规则id
     * @return ParameterCheckDto
     */
    ParameterCheckDto selectParameterCheckById(Long id);

    /**
     * 查询参数校验规则
     *
     * @param ruleName 规则名
     * @return ParameterCheckDto
     */
    ParameterCheckDto selectParameterCheckByName(String ruleName);

    /**
     * 新增
     *
     * @param parameterCheckDto 参数验证规则dto
     */
    void insertParameterCheck(ParameterCheckDto parameterCheckDto) throws ScriptException;

    /**
     * 修改
     *
     * @param parameterCheckDto 参数验证规则dto
     */
    void updateParameterCheck(ParameterCheckDto parameterCheckDto) throws ScriptException;

    /**
     * 分页查询
     * @param parameterCheckDto 参数验证规则dto
     * @param pageNum   起始页
     * @param pageSize  每页大小
     * @return  结果
     */
    PageInfo<ParameterCheckDto> selectParameterCheckList(ParameterCheckDto parameterCheckDto, Integer pageNum, Integer pageSize);

    /**
     * 删除
     *
     * @param ids id数组
     */
    void deleteParameterCheckByIds(Long[] ids);

    /**
     * 验证规则是否已存在
     *
     * @param ruleName 规则名
     * @return {@link Boolean }
     */
    Boolean validParamterCheckExist(String ruleName);
}

