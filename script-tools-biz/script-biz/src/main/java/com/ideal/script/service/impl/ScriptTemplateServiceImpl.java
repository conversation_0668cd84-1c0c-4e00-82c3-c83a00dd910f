package com.ideal.script.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.ScriptTemplateMapper;
import com.ideal.script.model.dto.ScriptTemplateDto;
import com.ideal.script.model.entity.ScriptTemplate;
import com.ideal.script.service.IScriptTemplateService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * 脚本模版维护实现类
 */
@Transactional(rollbackFor = Exception.class)
@Service
public class ScriptTemplateServiceImpl implements IScriptTemplateService {

    private final ScriptTemplateMapper scriptTemplateMapper;

    public ScriptTemplateServiceImpl(ScriptTemplateMapper scriptTemplateMapper) {
        this.scriptTemplateMapper = scriptTemplateMapper;
    }

    @Override
    public PageInfo<ScriptTemplateDto> selectScriptTemplateList(ScriptTemplateDto scriptTemplateDto, int pageNum, int pageSize) {
        PageMethod.startPage(pageNum, pageSize);
        List<ScriptTemplate> scriptTemplateList = new ArrayList<>();
        if (null != scriptTemplateDto) {
            ScriptTemplate scriptTemplate = BeanUtils.copy(scriptTemplateDto, ScriptTemplate.class);
            scriptTemplateList = scriptTemplateMapper.selectScriptTemplateList(scriptTemplate);
        }
        return PageDataUtil.toDtoPage(scriptTemplateList, ScriptTemplateDto.class);
    }

    @Override
    public void insertScriptTemplate(ScriptTemplateDto scriptTemplateDto) throws ScriptException {
        ScriptTemplate scriptTemplate = BeanUtils.copy(scriptTemplateDto, ScriptTemplate.class);
        //判断脚本模版重名
        checkScriptTemplateName(scriptTemplate);
        scriptTemplateMapper.insertScriptTemplate(scriptTemplate);
    }

    @Override
    public void updateScriptTemplate(ScriptTemplateDto scriptTemplateDto) throws ScriptException {
        ScriptTemplate scriptTemplate = BeanUtils.copy(scriptTemplateDto, ScriptTemplate.class);
        //判断脚本模版重名
        checkScriptTemplateName(scriptTemplate);
        scriptTemplateMapper.updateScriptTemplate(scriptTemplate);
    }

    private void checkScriptTemplateName(ScriptTemplate scriptTemplate) throws ScriptException {
        //判断脚本模版重名
        List<ScriptTemplate> scriptTemplateList = scriptTemplateMapper.getScriptTemplateByName(scriptTemplate.getName());
        boolean isSingleScriptTemplate = scriptTemplateList.size() == 1 && !scriptTemplateList.get(0).getId().equals(scriptTemplate.getId());
        if(scriptTemplateList.size() > 1 || isSingleScriptTemplate) {
            throw new ScriptException("duplicate.script.template");
        }
    }

    @Override
    public void deleteScriptTemplateByIds(Long[] ids) {
        scriptTemplateMapper.deleteScriptTemplateByIds(ids);
    }

    @Override
    public ScriptTemplateDto getScriptTemplateDetail(Long id) {
        ScriptTemplate scriptTemplate;
        scriptTemplate = scriptTemplateMapper.getScriptTemplateDetail(id);
        return BeanUtils.copy(scriptTemplate,ScriptTemplateDto.class);
    }


}
