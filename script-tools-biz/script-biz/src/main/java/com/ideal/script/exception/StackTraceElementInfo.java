package com.ideal.script.exception;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@SuppressWarnings("unused")
public class StackTraceElementInfo  implements Serializable {
    private static final long serialVersionUID = 1L;
    private String methodName;
    private String fileName;
    private int lineNumber;
    private String className;
    private boolean nativeMethod;

    public String getMethodName() {
        return methodName;
    }

    public void setMethodName(String methodName) {
        this.methodName = methodName;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public int getLineNumber() {
        return lineNumber;
    }

    public void setLineNumber(int lineNumber) {
        this.lineNumber = lineNumber;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public boolean getNativeMethod() {
        return nativeMethod;
    }

    public void setNativeMethod(boolean nativeMethod) {
        this.nativeMethod = nativeMethod;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("methodName", getMethodName())
                .append("fileName", getFileName())
                .append("lineNumber", getLineNumber())
                .append("className", getClassName())
                .append("nativeMethod", getNativeMethod())
                .toString();
    }
}
