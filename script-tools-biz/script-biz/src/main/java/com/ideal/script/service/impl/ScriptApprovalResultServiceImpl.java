package com.ideal.script.service.impl;

import com.ideal.approval.dto.DoubleCheckApiDto;
import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.common.util.TransactionSyncUtil;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.exception.SystemException;
import com.ideal.script.model.bean.MyScriptBean;
import com.ideal.script.model.dto.AuditRelationDto;
import com.ideal.script.service.IAuditRelationService;
import com.ideal.script.service.IMyScriptService;
import com.ideal.script.service.ITaskApplyService;
import com.ideal.script.service.ScriptApprovalResultService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * 脚本服务化接收审核结果实现类
 *
 * <AUTHOR>
 */
@Transactional(rollbackFor = Exception.class)
@Service
public class ScriptApprovalResultServiceImpl implements ScriptApprovalResultService {

    private static final Logger logger = LoggerFactory.getLogger(ScriptApprovalResultServiceImpl.class);
    private final IAuditRelationService auditRelationService;
    private final ITaskApplyService taskApplyService;
    private final IMyScriptService myScriptService;


    public ScriptApprovalResultServiceImpl(IAuditRelationService auditRelationService, ITaskApplyService taskApplyService, IMyScriptService myScriptService) {
        this.auditRelationService = auditRelationService;
        this.taskApplyService = taskApplyService;
        this.myScriptService = myScriptService;
    }

    @Override
    public void scriptCall(DoubleCheckApiDto doubleCheckApiDto) throws ScriptException {
        try {
            logger.info("Audit information：{}", doubleCheckApiDto);
            // 根据doubleCheckApiDto.getServiceId() 判断是发布审核的结果，还是任务申请的审核结果
            AuditRelationDto auditRelationDto;
            if (!doubleCheckApiDto.getItemType().equals("toolbox")) {
                //脚本服务化操作
                auditRelationDto = auditRelationService.selectAuditRelationById(doubleCheckApiDto.getServiceId());
            } else {
                //工具箱
                auditRelationDto = auditRelationService.selectAuditRelationByWorkItemId(doubleCheckApiDto.getId());
            }
            //设置双人复核与脚本服务化关系表id(避免使用的是工具箱传回来的的关系表id)
            doubleCheckApiDto.setServiceId(auditRelationDto.getId());
            if (null != auditRelationDto.getAuditType()) {
                if (Enums.AuditType.SCRIPT_PUBLISH.getValue().intValue() == auditRelationDto.getAuditType().intValue()) {
                    myScriptService.doubleCheckScriptCallBack(doubleCheckApiDto, Enums.AuditType.SCRIPT_PUBLISH.getValue());
                    //审批通过发送mq
                    if (Objects.equals(doubleCheckApiDto.getApprovalState(), Enums.ApprovalProcessAudiState.PASS.getValue())) {
                        MyScriptBean myScriptBean = new MyScriptBean();
                        myScriptBean.setSrcScriptUuid(auditRelationDto.getSrcScriptUuid());
                        //事务提交成功后发送mq
                        TransactionSyncUtil.execute(myScriptService::noticeScriptChangeDefaultVersion, null, null,myScriptBean);
                    }
                } else if (Enums.AuditType.SCRIPT_TASK.getValue().intValue() == auditRelationDto.getAuditType().intValue()) {
                    taskApplyService.doubleCheckCallBack(doubleCheckApiDto);
                } else if (Enums.AuditType.SCRIPT_DELETE.getValue().intValue() == auditRelationDto.getAuditType().intValue()) {
                    myScriptService.doubleCheckScriptCallBack(doubleCheckApiDto, Enums.AuditType.SCRIPT_DELETE.getValue());
                }
            }
        } catch (SystemException | ScriptException e) {
            logger.error("receiveAuditResult exception:", e);
            throw new ScriptException("error.receive.audit.information");
        }
    }
}
