package com.ideal.script.service;

import com.github.pagehelper.PageInfo;
import com.ideal.script.exception.ScriptException;
import com.ideal.system.common.component.model.CurrentUser;
import com.ideal.script.model.dto.IssuerecordDto;
import com.ideal.script.model.dto.IssuerecordQueryDto;

/**
 * 脚本下发的Service接口
 *
 * <AUTHOR>
 * @date 2024-04-13
 */
public interface IIssuerecordService {
    /**
     * 查询脚本下发的
     *
     * @param id 脚本下发的主键
     * @return 脚本下发的
     */
    IssuerecordDto selectIssuerecordById(Long id);

    /**
     * 查询脚本下发的列表
     *
     * @param issuerecordQueryDto 脚本下发的
     * @param pageNum             页码
     * @param pageSize            单页长度
     * @return 脚本下发的集合
     */
    PageInfo<IssuerecordDto> selectIssuerecordList(IssuerecordQueryDto issuerecordQueryDto, Integer pageNum, Integer pageSize);

    /**
     * 新增脚本下发的
     *
     * @param issuerecordDto 脚本下发的
     * @return 结果
     */
    int insertIssuerecord(IssuerecordDto issuerecordDto);

    /**
     * 修改脚本下发的
     *
     * @param issuerecordDto 脚本下发的
     * @return 结果
     */
    int updateIssuerecord(IssuerecordDto issuerecordDto);

    /**
     * 批量删除脚本下发的
     *
     * @param ids 需要删除的脚本下发的主键集合
     * @return 结果
     */
    int deleteIssuerecordByIds(Long[] ids);

    /**
     * 根据batchNumber更新脚本下发状态
     *
     * @param issuerecordDto 脚本下发
     * @return int 结果
     */
    int updateIssuerecordByBatchNumber(IssuerecordDto issuerecordDto);
    /**
     * 脚本下发
     *
     * @param issuerecordQueryDto 脚本下发Dto，包含脚本集合、目标agent集合、下发路径、权限参数、所属用户、所属组
     * @param currentUser         用户
     * @throws ScriptException    自定义异常
     * @return {@link String }
     */
    String sendScriptToAgents(IssuerecordQueryDto issuerecordQueryDto, CurrentUser currentUser) throws ScriptException;
}
