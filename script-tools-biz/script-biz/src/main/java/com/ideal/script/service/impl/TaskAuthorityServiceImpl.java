package com.ideal.script.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.InfoVersionMapper;
import com.ideal.script.model.bean.TaskAuthorityBean;
import com.ideal.script.model.dto.TaskAuthorityDto;
import com.ideal.script.model.entity.InfoVersion;
import com.ideal.script.service.ICategoryService;
import com.ideal.script.service.ITaskAuthorityService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 任务权限（任务启用、任务禁用）Service接口
 *
 * <AUTHOR>
 */
@Transactional(rollbackFor = Exception.class)
@Service
public class TaskAuthorityServiceImpl implements ITaskAuthorityService {
    private static final Logger logger = LoggerFactory.getLogger(TaskAuthorityServiceImpl.class);

    private final InfoVersionMapper infoVersionMapper;

    private final ICategoryService categoryService;

    public TaskAuthorityServiceImpl(InfoVersionMapper infoVersionMapper, ICategoryService categoryService) {
        this.infoVersionMapper = infoVersionMapper;
        this.categoryService = categoryService;
    }

    /**
     * 获取任务列表
     *
     * @param taskAuthorityDto 任务权限Dto
     * @param pageNum          页码
     * @param pageSize         每页大小
     * @return PageInfo<TaskAuthorityDto>
     * <AUTHOR>
     */
    @Override
    public PageInfo<TaskAuthorityDto> selectTaskAuthorityList(TaskAuthorityDto taskAuthorityDto, Integer pageNum, Integer pageSize) {
        PageMethod.startPage(pageNum, pageSize);
        List<TaskAuthorityBean> taskAuthorityBeanList = new ArrayList<>();
        if (null != taskAuthorityDto) {
            TaskAuthorityBean taskAuthorityBean = BeanUtils.copy(taskAuthorityDto, TaskAuthorityBean.class);
            taskAuthorityBeanList = infoVersionMapper.selectTaskAuthorityList(taskAuthorityBean);
            for (TaskAuthorityBean authorityBean : taskAuthorityBeanList) {
                Long categoryId = authorityBean.getCategoryId();
                if (null == categoryId) {
                    authorityBean.setScriptCategoryName(null);
                } else {
                    String categoryName = categoryService.getCategoryFullPath(categoryId);
                    authorityBean.setScriptCategoryName(categoryName);
                }
            }
        }
        return PageDataUtil.toDtoPage(taskAuthorityBeanList, TaskAuthorityDto.class);
    }

    /**
     * 任务启用/禁用
     *
     * @param taskAuthorityDto 任务权限Dto
     * <AUTHOR>
     */
    @Override
    public boolean updateUseState(TaskAuthorityDto taskAuthorityDto) throws ScriptException {
        try {
            InfoVersion infoVersion = new InfoVersion();
            infoVersion.setId(taskAuthorityDto.getScriptInfoVersionId());
            infoVersion.setUseState(taskAuthorityDto.getUseState());
            int row = 0;
            //禁用情况时，如果当前脚本为默认脚本，寻找未被删除且启用版本迁移默认字段,不存在只清空该数据默认字段信息
            //查询版本信息
            InfoVersion infoVersion1 = infoVersionMapper.selectInfoVersionById(taskAuthorityDto.getScriptInfoVersionId());
            if(taskAuthorityDto.getUseState().equals(0)){
                //先更新状态后判断
                row = infoVersionMapper.updateInfoVersion(infoVersion);
                //如果该版本脚本是默认脚本，迁移默认字段到未被删除未被禁用的最大版本脚本上
                if(infoVersion1.getIsDefault().equals(1)){
                    Long versionId = infoVersionMapper.getLastVersionByUuid(infoVersion1.getInfoUniqueUuid());
                    if(versionId != null){
                        //迁移默认脚本
                        InfoVersion infoVersion2 = new InfoVersion();
                        infoVersion2.setId(versionId);
                        infoVersion2.setIsDefault(1);
                        infoVersionMapper.updateInfoVersion(infoVersion2);
                    }
                    //清除该条数据默认属性
                    infoVersion.setIsDefault(0);
                    infoVersionMapper.updateInfoVersion(infoVersion);
                }
            }else if(taskAuthorityDto.getUseState().equals(1)){
                //启用情况下，判断此时未被删除版本是否全部为禁用，是则给该条赋值默认，反之不作操作
                Long versionId = infoVersionMapper.getLastVersionByUuid(infoVersion1.getInfoUniqueUuid());
                if(versionId == null){
                    //不存在可用的启用版本，该条数据将为唯一启用
                    InfoVersion infoVersion2 = new InfoVersion();
                    infoVersion2.setId(infoVersion1.getId());
                    infoVersion2.setIsDefault(1);
                    infoVersionMapper.updateInfoVersion(infoVersion2);
                }
                //先判断后改状态
                row = infoVersionMapper.updateInfoVersion(infoVersion);
            }
            return row > 0;
        } catch (Exception e) {
            logger.error("修改任务权限失败！", e);
            throw new ScriptException("task.authority.updateUseState.failed");
        }
    }


    /**
     * 任务启用/禁用 （批量）
     *
     * @param taskAuthorityDto 任务权限Dto
     * <AUTHOR>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateUseState(TaskAuthorityDto taskAuthorityDto) throws ScriptException {

        try {
            InfoVersion infoVersion = getInfoVersion(taskAuthorityDto);
            Long versionId = infoVersion.getIds()[0];
            InfoVersion infoVersion1 = infoVersionMapper.selectInfoVersionById(versionId);
            int row = 0;
            if(infoVersion.getUseState().equals(0)) {
                 row = infoVersionMapper.batchUpdateInfoVersion(infoVersion);
                //首先获取到当前脚本的默认版本的id在不在ids中

                InfoVersion infoVersion2 = infoVersionMapper.selectDefaultInfoVersionByUuid(infoVersion1.getInfoUniqueUuid());
                // 将 Long 数组转换为 List
                List<Long> idList = Arrays.asList(infoVersion.getIds());
                if (idList.contains(infoVersion2.getId())) {
                    //说明此时默认版本被禁用，寻找最新可用版本脚本作为默认版本
                    Long lastVersionId = infoVersionMapper.getLastVersionByUuid(infoVersion1.getInfoUniqueUuid());
                    if (lastVersionId != null) {
                        //迁移默认脚本
                        InfoVersion infoVersion3 = new InfoVersion();
                        infoVersion3.setId(lastVersionId);
                        infoVersion3.setIsDefault(1);
                        infoVersionMapper.updateInfoVersion(infoVersion3);
                    }
                    //清除该条数据默认属性
                    infoVersion2.setIsDefault(0);
                    infoVersionMapper.updateInfoVersion(infoVersion2);
                }
            } else if (infoVersion.getUseState().equals(1)) {
                //启用情况下，判断此时未被删除版本是否全部为禁用，是则给这批中最大的版本设置为为默认版本
                Long lastVersionId = infoVersionMapper.getLastVersionByUuid(infoVersion1.getInfoUniqueUuid());
                if(lastVersionId == null){
                    //查询这批中版本号最大的脚本的信息
                    InfoVersion lastInfoVersionByIds = infoVersionMapper.getLastInfoVersionByIds(infoVersion1.getInfoUniqueUuid(), infoVersion.getIds());
                    if(lastInfoVersionByIds != null){
                        //迁移默认版本到这个版本上面
                        //迁移默认脚本
                        InfoVersion infoVersion2 = new InfoVersion();
                        infoVersion2.setId(lastInfoVersionByIds.getId());
                        infoVersion2.setIsDefault(1);
                        infoVersionMapper.updateInfoVersion(infoVersion2);
                    }
                }
                //先判断后改状态
                row = infoVersionMapper.batchUpdateInfoVersion(infoVersion);
            }
            return row == taskAuthorityDto.getScriptInfoVersionIds().length;
        } catch (ScriptException e) {
            // 可以选择重新抛出异常，保留原始异常的信息和堆栈
            throw e;
        } catch (Exception e) {
            // 捕获其他异常，记录错误日志，并抛出自定义异常
            logger.error("批量修改任务权限失败！", e);
            throw new ScriptException("task.authority.updateUseState.failed");
        }
    }

    /**
     * 提取校验方法
     * @param taskAuthorityDto      任务权限DTO
     * @return  脚本信息
     * @throws ScriptException  脚本服务化自定义异常
     */
    private static InfoVersion getInfoVersion(TaskAuthorityDto taskAuthorityDto) throws ScriptException {
        InfoVersion infoVersion = new InfoVersion();
        infoVersion.setIds(taskAuthorityDto.getScriptInfoVersionIds());
        infoVersion.setUseState(taskAuthorityDto.getUseState());
        boolean isNullForIds = infoVersion.getIds() == null || infoVersion.getIds().length == 0;
        if(infoVersion.getId() == null && isNullForIds){
           if(infoVersion.getUseState() == 0){
               throw new ScriptException("no.script.forbidden");
           }else if(infoVersion.getUseState() == 1){
               throw new ScriptException("no.script.enable");
           }
        }
        return infoVersion;
    }

    /**
     * 查看是否有未完成的任务。
     *
     * @param scriptInfoVersionId 版本ID
     * @return boolean
     */
    @Override
    public boolean checkExistRunTask(Long scriptInfoVersionId) throws ScriptException {
        try {
            Integer result = infoVersionMapper.checkExistRunTask(scriptInfoVersionId);
            return result != null && result > 0;
        } catch (Exception e) {
            logger.error("check exist run task by versionId :{} fail！", scriptInfoVersionId, e);
            throw new ScriptException("error.check.unfinished.task.failed");
        }
    }
}
