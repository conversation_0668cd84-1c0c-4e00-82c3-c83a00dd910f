package com.ideal.script.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.sc.util.CurrentUserUtil;
import com.ideal.script.common.util.BatchDataUtil;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.TaskGroupsMapper;
import com.ideal.script.model.dto.*;
import com.ideal.script.model.dto.interaction.agent.SystemComputerGroupDto;
import com.ideal.script.model.dto.interaction.agent.SystemComputerGroupQueryDto;
import com.ideal.script.model.entity.TaskGroups;
import com.ideal.script.service.ITaskGroupsService;
import com.ideal.system.api.IAgentInfo;
import com.ideal.system.api.IComputerGroup;
import com.ideal.system.common.component.model.CurrentUser;
import com.ideal.system.dto.AgentGroupRoleQueryBean;
import com.ideal.system.dto.AgentInfoApiDto;
import com.ideal.system.dto.ComputerGroupApiDto;
import com.ideal.system.dto.SystemAgentInfoApiDto;
import org.apache.ibatis.session.SqlSession;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

/**
 * 任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class TaskGroupsServiceImpl implements ITaskGroupsService {

    private final TaskGroupsMapper taskGroupsMapper;
    private final IAgentInfo iAgentInfo;
    private final BatchDataUtil batchDataUtil;

    private final IComputerGroup computerGroup;

    public TaskGroupsServiceImpl(TaskGroupsMapper taskGroupsMapper, IAgentInfo iAgentInfo, BatchDataUtil batchDataUtil, IComputerGroup computerGroup) {
        this.taskGroupsMapper = taskGroupsMapper;
        this.iAgentInfo = iAgentInfo;
        this.batchDataUtil = batchDataUtil;
        this.computerGroup = computerGroup;
    }

    /**
     * 查询任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉
     *
     * @param id 任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉主键
     * @return 任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉
     */
    @Override
    public TaskGroupsDto selectTaskGroupsById(Long id) {
        return BeanUtils.copy(taskGroupsMapper.selectTaskGroupsById(id), TaskGroupsDto.class);
    }

    /**
     * 查询任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉列表
     *
     * @param taskGroupsDto 任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉
     * @return 任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉
     */
    @Override
    public PageInfo<TaskGroupsDto> selectTaskGroupsList(TaskGroupsDto taskGroupsDto, int pageNum, int pageSize) {
        PageMethod.startPage(pageNum, pageSize);
        List<TaskGroups> taskGroupsList = new ArrayList<>();
        if (null != taskGroupsDto) {
            TaskGroups taskGroups = BeanUtils.copy(taskGroupsDto, TaskGroups.class);
            taskGroupsList = taskGroupsMapper.selectTaskGroupsList(taskGroups);
        }
        return PageDataUtil.toDtoPage(taskGroupsList, TaskGroupsDto.class);

    }

    /**
     * 新增任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉
     *
     * @param taskGroupsDto 任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉
     * @return 结果
     */
    @Override
    public int insertTaskGroups(TaskGroupsDto taskGroupsDto) {
        TaskGroups taskGroups = BeanUtils.copy(taskGroupsDto, TaskGroups.class);
        return taskGroupsMapper.insertTaskGroups(taskGroups);
    }

    /**
     * 修改任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉
     *
     * @param taskGroupsDto 任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉
     * @return 结果
     */
    @Override
    public int updateTaskGroups(TaskGroupsDto taskGroupsDto) {
        TaskGroups taskGroups = BeanUtils.copy(taskGroupsDto, TaskGroups.class);
        return taskGroupsMapper.updateTaskGroups(taskGroups);
    }

    /**
     * 批量删除任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉
     *
     * @param ids 需要删除的任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉主键
     * @return 结果
     */
    @Override
    public int deleteTaskGroupsByIds(Long[] ids) {
        return taskGroupsMapper.deleteTaskGroupsByIds(ids);
    }

    /**
     * 删除任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉信息
     *
     * @param id 任务与资源组关系 平台资源组名称变更，此处选择的资源组名称名称不变 选择资源组后，将资源组下涉主键
     * @return 结果
     */
    @Override
    public int deleteTaskGroupsById(Long id) {
        return taskGroupsMapper.deleteTaskGroupsById(id);
    }

    @Override
    public int deleteTaskGroupsByTaskId(Long taskId) {
        return taskGroupsMapper.deleteTaskGroupsByTaskId(taskId);
    }

    /**
     * 查询任务绑定资源组信息-双人复核业务详情界面使用
     *
     * @param serviceId 业务主键
     * @return List<TaskGroupsDto>
     */
    @Override
    public List<TaskGroupsDto> selectTaskGroupsByServiceId(Long serviceId,Long taskId) {
        return BeanUtils.copy(taskGroupsMapper.selectTaskGroupsByServiceId(serviceId,taskId),TaskGroupsDto.class);
    }

    /**
     * 查询某个agent分组下绑定的agent信息
     *
     * @param pageNum                     当前页
     * @param pageSize                    每页页数
     * @param systemComputerGroupQueryDto 组id
     * @return PageInfo<SystemComputerGroupDto>
     */
    @Override
    public PageInfo<AgentInfoDto> queryAgentPageListByGroupId(SystemComputerGroupQueryDto systemComputerGroupQueryDto, Integer pageNum, Integer pageSize) {
        CurrentUser currentUser = CurrentUserUtil.getCurrentUser();
        AgentGroupRoleQueryBean agentGroupRoleQueryBean = new AgentGroupRoleQueryBean();
        agentGroupRoleQueryBean.setComputerGroupId(systemComputerGroupQueryDto.getSysmComputerGroupId());
        agentGroupRoleQueryBean.setBusinessType("脚本服务化");
        agentGroupRoleQueryBean.setPageNum(pageNum);
        agentGroupRoleQueryBean.setPageSize(pageSize);
        agentGroupRoleQueryBean.setUserId(currentUser.getId());
        return getAgentInfoDtoPageInfo(agentGroupRoleQueryBean);
    }

    /**
     * 获取设备
     * @param agentGroupRoleQueryBean   设备查询信息Bean
     * @return  结果
     */
    private PageInfo<AgentInfoDto> getAgentInfoDtoPageInfo(AgentGroupRoleQueryBean agentGroupRoleQueryBean) {

        PageInfo<SystemAgentInfoApiDto> agentInfoDtoPageInfo = iAgentInfo.queryAgentInfoGroupRole(agentGroupRoleQueryBean);
        List<AgentInfoDto> agentInfoList = new ArrayList<>();
        for (SystemAgentInfoApiDto systemAgentInfoApiDto : agentInfoDtoPageInfo.getList()) {
            AgentInfoDto agentInfo = convertToAgentInfoDto(systemAgentInfoApiDto);
            agentInfoList.add(agentInfo);
        }

        PageInfo<AgentInfoDto> pageInfo = new PageInfo<>(agentInfoList);
        pageInfo.setPageNum(agentInfoDtoPageInfo.getPageNum());
        pageInfo.setPageSize(agentInfoDtoPageInfo.getPageSize());
        pageInfo.setTotal(agentInfoDtoPageInfo.getTotal());

        return pageInfo;
    }

    /**
     * 对象转换
     *
     * @param agentInfo 平台管理agent对象
     * @return AgentInfoDto
     */
    public AgentInfoDto convertToAgentInfoDto(AgentInfoApiDto agentInfo) {
        AgentInfoDto agentInfoDto = new AgentInfoDto();
        agentInfoDto.setSysmAgentInfoId(agentInfo.getId());
        agentInfoDto.setAgentIp(agentInfo.getIp());
        agentInfoDto.setAgentPort(agentInfo.getPort());
        agentInfoDto.setAgentName(agentInfo.getName());
        agentInfoDto.setAgentState(agentInfo.getRegisterState());
        return agentInfoDto;
    }

    /**
     * 存储任务与资源组关系
     *
     * @param scriptExecAuditDto 任务申请提交审核Dto
     * @param taskInfo           任务申请提交任务时的脚本任务对象
     * @param sqlSession         ​SqlSession​ 对象
     * <AUTHOR>
     */
    @Override
    public void saveTaskGroups(ScriptExecAuditDto scriptExecAuditDto, TaskDto taskInfo, SqlSession sqlSession) throws ScriptException {
        List<TaskGroupsDto> taskGroupsDtoList = scriptExecAuditDto.getChosedResGroups();
        if (null != taskGroupsDtoList) {
            for (TaskGroupsDto taskGroupDto : taskGroupsDtoList) {
                taskGroupDto.setId(null);
                taskGroupDto.setScriptTaskId(taskInfo != null ? taskInfo.getId() : null);
            }
        }
        if (null != taskGroupsDtoList) {
            TaskGroupsMapper taskGroupsMapperSession = sqlSession.getMapper(TaskGroupsMapper.class);
            batchDataUtil.batchData(TaskGroupsMapper.class, BeanUtils.copy(taskGroupsDtoList, TaskGroups.class), TaskGroups.class, "insertTaskGroups", sqlSession, taskGroupsMapperSession);

        }
    }

    /**
     * 功能描述：获取选定资源组绑定的agent信息
     *
     * @param taskGroupsDtoList 资源组集合
     * @return {@link List }<{@link AgentInfoDto }>
     */
    @Override
    public List<AgentInfoDto> retrieveUniqueAgentInfoList(List<TaskGroupsDto> taskGroupsDtoList) {
        // agentInfoDtoSet​是一个HashSet，用于存储唯一的AgentInfoDto对象
        HashSet<AgentInfoDto> agentInfoDtoSet = new HashSet<>();
        for (TaskGroupsDto taskGroupsDto : taskGroupsDtoList) {
            SystemComputerGroupQueryDto systemComputerGroupQueryDto = new SystemComputerGroupQueryDto();
            systemComputerGroupQueryDto.setSysmComputerGroupId(taskGroupsDto.getSysmComputerGroupId());
            PageInfo<AgentInfoDto> agentInfoDtoPageInfo = queryAgentPageListByGroupId(systemComputerGroupQueryDto, 1, 100000);
            List<AgentInfoDto> agentInfoDtoList = agentInfoDtoPageInfo.getList();
            agentInfoDtoSet.addAll(agentInfoDtoList);
        }
        return new ArrayList<>(agentInfoDtoSet);
    }


    /**
     * 获取用户所属角色绑定的设备组下的所有Agent
     *
     * @param agentInfoQueryDto agent查询条件封装Dto
     * @param pageNum           当前页
     * @param pageSize          每页页数
     * @return PageInfo<AgentInfoDto>
     */
    @Override
    public PageInfo<AgentInfoDto> queryAgentInfoGroupRole(AgentInfoQueryDto agentInfoQueryDto, Integer pageNum, Integer pageSize) {

        AgentGroupRoleQueryBean agentGroupRoleQueryBean = getAgentGroupRoleQueryBean(agentInfoQueryDto, pageNum, pageSize);

        return getAgentInfoDtoPageInfo(agentGroupRoleQueryBean);
    }

    /**
     * 构建参数
     * @param agentInfoQueryDto agent查询信息Dto
     * @param pageNum   起始页
     * @param pageSize  每页大小
     * @return  agent
     */
    private static AgentGroupRoleQueryBean getAgentGroupRoleQueryBean(AgentInfoQueryDto agentInfoQueryDto, Integer pageNum, Integer pageSize) {
        CurrentUser user = CurrentUserUtil.getCurrentUser();
        AgentGroupRoleQueryBean agentGroupRoleQueryBean = new AgentGroupRoleQueryBean();
        agentGroupRoleQueryBean.setUserId(user.getId());
        agentGroupRoleQueryBean.setAgentIp(agentInfoQueryDto.getAgentIp());
        agentGroupRoleQueryBean.setAgentPort(null == agentInfoQueryDto.getAgentPort() ? null : agentInfoQueryDto.getAgentPort().toString());
        agentGroupRoleQueryBean.setPageNum(pageNum);
        agentGroupRoleQueryBean.setPageSize(pageSize);
        agentGroupRoleQueryBean.setAgentName(agentInfoQueryDto.getAgentName());
        agentGroupRoleQueryBean.setComputerListName(agentInfoQueryDto.getDeviceName());
        agentGroupRoleQueryBean.setComputerListOsName(agentInfoQueryDto.getOsName());
        agentGroupRoleQueryBean.setBusinessType("脚本服务化");
        agentGroupRoleQueryBean.setCenterName(agentInfoQueryDto.getCenterName());
        agentGroupRoleQueryBean.setBusinessSystemName(agentInfoQueryDto.getBusinessSystemName());
        //获取agent状态
        agentGroupRoleQueryBean.setResultAgentState(true);
        return agentGroupRoleQueryBean;
    }

    /**
     * 对象转换
     *
     * @param systemAgentInfoApiDto 平台管理agent对象
     * @return AgentInfoDto
     */
    public AgentInfoDto convertToAgentInfoDto(SystemAgentInfoApiDto systemAgentInfoApiDto) {
        AgentInfoDto agentInfoDto = new AgentInfoDto();
        agentInfoDto.setSysmAgentInfoId(systemAgentInfoApiDto.getAgentId());
        agentInfoDto.setAgentIp(systemAgentInfoApiDto.getAgentIp());
        agentInfoDto.setAgentPort(Integer.parseInt(systemAgentInfoApiDto.getAgentPort()));
        agentInfoDto.setAgentName(systemAgentInfoApiDto.getAgentName());
        agentInfoDto.setAgentState(systemAgentInfoApiDto.getAgentState());
        agentInfoDto.setDeviceName(systemAgentInfoApiDto.getComputerListName());
        agentInfoDto.setOsName(systemAgentInfoApiDto.getComputerListOsName());
        agentInfoDto.setCenterName(systemAgentInfoApiDto.getCenterName());
        List<String> businessSystemNameList = systemAgentInfoApiDto.getBusinessSystemName();
        agentInfoDto.setBusinessSystemNameList(businessSystemNameList);
        agentInfoDto.setCenterId(systemAgentInfoApiDto.getCenterId());
        return agentInfoDto;
    }

    /**
     * 获取agent分组列表数据
     *
     * @param pageNum  当前页
     * @param pageSize 每页页数
     * @return PageInfo<SystemComputerGroupDto>
     */
    @Override
    public PageInfo<SystemComputerGroupDto> queryAgentGroupPageList(SystemComputerGroupQueryDto systemComputerGroupQueryDto, Integer pageNum, Integer pageSize) {

        CurrentUser currentUser = CurrentUserUtil.getCurrentUser();
        ComputerGroupApiDto computerGroupApiDto = new ComputerGroupApiDto();
        computerGroupApiDto.setName(systemComputerGroupQueryDto.getCpName());
        computerGroupApiDto.setUserId(currentUser.getId());
        computerGroupApiDto.setBusinessType("脚本服务化");
        PageInfo<ComputerGroupApiDto> computerGroupDtoPageInfo = computerGroup.queryCpGroupList(computerGroupApiDto, pageNum, pageSize);
        List<SystemComputerGroupDto> systemComputerGroupDtos = new ArrayList<>();
        for (ComputerGroupApiDto computerGroupDto : computerGroupDtoPageInfo.getList()) {
            SystemComputerGroupDto systemComputerGroupDto = convertToSystemComputerGroupDto(computerGroupDto);
            systemComputerGroupDtos.add(systemComputerGroupDto);
        }
        PageInfo<SystemComputerGroupDto> pageInfo = new PageInfo<>(systemComputerGroupDtos);
        pageInfo.setPageNum(computerGroupDtoPageInfo.getPageNum());
        pageInfo.setPageSize(computerGroupDtoPageInfo.getPageSize());
        pageInfo.setTotal(computerGroupDtoPageInfo.getTotal());
        return pageInfo;
    }

    /**
     * 对象转换
     *
     * @param computerGroupDto 平台管理agent组对象
     * @return SystemComputerGroupDto
     */
    public SystemComputerGroupDto convertToSystemComputerGroupDto(ComputerGroupApiDto computerGroupDto) {
        SystemComputerGroupDto systemComputerGroupDto = new SystemComputerGroupDto();
        systemComputerGroupDto.setSysmComputerGroupId(computerGroupDto.getId());
        systemComputerGroupDto.setDescription(computerGroupDto.getDescription());
        systemComputerGroupDto.setCpname(computerGroupDto.getName());
        systemComputerGroupDto.setEnName(computerGroupDto.getEnName());
        return systemComputerGroupDto;
    }

}
