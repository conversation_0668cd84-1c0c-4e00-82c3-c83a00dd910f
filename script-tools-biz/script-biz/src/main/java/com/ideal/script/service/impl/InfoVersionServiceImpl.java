package com.ideal.script.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.script.dto.ScriptInfoDto;
import com.ideal.script.dto.ScriptVersionDto;
import com.ideal.script.mapper.InfoMapper;
import com.ideal.script.mapper.InfoVersionMapper;
import com.ideal.script.model.bean.ScriptInfoQueryBean;
import com.ideal.script.model.bean.TaskApplyBean;
import com.ideal.script.model.dto.interaction.agent.UserInfoDto;
import com.ideal.script.model.entity.Info;
import com.ideal.script.model.entity.InfoVersion;
import com.ideal.script.service.IInfoVersionService;
import com.ideal.system.api.IUserInfo;
import com.ideal.system.dto.UserInfoApiDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * Service业务层处理
 *
 * <AUTHOR>
 */
@Transactional(rollbackFor = Exception.class)
@Service
public class InfoVersionServiceImpl implements IInfoVersionService {

    private final InfoVersionMapper infoVersionMapper;

    private final InfoMapper infoMapper;

    private final IUserInfo userInfoApi;

    public InfoVersionServiceImpl(InfoVersionMapper infoVersionMapper, IUserInfo userInfoApi, InfoMapper infoMapper) {
        this.infoVersionMapper = infoVersionMapper;
        this.userInfoApi = userInfoApi;
        this.infoMapper = infoMapper;
    }

    /**
     * 查询【脚本版本信息】
     *
     * @param id 【脚本版本】主键
     * @return 【脚本版本信息Dto】
     */
    @Override
    public ScriptVersionDto selectInfoVersionById(Long id) {
        InfoVersion infoVersion = infoVersionMapper.selectInfoVersionById(id);
        return BeanUtils.copy(infoVersion, ScriptVersionDto.class);
    }

    /**
     * 查询【脚本版本信息】
     *
     * @param ids 【脚本版本】主键
     * @return 【脚本版本信息Dto】
     */
    @Override
    public List<ScriptVersionDto> getInfoVersionInfoList(Long[] ids) {
        List<InfoVersion> infoVersionList = infoVersionMapper.selectInfoVersionByIds(ids);
        return BeanUtils.copy(infoVersionList, ScriptVersionDto.class);
    }

    /**
     * 根据脚本版本uuid查询脚本信息
     * @param srcScriptUuid 脚本版本uuid
     * @return  脚本版本信息
     */
    @Override
    public ScriptVersionDto selectInfoVersionBySrcScriptUuid(String srcScriptUuid) {
        InfoVersion infoVersion = infoVersionMapper.selectInfoVersionBysrcScriptUuid(srcScriptUuid);
        return BeanUtils.copy(infoVersion, ScriptVersionDto.class);
    }


    /**
     * 查询【脚本版本信息】列表
     *
     * @param infoVersionDto 【脚本版本信息Dto】
     * @return 【脚本版本信息(分页)】
     */
    @Override
    public PageInfo<ScriptVersionDto> selectInfoVersionList(ScriptVersionDto infoVersionDto, int pageNum, int pageSize) {
        PageMethod.startPage(pageNum, pageSize);
        List<InfoVersion> infoVersionList = new ArrayList<>();
        if (null != infoVersionDto) {
            InfoVersion infoVersion = BeanUtils.copy(infoVersionDto, InfoVersion.class);
            infoVersionList = infoVersionMapper.selectInfoVersionList(infoVersion);
        }
        return PageDataUtil.toDtoPage(infoVersionList, ScriptVersionDto.class);
    }

    /**
     * 新增【脚本版本信息】
     *
     * @param infoVersionDto 【脚本版本信息Dto】
     */
    @Override
    public void insertInfoVersion(ScriptVersionDto infoVersionDto) {
        InfoVersion infoVersion = BeanUtils.copy(infoVersionDto, InfoVersion.class);
        infoVersionMapper.insertInfoVersion(infoVersion);
        infoVersionDto.setId(infoVersion.getId());
    }

    /**
     * 修改【脚本版本信息】
     *
     * @param infoVersionDto 【脚本版本信息Dto】
     */
    @Override
    public void updateInfoVersion(ScriptVersionDto infoVersionDto) {
        InfoVersion infoVersion = BeanUtils.copy(infoVersionDto, InfoVersion.class);
        infoVersionMapper.updateInfoVersion(infoVersion);
    }

    /**
     * 批量删除【脚本版本信息】
     *
     * @param ids 需要删除的【脚本版本】主键
     */
    @Override
    public void deleteInfoVersionByIds(Long[] ids) {
        infoVersionMapper.deleteInfoVersionByIds(ids);
    }

    /**
     * 删除【脚本版本信息】信息
     *
     * @param id 【脚本版本】主键
     * @return 结果
     */
    @Override
    public int deleteInfoVersionById(Long id) {
        return infoVersionMapper.deleteInfoVersionById(id);
    }

    /**
     * 根据本版表uuid获取脚本类型（sh、python...)
     *
     * @param srcScriptUuid 本版表uuid
     * @return String
     * <AUTHOR>
     */
    @Override
    public String getScriptTypeBySrcScriptUuid(String srcScriptUuid) {
        return infoVersionMapper.getScriptTypeBySrcScriptUuid(srcScriptUuid);
    }

    /**
     * 验证版本uuid是否存在
     *
     * @param srcScriptUuid 版本uuid
     * @return Boolean
     */
    @Override
    public Boolean validSrcScriptUuidExist(String srcScriptUuid) {
        return infoVersionMapper.validSrcScriptUuidExist(srcScriptUuid);
    }

    /**
     * 将之前的版本的脚本默认属性置为0
     *
     * @param infoVersion 脚本版本实体类
     * @return 结果
     */
    @Override
    public int updateInfoVersionDefaultValue(InfoVersion infoVersion) {
        return infoVersionMapper.updateInfoVersionDefaultValue(infoVersion);
    }


    /**
     * 是否是白名单
     *
     * @param id 主键
     * @return boolean
     */
    @Override
    public boolean isInWhiteList(Long id) {
        return infoVersionMapper.isInWhiteList(id);
    }

    /**
     * 查询同组用户集合
     *
     * @param userId 用户id
     * @return {@link List }<{@link UserInfoDto }>
     */
    @Override
    public List<UserInfoDto> queryGroupUserInfoListByUserId(Long userId) {
        List<UserInfoApiDto> userInfoApiDtoList = userInfoApi.queryGroupUserInfoListByUserId(userId);
        return BeanUtils.copy(userInfoApiDtoList, UserInfoDto.class);
    }

    /**
     * 查询脚本信息
     *
     * @param scriptInfoQueryBean 脚本查询信息
     * @return {@link List}<{@link TaskApplyBean}>
     */
    @Override
    public List<TaskApplyBean> getInfoVersionList(ScriptInfoQueryBean scriptInfoQueryBean) {
        //默认查脚本服务化的脚本
        if(scriptInfoQueryBean.getScriptSource() == null){
            scriptInfoQueryBean.setScriptSource(0);
        }
        return infoVersionMapper.getInfoVersionList(scriptInfoQueryBean);

    }

    /**
     * 查询默认版本的脚本信息
     *
     * @param scriptInfoQueryBean 脚本信息查询Dto
     * @return {@link InfoVersion}
     */
    @Override
    public TaskApplyBean getInfoDefaultVersion(ScriptInfoQueryBean scriptInfoQueryBean) {
        return infoVersionMapper.getInfoDefaultVersion(scriptInfoQueryBean);
    }

    /**
     * 查询默认版本的脚本信息
     *
     * @param scriptInfoQueryBean 脚本信息查询Dto
     * @return {@link InfoVersion}
     */
    @Override
    public TaskApplyBean getInfoVersion(ScriptInfoQueryBean scriptInfoQueryBean) {
        return infoVersionMapper.getInfoVersion(scriptInfoQueryBean);
    }

    /**
     * 根据脚本版本uuid查询脚本信息
     *
     * @param srcScriptUuid 版本uuid
     * @return 脚本基本信息
     */
    @Override
    public ScriptInfoDto getInfoByVersionUuid(String srcScriptUuid) {
        //查询脚本版本信息
        InfoVersion infoVersion = infoVersionMapper.selectInfoVersionBysrcScriptUuid(srcScriptUuid);
        //根据uuid查询脚本信息
        return BeanUtils.copy(infoMapper.selectInfoByUniqueUuid(infoVersion.getInfoUniqueUuid()), ScriptInfoDto.class);
    }

    /**
     * 根据主表id获取版本表id
     *
     * @param ids 主表id集合
     * @return 版本id集合
     */
    @Override
    public List<Long> getVersionIdByIds(Long[] ids) {
        //分两种情况筛选
        List<Long> versionIdList = new ArrayList<>();
        //拆分成两部分进行，一部分是草稿，一部分是默认版本
        List<Long> editIdList = new ArrayList<>();
        List<Long> publishIdList = new ArrayList<>();
        List<Info> infoList = infoMapper.selectInfoByIds(ids);
        for (Info info : infoList) {
            if(info.getEditState().equals(0)){
                editIdList.add(info.getId());
            }else{
                publishIdList.add(info.getId());
            }
        }
        String scriptState = "edit";
        List<String> uniqueUuidList1 = new ArrayList<>();
        if(!editIdList.isEmpty()) {
           uniqueUuidList1 = infoMapper.getUniqueUuidByIds(editIdList, scriptState);
        }
        scriptState = "publish";
        List<String> uniqueUuidList2 = new ArrayList<>();
        if(!publishIdList.isEmpty()) {
            uniqueUuidList2 = infoMapper.getUniqueUuidByIds(publishIdList, scriptState);
        }

        List<InfoVersion> infoVersionList = new ArrayList<>();

        //通过不同的情况查询不同的脚本数据
        if (uniqueUuidList1 != null && !uniqueUuidList1.isEmpty()) {
            List<InfoVersion> infoVersionList1 = infoVersionMapper.selectInfoVersionListForEdit(uniqueUuidList1);
            infoVersionList.addAll(infoVersionList1);
        }
        if (uniqueUuidList2 != null && !uniqueUuidList2.isEmpty()) {
            List<InfoVersion> infoVersionList2 = infoVersionMapper.selectInfoVersionListForDefault(uniqueUuidList2);
            infoVersionList.addAll(infoVersionList2);
        }
        for (InfoVersion infoVersion : infoVersionList) {
            versionIdList.add(infoVersion.getId());
        }
        return versionIdList;
    }

    /**
     * 根据任意的脚本的版本uuid查询脚本的
     * @param srcScriptUuid 脚本uuid
     * @return  默认版本信息
     */
    public ScriptVersionDto getInfoDefaultVersionByUuid(String srcScriptUuid){
        InfoVersion infoVersion = infoVersionMapper.selectDefaultInfoVersionByUuid(srcScriptUuid);
        return BeanUtils.copy(infoVersion, ScriptVersionDto.class);
    }

    @Override
    public InfoVersion getInfoDefaultVersionByUniqueUuid(String uniqueUuid) {
        return infoVersionMapper.getInfoDefaultVersionByUniqueUuid(uniqueUuid);
    }

    @Override
    public Integer getTaskCountByVersionId(Long versionId) {
        //根据脚本版本id查询版本uuid
        InfoVersion infoVersion = infoVersionMapper.selectInfoVersionById(versionId);
        return infoVersionMapper.getTaskCountByVersionUuid(infoVersion.getSrcScriptUuid());
    }

    @Override
    public Long selectIdBySrcScriptUuid(String srcScriptUuid) {
        return infoVersionMapper.selectIdBySrcScriptUuid(srcScriptUuid);
    }
}
