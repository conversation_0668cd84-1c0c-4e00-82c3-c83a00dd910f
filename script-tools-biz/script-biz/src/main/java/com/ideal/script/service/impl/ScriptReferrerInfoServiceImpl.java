package com.ideal.script.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.script.mapper.ScriptReferrerInfoMapper;
import com.ideal.script.model.dto.ScriptReferrerInfoDto;
import com.ideal.script.model.entity.ScriptReferrerInfo;
import com.ideal.script.service.IScriptReferrerInfoService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class ScriptReferrerInfoServiceImpl implements IScriptReferrerInfoService {

    private final ScriptReferrerInfoMapper scriptReferrerInfoMapper;

    public ScriptReferrerInfoServiceImpl(ScriptReferrerInfoMapper scriptReferrerInfoMapper) {
        this.scriptReferrerInfoMapper = scriptReferrerInfoMapper;
    }

    /**
     * 获取分页数据
     */
    @Override
    public PageInfo<ScriptReferrerInfoDto> selectPageList(ScriptReferrerInfoDto scriptReferrerInfoDto,int pageNum,int pageSize) {
        //分页查询
        PageMethod.startPage(pageNum, pageSize);
        List<ScriptReferrerInfo> scriptReferrerInfoList = scriptReferrerInfoMapper.selectPageList(BeanUtils.copy(scriptReferrerInfoDto,ScriptReferrerInfo.class));
        return PageDataUtil.toDtoPage(scriptReferrerInfoList, ScriptReferrerInfoDto.class);
    }

    /**
     * 新增数据
     * @param scriptReferrerInfo 新增数据对象
     */
    @Override
    public void insertReferrerInfo(ScriptReferrerInfo scriptReferrerInfo){
        scriptReferrerInfoMapper.insertReferrerInfo(scriptReferrerInfo);
    }

    /**
     * 根据引用对象id删除数据
     * @param scriptReferrerInfo 数据对象
     */
    @Override
    public void deleteByReferrerBizId(ScriptReferrerInfo scriptReferrerInfo){
        scriptReferrerInfoMapper.deleteByReferrerBizId(scriptReferrerInfo);
    }

}
