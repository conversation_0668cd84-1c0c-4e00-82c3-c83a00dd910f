package com.ideal.script.observer.numerical;


import com.alibaba.fastjson.JSON;
import com.ideal.common.util.spring.SpringUtil;
import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.config.PsbcProperties;
import com.ideal.script.mapper.TaskMapper;
import com.ideal.script.mapper.TaskRuntimeMapper;
import com.ideal.script.model.entity.Task;
import com.ideal.script.model.entity.TaskRuntime;
import com.ideal.script.service.impl.builders.MyScriptServiceScripts;
import com.ideal.system.dto.UserInfoApiDto;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 任务执行完毕，异步推送统计信息
 * <AUTHOR>
 */
@Component
public class ScheduledTaskNumericalResultPush {

    private static final Logger logger = LoggerFactory.getLogger(ScheduledTaskNumericalResultPush.class);

    private final MyScriptServiceScripts scripts;
    private final TaskRuntimeMapper taskRuntimeMapper;
    private final ApplicationContext applicationContext;
    private final TaskMapper taskMapper;

    public ScheduledTaskNumericalResultPush(TaskMapper taskMapper,@Lazy MyScriptServiceScripts scripts, TaskRuntimeMapper taskRuntimeMapper, ApplicationContext applicationContext) {
        this.taskMapper = taskMapper;
        this.scripts = scripts;
        this.taskRuntimeMapper = taskRuntimeMapper;
        this.applicationContext = applicationContext;
    }

    @Async
    public void pushMessage(Long taskRuntimeId) {
        //如果spring中没有PsbcProperties这个bean，说明没有开启bankCode001个性化配置，不推送结果
        if(applicationContext.getBeanNamesForType(PsbcProperties.class).length > 0
                && ObjectUtils.notEqual(SpringUtil.getBean(PsbcProperties.class),null)){
            //根据taskRuntimeId查询任务调度方式，只有定时任务才发统计消息
            Task task = taskMapper.selectTaskByRuntimeId(taskRuntimeId);
            //定时任务标识
            if(task.getTaskScheduler().equals(Enums.TaskScheduler.TIMED.getValue())){
                //短信内容，agent成功完成数量、失败数量、超时完成数量
                String message = agentResultStatistics(task);
                //发送短信
                if(StringUtils.isNotBlank(message)){
                    statisticsMessage(message,task.getCreatorId(),task.getSrcScriptUuid());
                }
            }
        }
    }

    /**
     * 统计本次任务agent执行情况
     * @param task 任务对象
     * @return 返回agent成功完成数量、失败数量、超时完成数量
     */
    private String agentResultStatistics(Task task){
        //根据任务id查询本次所有agent实例
        int finishedCount = 0;
        int errorCount = 0;
        int timeOutFinished = 0;
        List<TaskRuntime> taskRuntimes = taskRuntimeMapper.selectTaskRunTimeByTaskId(task.getId());
        if(taskRuntimes.isEmpty()){
            return "";
        }
        for(TaskRuntime taskRuntime : taskRuntimes){
            //agent执行完成时间
            long finishedTime = taskRuntime.getEndTime().getTime() - taskRuntime.getStartTime().getTime();
            //执行状态
            int stateVal = taskRuntime.getState();
            //异常
            if(30 == stateVal){
                errorCount++;
            }else if(20 == stateVal && finishedTime > taskRuntime.getTimeoutValue() * 1000) {
                //超时完成（超时，并且状态为完成）
                timeOutFinished++;
            }else if(20 == stateVal && finishedTime <= taskRuntime.getTimeoutValue() * 1000){
                //正常（未超时，并且状态为完成）
                finishedCount++;
            }
        }

        //短信内容
        String message = "任务“"+ task.getTaskName() +"”本次执行结果：共执行Agent:"+taskRuntimes.size()+"台";

        if(finishedCount > 0){
            message += "，正常完成"+finishedCount+"台";
        }
        if(errorCount > 0){
            message += "，异常"+errorCount+"台";
        }
        if(timeOutFinished > 0){
            message += "，超时完成"+timeOutFinished+"台";
        }
        message += "。";

        return message;
    }


    /**
     * 发送统计短信
     * @param message 发送的短信信息
     * @param userId 用户id
     */
    private void statisticsMessage(String message,Long userId,String uuid){
        Map<String,Object> res = new HashMap<>(6);
        res.put("moduleCode","scriptservice");
        res.put("typeCode","scanUnrevisedScript");
        res.put("warnMsg",message);
        res.put("serviceUuid",uuid);
        res.put("extendInfoUrl","");
        List<Long> userList = new ArrayList<>();
        userList.add(userId);
        List<UserInfoApiDto> userInfoList = scripts.getiUserInfo().getUserInfoList(userList);
        List<UserInfoApiDto> useList = new ArrayList<>();
        if(!userInfoList.isEmpty()){
            useList.add(userInfoList.get(0));
        }
        res.put("userList",useList);
        String str = JSON.toJSONString(res);
        scripts.getWarn().receiveWarn(str);
        logger.info("statisticsMessage send success!!!, srcScriptUuid: {}, userId: {}", uuid, userId);
    }
}
