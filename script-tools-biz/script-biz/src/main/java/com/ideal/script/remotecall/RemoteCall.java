package com.ideal.script.remotecall;

import com.ideal.approval.api.IDoubleCheckApiService;
import com.ideal.approval.dto.DoubleCheckApiDto;
import com.ideal.approval.dto.ResultApiDto;
import com.ideal.system.api.IBusinessSystem;
import org.springframework.stereotype.Component;

/**
 * 接口调用分装类
 *  <AUTHOR>
 */
@Component
public class RemoteCall {

    private final IDoubleCheckApiService doubleCheckApiService;
    private final IBusinessSystem businessSystem;

    public RemoteCall(IDoubleCheckApiService doubleCheckApiService, IBusinessSystem businessSystem) {
        this.doubleCheckApiService = doubleCheckApiService;
        this.businessSystem = businessSystem;
    }

    /**
     * 调用dubbo接口发起双人复核
     * @return ResultApiDto
     */

    public ResultApiDto applyForDoubleCheck(String uuid, DoubleCheckApiDto doubleCheckDto) {
        return doubleCheckApiService.receiveDoubleCheck(uuid, doubleCheckDto);
    }

}
