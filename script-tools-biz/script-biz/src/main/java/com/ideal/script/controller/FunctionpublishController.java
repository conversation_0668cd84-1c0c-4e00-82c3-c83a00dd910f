package com.ideal.script.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.permission.MenuPermitConstant;
import com.ideal.script.model.dto.FunctionpublishDto;
import com.ideal.script.model.dto.VarAndFuncForEditDto;
import com.ideal.script.service.IFunctionpublishService;
import com.ideal.system.common.component.aop.MethodPermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 函数库基础发布Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("${app.script-tools-url:}/functionpublish")
@MethodPermission(MenuPermitConstant.MY_SCRIPT_PER)
public class FunctionpublishController
{
    private final IFunctionpublishService functionpublishService;
    private static final String LIST_SUCCESS_MESSAGE = Constants.LIST_SUCCESS;
    private static final Logger logger = LoggerFactory.getLogger(FunctionpublishController.class);


    public FunctionpublishController(IFunctionpublishService functionpublishService){
       this.functionpublishService=functionpublishService;
    }

    /**
     * 查询函数库基础发布列表
     */
    @PostMapping("/listFunctionpublishForScriptEdit")
    public R<PageInfo<FunctionpublishDto>> listFunctionpublishForScriptEdit(@RequestBody TableQueryDto<VarAndFuncForEditDto> tableQueryDTO)
    {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, functionpublishService.selectFunctionpublishListForScriptEdit(tableQueryDTO.getQueryParam(), tableQueryDTO.getPageNum(),
                tableQueryDTO.getPageSize()), LIST_SUCCESS_MESSAGE);
    }
}
