package com.ideal.script.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.permission.MenuPermitConstant;
import com.ideal.script.model.dto.AgentInfoDto;
import com.ideal.script.model.dto.QueryAgentInfoDto;
import com.ideal.script.service.IAgentInfoService;
import com.ideal.system.common.component.aop.MethodPermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Agent基本信息-冗余平台管理Controller
 *
 * <AUTHOR>
 */
@RestController("scriptAgentInfoController")
@RequestMapping("${app.script-tools-url:}/info")
@MethodPermission(MenuPermitConstant.EXECUTION_TASK_PER)
public class AgentInfoController {
    private static final Logger logger = LoggerFactory.getLogger(AgentInfoController.class);
    private final IAgentInfoService agentInfoService;


    public AgentInfoController(IAgentInfoService agentInfoService) {
        this.agentInfoService = agentInfoService;
    }

    /**
     * 根据任务taskId查询任务绑定的还未运行的agent信息
     *
     * @return R<List < AgentInfoDto>>
     */
    @PostMapping("/getTaskBindAgentInfo")
    public R<PageInfo<AgentInfoDto>> getTaskBindAgentInfo(@RequestBody TableQueryDto<QueryAgentInfoDto> tableQueryDTO) {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, agentInfoService.getTaskBindAgentInfo(tableQueryDTO.getQueryParam(), tableQueryDTO.getPageNum(),
                tableQueryDTO.getPageSize()), Constants.LIST_SUCCESS);
    }

    /**
     * 分页查询脚本任务绑定的所有agent信息
     *
     * @param tableQueryDto 查询条件
     * @return {@link R }<{@link PageInfo }<{@link AgentInfoDto }>>
     */
    @PostMapping("/getTaskAllAgentInfo")
    public R<PageInfo<AgentInfoDto>> getTaskAllAgentInfo(@RequestBody TableQueryDto<QueryAgentInfoDto> tableQueryDto) {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, agentInfoService.getTaskAllAgentInfo(tableQueryDto.getQueryParam(), tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()), Constants.LIST_SUCCESS);
    }
}
