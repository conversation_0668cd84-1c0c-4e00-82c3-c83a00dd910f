package com.ideal.script.exception;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ErrorCause implements Serializable {
    private static final long serialVersionUID = 1L;
    private ErrorCause cause;
    private List<StackTraceElementInfo> stackTrace;

    @SuppressWarnings("unused")
    public ErrorCause getCause() {
        return cause;
    }

    @SuppressWarnings("unused")
    public void setCause(ErrorCause cause) {
        this.cause = cause;
    }

    public List<StackTraceElementInfo> getStackTrace() {
        return stackTrace;
    }

    @SuppressWarnings("unused")
    public void setStackTrace(List<StackTraceElementInfo> stackTrace) {
        this.stackTrace = stackTrace;
    }

    @Override
    public String toString() {
        return "ErrorCause{" +
                "cause=" + cause +
                ", stackTrace=" + stackTrace +
                '}';
    }
}
