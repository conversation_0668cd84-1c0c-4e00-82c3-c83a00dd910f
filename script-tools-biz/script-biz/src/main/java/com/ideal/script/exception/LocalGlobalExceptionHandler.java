package com.ideal.script.exception;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import com.ideal.common.dto.R;
import com.ideal.sc.util.ValidationUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.Optional;


/**
 * 脚本服务化异常处理
 *
 * <AUTHOR>
 **/
@RestControllerAdvice(annotations = {RestController.class, Controller.class})
@Order(1)
public class LocalGlobalExceptionHandler {
    private static final String FAIL_CODE = "00000";
    private final Logger logger = LoggerFactory.getLogger(LocalGlobalExceptionHandler.class);

    /**
     *
     * <P>丢失参数时触发</P>
     *
     * @param ex MissingServletRequestParameterException
     * @return CommonResult
     */
    @ResponseBody
    @ExceptionHandler(value = MissingServletRequestParameterException.class)
    public R<Object> missingServletRequestParameterException(MissingServletRequestParameterException ex) {
        logger.error("[missingServletRequestParameterException]", ex);
        return R.fail(FAIL_CODE, "缺失参数:" + ex.getParameterName());
    }
    @ResponseBody
    @ExceptionHandler(value = JsonParseException.class)
    public R<Object> jsonParseException(JsonParseException ex) {
        logger.error("[jsonParseException]", ex);
        return R.fail(FAIL_CODE, "不合法JSON:" + ex.getMessage());
    }

    /**
     * 脚本服务化业务异常处理
     * @param ex 脚本服务化业务异常
     * @return 错误信息
     */
    @ResponseBody
    @ExceptionHandler(value = ScriptException.class)
    public R<Object> scriptException(ScriptException ex) {
        logger.error("[脚本服务化业务异常]：", ex);
        Optional<Throwable> cause = Optional.ofNullable(ex.getCause());
        String msg;
        msg = cause.map(throwable -> Optional.ofNullable(throwable.getMessage()).orElse("脚本服务化业务异常")).orElseGet(() -> Optional.ofNullable(ex.getMessage()).orElse("脚本服务化业务异常"));
        return ValidationUtils.customFailResult(msg, msg);
    }

    @ResponseBody
    @ExceptionHandler(value = InvalidFormatException.class)
    public R<Object> invalidFormatException(InvalidFormatException ex) {
        logger.error("[InvalidFormatException]", ex);

        return R.fail(FAIL_CODE, "无效参数格式:" + ex.getOriginalMessage());
    }

}
