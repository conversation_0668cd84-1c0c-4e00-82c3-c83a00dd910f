package com.ideal.script.service;

import com.ideal.script.dto.AttachmentDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.ScriptExecAuditDto;
import com.ideal.script.model.dto.TaskAttachmentDto;
import com.github.pagehelper.PageInfo;
import com.ideal.script.model.dto.TaskDto;
import org.apache.ibatis.session.SqlSession;

import java.io.IOException;
import java.util.List;

/**
 * 脚本任务附件附Service接口
 * 
 * <AUTHOR>
 */
 public interface ITaskAttachmentService
{
    /**
     * 查询脚本任务附件附
     * 
     * @param id 脚本任务附件附主键
     * @return 脚本任务附件附
     */
     TaskAttachmentDto selectTaskAttachmentById(Long id);

    /**
     * 查询脚本任务附件附列表
     * @param pageNum 当前页
     * @param pageSize 每页条数
     * @param taskAttachmentDto 脚本任务附件附
     * @return 脚本任务附件附集合
     */
     PageInfo<TaskAttachmentDto> selectTaskAttachmentList(TaskAttachmentDto taskAttachmentDto, int pageNum, int pageSize);

    /**
     * 新增脚本任务附件附
     * 
     * @param taskAttachmentDto 脚本任务附件附
     * @return 结果
     */
     int insertTaskAttachment(TaskAttachmentDto taskAttachmentDto);

    /**
     * 修改脚本任务附件附
     * 
     * @param taskAttachmentDto 脚本任务附件附
     * @return 结果
     */
     int updateTaskAttachment(TaskAttachmentDto taskAttachmentDto);

    /**
     * 批量删除脚本任务附件附
     * 
     * @param ids 需要删除的脚本任务附件附主键集合
     * @return 结果
     */
     int deleteTaskAttachmentByIds(Long[] ids);

    /**
     * 删除脚本任务附件附信息
     * 
     * @param id 脚本任务附件附主键
     * @return 结果
     */
     int deleteTaskAttachmentById(Long id);

    /**
     * 双人复核业务详情页面-查询任务绑定的附件
     * @param serviceId 业务主键
     * @return List<TaskAttachmentDto>
     */
    List<TaskAttachmentDto> selectTaskAttachmentByServiceId(Long serviceId,Long taskId);

    /**
     * 双人复核业务详情页面-查询任务绑定的附件 不包含文件内容
     * @param serviceId 业务主键
     * @return List<TaskAttachmentDto>
     */
    List<TaskAttachmentDto> selectTaskAttachmentNoContentByServiceId(Long serviceId,Long taskId);

    /**
     * 存储附件信息
     *
     * @param scriptExecAuditDto 任务申请提交审核Dto
     * @param taskInfo           任务申请提交任务时的脚本任务对象
     * @param sqlSession         ​SqlSession​ 对象
     * @throws ScriptException 抛出自定义异常
     */
    void saveTaskAttachement(ScriptExecAuditDto scriptExecAuditDto, TaskDto taskInfo, SqlSession sqlSession) throws ScriptException;

    /**
     * 上传附件
     *
     * @param taskAttachmentDto  文件
     * @return      dto
     * @throws IOException exception
     */
    TaskAttachmentDto uploadAttachment(TaskAttachmentDto taskAttachmentDto) throws IOException, ScriptException;

    /**
     * 根据任务id 获取附件id
     * @param taskId 任务id
     * @return 附件id
     */
    List<Long> getIdsByTaskId(Long taskId);

    /**
     * 根据任务id更新为0值
     * @param taskId
     * @return
     */
    int updateTaskIdEmptyByTaskId(Long taskId);

    /**
     * 清理临时附件方法
     * @return 处理临时附件的数量
     */
    int cleanAttachment();
}
