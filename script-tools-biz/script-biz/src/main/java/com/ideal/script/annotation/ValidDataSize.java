package com.ideal.script.annotation;


import com.ideal.script.common.validation.DataSizeValidator;
import org.springframework.util.unit.DataSize;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.ANNOTATION_TYPE;
import static java.lang.annotation.ElementType.CONSTRUCTOR;
import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.ElementType.TYPE_USE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * 容量验证类{@link DataSize}验证容量最大值注解
 * @see com.ideal.script.config.ScriptBusinessConfig
 * <AUTHOR>
 */
@Target({ElementType.METHOD, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = DataSizeValidator.class)
public @interface ValidDataSize {

    String message() default "{com.ideal.script.annotation.ValidDataSize.message}";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    String max() default "15MB";

    @Target({METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE})
    @Retention(RUNTIME)
    @Documented
    @interface List {
        ValidDataSize[] value();
    }
}