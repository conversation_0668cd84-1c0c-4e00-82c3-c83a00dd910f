package com.ideal.script.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.audit.producer.annotation.Auditable;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.sc.util.ValidationUtils;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.permission.MenuPermitConstant;
import com.ideal.script.common.constant.permission.ParamValidationBtnPermitConstant;
import com.ideal.script.common.validation.Create;
import com.ideal.script.common.validation.Update;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.ParameterCheckDto;
import com.ideal.script.service.IParameterCheckService;
import com.ideal.system.common.component.aop.MethodPermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("${app.script-tools-url:}/check")
@MethodPermission(MenuPermitConstant.PARAM_VALIDATION_PER)
public class ParameterCheckController {
    private final IParameterCheckService parameterCheckService;
    private static final String LIST_SUCCESS = Constants.LIST_SUCCESS;
    private static final Logger logger = LoggerFactory.getLogger(ParameterCheckController.class);


    public ParameterCheckController(IParameterCheckService parameterCheckService){
        this.parameterCheckService = parameterCheckService;
    }
    /**
     * 新增参数验证规则
     */
    @PostMapping("/saveParameterCheck")
    @Auditable("参数验证|新增")
    @MethodPermission(ParamValidationBtnPermitConstant.SAVE_PARAMETER_CHECK_PER)
    public R<Object> saveParameterCheck(@RequestBody @Validated(Create.class) ParameterCheckDto parameterCheckDto) throws ScriptException {
        try {
            parameterCheckService.insertParameterCheck(parameterCheckDto);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "", "save.success");
        } catch (ScriptException e) {
            logger.error("saveParameterCheck error",e);
            return ValidationUtils.customFailResult("ruleName",e.getMessage());
        }
    }
    /**
     * 修改参数验证规则
     */
    @PostMapping("/updateParameterCheck")
    @Auditable("参数验证|编辑")
    @MethodPermission(ParamValidationBtnPermitConstant.UPDATE_PARAMETER_CHECK_PER)
    public R<Object> updateParameterCheck(@RequestBody @Validated(Update.class) ParameterCheckDto parameterCheckDto)
    {
        try {
            parameterCheckService.updateParameterCheck(parameterCheckDto);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "", "update.success");
        } catch (ScriptException e) {
            logger.error("updateParameterCheck error",e);
            return ValidationUtils.customFailResult("ruleName",e.getMessage());

        }
    }
    /**
     * 查询【参数验证规则】列表
     */
    @PostMapping("/listParameterCheck")
    @MethodPermission(MenuPermitConstant.MY_SCRIPT_PER)
    public R<PageInfo<ParameterCheckDto>> listParameterCheck(@RequestBody TableQueryDto<ParameterCheckDto> tableQueryDTO)
    {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, parameterCheckService.selectParameterCheckList(tableQueryDTO.getQueryParam(), tableQueryDTO.getPageNum(),
                tableQueryDTO.getPageSize()), LIST_SUCCESS);
    }

    /**
     * 删除【参数验证规则】
     */
    @GetMapping("/removeParameterCheck")
    @Auditable("参数验证|删除")
    @MethodPermission(ParamValidationBtnPermitConstant.REMOVE_PARAMETER_CHECK_PER)
    public R<Object> removeParameterCheck(@RequestParam(value = "ids") Long[] ids)
    {
        parameterCheckService.deleteParameterCheckByIds(ids);
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "remove.success");
    }
}
