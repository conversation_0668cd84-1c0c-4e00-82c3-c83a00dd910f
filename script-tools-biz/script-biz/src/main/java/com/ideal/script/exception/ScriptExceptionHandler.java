package com.ideal.script.exception;


import com.ideal.common.dto.R;
import com.ideal.common.dto.ValidateError;
import com.ideal.common.util.spring.MessageUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestControllerAdvice(annotations = {RestController.class, Controller.class})
public class ScriptExceptionHandler {
    private static final String FAIL_CODE = "10605";
    private final Logger logger = LoggerFactory.getLogger(ScriptExceptionHandler.class);

    /**
     * 验证异常处理 - @Validated加在controller类上，且在参数列表中直接指定constraints时触发
     * <P>不符合验证规则时触发</P>
     *
     * @param ex ConstraintViolationException
     * @return CommonResult
     */
    @ResponseBody
    @ExceptionHandler(value = ScriptException.class)
    public R<Object> handleScriptException(ScriptException ex) {
        logger.error("[scriptExceptionHandler]", ex);
        Map<String, List<ValidateError>> validateErrorMap = new HashMap<>(1);
        List<ValidateError> validateErrorList = new ArrayList<>();
        String errMsg = MessageUtil.message(ex.getMessage());
        ValidateError validateError = new ValidateError(ex.getMessage(), errMsg);
        validateErrorList.add(validateError);
        validateErrorMap.put("validateError", validateErrorList);
        return R.fail(FAIL_CODE, validateErrorMap, errMsg);
    }

}
