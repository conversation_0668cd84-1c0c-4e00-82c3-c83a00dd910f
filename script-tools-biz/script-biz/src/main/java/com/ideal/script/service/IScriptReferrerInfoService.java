package com.ideal.script.service;
import com.github.pagehelper.PageInfo;
import com.ideal.script.model.dto.ScriptReferrerInfoDto;
import com.ideal.script.model.entity.ScriptReferrerInfo;

/**
 * <AUTHOR>
 */
public interface IScriptReferrerInfoService {
    /**
     * 获取分页数据
     */
    PageInfo<ScriptReferrerInfoDto> selectPageList(ScriptReferrerInfoDto scriptReferrerInfoDto, int pageNum, int pageSize);

    /**
     * 新增数据
     * @param scriptReferrerInfo 新增数据对象
     */
    void insertReferrerInfo(ScriptReferrerInfo scriptReferrerInfo);

    /**
     * 根据引用对象id删除数据
     * @param scriptReferrerInfo 数据对象
     */
    void deleteByReferrerBizId(ScriptReferrerInfo scriptReferrerInfo);

}
