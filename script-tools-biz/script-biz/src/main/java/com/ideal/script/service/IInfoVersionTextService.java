package com.ideal.script.service;

import com.github.pagehelper.PageInfo;
import com.ideal.script.model.dto.InfoVersionTextDto;
/**
 * Service接口
 * 
 * <AUTHOR>
 */
public interface IInfoVersionTextService 
{
    /**
     * 查询
     * 
     * @param id 主键
     * @return 
     */
     InfoVersionTextDto selectInfoVersionTextById(Long id);

    /**
     * 查询列表
     * 
     * @param infoVersionTextDto 
     * @param pageNum 
     * @param pageSize 
     * @return 集合
     */
     PageInfo<InfoVersionTextDto> selectInfoVersionTextList(InfoVersionTextDto infoVersionTextDto, int pageNum, int pageSize);

    /**
     * 新增
     *
     * @param infoVersionTextDto 
     */
     void insertInfoVersionText(InfoVersionTextDto infoVersionTextDto);

    /**
     * 修改
     *
     * @param infoVersionTextDto 
     */
     void updateInfoVersionText(InfoVersionTextDto infoVersionTextDto);

    /**
     * 批量删除
     *
     * @param ids 需要删除的主键集合
     */
     void deleteInfoVersionTextByIds(Long[] ids);

    /**
     * 删除信息
     * 
     * @param id 主键
     * @return 结果
     */
     int deleteInfoVersionTextById(Long id);
    
	/**
	 * 根据脚本id获取脚本内容
	 * @param scriptId  脚本id
	 * @return  结果
	 */
	 InfoVersionTextDto selectInfoVersionTextByScriptId(Long scriptId);

	 /**
	  * 根据版本uuid获取脚本内容
	  * @param srcScriptUuid 版本uuid
	  * @return InfoVersionTextDto
	  */

    InfoVersionTextDto selectInfoVersionTextByScriptUuid(String srcScriptUuid);


}
