package com.ideal.script.controller;

import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.permission.MenuPermitConstant;
import com.ideal.script.model.dto.VarAndFuncForEditDto;
import com.ideal.script.service.IVariablePublishService;
import com.ideal.system.common.component.aop.MethodPermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 变量库基础信息发布Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("${app.script-tools-url:}/variablePublish")
@MethodPermission(MenuPermitConstant.MY_SCRIPT_PER)
public class VariablePublishController {
    private static final Logger logger = LoggerFactory.getLogger(VariablePublishController.class);
    private static final String LIST_SUCCESS_MESSAGE = Constants.LIST_SUCCESS;
    private final IVariablePublishService variablePService;

    public VariablePublishController(IVariablePublishService variablePublishService) {
        this.variablePService = variablePublishService;
    }

    @PostMapping("/listVariablePForEdit")
    public R<Object> listVariablePublishForEdit(@RequestBody TableQueryDto<VarAndFuncForEditDto> tableQueryDTO) {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, variablePService.selectVariablePublishListForEdit(tableQueryDTO.getQueryParam(), tableQueryDTO.getPageNum(), tableQueryDTO.getPageSize()), LIST_SUCCESS_MESSAGE);
    }
}
