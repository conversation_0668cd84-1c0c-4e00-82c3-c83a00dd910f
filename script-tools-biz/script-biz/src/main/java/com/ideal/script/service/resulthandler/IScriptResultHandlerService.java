package com.ideal.script.service.resulthandler;

import com.ideal.script.exception.ScriptException;

import java.util.List;

/**
 * 脚本执行结果处理类
 * <AUTHOR>
 */

public interface IScriptResultHandlerService {

    /**
     * 处理脚本执行结果
     * This method processes the result of script execution.
     * @param messageBody 包含执行结果信息的JSON消息（script-execute-result）
     * @throws ScriptException 抛出自定义通知异常
     */
    void handleScriptExecuteResult(List<String> messageBody) throws ScriptException;



    /**
     * 处理管理服务器发送agent执行后返回的消息
     * @param message 管理服务发送agent执行后返回的调用结果（script-send-result）
     * @throws ScriptException 抛出自定义通知异常
     */

    void handleScriptSendResult(String message) throws ScriptException;


    /**
     * 处理script-error-result主题的消息
     * @param bizId 业务主键
     * @param agentTaskId 管理服务器消息中返回的任务id
     * @param message  管理服务发送异常返回的调用结果（script-error-result）
     * @throws ScriptException 抛出自定义通知异常
     */
    void handleScriptErrorResult(String message,String bizId,Long agentTaskId) throws ScriptException;
}
