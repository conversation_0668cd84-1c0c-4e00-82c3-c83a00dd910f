package com.ideal.script.service.impl;

import com.ideal.message.center.IPublisher;
import com.ideal.message.center.exception.CommunicationException;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.model.dto.JobDto;
import com.ideal.script.model.dto.JobSourceDictSourceCode;
import com.ideal.script.model.dto.TaskStartDto;
import com.ideal.script.service.IScriptTaskStatePublisher;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 中信 作业中心脚本服务化任务统计发布通知处理类
 * <AUTHOR>
 **/
@Service
public class ScriptTaskStateJobCenterPublisherImpl implements IScriptTaskStatePublisher {
    private final Logger LOGGER = LoggerFactory.getLogger(ScriptTaskStateJobCenterPublisherImpl.class);
    private final IPublisher publisher;

    public ScriptTaskStateJobCenterPublisherImpl(IPublisher publisher) {
        this.publisher = publisher;
    }

    /**
     * 作业中心任务状态变更通知，任务开始、任务完成(所有agent执行完毕)、任务终止都会调用
     * @param taskStartDto 任务相关dto
     * @param state 任务状态
     */
    @Override
    public void publish(TaskStartDto taskStartDto , Enums.TaskInstanceStatus state) {
        Date date = new Date();
        //根据state，运行中、完成/终止，生成不同的JobDto
        JobDto jobDto = new JobDto();
        jobDto.setJobName(taskStartDto.getTaskName());
        jobDto.setJobDescription(taskStartDto.getPublishDesc());
        jobDto.setBizId(taskStartDto.getIscriptTaskInstanceId().toString());
        jobDto.setJobSourceDictSourceCode(JobSourceDictSourceCode.SCRIPT);
        try {
            String channel = "";
            switch (state) {
                case RUNNING:
                    jobDto.setStatus("RUNNING");
                    jobDto.setStartTime(date);
                    channel = Constants.SCRIPT_JOB_START_CHANEL;
                    break;
                case COMPLETED:
                    channel = Constants.SCRIPT_JOB_END_CHANEL;
                    jobDto.setEndTime(date);
                    jobDto.setStatus("COMPLETED");
                    break;
                case TERMINATED:
                    channel = Constants.SCRIPT_JOB_END_CHANEL;
                    jobDto.setEndTime(date);
                    jobDto.setStatus("CANCELLED");
                    break;
                default:
                    break;
            }
            //发送mq
            publisher.apply(channel, jobDto);
            LOGGER.info("推送作业中心任务状态变更成功, 任务状态：{},{}", state.getDescription(),jobDto);
        } catch (CommunicationException e) {
            LOGGER.error("推送作业中心任务状态变更失败,{}", jobDto, e);
        }

    }
}
