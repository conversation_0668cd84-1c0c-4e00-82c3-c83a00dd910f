package com.ideal.script.service;

import com.ideal.script.dto.ScriptFileImportExportApiDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.bean.ReleaseMediaBean;
import com.ideal.system.common.component.model.CurrentUser;
import com.ideal.script.model.dto.ToProductQueryDto;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.List;
import java.util.Map;

/**
 * 服务投产Service接口
 *
 * <AUTHOR>
 */
public interface IReleaseMediaService {

    /**
     * 投产介质导出
     *
     * @param ids      版本id
     * @param response 响应
     * @throws ScriptException 抛出自定义异常
     */
    void exportReleaseMedia(Long[] ids, HttpServletResponse response) throws ScriptException;


    /**
     * 导入投产介质
     *
     * @param file              投产介质文件
     * @param user              用户
     * @param toProductQueryDto 包含所属人、投产描述
     * @throws ScriptException 抛出自定义通知异常
     */
    void importReleaseMedia(MultipartFile file, CurrentUser user, ToProductQueryDto toProductQueryDto) throws ScriptException;

    /**
     * 获取脚本服务化导出zip文件相关信息
     * @param ids
     * @return
     * @throws ScriptException
     */
    Map<String,Object> scriptZipFile(Long[] ids) throws ScriptException;

    /**
     * 删除临时落地文件
     * @param zipFile
     * @param sourceFolder
     * @throws ScriptException
     */
    void cleanupFiles(File zipFile, File sourceFolder) throws ScriptException;

    /**
     * 导出脚本
     * @param srcScriptUuids 脚本的uuid
     * @throws ScriptException
     * @return ScriptFileImportExportApiDto
     */
    ScriptFileImportExportApiDto exportScriptProductionApi(List<String> srcScriptUuids);

    /**
     * 导入脚本（服务投产）
     * @param scriptFileImportExportApiDto 脚本的dto参数
     * @throws ScriptException
     * @return Map<String,String>
     */
    Map<String,String> importScriptProduction(ScriptFileImportExportApiDto scriptFileImportExportApiDto);

    /**
     * 获取临时路径
     * @return 临时路径
     */
    String getProductFilePath();

    /**
     * 向服务投产业务表插入数据
     * @param toProductQueryDto 投产数据
     */
    void saveProductInfo(ToProductQueryDto toProductQueryDto);

    /**
     * 导入脚本业务表数据
     * @param outputDirectory 文件
     * @param toProductQueryDto 参数dto
     */
    void handlerImportMedia(File outputDirectory, ToProductQueryDto toProductQueryDto) throws ScriptException;

    /**
     * 处理.json格式问及那
     * @param jsonFile 文件书
     * @param toProductQueryDto 相关参数
     */
    ReleaseMediaBean handleJsonFile(File jsonFile, ToProductQueryDto toProductQueryDto) throws ScriptException;

    /**
     * 整理附件信息
     * @param attachmentsFolder 文件
     * @param releaseMediaBean 脚本基础数据
     * @param saveAttachmentFlag 是否保存附件标识，true保存，false不保存
     */
    void handleAttachmentsFolder(File attachmentsFolder, ReleaseMediaBean releaseMediaBean,boolean saveAttachmentFlag) throws
            ScriptException;
}
