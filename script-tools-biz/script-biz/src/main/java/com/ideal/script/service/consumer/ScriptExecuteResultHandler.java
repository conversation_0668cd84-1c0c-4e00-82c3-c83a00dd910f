package com.ideal.script.service.consumer;

import com.ideal.message.center.ISubscriber;
import com.ideal.script.service.resulthandler.IScriptResultHandlerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 监听mq中script-execute-result主题中的消息
 *
 * <AUTHOR>
 */
@Component
public class ScriptExecuteResultHandler implements ISubscriber {
    private final Logger logger = LoggerFactory.getLogger(ScriptExecuteResultHandler.class);
    private final IScriptResultHandlerService scriptResultHandlerService;


    public ScriptExecuteResultHandler(IScriptResultHandlerService scriptResultHandlerService) {
        this.scriptResultHandlerService = scriptResultHandlerService;
    }

    /**
     * @param obj   原对象
     * @param clazz Class
     * @param <E>   泛型
     * @return List<E>
     */
    public <E> List<E> castList(Object obj, Class<E> clazz) {
        List<E> result = new ArrayList<>();
        if (obj instanceof List<?>) {
            ((List<?>) obj).forEach(o -> result.add(clazz.cast(o)));
            return result;
        } else {
            return Collections.emptyList();
        }
    }

    /**
     * 监听mq中script-execute-result主题中的消息
     *
     * @param message 消息
     */
    @Override
    public void notice(Object message) {
        try {
            Optional<List<String>> messageBody = Optional.of(castList(message, String.class));
            scriptResultHandlerService.handleScriptExecuteResult(messageBody.get());
        } catch (Exception e) {
            logger.error("Failed to handle the result from script-execute-result topic.The received message is:{}",message, e);
        }
    }
}
