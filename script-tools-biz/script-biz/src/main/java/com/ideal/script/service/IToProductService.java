package com.ideal.script.service;

import com.github.pagehelper.PageInfo;
import com.ideal.script.dto.ItsmChildrenDto;
import com.ideal.script.dto.ItsmPublishScriptAuditResultDto;
import com.ideal.script.dto.ItsmPublishScriptDto;
import com.ideal.script.dto.ScriptInfoDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.ToProductDto;
import com.ideal.script.model.dto.ToProductQueryDto;
import com.ideal.script.model.entity.ItsmProductAttachment;

import java.io.IOException;
import java.util.List;

/**
 * Service接口
 *
 * <AUTHOR>
 * @date 2024-04-02
 */
public interface IToProductService {
    /**
     * 查询
     *
     * @param id 主键
     * @return 
     */
    ToProductDto selectToProductById(Long id);

    /**
     * 查询列表
     *
     * @param toProductQueryDto 
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 集合
     */
    PageInfo<ToProductDto> selectToProductList(ToProductQueryDto toProductQueryDto, Integer pageNum, Integer pageSize);

    /**
     * 新增
     *
     * @param toProductDto 
     * @return 结果
     */
    int insertToProduct(ToProductDto toProductDto);

    /**
     * 修改
     *
     * @param toProductDto 
     * @return 结果
     */
    int updateToProduct(ToProductDto toProductDto);

    /**
     * 批量删除
     *
     * @param ids 需要删除的主键集合
     * @return 结果
     */
    int deleteToProductByIds(Long[] ids);

    /**
     * itsm投产
     * @param itsmPublishScriptDto 参数
     */
    void publishItsm(ItsmPublishScriptDto itsmPublishScriptDto) throws Exception;

    /**
     * 开发测试环境审批
     * @param itsmPublishScriptAuditResultDto 审批参数
     */
    void testEnvironmentAuditResult(ItsmPublishScriptAuditResultDto itsmPublishScriptAuditResultDto);

    /**
     * 生产环境审批
     * @param itsmPublishScriptAuditResultDto 审批参数
     */
    void productEnvironmentAuditResult(ItsmPublishScriptAuditResultDto itsmPublishScriptAuditResultDto) throws IOException, ScriptException;

    /**
     * 服务投产获取子数据列表
     * @param itsmPublishScriptDto 参数
     */
    List<ItsmChildrenDto> getChildrenDataList(ItsmPublishScriptDto itsmPublishScriptDto);

    /**
     * 服务投产获取子数据详情
     * @param itsmPublishScriptDto 参数
     */
    ScriptInfoDto itsmProductDetails(ItsmPublishScriptDto itsmPublishScriptDto) throws ScriptException;

    /**
     * itsm晋级方法
     * @param itsmPublishScriptDto 参数
     */
    void promotionProducts(ItsmPublishScriptDto itsmPublishScriptDto) throws ScriptException;

    /**
     * 生产环境下载附件
     * @param attachmentId 附件id
     */
    ItsmProductAttachment selectAttachmentById(Long attachmentId);

}
