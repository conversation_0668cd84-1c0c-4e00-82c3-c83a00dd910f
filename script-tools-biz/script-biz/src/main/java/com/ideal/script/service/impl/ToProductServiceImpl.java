package com.ideal.script.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.sc.util.CurrentUserUtil;
import com.ideal.sc.util.HttpClientUtil;
import com.ideal.sc.util.ZipUtil;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.config.CiticProperties;
import com.ideal.script.dto.AttachmentResponseDto;
import com.ideal.script.dto.AttachmentUploadDto;
import com.ideal.script.dto.ItsmChildrenDto;
import com.ideal.script.dto.ItsmPublishScriptAuditResultDto;
import com.ideal.script.dto.ItsmPublishScriptDto;
import com.ideal.script.dto.ParameterValidationDto;
import com.ideal.script.dto.ScriptContentDto;
import com.ideal.script.dto.ScriptInfoDto;
import com.ideal.script.dto.ScriptInfoQueryDto;
import com.ideal.script.dto.ScriptVersionDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.ItsmProductAttachmentMapper;
import com.ideal.script.mapper.ItsmProductInfoMapper;
import com.ideal.script.mapper.ItsmPublishInfoMapper;
import com.ideal.script.mapper.ToProductMapper;
import com.ideal.script.model.bean.MyScriptBean;
import com.ideal.script.model.bean.ReleaseMediaBean;
import com.ideal.script.model.dto.ItsmPublishOrderNumberDto;
import com.ideal.script.model.dto.ScriptValidationResultDto;
import com.ideal.script.model.dto.ToProductDto;
import com.ideal.script.model.dto.ToProductQueryDto;
import com.ideal.script.model.entity.Attachment;
import com.ideal.script.model.entity.ItsmProductAttachment;
import com.ideal.script.model.entity.ItsmProductInfo;
import com.ideal.script.model.entity.ItsmPublishScript;
import com.ideal.script.model.entity.ToProductEntity;
import com.ideal.script.service.IMyScriptService;
import com.ideal.script.service.IReleaseMediaService;
import com.ideal.script.service.IToProductService;
import com.ideal.script.service.impl.builders.MyScriptServiceScripts;
import com.ideal.system.api.IPublicInfo;
import com.ideal.system.common.component.model.CurrentUser;
import com.ideal.system.dto.PublicInfoPropertiesApiDto;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class ToProductServiceImpl implements IToProductService {
    private final  ToProductMapper toProductMapper;
    private final ItsmPublishInfoMapper itsmPublishInfoMapper;
    private final IReleaseMediaService releaseMediaService;
    private final MyScriptServiceScripts scripts;
    private final ItsmProductInfoMapper itsmProductInfoMapper;
    private final ItsmProductAttachmentMapper itsmProductAttachmentMapper;
    private final IMyScriptService myScriptService;
    private final IPublicInfo publicInfo;
    private final ObjectMapper objectMapper;
    private final CiticProperties citicProperties;

    private static final Logger logger = LoggerFactory.getLogger(ToProductServiceImpl.class);

    public ToProductServiceImpl(@Nullable CiticProperties citicProperties, ObjectMapper objectMapper, IPublicInfo publicInfo, ItsmProductAttachmentMapper itsmProductAttachmentMapper, IMyScriptService myScriptService, ItsmProductInfoMapper itsmProductInfoMapper, MyScriptServiceScripts scripts, @Lazy IReleaseMediaService releaseMediaService, ItsmPublishInfoMapper itsmPublishInfoMapper, ToProductMapper toProductMapper) {
        this.citicProperties = citicProperties;
        this.objectMapper = objectMapper;
        this.publicInfo = publicInfo;
        this.itsmProductAttachmentMapper = itsmProductAttachmentMapper;
        this.myScriptService = myScriptService;
        this.itsmProductInfoMapper = itsmProductInfoMapper;
        this.scripts = scripts;
        this.releaseMediaService = releaseMediaService;
        this.itsmPublishInfoMapper = itsmPublishInfoMapper;
        this.toProductMapper = toProductMapper;
    }

    /**
     * 查询
     *
     * @param id 主键
     * @return 
     */
    @Override
    public ToProductDto selectToProductById(Long id) {
        ToProductEntity toProduct = toProductMapper.selectToProductById(id);
        return BeanUtils.copy(toProduct, ToProductDto.class);
    }

    /**
     * 查询列表
     *
     * @param toProductQueryDto 
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 
     */
    @Override
    public PageInfo<ToProductDto> selectToProductList(ToProductQueryDto toProductQueryDto, Integer pageNum, Integer pageSize) {
        ToProductEntity query = BeanUtils.copy(toProductQueryDto, ToProductEntity.class);
        PageMethod.startPage(pageNum, pageSize);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            if (StringUtils.isNotEmpty(query.getDateTimeStart())) {
                query.setStartTime(new Timestamp(simpleDateFormat.parse(query.getDateTimeStart()).getTime()));
            }
            if (StringUtils.isNotEmpty(query.getDateTimeEnd())) {
                query.setEndTime(new Timestamp(simpleDateFormat.parse(query.getDateTimeEnd()).getTime()));
            }
        } catch (Exception e) {
            logger.error("select product list, type String to Timestamp error", e);
        }
        List<ToProductEntity> toProductList = toProductMapper.selectToProductList(query);

        return PageDataUtil.toDtoPage(toProductList, ToProductDto.class);
    }

    /**
     * 新增
     *
     * @param toProductDto 
     * @return 结果
     */
    @Override
    public int insertToProduct(ToProductDto toProductDto) {
        ToProductEntity toProduct = BeanUtils.copy(toProductDto, ToProductEntity.class);
        int i= toProductMapper.insertToProduct(toProduct);
        toProductDto.setId(toProduct.getId());
        return i;
    }

    /**
     * 修改
     *
     * @param toProductDto 
     * @return 结果
     */
    @Override
    public int updateToProduct(ToProductDto toProductDto) {
        ToProductEntity toProduct = BeanUtils.copy(toProductDto, ToProductEntity.class);
        return toProductMapper.updateToProduct(toProduct);
    }

    /**
     * 批量删除
     *
     * @param ids 需要删除的主键
     * @return 结果
     */
    @Override
    public int deleteToProductByIds(Long[] ids) {
        return toProductMapper.deleteToProductByIds(ids);
    }

    /**
     * itsm投产提交
     * @param itsmPublishScriptDto 参数
     * @throws ScriptException 脚本服务化异常
     */
    @Override
    public void publishItsm(ItsmPublishScriptDto itsmPublishScriptDto) throws ScriptException {
        CurrentUser currentUser = CurrentUserUtil.getCurrentUser();
        //根据uniqueUuid查询isrc_script_uuid
        List<MyScriptBean> myScriptBeanList = scripts.getMyScriptMapper().getScriptInfoByUniqueUuid(itsmPublishScriptDto.getUniqueUuid());
        //关键命令、脚本版本id、脚本版本uuid集合
        StringBuilder allDangerCmd = new StringBuilder();
        List<Long> scriptVersionIdList = new ArrayList<>();
        List<String> srcScriptUuidList = new ArrayList<>();
        //遍历脚本，校验关键命令、校验脚本是否存在审批中的任务
        for(MyScriptBean myScriptBean : myScriptBeanList){
            //获取脚本详情信息
            ScriptInfoQueryDto scriptInfoQueryDto = new ScriptInfoQueryDto();
            scriptInfoQueryDto.setDubboFlag(false);
            scriptInfoQueryDto.setScriptInfoId(myScriptBean.getScriptInfoId());
            ScriptInfoDto scriptDetail = myScriptService.getScriptDetail(scriptInfoQueryDto);
            //为对应参数赋值
            itsmPublishScriptDto.setSrcScriptUuid(scriptDetail.getScriptVersionDto().getSrcScriptUuid());
            scriptVersionIdList.add(scriptDetail.getScriptVersionDto().getId());
            srcScriptUuidList.add(scriptDetail.getScriptVersionDto().getSrcScriptUuid());
            //根据脚本的isrc_script_uuid查询当前是否有正在审批中的脚本，如果审批中则不能提交审批
            List<ItsmPublishScript> itsmPublishList = itsmPublishInfoMapper.getAuditingDataBySrcScriptUuid(itsmPublishScriptDto.getSrcScriptUuid());
            if(Objects.nonNull(itsmPublishList) && !itsmPublishList.isEmpty()){
                throw new ScriptException("script.itsm.publish.exist");
            }
            //如果校验通过，先检查关键命令，将对应的提示获取到
            List<ScriptValidationResultDto> scriptValidationResultDtos = myScriptService.validateScriptWithKeywords(scriptDetail);
            String dangerCmd = "";
            if(Objects.nonNull(scriptValidationResultDtos) && !scriptValidationResultDtos.isEmpty()){
                dangerCmd += "脚本" + scriptDetail.getScriptName() + "存在关键命令，";
                for(ScriptValidationResultDto scriptValidationResultDto : scriptValidationResultDtos){
                    dangerCmd += "第" + scriptValidationResultDto.getLine() + "行存在关键命令：" + JSONObject.toJSONString(scriptValidationResultDto.getTargetContent()) + ";" ;
                }
            }
            allDangerCmd.append(dangerCmd);
        }
        //调用投产介质接口，获取投产介质zip包
        File zipFile;
        String fileName;
        try {
            Map<String, Object> zipFileMap = getZipFile(scriptVersionIdList);
            zipFile = (File)zipFileMap.get(Enums.ProductionFile.FILENAME.getValue());
            fileName = (String)zipFileMap.get(Enums.ProductionFile.ZIPNAME.getValue());
        } catch (Exception e) {
            throw new ScriptException("script.analysis.zip.file.error",e);
        }
        //将投产信息保存到本地投产表与投产关系表中
        ToProductQueryDto toProductQueryDto = insertToProduct(currentUser, srcScriptUuidList, fileName, itsmPublishScriptDto);
        //调用itsm上传附件接口，将zip包上传到制品库
        Path source = zipFile.toPath();
        Path target = source.resolveSibling(fileName);

        try {
            Files.move(source, target);
        } catch (IOException e) {
            throw new ScriptException("script.upload.itsm.product.error",e);
        }
        zipFile = target.toFile();
        String itsmFilePath = uploadZipFileToProductLibrary(zipFile,fileName);
        if(StringUtils.isBlank(itsmFilePath)){
            //如果获取的路径为空，返回前台提示信息
            throw new ScriptException("script.itsm.properties.error");
        }
        //调用itsm获取单号接口，获取工单编号
        String orderNumber = getOrderNumber(currentUser, allDangerCmd.toString(),toProductQueryDto,itsmFilePath);
        //更新itsm投产单号与审批状态
        ToProductEntity toProductEntity = new ToProductEntity();
        toProductEntity.setOrderNumber(orderNumber);
        toProductEntity.setId(toProductQueryDto.getId());
        toProductMapper.updateToProduct(toProductEntity);

        if(StringUtils.isBlank(orderNumber)){
            throw new ScriptException("script.itsm.properties.error");
        }

        //将工单编号、脚本isrc_script_uuid保存到本itsm工单与脚本信息表，审批状态是“审批中”
        itsmPublishScriptDto.setCreatedUserId(currentUser.getId());
        itsmPublishScriptDto.setOrderNumber(orderNumber);
        itsmPublishScriptDto.setItsmProductUrl(itsmFilePath);
        itsmPublishScriptDto.setAuditState(2);
        itsmPublishScriptDto.setProductId(toProductQueryDto.getId());
        itsmPublishScriptDto.setFileName(fileName);
        try {
            // 创建新的File对象（新路径）
            itsmPublishScriptDto.setZipFileContent(Files.readAllBytes(Paths.get(zipFile.getPath())));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        try {
            analysisZipFile(itsmPublishScriptDto,zipFile,currentUser,false);
        }catch (Exception e){
            throw new ScriptException("download Itsm Product error",e);
        }

    }

    /**
     * 上传zip到制品库
     * @param zipFile zip压缩包
     */
    public String uploadZipFileToProductLibrary(File zipFile,String fileName) throws ScriptException{
        String zipFileInItsmProductPath = "";
        HttpURLConnection connection = null;
        try {
            String apiKey = citicProperties.getItsmApiKey();
            String url = citicProperties.getItsmUploadProductFileUrl();
            String repoKey = citicProperties.getItsmRepoKey();
            if(StringUtils.isBlank(apiKey) || StringUtils.isBlank(url) || StringUtils.isBlank(repoKey)){
                return "";
            }
//            String lastUrl = url + "/" + repoKey+ "/" + getNowData()+ "/" + zipFile.getName();
//            String lastUrl = url + "/" + zipFile.getName();
//            String sourceFilePath = zipFile.getPath();
            // 对文件名进行URL编码
            String encodedFileName = URLEncoder.encode(zipFile.getName(), "UTF-8");
            // 构造URL时使用编码后的文件名
//            String lastUrl = url + "/" + encodedFileName;
            String lastUrl = url + "/" + repoKey+ "/" + getNowData()+ "/" + encodedFileName;
            String sourceFilePath = zipFile.getPath();
            Path file = Paths.get(sourceFilePath);
            URL fileUrl = new URL(lastUrl);
            connection = (HttpURLConnection) fileUrl.openConnection();
            connection.setDoOutput(true);
            connection.setRequestMethod("PUT");
            connection.setRequestProperty("Content-Type", "application/Octet-stream");
            connection.setRequestProperty("X-JFrog-Art-Api",apiKey);
            byte[] fileBytes = Files.readAllBytes(file);
            try (OutputStream os = connection.getOutputStream()) {
                os.write(fileBytes);
            }
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK || responseCode == HttpURLConnection.HTTP_CREATED) {
                zipFileInItsmProductPath = connection.getResponseMessage();
            }else{
                logger.error("upload itsm zip file error{}", connection.getResponseMessage());
            }
        }catch (Exception e){
            logger.error("upload file to itsm product error", e);
            throw new ScriptException("upload.script.zip.file.product.library.error",e);
        }finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
        return zipFileInItsmProductPath;
    }

    private String getNowData(){
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        return LocalDate.now().format(formatter);
    }

    /**
     * 调用itsm接口，获取工单编号
     * @param currentUser 登录用户
     * @param allDangerCmd 关键命令
     * @return 返回工单号
     */
    public String getOrderNumber(CurrentUser currentUser,String allDangerCmd,ToProductQueryDto toProductQueryDto,String itsmFilePath){
        String createUser = currentUser.getLoginName();
        String modelCode = "a";
        String routeCode = "a";
        String activityCode = "a";
        String url = citicProperties.getItsmGetOrderNumberUrl();
        if(StringUtils.isBlank(url)){
            return "";
        }

        JSONObject form = new JSONObject();
        form.put("dangerCmdRemark", allDangerCmd);
        form.put("productId",toProductQueryDto.getId());
        form.put("productionDescription",toProductQueryDto.getDescription());
        form.put("itsmFilePath",itsmFilePath);


        //请求参数
        JSONObject body = new JSONObject();
        body.put("createUser", createUser);
        body.put("modelCode", modelCode);
        body.put("routeCode", routeCode);
        body.put("activityCode", activityCode);
        body.put("form", form);

        // 请求对象转字符串
        String paramsStr = body.toJSONString();
        //整理必要的参数
        Map<String,String> headers = new HashMap<>();
        headers.put("Content-Type","application/json");
        headers.put("Content-Length",
                String.valueOf(paramsStr.getBytes().length));

        ItsmPublishOrderNumberDto itsmPublishOrderNumberDto = HttpClientUtil.postByJson(url, headers, paramsStr, ItsmPublishOrderNumberDto.class);

        if(ObjectUtils.notEqual(itsmPublishOrderNumberDto,null)){
            return itsmPublishOrderNumberDto.getFlowNo();
        }
        return "";
    }

    /**
     * itsm回调测试环境审批
     * @param itsmPublishScriptAuditResultDto 参数
     */
    @Override
    public void testEnvironmentAuditResult(ItsmPublishScriptAuditResultDto itsmPublishScriptAuditResultDto){
        //开发测试环境审核更新审核状态
        ToProductEntity toProductEntity = new ToProductEntity();
        toProductEntity.setOrderNumber(itsmPublishScriptAuditResultDto.getOrderNumber());
        toProductEntity.setProductState(itsmPublishScriptAuditResultDto.getAuditState());
        toProductMapper.updateToProductByOrderNumber(toProductEntity);
    }

    /**
     * itsm回调生产环境审批
     * @param itsmPublishScriptAuditResultDto 参数
     */
    @Override
    public void productEnvironmentAuditResult(ItsmPublishScriptAuditResultDto itsmPublishScriptAuditResultDto) throws IOException, ScriptException {
        CurrentUser currentUser = CurrentUserUtil.getCurrentUser();
        //如果审核通过，将脚本信息写入脚本业务表
        if(itsmPublishScriptAuditResultDto.getAuditState().equals(1)){
            //直接使用handleScriptFolder
            //根据id查询zip文件
            ItsmPublishScript itsmPublishScript = itsmPublishInfoMapper.getDetailByOrderNumber(itsmPublishScriptAuditResultDto.getOrderNumber());
            if(ObjectUtils.notEqual(itsmPublishScript,null)){
                byte[] zipFileContent = itsmPublishScript.getZipFileContent();
                // 创建临时文件（前缀 + 随机后缀，保留原始扩展名）
                Path tempPath = Files.createTempFile("temp_", "_" + itsmPublishScript.getIid());
                File tempFile = tempPath.toFile();
                // 写入数据
                Files.write(tempPath, zipFileContent);
                // 设置JVM退出时自动删除
                tempFile.deleteOnExit();
                ToProductQueryDto toProductQueryDto = new ToProductQueryDto();
                toProductQueryDto.setScriptUserId(currentUser.getId());
                releaseMediaService.handlerImportMedia(tempFile,toProductQueryDto);
            }
        }
        //生产审核更新审核状态
        ToProductEntity toProductEntity = new ToProductEntity();
        toProductEntity.setOrderNumber(itsmPublishScriptAuditResultDto.getOrderNumber());
        toProductEntity.setProductState(itsmPublishScriptAuditResultDto.getAuditState());
        toProductMapper.updateToProductByOrderNumber(toProductEntity);
        //删除临时表数据
        ItsmPublishScript itsmPublishScript = itsmPublishInfoMapper.getDetailByOrderNumber(itsmPublishScriptAuditResultDto.getOrderNumber());
        if(Objects.nonNull(itsmPublishScript)){
            itsmProductInfoMapper.deleteByPublishInfoId(itsmPublishScript.getIid());
            itsmProductAttachmentMapper.deleteByPublishInfoId(itsmPublishScript.getIid());
            itsmPublishInfoMapper.deleteByIid(itsmPublishScript.getIid());
        }
    }

    /**
     * 将文件保存到投产表中
     * @param currentUser 当前登录用户
     * @param srcScriptUuids 脚本版本id集合
     */
    private ToProductQueryDto insertToProduct(CurrentUser currentUser,List<String> srcScriptUuids,String fileName,ItsmPublishScriptDto itsmPublishScriptDto)  {
        ToProductQueryDto toProductQueryDto = new ToProductQueryDto();
        toProductQueryDto.setSrcScriptUuidList(srcScriptUuids);
        toProductQueryDto.setFileName(fileName);
        toProductQueryDto.setUserName(currentUser.getFullName());
        toProductQueryDto.setUserId(currentUser.getId());
        toProductQueryDto.setScriptUserName(currentUser.getFullName());
        toProductQueryDto.setScriptUserId(currentUser.getId());
        toProductQueryDto.setDescription(itsmPublishScriptDto.getProductionDescription());
        toProductQueryDto.setId(itsmPublishScriptDto.getProductId());
        toProductQueryDto.setDateTime(new Timestamp(System.currentTimeMillis()));
        toProductQueryDto.setOrderNumber(itsmPublishScriptDto.getOrderNumber());
        toProductQueryDto.setProductState(2);
        releaseMediaService.saveProductInfo(toProductQueryDto);
        return toProductQueryDto;
    }

    private Map<String, Object> getZipFile(List<Long> scriptVersionId) throws ScriptException{
        return releaseMediaService.scriptZipFile(scriptVersionId.toArray(new Long[0]));
    }

    /**
     * 服务投产获取子数据信息
     * @param itsmPublishScriptDto 参数
     * @return 子数据信息
     */
    @Override
    public List<ItsmChildrenDto> getChildrenDataList(ItsmPublishScriptDto itsmPublishScriptDto){
        return BeanUtils.copy(scripts.getMyScriptMapper().getScriptInfoByProductId(itsmPublishScriptDto.getProductId()), ItsmChildrenDto.class);
    }

    /**
     * 服务投产获取子数据详情
     * @param itsmPublishScriptDto 查询参数
     */
    @Override
    public ScriptInfoDto itsmProductDetails(ItsmPublishScriptDto itsmPublishScriptDto) throws ScriptException {
        //如果是开发测试环境，直接查询本地脚本业务表详情即可，如果是生产环境，需要查询itsm相关业务表
        PublicInfoPropertiesApiDto publicInfo1 = publicInfo.getPublicInfo();
        if(publicInfo1.getEnvName().contains(scripts.getScriptBusinessConfig().getEnvironmentFlag())){
//        if(true){
            ItsmProductInfo itsmProductInfo = itsmProductInfoMapper.getItsmProductInfoByProductId(itsmPublishScriptDto.getChildrenId());
            if(Objects.isNull(itsmProductInfo)){
                return new ScriptInfoDto();
            }
            return com.alibaba.fastjson.JSONObject.parseObject(itsmProductInfo.getScriptJsonStr(),ScriptInfoDto.class);
        }else{
            ItsmProductInfo itsmProductInfo = itsmProductInfoMapper.getItsmProductInfoById(itsmPublishScriptDto.getChildrenId());
            if(Objects.nonNull(itsmProductInfo)){
                ScriptInfoQueryDto scriptInfoQueryDto = new ScriptInfoQueryDto();
                scriptInfoQueryDto.setDubboFlag(false);
                scriptInfoQueryDto.setSrcScriptUuid(itsmProductInfo.getSrcScriptUuid());
                return myScriptService.getScriptDetail(scriptInfoQueryDto);
            }
        }
        return new ScriptInfoDto();
    }

    /**
     * itsm调用晋级接口方法
     * @param itsmPublishScriptDto 参数
     */
    @Override
    public void promotionProducts(ItsmPublishScriptDto itsmPublishScriptDto) throws ScriptException{
        CurrentUser currentUser = CurrentUserUtil.getCurrentUser();
        String allUrl = itsmPublishScriptDto.getItsmProductUrl();
        String fileName = allUrl.substring(allUrl.lastIndexOf('/') + 1);
        String repoKey = citicProperties.getItsmRepoKey();
        //获取制品库zip文件地址，并下载文件
//        String fileUrl = citicProperties.getItsmDownloadProductFileUrl() + "/" +fileName;
        String fileUrl = citicProperties.getItsmDownloadProductFileUrl() + "/" + repoKey + "/" + getNowData() + "/" + fileName;
        logger.info("itsm upload product url: {}", fileUrl);
        HashMap<String,String> headers = new HashMap<>();
        HashMap<String,String> params = new HashMap<>();
        headers.put("X-JFrog-Art-Api","apiKey");
        byte[] bytes = HttpClientUtil.downloadFile(fileUrl, headers, params);
        String substring = fileUrl.substring(fileUrl.lastIndexOf('/') + 1);
        String saveDir = releaseMediaService.getProductFilePath() + "/" + substring;
        Path path = Paths.get(saveDir);
        // 确保父目录存在
        try {
            Files.createDirectories(path.getParent());
            // 写入字节数组到文件
            Files.write(path, bytes);
            File zipFile = path.toFile();
            itsmPublishScriptDto.setZipFileContent(bytes);
            itsmPublishScriptDto.setFileName(fileName);
            analysisZipFile(itsmPublishScriptDto,zipFile,currentUser,true);
        }catch (Exception e){
            throw new ScriptException("download Itsm Product error",e);
        }
    }

    /**
     * 解析zip文件
     * @param itsmPublishScriptDto 相关参数
     * @param zipFile zip压缩文件
     * @param currentUser 当前登录用户
     * @param saveScriptProductFlag 是否保存到投产表标识true保存到投产表，false不保存
     */
    private void analysisZipFile(ItsmPublishScriptDto itsmPublishScriptDto,File zipFile,CurrentUser currentUser,boolean saveScriptProductFlag) throws Exception{
        //解析zip文件，保存到本地表
        if (zipFile.getName().startsWith(Enums.PrefixEnum.PRODUCTION_PREFIX.getPrefix()) && zipFile.getName().endsWith(Enums.FileExtensionEnum.ZIP.getValue())) {
            String zipOutputDirectory = zipFile.getAbsolutePath().replace(".zip", "");
            // 将zip压缩文件解压到 同名的文件夹下（d:/aa/d.zip 解压到d:/aa/d/a.json 和附件文件夹）
            Path path = Paths.get(zipOutputDirectory);
            // 判断路径是否存在
            if (!Files.exists(path)) {
                // 创建目录（包括所有不存在的父目录）
                Files.createDirectories(path);
            }

            ZipUtil.upZipFile(zipFile.getAbsolutePath(), zipOutputDirectory);
            File outputDirectory = new File(zipOutputDirectory);


            if (!outputDirectory.isDirectory()) {
                logger.error("outputDirectory is not a directory");
                return;
            }
            File[] tempFiles = outputDirectory.listFiles();
            if (tempFiles == null || tempFiles.length == 0) {
                logger.error("tempFiles is empty");
                return;
            }
            // 取得temp_exportReleaseMedia_开头的文件夹
            File tempFolder = tempFiles[0];
            // 取得temp_exportReleaseMedia_ 文件夹下的脚本文件夹目录集
            File[] scriptFiles = tempFolder.listFiles();
            if (null == scriptFiles) {
                logger.error("scriptFiles is null");
                return;
            }

            //记录srcUuid
            List<String> srcUuidList = new ArrayList<>();

            //保存itsm投产主信息
            itsmPublishScriptDto.setCreatedUserId(currentUser.getId());
            ItsmPublishScriptDto itsmPublishScriptDto1 = insertItsmPublishScript(itsmPublishScriptDto, saveScriptProductFlag);
            //定义附件集合
            List<ItsmProductAttachment> itsmProductAttachmentList = new ArrayList<>();

            //解析文件，获取附件、脚本基本信息等
            analysisFiles(scriptFiles,itsmPublishScriptDto,itsmProductAttachmentList,itsmPublishScriptDto1,srcUuidList);

            //数据插入投产业务表
            if(saveScriptProductFlag){
                insertToProduct(currentUser,srcUuidList,zipFile.getName(),itsmPublishScriptDto);
            }
        }
    }

    private ItsmPublishScriptDto insertItsmPublishScript(ItsmPublishScriptDto itsmPublishScriptDto,boolean saveScriptProductFlag){
        ItsmPublishScriptDto itsmPublishScriptDto1 = new ItsmPublishScriptDto();
        itsmPublishScriptDto1.setProductId(itsmPublishScriptDto.getProductId());
        itsmPublishScriptDto1.setItsmProductUrl(itsmPublishScriptDto.getItsmProductUrl());
        //如果是晋级接口调用，直接赋值成审核中
        if(saveScriptProductFlag){
            //生产环境导入后默认是审批中
            itsmPublishScriptDto1.setAuditState(2);
        }else{
            itsmPublishScriptDto1.setAuditState(itsmPublishScriptDto.getAuditState());
        }
        itsmPublishScriptDto1.setOrderNumber(itsmPublishScriptDto.getOrderNumber());
        itsmPublishScriptDto1.setZipFileContent(itsmPublishScriptDto.getZipFileContent());
        itsmPublishScriptDto1.setFileName(itsmPublishScriptDto.getFileName());
        itsmPublishScriptDto1.setCreatedUserId(itsmPublishScriptDto.getCreatedUserId());
        ItsmPublishScript insertEntity = BeanUtils.copy(itsmPublishScriptDto1, ItsmPublishScript.class);
        itsmPublishInfoMapper.insertItsmPublishInfo(insertEntity);
        itsmPublishScriptDto1.setIid(insertEntity.getIid());
        return itsmPublishScriptDto1;
    }


    //解析文件，获取附件、脚本基本信息等
    public void analysisFiles(File[] scriptFiles,ItsmPublishScriptDto itsmPublishScriptDto,List<ItsmProductAttachment> itsmProductAttachmentList,ItsmPublishScriptDto itsmPublishScriptDto1,List<String> srcUuidList) throws ScriptException {
        for (File scriptFile : scriptFiles) {
            if(!Constants.SCRIPT_FILE_DIR_NAME.equals(scriptFile.getName())) {
                if (!scriptFile.isDirectory()) {
                    return;
                }
                //===============开始解析json文件===============
                File[] jsonFiles = scriptFile.listFiles((dir, name) -> name.toLowerCase().endsWith(".json"));
                if (null == jsonFiles) {
                    return;
                }
                ReleaseMediaBean releaseMediaBean;
                // 从 JSON 文件中读取内容，并将其转换为 ReleaseMediaBean 对象
                try (InputStream inputStream = Files.newInputStream(jsonFiles[0].toPath());
                     InputStreamReader reader = new InputStreamReader(inputStream, StandardCharsets.UTF_8)) {
                    releaseMediaBean = objectMapper.readValue(reader, ReleaseMediaBean.class);
                    srcUuidList.add(releaseMediaBean.getInfoVersion().getSrcScriptUuid());
                } catch (Exception e) {
                    logger.error("handleJsonFile error:", e);
                    throw new ScriptException("handle.json.file.error");
                }
                //保存每个脚本信息
                ItsmProductInfo itsmProductInfo = new ItsmProductInfo();
                itsmProductInfo.setPublishInfoId(itsmPublishScriptDto1.getIid());
                itsmProductInfo.setSrcScriptUuid(releaseMediaBean.getInfoVersion().getSrcScriptUuid());
//                itsmProductInfo.setScriptJsonStr(JSON.toJSONString(releaseMediaBean));
                itsmProductInfo.setCreatedUserId(itsmPublishScriptDto.getCreatedUserId());
                //===============解析json文件结束===============

                //===============开始解析附件文件===============
                // 获取子目录中以 "_attachment" 结尾的文件夹
                File[] attachmentFolders = new File[0];
                try {
                    attachmentFolders = scriptFile.listFiles(file -> file.isDirectory() && file.getName().endsWith("_attachment"));
                } catch (Exception e) {
                    logger.error("handleScriptFolder is error:", e);
                }
                if (null != attachmentFolders && attachmentFolders.length != 0) {

                    // 处理附件
                    releaseMediaService.handleAttachmentsFolder(attachmentFolders[0], releaseMediaBean,false);

                    //整理成ScriptInfoDto类型的json存入表中
                    ScriptInfoDto scriptInfoDto = tidyDataToScriptInfoDto(releaseMediaBean);
                    itsmProductInfo.setScriptJsonStr(JSON.toJSONString(scriptInfoDto));

                    //保存每个脚本信息
                    itsmProductInfoMapper.insertProductInfo(itsmProductInfo);

                    //整理附件信息
                    List<Attachment> attachmentList = releaseMediaBean.getAttachmentList();
                    if (null != attachmentList) {
                        for (Attachment attachment : attachmentList) {
                            ItsmProductAttachment itsmProductAttachment = new ItsmProductAttachment();
                            itsmProductAttachment.setItsmProductInfoId(itsmProductInfo.getIid());
                            itsmProductAttachment.setPublishInfoId(itsmPublishScriptDto1.getIid());
                            itsmProductAttachment.setSrcScriptUuid(releaseMediaBean.getInfoVersion().getSrcScriptUuid());
                            itsmProductAttachment.setAttachmentName(attachment.getName());
                            itsmProductAttachment.setAttachmentSize(attachment.getSize());
                            itsmProductAttachment.setAttachmentContent(attachment.getContents());
                            itsmProductAttachmentMapper.insert(itsmProductAttachment);

                            //因为导出的zip文件只有附件文件，没有对应的数据id，所以插入附件后需要更新投产json里的附件id
                            if(Objects.nonNull(scriptInfoDto.getScriptVersionDto().getAttachmentUploadDtoList())){
                                List<AttachmentUploadDto> attachmentlist = scriptInfoDto.getScriptVersionDto().getAttachmentUploadDtoList();
                                for (AttachmentUploadDto attachmentUploadDto : attachmentlist) {
                                    if(attachmentUploadDto.getName().equals(itsmProductAttachment.getAttachmentName())){
                                        AttachmentResponseDto attachmentResponseDto = new AttachmentResponseDto();
                                        attachmentResponseDto.setId(itsmProductAttachment.getIid());
                                        attachmentUploadDto.setResponse(attachmentResponseDto);
                                        itsmProductInfo.setScriptJsonStr(JSON.toJSONString(scriptInfoDto));
                                        break;
                                    }
                                }
                            }
                        }
                        itsmProductInfoMapper.updateScriptInfoJsonById(itsmProductInfo);
                    }
                }else{
                    itsmProductInfoMapper.insertProductInfo(itsmProductInfo);
                }
            }
        }
    }

    /**
     * 为保存的json准备数据
     * @param releaseMediaBean 解析zip后的对象数据
     */
    private ScriptInfoDto tidyDataToScriptInfoDto(ReleaseMediaBean releaseMediaBean){
        if(Objects.isNull(releaseMediaBean.getInfoDto())){
            return new ScriptInfoDto();
        }
        //这里可以将基本信息主要字段映射到新的对象中
        ScriptInfoDto scriptInfoDto = BeanUtils.copy(releaseMediaBean.getInfoDto(), ScriptInfoDto.class);
        //版本信息字段赋值
        if(Objects.nonNull(releaseMediaBean.getInfoVersion())){
            ScriptVersionDto scriptVersionDto = BeanUtils.copy(releaseMediaBean.getInfoVersion(), ScriptVersionDto.class);
            //脚本内容赋值
            if(Objects.nonNull(releaseMediaBean.getInfoVersionText())){
                ScriptContentDto scriptContentDto = BeanUtils.copy(releaseMediaBean.getInfoVersionText(), ScriptContentDto.class);
                scriptVersionDto.setScriptContentDto(scriptContentDto);
            }
            //参数数据
            if(Objects.nonNull(releaseMediaBean.getParameters())){
                List<ParameterValidationDto> paramsList = BeanUtils.copy(releaseMediaBean.getParameters(), ParameterValidationDto.class);
                scriptVersionDto.setParameterValidationDtoList(paramsList);
            }
            //附件数据
            if(Objects.nonNull(releaseMediaBean.getAttachmentList())){
                List<AttachmentUploadDto> attachmentUploadDtoList = BeanUtils.copy(releaseMediaBean.getAttachmentList(), AttachmentUploadDto.class);
                scriptVersionDto.setAttachmentUploadDtoList(attachmentUploadDtoList);
            }
            //为脚本信息赋值
            scriptInfoDto.setScriptVersionDto(scriptVersionDto);
        }
        return scriptInfoDto;
    }

    /**
     * 生产环境下载附件
     * @param attachmentId 附件id
     */
    @Override
    public ItsmProductAttachment selectAttachmentById(Long attachmentId){
        return itsmProductAttachmentMapper.selectAttachmentById(attachmentId);
    }
}
