package com.ideal.script.service;

import com.github.pagehelper.PageInfo;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.AgentInfoDto;
import com.ideal.script.model.dto.QueryAgentInfoDto;
import com.ideal.script.model.entity.AgentInfo;

import java.util.List;

/**
 * Agent基本信息-冗余平台管理Service接口
 * 
 * <AUTHOR>
 */
 public interface IAgentInfoService
{
    /**
     * 查询Agent基本信息-冗余平台管理
     * 
     * @param id Agent基本信息-冗余平台管理主键
     * @return Agent基本信息-冗余平台管理
     */
     AgentInfoDto selectAgentInfoById(Long id);

    /**
     * 功能描述：查询Agent基本信息-冗余平台管理列表
     *
     * @param agentInfoDto Agent基本信息-冗余平台管理
     * @param pageNum 当前页
     * @param pageSize 每页条数
     * @return {@link PageInfo }<{@link AgentInfoDto }>
     */
    PageInfo<AgentInfoDto> selectAgentInfoList(AgentInfoDto agentInfoDto, int pageNum, int pageSize);

    /**
     * 新增Agent基本信息-冗余平台管理
     * 
     * @param agentInfoDto Agent基本信息-冗余平台管理
     * @return 结果
     */
     int insertAgentInfo(AgentInfoDto agentInfoDto);

    /**
     * 修改Agent基本信息-冗余平台管理
     * 
     * @param agentInfoDto Agent基本信息-冗余平台管理
     * @return 结果
     */
     int updateAgentInfo(AgentInfoDto agentInfoDto);

    /**
     * 批量删除Agent基本信息-冗余平台管理
     * 
     * @param ids 需要删除的Agent基本信息-冗余平台管理主键集合
     * @return 结果
     */
     int deleteAgentInfoByIds(Long[] ids);

    /**
     * 删除Agent基本信息-冗余平台管理信息
     * 
     * @param id Agent基本信息-冗余平台管理主键
     * @return 结果
     */
     int deleteAgentInfoById(Long id);



    /**
     * 功能描述： 根据任务taskId查询任务绑定的还未运行的agent信息
     *
     * @param queryAgentInfoDto queryAgentInfoDto
     * @param pageNum 当前页
     * @param pageSize 每页条数
     * @return {@link PageInfo }<{@link AgentInfoDto }>
     * <AUTHOR>
     */
    PageInfo<AgentInfoDto> getTaskBindAgentInfo(QueryAgentInfoDto queryAgentInfoDto, Integer pageNum, Integer pageSize);



    /**
     * 分页查询任务taskId查询任务绑定的所有agent信息
     *
     * @param queryAgentInfoDto queryAgentInfoDto
     * @param pageNum 当前页
     * @param pageSize 每页条数
     * @return {@link PageInfo }<{@link AgentInfoDto }>
     * <AUTHOR>
     */
    PageInfo<AgentInfoDto> getTaskAllAgentInfo(QueryAgentInfoDto queryAgentInfoDto, Integer pageNum, Integer pageSize);


    /**
     * 功能描述： 检查ieai_script_agent_info表中是否已存在相同的记录(IP+端口)
     *
     * @param agentIp agentIp
     * @param agentPort agent端口
     * @return boolean
     * <AUTHOR>
     */
    boolean checkAgentInfoExists(String agentIp, Long agentPort);

    /**
     * 功能描述：根据ip端口获取agent基本信息
     *
     * @param dto Agent基本信息Dto
     * @return {@link AgentInfo }
     * @throws ScriptException 抛出自定义通知异常
     * <AUTHOR>
     */
    AgentInfo selectAgentInfoByIpAndPort(AgentInfoDto dto) throws ScriptException;

    /**
     * 根据业务主键-脚本服务化关系表主键获取任务绑定的agent信息
     * @param serviceId 业务主键
     * @return List<AgentInfoDto>
     */

    List<AgentInfoDto> selectAgentInfoByServiceId(Long serviceId,Long taskId);

    /**
     * 根据ip、端口批量查询agent数据
     * @param agentInfoDtoList 查询条件
     * @return agent结果集
     */
    List<AgentInfo> selectAgentInfoByIpAndPort(List<AgentInfoDto> agentInfoDtoList);
}
