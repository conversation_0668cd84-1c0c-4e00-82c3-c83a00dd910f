package com.ideal.script.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.audit.producer.annotation.Auditable;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.common.util.spring.MessageUtil;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.permission.ExecutionTaskBtnPermitConstant;
import com.ideal.script.common.constant.permission.MenuPermitConstant;
import com.ideal.script.common.constant.permission.MyScriptBtnPermitConstant;
import com.ideal.script.common.constant.permission.TimeTaskManagerBtnPermitConstant;
import com.ideal.script.common.validation.Create;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.ScriptStopShellDto;
import com.ideal.script.model.dto.ScriptTestExecutionDto;
import com.ideal.script.model.dto.TaskExecuteDto;
import com.ideal.script.model.dto.TaskExecuteQueryDto;
import com.ideal.script.model.dto.TaskStartDto;
import com.ideal.script.service.ITaskExecuteService;
import com.ideal.system.common.component.aop.MethodPermission;
import com.ideal.system.common.component.model.CurrentUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

import static com.ideal.sc.util.CurrentUserUtil.getCurrentUser;

/**
 * 任务执行Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("${app.script-tools-url:}/taskExecute")
@MethodPermission(MenuPermitConstant.EXECUTION_TASK_PER)
public class TaskExecuteController {

    private static final Logger logger = LoggerFactory.getLogger(TaskExecuteController.class);

    private final ITaskExecuteService taskExecuteService;
    private final RedisTemplate redisTemplate;

    public TaskExecuteController(ITaskExecuteService taskExecuteService, @Qualifier("redisTemplate") RedisTemplate redisTemplate) {
        this.taskExecuteService = taskExecuteService;
        this.redisTemplate = redisTemplate;
    }

    /**
     * 待执行任务列表
     *
     * @param tableQueryDTO 任务执行Dto
     * @return R<PageInfo < TaskExecuteDto>>
     */
    @PostMapping("/listTaskReadyToExecute")
    public R<PageInfo<TaskExecuteDto>> listTaskReadyToExecute(@RequestBody TableQueryDto<TaskExecuteQueryDto> tableQueryDTO) {
        CurrentUser user = getCurrentUser();
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, taskExecuteService.selectTaskReadyToExecuteList(tableQueryDTO.getQueryParam(), tableQueryDTO.getPageNum(),
                tableQueryDTO.getPageSize(), user), Constants.LIST_SUCCESS);
    }

    /**
     * 查询定时、周期任务列表
     *
     * @param tableQueryDTO
     * @return
     */
    @PostMapping("/listTimeTasks")
    @MethodPermission(MenuPermitConstant.EXECUTION_TASK_OR_TIME_TASK_MANAGER_OR_SCHEDULED_TASK_PER)
    public R<PageInfo<TaskExecuteDto>> listTimeTasks(@RequestBody TableQueryDto<TaskExecuteQueryDto> tableQueryDTO) {
        CurrentUser user = getCurrentUser();
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, taskExecuteService.selectTimeTasks(tableQueryDTO.getQueryParam(), tableQueryDTO.getPageNum(),
                tableQueryDTO.getPageSize(), user), Constants.LIST_SUCCESS);
    }

    /**
     * 定时、周期维护任务启停接口
     * @param scriptTaskId
     * @param state
     * @return
     */
    @GetMapping("/timeTaskSwitch")
    @Auditable("定时任务维护|启动")
    @MethodPermission(TimeTaskManagerBtnPermitConstant.SCRIPT_TIMED_TASK_START_OR_STOP_PER)
    public R<Object> timeTaskSwitch(@RequestParam("scriptTaskId") long scriptTaskId, @RequestParam("state")int state) {
        try {
            taskExecuteService.timeTaskSwitch(scriptTaskId, state);
        } catch (ScriptException e) {
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "", MessageUtil.message("script.time.task.switch.fail"));
        }
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "", MessageUtil.message("script.time.task.switch.success"));
    }

    /**
     * 定时、周期任务终止接口
     * @param scriptTaskId
     * @return
     */
    @GetMapping("/timeTaskKill")
    @Auditable("定时任务维护|终止")
    @MethodPermission(TimeTaskManagerBtnPermitConstant.SCRIPT_TIMED_TASK_OVER_PER)
    public R<Object> timeTaskKill(@RequestParam("scriptTaskId") long scriptTaskId) {
        try {
            taskExecuteService.timeTaskKill(scriptTaskId);
        } catch (ScriptException e) {
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "", MessageUtil.message("script.time.task.kill.fail"));
        }
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "", MessageUtil.message("script.time.task.kill.success"));
    }

    /**
     * 定制、周期更新cron
     * @param taskExecuteDto
     * @return
     */
    @PostMapping("/updateTimeTaskCron")
    @MethodPermission(MenuPermitConstant.EXECUTION_TASK_OR_TIME_TASK_MANAGER_OR_SCHEDULED_TASK_PER)
    public R<Object> updateTimeTaskCron(@RequestBody TaskExecuteDto taskExecuteDto) {
        try {
            taskExecuteService.updateTimeTaskCron(taskExecuteDto);
        } catch (ScriptException e) {
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "", MessageUtil.message("script.time.task.cron.fail"));
        }
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "", MessageUtil.message("script.time.task.cron.success"));
    }

    /**
     * 启动脚本任务
     * @tags 对外API接口
     * @return R<Object>
     */
    @PostMapping("/scriptTaskStart")
    @Auditable("任务监控|待执行-执行继续")
    @MethodPermission(ExecutionTaskBtnPermitConstant.SCRIPT_TASK_START_PER)
    public R<Object> scriptTaskStart(@RequestBody @Validated(Create.class)TaskStartDto taskStartDto) {
        try {
            CurrentUser user = getCurrentUser();
            Long taskInstanceId = taskExecuteService.scriptTaskStartFormApply(taskStartDto, user);
            logger.info("scriptTaskStart taskInstanceId:{} success!", taskInstanceId);
        } catch (ScriptException e) {
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "", "script.task.start.fail");
        }
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "", "script.task.start.success");
    }


    /**
     * 运行中获取实时输出
     *
     * @param taskRuntimeId agent运行实例Id
     * @return {@link R }<{@link Object }>
     */
    @GetMapping("/getRealTimeOutPutMessage")
    @MethodPermission(MenuPermitConstant.EXECUTION_TASK_OR_MY_SCRIPT_OR_SCHEDULED_TASK_PER)
    public R<Object> getRealTimeOutPutMessage(@RequestParam(value = "taskRuntimeId") Long taskRuntimeId ) {
        String outPut = taskExecuteService.getRealTimeOutPutMessage(taskRuntimeId);
        logger.info("getRealTimeOutPutMessage runtimeId:{} success!", taskRuntimeId);
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, outPut, "get.real.time.out.put.message.success");
    }


    /**
     * agent实例重试
     *
     * @return R<Object>
     */
    @GetMapping("/retryScriptServiceShell")
    @Auditable("任务监控|执行中-查看-重试")
    @MethodPermission(ExecutionTaskBtnPermitConstant.RETRY_SCRIPT_PER)
    public R<Object> retryScriptServiceShell(@RequestParam(value = "id") Long id, @RequestParam(value = "taskInstanceId") Long taskInstanceId) {
        try {
            CurrentUser user = getCurrentUser();
            taskExecuteService.retryScriptServiceShell(id, taskInstanceId, user);
            logger.info("retryScriptServiceShell runtimeId:{} success!", id);
        } catch (ScriptException e) {
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "", "script.task.start.fail");
        }
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "", "script.task.start.success");
    }

    /**
     * agent实例重试
     *
     * @return R<Object>
     */
    @GetMapping("/batchRetryScriptServiceShell")
    @Auditable("任务监控|执行中-查看-重试agent实例")
    @MethodPermission(ExecutionTaskBtnPermitConstant.RETRY_SCRIPT_PER)
    public R<Object> batchRetryScriptServiceShell(@RequestParam(value = "ids") Long[] ids, @RequestParam(value = "taskInstanceId") Long taskInstanceId) {
        try {
            CurrentUser user = getCurrentUser();
            for (Long id : ids) {
                taskExecuteService.retryScriptServiceShell(id, taskInstanceId, user);
            }
        } catch (ScriptException e) {
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "", "script.task.start.fail");
        }
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "", "script.task.start.success");
    }



    /**
     * 终止agent
     *
     * @param taskInstanceId 脚本任务运行实例Id
     * @param runTimeIds     agent运行实例id集合
     * @return {@link R }<{@link Object }>
     * <AUTHOR>
     */
    @GetMapping("/scriptShellKill")
    @MethodPermission(ExecutionTaskBtnPermitConstant.STOP_TASK_PER)
    public R<Object> scriptShellKill(@RequestParam(value = "taskInstanceId") Long taskInstanceId, @RequestParam(value = "runTimeIds") Long[] runTimeIds) {
        try {
            taskExecuteService.scriptShellKillByRunTimeIds(taskInstanceId, runTimeIds);
            logger.info("scriptShellKill taskInstanceId:{} success!", taskInstanceId);
        } catch (ScriptException e) {
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "", "script.shell.kill.fail");
        }
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "", "script.shell.kill.success");
    }


    /**
     * 略过agent
     *
     * @param taskInstanceId 脚本任务运行实例Id
     * @param runTimeIds     agent运行实例id集合
     * @return {@link R }<{@link Object }>
     */
    @GetMapping("/skipScriptShell")
    @Auditable("任务监控|执行中-查看-忽略")
    @MethodPermission(ExecutionTaskBtnPermitConstant.SKIP_SCRIPT_PER)
    public R<Object> skipScriptShell(@RequestParam(value = "taskInstanceId") Long taskInstanceId, @RequestParam(value = "runTimeIds") Long[] runTimeIds) {
        try {
            taskExecuteService.skipScriptShell(taskInstanceId, runTimeIds);
            logger.info("skipScriptShell taskInstanceId:{} success!", taskInstanceId);
        } catch (ScriptException e) {
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "", "script.shell.skip.fail");
        }
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "", "script.shell.skip.success");
    }

    /**
     * 任务取消（终止）
     *
     * @param taskInstanceId 脚本任务运行实例Id
     * @return {@link R }<{@link Object }>
     */
    @GetMapping("/stopTask")
    @Auditable("任务监控|执行中-终止任务")
    @MethodPermission(ExecutionTaskBtnPermitConstant.STOP_TASK_PER)
    public R<Object> stopTask(@RequestParam(value = "taskInstanceId") Long [] taskInstanceId) throws ScriptException{
        taskExecuteService.stopTask(taskInstanceId);
        logger.info("stopTask taskInstanceId:{} success!", Arrays.toString(taskInstanceId));
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "", "stop.task.success");
    }

    /**
     * 任务取消（待执行）
     *
     * @param scriptTaskId 脚本任务Id
     * @return {@link R }<{@link Object }>
     * <AUTHOR>
     */
    @GetMapping("/cancelTask")
    @Auditable("任务监控|待执行-取消")
    @MethodPermission(ExecutionTaskBtnPermitConstant.CANCEL_TASK_PER)
    public R<Object> cancelTask(@RequestParam(value = "scriptTaskId") Long scriptTaskId) {
        try {
            taskExecuteService.cancelTask(scriptTaskId);
            logger.info("cancelTask scriptTaskId:{} success!", scriptTaskId);
        } catch (ScriptException e) {
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "", "cancel.task.fail");
        }
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "", "cancels.task.success");
    }

    /**
     * 查询当前用户可见的正在运行任务列表。
     * @tags 对外API接口
     * @param tableQueryDTO 查询条件的封装对象，包含页面查询条件属性（适用于"执行中"选项卡）
     * @return R<PageInfo < TaskExecuteDto>> 包含运行中任务信息的分页结果对象
     */
    @PostMapping("/listRunningScriptTasks")
    @MethodPermission(MenuPermitConstant.EXECUTION_TASK_OR_SCHEDULED_TASK_PER)
    public R<PageInfo<TaskExecuteDto>> listRunningScriptTasks(@RequestBody TableQueryDto<TaskExecuteQueryDto> tableQueryDTO) {
        CurrentUser currentUser = getCurrentUser();
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, taskExecuteService.listRunningScriptTasks(tableQueryDTO.getQueryParam(), tableQueryDTO.getPageNum(),
                tableQueryDTO.getPageSize(), currentUser), Constants.LIST_SUCCESS);
    }

    /**
     * 查询当前用户可见的正在运行任务列表。
     * @tags 对外API接口
     * @param tableQueryDto 查询条件的封装对象，包含页面查询条件属性（适用于"执行历史"选项卡）
     * @return R<PageInfo < TaskExecuteDto>> 包含已完成任务信息的分页结果对象
     */
    @PostMapping("/listCompleteScriptTasks")
    @MethodPermission(MenuPermitConstant.EXECUTION_TASK_OR_SCHEDULED_TASK_PER)
    public R<PageInfo<TaskExecuteDto>> listCompleteScriptTasks(@RequestBody TableQueryDto<TaskExecuteQueryDto> tableQueryDto) {
        CurrentUser currentUser = getCurrentUser();
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, taskExecuteService.listCompleteScriptTasks(tableQueryDto.getQueryParam(), tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize(), currentUser), Constants.LIST_SUCCESS);
    }


    /**
     * 脚本测试
     *
     * @param testExecutionDto 脚本测试dto
     * @return R<Object>
     */
    @PostMapping("/scriptTestExecution")
    @Auditable("我的脚本|测试")
    @MethodPermission(MyScriptBtnPermitConstant.SCRIPT_TEST_EXECUTION_PER)
    public R<Object> scriptTestExecution(@RequestBody ScriptTestExecutionDto testExecutionDto) {
        try {
            CurrentUser user = getCurrentUser();
            Long taskInstanceId = taskExecuteService.scriptTestExecution(testExecutionDto, user);
            logger.info("scriptTestExecution taskInstanceId:{} success!", taskInstanceId);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, taskInstanceId, "script.test.execution.success");
        } catch (ScriptException e) {
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "", "script.test.execution.fail");
        }
    }


    /**
     * 导出
     *
     * @param ids 基础信息表id列表
     */
    @PostMapping("/export")
    @MethodPermission(ExecutionTaskBtnPermitConstant.EXCEL_EXPORT_PER)
    public void exportExcel(@RequestBody List<Long> ids, HttpServletResponse response) {
            taskExecuteService.exportExcel(ids,response);
    }

    /**
     * 根据任务id去redis校验
     * @param taskId 任务id
     */
    @GetMapping("/checkBefore")
    public R<?> checkBefore(@RequestParam("taskId") long taskId) {
        Boolean isExist = redisTemplate.hasKey("script:checkBefore:" + taskId);
        return R.ok(isExist);
    }

    /**
     * 任务监控-执行历史-导出功能
     * @param taskInstanceIds 任务实例id
     * @param response 响应结果
     */
    @PostMapping("/exportAgentHisExcel")
    @Auditable("任务监控|执行历史-导出")
    @MethodPermission(ExecutionTaskBtnPermitConstant.EXCEL_EXPORT_PER)
    public void exportAgentHisExcel(@RequestBody List<Long> taskInstanceIds, HttpServletResponse response) {
        try {
            taskExecuteService.exportAgentHisExcel(taskInstanceIds, response);
        } catch (Exception e) {
            logger.error("exportAgentHisExcel error", e);
        }
    }

    /**
     * 终止agent实例
     * @tags 对外API接口
     * @param scriptStopShellDto 相关参数dto
     * @throws ScriptException 异常提示
     */
    @PostMapping("/stopScriptInstanceShell")
    @Auditable("任务监控|执行中-查看-终止Agent实例")
    @MethodPermission(ExecutionTaskBtnPermitConstant.STOP_SCRIPT_PER)
    public R<Object> stopScriptInstanceShell(@RequestBody ScriptStopShellDto scriptStopShellDto) throws ScriptException{
        taskExecuteService.stopScriptInstanceShell(scriptStopShellDto);
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "", "script.shell.agent.stop.success");
    }

}
