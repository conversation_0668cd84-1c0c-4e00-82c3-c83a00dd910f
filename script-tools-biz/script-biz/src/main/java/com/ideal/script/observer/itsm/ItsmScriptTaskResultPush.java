package com.ideal.script.observer.itsm;


import com.ideal.common.util.spring.SpringUtil;
import com.ideal.sc.util.HttpClientUtil;
import com.ideal.script.config.PsbcProperties;
import com.ideal.script.model.dto.TaskInstanceDto;
import com.ideal.script.service.ITaskInstanceService;
import com.ideal.script.service.ITaskRuntimeService;
import com.ideal.script.service.impl.builders.MyScriptServiceScripts;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.ListableBeanFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class ItsmScriptTaskResultPush{

    private static final Logger logger = LoggerFactory.getLogger(ItsmScriptTaskResultPush.class);

    private final MyScriptServiceScripts scripts;
    private ITaskRuntimeService taskRuntimeService;
    private final ITaskInstanceService taskInstanceService;
    private final RedisTemplate<String, String> redisTemplate;
    private final ApplicationContext applicationContext;

    public ItsmScriptTaskResultPush(@Lazy MyScriptServiceScripts scripts, ITaskRuntimeService taskRuntimeService, ITaskInstanceService taskInstanceService, RedisTemplate<String, String> redisTemplate,ApplicationContext applicationContext) {
        this.scripts = scripts;
        this.taskRuntimeService = taskRuntimeService;
        this.taskInstanceService = taskInstanceService;
        this.redisTemplate = redisTemplate;
        this.applicationContext = applicationContext;
    }

    @Async
    public void pushMessage(Long id,String message,boolean taskIdFlag) {
        //如果spring中没有PsbcProperties这个bean，说明没有开启bankCode001个性化配置，不推送结果
        if(((ListableBeanFactory) applicationContext).getBeanNamesForType(PsbcProperties.class).length > 0
                && ObjectUtils.notEqual(SpringUtil.getBean(PsbcProperties.class),null)){
            //itsm地址
            String url = SpringUtil.getBean(PsbcProperties.class).getPushTaskMessageToItsmUrl();
            if(StringUtils.isBlank(url)){
                logger.error("itsm推送地址配置异常");
                return;
            }
            //参数map
            Map<String,Object> data = new HashMap<>(7);
            // 这里可以添加实际的发送消息逻辑，比如发送邮件或短信
            //参数1：任务id
            Long scriptTaskId = 0L;
            if(taskIdFlag){
                scriptTaskId = id;
            }else{
                scriptTaskId = taskRuntimeService.selectTaskRuntimeById(id).getScriptTaskId();
            }
            data.put("flowId", scriptTaskId);
            //参数2：invokeId
            String invokeId = redisTemplate.opsForValue().get("itsm_task_" + scriptTaskId);
            if(StringUtils.isNotBlank(invokeId)){
                data.put("fmId", invokeId);
            }else{
                logger.error("invokeId为空，脚本任务取消推送itsm，scriptTaskId:{} ",scriptTaskId);
                return;
            }
            //参数3：操作类型（完成或者终止）
            data.put("executorResult", message);
            //参数4：脚本uuid
            TaskInstanceDto taskInstanceByTaskInfoId = taskInstanceService.getTaskInstanceByTaskInfoId(scriptTaskId);
            data.put("scriptId", taskInstanceByTaskInfoId.getSrcScriptUuid());
            //参数5：任务结束时间
            data.put("executorEndTime", taskInstanceByTaskInfoId.getEndTime());
            //参数6：任务状态
            String stateStr = "";
            if(-1 == taskInstanceByTaskInfoId.getStatus()){
                stateStr = "未运行";
            }
            if(1 == taskInstanceByTaskInfoId.getStatus()){
                stateStr = "初始化";
            }
            if(10 == taskInstanceByTaskInfoId.getStatus() || 11 == taskInstanceByTaskInfoId.getStatus()){
                stateStr = "运行";
            }
            if(5 == taskInstanceByTaskInfoId.getStatus() || 20 == taskInstanceByTaskInfoId.getStatus()){
                stateStr = "完成";
            }
            if(30 == taskInstanceByTaskInfoId.getStatus()){
                stateStr = "异常";
            }
            if(40 == taskInstanceByTaskInfoId.getStatus()){
                stateStr = "异常完成";
            }
            if(50 == taskInstanceByTaskInfoId.getStatus()){
                stateStr = "异常运行";
            }
            if(60 == taskInstanceByTaskInfoId.getStatus()){
                stateStr = "已终止";
            }
            data.put("resultStatus", stateStr);
            //参数7：脚本名
            data.put("scriptName", scripts.getInfoMapper().selectInfoByScriptUuid(taskInstanceByTaskInfoId.getSrcScriptUuid()));
            //推送消息到itsm
            Map<String,String> headers = new HashMap<>();
            headers.put("Content-Type","application/json; charset=utf-8");
            try {
                HttpClientUtil.postByFrom(url, headers, data, Map.class);
            }catch (Exception e){
                logger.error("任务结束，推送itsm消息失败，scriptTaskId:{} , 异常消息:{} ",scriptTaskId,e.getMessage());
            }
        }
    }
}
