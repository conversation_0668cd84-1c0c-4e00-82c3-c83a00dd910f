package com.ideal.script.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.util.FileSizeValidUtil;
import com.ideal.script.config.ScriptBusinessConfig;
import com.ideal.script.dto.*;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.AttachmentEphemeralMapper;
import com.ideal.script.mapper.AttachmentMapper;
import com.ideal.script.model.entity.Attachment;
import com.ideal.script.model.entity.InfoVersion;
import com.ideal.script.service.IAttachmentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 【附件】Service业务层处理
 *
 * <AUTHOR>
 */
@Transactional(rollbackFor = Exception.class)
@Service
public class AttachmentServiceImpl implements IAttachmentService {

    private static final Logger logger = LoggerFactory.getLogger(AttachmentServiceImpl.class);
    private final AttachmentMapper attachmentMapper;
    private final AttachmentEphemeralMapper attachmentEphemeralMapper;
    private final FileSizeValidUtil fileSizeValidUtil;

    public AttachmentServiceImpl(AttachmentEphemeralMapper attachmentEphemeralMapper,AttachmentMapper attachmentMapper, ScriptBusinessConfig scriptBusinessConfig, FileSizeValidUtil fileSizeValidUtil) {
        this.attachmentEphemeralMapper = attachmentEphemeralMapper;
        this.attachmentMapper = attachmentMapper;
        this.fileSizeValidUtil = fileSizeValidUtil;
    }

    /**
     * 查询【附件信息】
     *
     * @param id 【附件id】主键
     * @return 【附件信息】
     */
    @Override
    public AttachmentDto selectAttachmentById(Long id) {
        return BeanUtils.copy(attachmentMapper.selectAttachmentById(id), AttachmentDto.class);
    }

    /**
     * 根据id批量查询附件
     * @param ids   附件主键id
     * @return  附件列表
     */
    @Override
    public List<AttachmentDto> selectAttachmentByIds(Long[] ids){
        return BeanUtils.copy(attachmentMapper.selectAttachmentByIds(ids), AttachmentDto.class);
    }

    /**
     * 查询【附件】列表
     *
     * @param attachmentDto 【附件实体Dto】
     * @return 【分页附件列表】
     */
    @Override
    public PageInfo<AttachmentDto> selectAttachmentList(AttachmentDto attachmentDto, int pageNum, int pageSize) {
        PageMethod.startPage(pageNum, pageSize);
        List<Attachment> attachmentList = new ArrayList<>();
        if (null != attachmentDto) {
            Attachment attachment = BeanUtils.copy(attachmentDto, Attachment.class);
            attachmentList = attachmentMapper.selectAttachmentList(attachment);
        }
        return PageDataUtil.toDtoPage(attachmentList, AttachmentDto.class);
    }

    /**
     * 新增【插入附件】
     *
     * @param attachmentDto 【附件实体Dto】
     */
    @Override
    public int insertAttachment(AttachmentDto attachmentDto) {
        Attachment attachment = BeanUtils.copy(attachmentDto, Attachment.class);
        return attachmentMapper.insertAttachment(attachment);
    }

    /**
     * 修改【修改附件】
     *
     * @param attachmentDto 【附件实体Dto】
     */
    @Override
    public void updateAttachment(AttachmentDto attachmentDto) {
        Attachment attachment = BeanUtils.copy(attachmentDto, Attachment.class);
        attachmentMapper.updateAttachment(attachment);
    }

    /**
     * 批量删除【批量删除附件】
     *
     * @param ids 需要删除的【附件】主键
     */
    @Override
    public void deleteAttachmentByIds(Long[] ids) {
        attachmentMapper.deleteAttachmentByIds(ids);
    }

    /**
     * 删除【删除附件】信息
     *
     * @param id 【附件】主键
     * @return 结果
     */
    @Override
    public int deleteAttachmentById(Long id) {
        return attachmentMapper.deleteAttachmentById(id);
    }

    /**
     * 上传附件
     *
     * @param attachmentDto 文件
     * @return Map
     */
    @Override
    public AttachmentDto uploadAttachment(AttachmentDto attachmentDto) throws ScriptException {
        logger.info("上传附件，附件名为: {}, 字节数: {}", attachmentDto.getName(), attachmentDto.getSize());
        Attachment attachment = BeanUtils.copy(attachmentDto, Attachment.class);
        //验证文件大小
        fileSizeValidUtil.validateFileSize(attachmentDto.getSize());
        attachmentMapper.insertAttachment(attachment);
        attachment.setId(attachment.getId());
        return BeanUtils.copy(attachment, AttachmentDto.class);
    }

    /**
     * 上传临时附件
     * @param scriptFileAttachmentTempList 附件参数
     * @return 上传成功附件id信息
     * @throws ScriptException 脚本服务化异常
     */
    @Override
    public ScriptAttachmentTempMegDto uploadAttachmentTemp(List<ScriptFileAttachmentTempApiDto> scriptFileAttachmentTempList)throws ScriptException {
        ScriptAttachmentTempMegDto scriptAttachmentTempMegDto = new ScriptAttachmentTempMegDto();
        try {
            List<Attachment> insertAttachments = new ArrayList<>();
            //验证文件大小,并整理要保存的数据
            for(ScriptFileAttachmentTempApiDto attachmentDto : scriptFileAttachmentTempList){
                List<ScriptAttachmentTempApiDto> attachment = attachmentDto.getAttachment();
                for(ScriptAttachmentTempApiDto attachmentTempApiDto : attachment){
                    fileSizeValidUtil.validateFileSize((long) attachmentTempApiDto.getFileContentByte().length);
                    Attachment attachment1 = new Attachment();
                    attachment1.setName(attachmentTempApiDto.getFileName());
                    attachment1.setSrcScriptUuid(attachmentDto.getSrcScriptUuid());
                    attachment1.setContents(attachmentTempApiDto.getFileContentByte());
                    attachment1.setSize((long)attachmentTempApiDto.getFileContentByte().length);
                    insertAttachments.add(attachment1);
                }
            }
            //定义list，里面是一个个uuid加上附件id的字符串，uuid可能有重复的，后续逻辑会根据uuid分组
            List<String> uuidAndAttachmentId = new ArrayList<>();
            //遍历保存附件
            for(Attachment attachment : insertAttachments){
                //写单独的后台，用新表
                attachmentEphemeralMapper.insertAttachment(attachment);
                Long attachmentId = attachment.getId();
                uuidAndAttachmentId.add(attachment.getSrcScriptUuid() + Constants.ATTACHMENT_SPLIT_FLAG + attachmentId);
            }
            //将附件整理成uuid对应多个附件id的格式
            List<ScriptAfterUploadAttachmentTempApiDto> result = uuidAndAttachmentId.parallelStream()
                    .collect(Collectors.groupingBy(
                            str -> str.split(Constants.ATTACHMENT_SPLIT_FLAG)[0], // 按 uuid 分组
                            Collectors.mapping(
                                    str -> Long.parseLong(str.split(Constants.ATTACHMENT_SPLIT_FLAG)[1]), // 提取 attachmentId 并转为 Long
                                    Collectors.toList()
                            )
                    ))
                    .entrySet()
                    .parallelStream()
                    .map(entry -> new ScriptAfterUploadAttachmentTempApiDto(
                            entry.getKey(), // uuid
                            entry.getValue().toArray(new Long[0]) // attachmentIds
                    ))
                    .collect(Collectors.toList());
            //数据赋值
            scriptAttachmentTempMegDto.setData(result);
        }catch (ScriptException e){
            scriptAttachmentTempMegDto.setSuccess(false);
            scriptAttachmentTempMegDto.setMessage(e.getMessage());
            scriptAttachmentTempMegDto.setData(new ArrayList<>());
            logger.error("upload script attachment template error : " , e);
        }
        return scriptAttachmentTempMegDto;
    }

    /**
     * 保存附件
     *
     * @param scriptInfoDto      脚本信息
     * @param isNewVersionScript 产生新脚本标识
     */
    @Transactional(rollbackFor = Exception.class)
    public void createAttachments(ScriptInfoDto scriptInfoDto, boolean isNewVersionScript) {
        if (!scriptInfoDto.getScriptVersionDto().getAttachmentUploadDtoList().isEmpty()) {
            if (!isNewVersionScript) {
                Long[] ids = new Long[scriptInfoDto.getScriptVersionDto().getAttachmentUploadDtoList().size()];
                for (int i = 0; i < ids.length; i++) {
                    ids[i] = scriptInfoDto.getScriptVersionDto().getAttachmentUploadDtoList().get(i).getResponse().getId();
                    // 只需要复制上个版本的附件，新增的附件不用复制，sql校验
                    Attachment attachment = attachmentMapper.selectAttachmentById(ids[i]);
                    attachment.setId(null);
                    attachmentMapper.insertAttachment(attachment);
                }
            }
            Attachment[] attachments = new Attachment[scriptInfoDto.getScriptVersionDto().getAttachmentUploadDtoList().size()];
            for (int i = 0; i < attachments.length; i++) {
                attachments[i] = new Attachment();
                attachments[i].setId(scriptInfoDto.getScriptVersionDto().getAttachmentUploadDtoList().get(i).getResponse().getId());
                attachments[i].setSrcScriptUuid(scriptInfoDto.getScriptVersionDto().getSrcScriptUuid());
            }
            for (Attachment attachment : attachments) {
                attachmentMapper.updateAttachment(attachment);
            }
        }
    }

    /**
     * 更新Attachment表信息
     *
     * @param scriptInfoDto 脚本信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAttachments(ScriptInfoDto scriptInfoDto) {
        Attachment[] attachments = new Attachment[scriptInfoDto.getScriptVersionDto().getAttachmentUploadDtoList().size()];
        List<Long> ids = new ArrayList<>();
        //没有附件信息传回认为附件被清空
        if (scriptInfoDto.getScriptVersionDto().getAttachmentUploadDtoList().isEmpty()) {
            String[] scriptUuids = new String[]{scriptInfoDto.getScriptVersionDto().getSrcScriptUuid()};
            attachmentMapper.deleteAttachmentByScriptUuids(scriptUuids);
        } else {
            for (int i = 0; i < attachments.length; i++) {
                attachments[i] = new Attachment();
                attachments[i].setId(scriptInfoDto.getScriptVersionDto().getAttachmentUploadDtoList().get(i).getResponse().getId());
                attachments[i].setSrcScriptUuid(scriptInfoDto.getScriptVersionDto().getSrcScriptUuid());
            }
            //变更
            for (Attachment attachment : attachments) {
                attachmentMapper.updateAttachment(attachment);
                ids.add(attachment.getId());
            }
            //去除取消勾选的数据
            if (!scriptInfoDto.getScriptVersionDto().getAttachmentUploadDtoList().isEmpty()) {
                attachmentMapper.deleteAttachmentByIdAndUuid(scriptInfoDto.getScriptVersionDto().getSrcScriptUuid(), ids);
            }
        }
        return true;
    }

    /**
     * 获取附件
     *
     * @param infoVersion 脚本版本信息
     * @return 附件数组
     */
    public List<AttachmentUploadDto> getAttachmentUploadDtos(InfoVersion infoVersion) {
        //获取附件
        Attachment attachment = new Attachment();
        attachment.setSrcScriptUuid(infoVersion.getSrcScriptUuid());
        List<Attachment> attachments = attachmentMapper.selectAttachmentList(attachment);
        List<AttachmentUploadDto> attachmentUploadDtoList = new ArrayList<>();
        getAttachmentUploadDto(attachments, attachmentUploadDtoList);
        return attachmentUploadDtoList;
    }

    /**
     * 设置Attachment信息
     *
     * @param attachments          附件信息
     * @param attachmentUploadDtoList 附件上传信息
     */
    private static void getAttachmentUploadDto(List<Attachment> attachments, List<AttachmentUploadDto> attachmentUploadDtoList) {
        for (Attachment attachment : attachments) {
            AttachmentUploadDto attachmentUploadDto = new AttachmentUploadDto();
            attachmentUploadDto.setName(attachment.getName());
            attachmentUploadDto.setSize(attachment.getSize());
            AttachmentResponseDto attachmentResponseDto = new AttachmentResponseDto();
            attachmentResponseDto.setId(attachment.getId());
            attachmentUploadDto.setResponse(attachmentResponseDto);
            attachmentUploadDto.setContents(attachment.getContents());
            attachmentUploadDtoList.add(attachmentUploadDto);
        }
    }

    /**
     * 根据版本srcScriptUuid查询绑定的附件
     *
     * @param srcScriptUuid 版本uuid
     * @return List<AttachmentDto>
     */
    @Override
    public List<AttachmentDto> getAttachmentByUuid(String srcScriptUuid) {
        return BeanUtils.copy(attachmentMapper.getAttachmentByUuid(srcScriptUuid), AttachmentDto.class);
    }
}
