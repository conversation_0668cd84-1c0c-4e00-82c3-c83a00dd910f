package com.ideal.script.service;

import com.github.pagehelper.PageInfo;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.*;
import org.apache.ibatis.session.SqlSession;

import java.util.List;

/**
 * 关联关系，包含任务执行的参数信息Service接口
 * 
 * <AUTHOR>
 */
 public interface ITaskParamsService
{
    /**
     * 查询关联关系，包含任务执行的参数信息
     * 
     * @param id 关联关系，包含任务执行的参数信息主键
     * @return 关联关系，包含任务执行的参数信息
     */
     TaskParamsDto selectTaskParamsById(Long id);

    /**
     * 查询关联关系，包含任务执行的参数信息列表
     * @param pageNum 当前页
     * @param pageSize 每页条数
     * @param taskParamsDto 关联关系，包含任务执行的参数信息
     * @return 关联关系，包含任务执行的参数信息集合
     */
     PageInfo<TaskParamsDto> selectTaskParamsList(TaskParamsDto taskParamsDto, int pageNum, int pageSize);

    /**
     * 新增关联关系，包含任务执行的参数信息
     * 
     * @param taskParamsDto 关联关系，包含任务执行的参数信息
     * @return 结果
     */
     int insertTaskParams(TaskParamsDto taskParamsDto);

    /**
     * 修改关联关系，包含任务执行的参数信息
     * 
     * @param taskParamsDto 关联关系，包含任务执行的参数信息
     * @return 结果
     */
     int updateTaskParams(TaskParamsDto taskParamsDto);

    /**
     * 批量删除关联关系，包含任务执行的参数信息
     * 
     * @param ids 需要删除的关联关系，包含任务执行的参数信息主键集合
     * @return 结果
     */
     int deleteTaskParamsByIds(Long[] ids);

    /**
     * 删除关联关系，包含任务执行的参数信息信息
     * 
     * @param id 关联关系，包含任务执行的参数信息主键
     * @return 结果
     */
     int deleteTaskParamsById(Long id);
    /**
     * 删除关联关系，包含任务执行的参数信息信息
     *
     * @param taskId 任务id
     * @return 结果
     */
    int deleteTaskParamsByTaskId(Long taskId);

    /**
     * 功能描述： 获取脚本任务执行的历史参数
     *
     * @param srcScriptUuid 版本uuid
     * @return {@link List }<{@link TaskHisParamsDto }>
     */
    List<TaskHisParamsDto> selectHisParam(String srcScriptUuid);

    /**
     * 双人复核详情页面-查询参数
     * @param serviceId 业务主键
     * @return List<TaskParamsDto>
     */
    List<TaskParamsDto> selectTaskParamsByServiceId(Long serviceId,Long taskId);

    /**
     * 存储参数信息
     *
     * @param scriptExecAuditDto 任务申请提交审核Dto
     * @param taskInfo           任务申请提交任务时的脚本任务对象
     * @param sqlSession         ​SqlSession​ 对象
     * @throws ScriptException 抛出自定义异常
     */
    void saveTaskParams(ScriptExecAuditDto scriptExecAuditDto, TaskDto taskInfo, SqlSession sqlSession) throws ScriptException;


    /**
     * 校验参数是否符合表达式规则
     *
     * @param parameterDtoList 参数列表
     * @throws ScriptException 抛出自定义通知异常
     * @return {@link String } 返回不符合规则的值
     */
    String validateParameterList(List<ParameterDto> parameterDtoList) throws ScriptException;
}
