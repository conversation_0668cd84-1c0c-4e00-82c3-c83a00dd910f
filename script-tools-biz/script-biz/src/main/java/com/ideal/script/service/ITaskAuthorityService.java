package com.ideal.script.service;

import com.github.pagehelper.PageInfo;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.TaskAuthorityDto;

/**
 * 任务权限（任务启用、任务禁用）Service接口
 * <AUTHOR>
 */
public interface ITaskAuthorityService {
    /**
     * 获取任务列表
     * @param pageNum 当前页
     * @param pageSize 每页条数
     * @param taskAuthorityDto 任务权限Dto
     * @return PageInfo<TaskAuthorityDto>
     */
    PageInfo<TaskAuthorityDto> selectTaskAuthorityList(TaskAuthorityDto taskAuthorityDto, Integer pageNum, Integer pageSize);


    /**
     * 功能描述：任务启用/禁用
     *
     * @param taskAuthorityDto 任务权限Dto
     * @return boolean
     * @throws ScriptException 抛出自定义通知异常
     * <AUTHOR>
     */
    boolean updateUseState(TaskAuthorityDto taskAuthorityDto) throws ScriptException;

    /**
     * 功能描述：任务启用/禁用 （批量）
     * @param taskAuthorityDto  任务权限Dto
     * @return boolean
     * @throws ScriptException 抛出自定义通知异常
     */
    boolean batchUpdateUseState(TaskAuthorityDto taskAuthorityDto) throws ScriptException;


    /**
     * 查看是否有未完成的任务。
     * @param scriptInfoVersionId 版本ID
     * @throws ScriptException 抛出自定义通知异常
     * @return boolean
     */
    boolean checkExistRunTask(Long scriptInfoVersionId) throws ScriptException;
}
