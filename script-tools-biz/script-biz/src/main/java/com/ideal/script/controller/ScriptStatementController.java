package com.ideal.script.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.common.util.BeanUtils;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.permission.MenuPermitConstant;
import com.ideal.script.model.dto.StatementDto;
import com.ideal.script.service.IScriptStatementService;
import com.ideal.system.common.component.aop.MethodPermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping("${app.script-tools-url:}/statement")
@MethodPermission(MenuPermitConstant.SCRIPT_STATEMENT_PER)
public class ScriptStatementController {


    private static final Logger logger = LoggerFactory.getLogger(ScriptStatementController.class);
    private final IScriptStatementService scriptStatementService;

    public ScriptStatementController(IScriptStatementService scriptStatementService) {
        this.scriptStatementService = scriptStatementService;
    }

    @PostMapping("/list")
    public R<PageInfo<StatementDto>> scriptStatementList(@RequestBody TableQueryDto<StatementDto> tableQueryDTO)
    {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, scriptStatementService.selectScriptStatementPage(BeanUtils.copy(tableQueryDTO.getQueryParam(),StatementDto.class), tableQueryDTO.getPageNum(),
                tableQueryDTO.getPageSize()), Constants.LIST_SUCCESS);
    }


    /**
     * 导出
     *
     * @param ids 定时任务基础信息表id列表
     */
    @PostMapping("/export")
    public void exportExcel(@RequestBody List<Long> ids, HttpServletResponse response) {
        try {
            scriptStatementService.exportExcel(ids,response);
        } catch (Exception e) {
            logger.error("ScriptStatementController export fail",e);
        }
    }

}
