package com.ideal.script.service.consumer;

import com.alibaba.fastjson2.JSON;
import com.ideal.message.center.ISubscriber;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.service.resulthandler.IScriptResultHandlerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 监听mq中script-send-result主题中的消息
 *
 * <AUTHOR>
 */
@Component
public class ScriptSendResultHandler implements ISubscriber {
    private final Logger logger = LoggerFactory.getLogger(ScriptSendResultHandler.class);

    private final IScriptResultHandlerService scriptResultHandlerService;

    public ScriptSendResultHandler(IScriptResultHandlerService scriptResultHandlerService) {
        this.scriptResultHandlerService = scriptResultHandlerService;
    }

    @Override
    public void notice(Object message) {
        if (message instanceof String) {
            // 处理调用agent-gateway异步发送请求后的反馈结果
            try {
                scriptResultHandlerService.handleScriptSendResult((String) message);
            } catch (ScriptException e) {
                logger.error("Failed to handle the result from script-send-result topic,The received message is:{}",message,e);
            }
        }else {
            try {
                scriptResultHandlerService.handleScriptSendResult(JSON.toJSONString(message));
            } catch (ScriptException e) {
                logger.error("Failed to handle the result from script-send-result topic,The received message is:{}",message,e);
            }
        }
    }
}
