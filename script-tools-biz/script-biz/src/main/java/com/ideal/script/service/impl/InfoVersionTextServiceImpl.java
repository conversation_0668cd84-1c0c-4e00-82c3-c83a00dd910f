package com.ideal.script.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.script.dto.ScriptInfoDto;
import com.ideal.script.mapper.InfoMapper;
import com.ideal.script.mapper.InfoVersionTextMapper;
import com.ideal.script.model.dto.InfoVersionTextDto;
import com.ideal.script.model.entity.InfoVersionText;
import com.ideal.script.service.IAttachmentService;
import com.ideal.script.service.IInfoVersionTextService;
import com.ideal.script.service.IParameterService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 【脚本版本内容信息】Service业务层处理
 *
 * <AUTHOR>
 */
@Transactional(rollbackFor = Exception.class)
@Service
public class InfoVersionTextServiceImpl implements IInfoVersionTextService {

    private final InfoVersionTextMapper infoVersionTextMapper;
    private final IParameterService parameterService;
    private final IAttachmentService attachmentService;

    private final InfoMapper infoMapper;

    public InfoVersionTextServiceImpl(InfoVersionTextMapper infoVersionTextMapper, IParameterService parameterService, IAttachmentService attachmentService, InfoMapper infoMapper) {
        this.infoVersionTextMapper = infoVersionTextMapper;
        this.parameterService = parameterService;
        this.attachmentService = attachmentService;
        this.infoMapper = infoMapper;
    }

    /**
     * 查询【脚本版本内容信息】
     *
     * @param id 【脚本版本内容】主键
     * @return 【脚本版本内容信息Dto】
     */
    @Override
    public InfoVersionTextDto selectInfoVersionTextById(Long id) {
        InfoVersionText infoVersionText = infoVersionTextMapper.selectInfoVersionTextById(id);
        return BeanUtils.copy(infoVersionText,InfoVersionTextDto.class);
    }

    /**
     * 查询【脚本版本内容信息】列表
     *
     * @param infoVersionTextDto 【脚本版本内容信息Dto】
     * @return 【脚本版本内容信息（分页）】
     */
    @Override
    public PageInfo<InfoVersionTextDto> selectInfoVersionTextList(InfoVersionTextDto infoVersionTextDto, int pageNum, int pageSize) {
        PageMethod.startPage(pageNum, pageSize);
        List<InfoVersionText> infoVersionTextList = new ArrayList<>();
        if (null != infoVersionTextDto) {
            InfoVersionText infoVersionText =BeanUtils.copy(infoVersionTextDto,InfoVersionText.class);
            infoVersionTextList = infoVersionTextMapper.selectInfoVersionTextList(infoVersionText);
        }
        return PageDataUtil.toDtoPage(infoVersionTextList,InfoVersionTextDto.class);
    }

    /**
     * 新增【脚本版本内容信息】
     *
     * @param infoVersionTextDto 【脚本版本内容信息Dto】
     */
    @Override
    public void insertInfoVersionText(InfoVersionTextDto infoVersionTextDto) {
        InfoVersionText infoVersionText = BeanUtils.copy(infoVersionTextDto,InfoVersionText.class);
        infoVersionTextMapper.insertInfoVersionText(infoVersionText);
    }

    /**
     * 修改【脚本版本内容信息】
     *
     * @param infoVersionTextDto 【脚本版本内容信息Dto】
     */
    @Override
    public void updateInfoVersionText(InfoVersionTextDto infoVersionTextDto) {
        InfoVersionText infoVersionText = BeanUtils.copy(infoVersionTextDto,InfoVersionText.class);
        infoVersionTextMapper.updateInfoVersionText(infoVersionText);
    }

    /**
     * 批量删除【脚本版本内容信息】
     *
     * @param ids 需要删除的【脚本版本内容】主键
     */
    @Override
    public void deleteInfoVersionTextByIds(Long[] ids) {
        infoVersionTextMapper.deleteInfoVersionTextByIds(ids);
    }

    /**
     * 删除【脚本版本内容信息】信息
     *
     * @param id 【脚本版本内容】主键
     * @return 结果
     */
    @Override
    public int deleteInfoVersionTextById(Long id) {
        return infoVersionTextMapper.deleteInfoVersionTextById(id);
    }

    /**
     * 根据脚本id获取脚本内容
     * @param scriptId  脚本id
     * @return 脚本版本内容信息Dto
     */
    @Override
    public InfoVersionTextDto selectInfoVersionTextByScriptId(Long scriptId) {
        InfoVersionText infoVersionText = infoVersionTextMapper.selectInfoVersionTextByScriptId(scriptId);
        return BeanUtils.copy(infoVersionText,InfoVersionTextDto.class);
    }

    /**
     * 根据版本uuid获取脚本内容
     *
     * @param srcScriptUuid 版本uuid
     * @return 脚本版本内容信息Dto
     */
    @Override
    public InfoVersionTextDto selectInfoVersionTextByScriptUuid(String srcScriptUuid) {
        InfoVersionText infoVersionText = infoVersionTextMapper.selectInfoVersionTextByScriptUuid(srcScriptUuid);
        return BeanUtils.copy(infoVersionText,InfoVersionTextDto.class);
    }



    /**
     * 保存各版本的脚本的内容
     *
     * @param scriptInfoDto 脚本信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void createInfoVersionText(ScriptInfoDto scriptInfoDto) {
        InfoVersionText infoVersionText = new InfoVersionText();
        setInfoVersionTextProperties(scriptInfoDto, infoVersionText);
        infoVersionTextMapper.insertInfoVersionText(infoVersionText);
    }

    /**
     * 更新InfoVersionText表信息
     *
     * @param scriptInfoDto 脚本信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateInfoVersionText(ScriptInfoDto scriptInfoDto) {
        InfoVersionText infoVersionText = new InfoVersionText();
        setInfoVersionTextProperties(scriptInfoDto, infoVersionText);
        infoVersionTextMapper.updateInfoVersionTextByScriptUuid(infoVersionText);
        return true;
    }
    /**
     * 封装InfoVersionText
     *
     * @param scriptInfoDto   脚本信息
     * @param infoVersionText InfoVersionText实体
     */
    private void setInfoVersionTextProperties(ScriptInfoDto scriptInfoDto, InfoVersionText infoVersionText) {
        infoVersionText.setContent(scriptInfoDto.getScriptVersionDto().getScriptContentDto().getContent());
        infoVersionText.setSrcScriptUuid(scriptInfoDto.getScriptVersionDto().getSrcScriptUuid());
    }
}
