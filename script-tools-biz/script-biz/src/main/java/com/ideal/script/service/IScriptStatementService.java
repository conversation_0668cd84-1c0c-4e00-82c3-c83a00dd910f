package com.ideal.script.service;

import com.github.pagehelper.PageInfo;

import com.ideal.script.model.dto.StatementDto;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface IScriptStatementService {

    PageInfo<StatementDto> selectScriptStatementPage(StatementDto statementDto, int pageNum, int pageSize);

    /**
     * 导出
     * @param ids 定时任务基础信息表id列表
     * @param response 响应
     */
    void exportExcel(List<Long> ids, HttpServletResponse response);

    void updateScriptStatementByIds(StatementDto statementDto,List<Long> ids);

    StatementDto getScriptStatementByInfoId(Long infoId);

}
