package com.ideal.script.interceptor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ideal.common.util.spring.SpringUtil;
import com.ideal.sc.util.CurrentUserUtil;
import com.ideal.sc.util.HttpClientUtil;
import com.ideal.sc.util.MD5Utils;
import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.config.PsbcProperties;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.filter.CachedRequestWrapper;
import com.ideal.script.model.dto.ItsmTaskOrderNumberDto;
import com.ideal.script.model.dto.ScriptExecAuditDto;
import com.ideal.script.model.entity.Category;
import com.ideal.script.service.impl.builders.MyScriptServiceScripts;
import com.ideal.system.api.IUserInfo;
import com.ideal.system.common.component.model.CurrentUser;
import com.ideal.system.dto.UserInfoApiDto;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.lang.NonNull;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * psbc 任务申请对接itsm单号验证拦截器
 *
 * <AUTHOR>
 */

public class PsbcItsmTicketInterceptor implements HandlerInterceptor {

    private static final Logger logger = LoggerFactory.getLogger(PsbcItsmTicketInterceptor.class);

    private final IUserInfo userInfo;
    private final MyScriptServiceScripts scripts;
    public PsbcItsmTicketInterceptor(IUserInfo userInfo,MyScriptServiceScripts scripts){
        this.userInfo = userInfo;
        this.scripts = scripts;
    }


    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler) throws Exception {
        boolean result = false;
        if (request instanceof CachedRequestWrapper) {
            ScriptExecAuditDto scriptExecAuditDto = JSONObject.parseObject(((CachedRequestWrapper) request).getBodyString(), ScriptExecAuditDto.class);
            ((CachedRequestWrapper) request).modifyBody(JSONObject.toJSONString(scriptExecAuditDto));
            PsbcProperties bean = SpringUtil.getBean(PsbcProperties.class);
            //true为获取单号，false为校验单号
            boolean createFlag = true;
            //单号
            String workOrderNumber = scriptExecAuditDto.getWorkOrderNum();
            //脚本等级
            Integer scriptLevel = scripts.getInfoVersionMapper().selectInfoVersionById(scriptExecAuditDto.getScriptInfoVersionId()).getLevel();
            //如果是白名单，直接返回ture，不拦截
            if(Enums.ScriptLevel.WHITE_SCRIPT.getValue().equals(scriptLevel)){
                return true;
            }
            //如果是风险脚本、且单号为空，调用itsm接口获取工单号
            //如果是风险脚本、且单号不为空，调用itsm接口校验工单号，校验通过放行，否则拦截
            if(StringUtils.isNotBlank(workOrderNumber)){
                createFlag = false;
            }
            //调用http接口，并返回结果
            ItsmTaskOrderNumberDto workOrderNumberDto = checkCreateWorkOrderNumber(bean, scriptExecAuditDto,createFlag);
            if(workOrderNumberDto != null){
                //返回的resCode为-1时，说明手机号验证失败，此时返回标识，前台弹出输入手机号抽屉
                if (createFlag && Enums.ItsmWorkOrderResultCode.ORDER_TELEPHONE_ERROR.getValue().equals(workOrderNumberDto.getResCode())) {
                    throw new ScriptException("error.apply.script.itsm.telephone");
                }
                //返回的resCode为0时为
                if(createFlag && Enums.ItsmWorkOrderResultCode.RIGHT_RESULT_CODE.getValue().equals(workOrderNumberDto.getResCode())){
                    scriptExecAuditDto.setWorkOrderNum(workOrderNumberDto.getResMsg());
                    result = true;
                }else if(!createFlag && Enums.ItsmWorkOrderResultCode.RIGHT_RESULT_CODE.getValue().equals(workOrderNumberDto.getResCode())){
                    result = true;
                }else{
                    logger.error("itsm interfaceApi result error message is : " + workOrderNumberDto.getResMsg());
                    throw new ScriptException("error.apply.script.itsm.task");
                }
            }else{
                logger.error("itsm interfaceApi result is empty");
            }
        } else {
            throw new ScriptException("error request type:" + request.getClass().getName());
        }
        return result;
    }



    private ItsmTaskOrderNumberDto checkCreateWorkOrderNumber(PsbcProperties bean ,ScriptExecAuditDto scriptExecAuditDto,boolean createFlag) throws Exception {
        //请求接口
        try {
            //单号
            String workOrderNumber = scriptExecAuditDto.getWorkOrderNum();
            //请求地址
            String url = "";
            //请求参数
            Object reqData = null;
            //如果是风险脚本、且单号为空，调用itsm接口获取工单号
            Map<String,Object> dataMap = new HashMap<>();
            if(createFlag){
                url = bean.getItsmCreateOrderUrl();
                //组装数据
                Category category = scripts.getCategoryMapper().getCategoryByScriptInfoVersionId(scriptExecAuditDto.getScriptInfoVersionId());
                dataMap.put("autoSysName",category.getName());//系统名称
                dataMap.put("autoTaskName",scriptExecAuditDto.getTaskInfo().getTaskName());//任务名称
                CurrentUser currentUser = CurrentUserUtil.getCurrentUser();
                List<Long> auditUserIdArray = new ArrayList<>();
                auditUserIdArray.add(currentUser.getId());
                List<UserInfoApiDto> userInfoList = userInfo.getUserInfoList(auditUserIdArray);
                //如果前台传递了手机号，则使用前台传递的手机号码，否则使用用户管理的手机号码
                if(StringUtils.isNotBlank(scriptExecAuditDto.getTelephone())){
                    dataMap.put("createrPhone",scriptExecAuditDto.getTelephone());
                }else{
                    if(!userInfoList.isEmpty()){
                        //提单人电话
                        dataMap.put("createrPhone",userInfoList.get(0).getTelephone());
                    }else{
                        throw new ScriptException("error.apply.script.itsm.telephone");
                    }
                }

                reqData = dataMap;
            }else{
                url = bean.getItsmCheckOrderUrl();
                reqData = workOrderNumber;
            }
            if(StringUtils.isBlank(url)){
                logger.error("请求人itsm单号校验、获取接口地址配置是否正确");
                return null;
            }
            //获取itsm接口鉴权key
            String itsmAuthenticationKey = bean.getItsmAuthenticationKey();
            if(StringUtils.isBlank(itsmAuthenticationKey)){
                logger.error("itsm鉴权key未配置");
                throw new ScriptException("error.apply.script.itsm.task");
            }
            // 时间戳
            Long timestamp = System.currentTimeMillis();
            // 请求对象转字符串
            String paramsStr = JSON.toJSONString(reqData);
            // 签名
            String signature = MD5Utils.md5(itsmAuthenticationKey + timestamp + paramsStr);
            logger.info("signature is : " + signature);
            //整理必要的参数
            Map<String,String> headers = new HashMap<>();
            headers.put("Timestamp",String.valueOf(timestamp));
            headers.put("Signature",signature);
            headers.put("Content-Type","application/json");
            headers.put("Content-Length",
                    String.valueOf(paramsStr.getBytes().length));
            headers.put("systemFlag",Enums.ItsmWorkOrderResultCode.SCRIPT_FLAG.getValue());

            if(createFlag){
                logger.debug("即将调用itsm"+Enums.ItsmWorkOrderResultCode.CREATE_FLAG.getValue()+"接口 : header is {}", JSONObject.toJSONString(headers));
                return HttpClientUtil.postByJson(url, headers, dataMap, ItsmTaskOrderNumberDto.class);
            }else{
                logger.debug("即将调用itsm"+Enums.ItsmWorkOrderResultCode.CHECK_FLAG.getValue()+"接口 : header is {}", JSONObject.toJSONString(headers));
                return HttpClientUtil.postByJson(url, headers, paramsStr, ItsmTaskOrderNumberDto.class);
            }

        }catch (Exception e){
            logger.error("get or check itsm interface error : " , e);
            throw new ScriptException("error.apply.script.itsm.task");
        }
    }


}