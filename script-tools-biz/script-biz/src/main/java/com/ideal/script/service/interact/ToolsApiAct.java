package com.ideal.script.service.interact;

import com.ideal.script.mapper.TaskMapper;
import com.ideal.tools.api.IToolsInfo;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 异步调用工具箱方法
 *
 * <AUTHOR>
 */
@Component
public class ToolsApiAct {
    private final TaskMapper taskMapper;
    private final IToolsInfo toolsInfo;

    public ToolsApiAct(TaskMapper taskMapper, IToolsInfo toolsInfo) {
        this.toolsInfo = toolsInfo;
        this.taskMapper = taskMapper;
    }

    @Async
    public void updateTaskStateToTool(Long taskInstanceId, Integer state) {
        //根据taskInstanceId获取任务来源，如果是工具箱，则推送任务状态
        Integer startType = taskMapper.getStartTypeByTaskInstanceId(taskInstanceId);
        if (startType != null && 1 == startType) {
            toolsInfo.updateMonitorRunStatusForScript(taskInstanceId, state);
        }
    }

}
