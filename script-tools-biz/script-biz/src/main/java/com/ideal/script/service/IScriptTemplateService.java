package com.ideal.script.service;

import com.github.pagehelper.PageInfo;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.ScriptTemplateDto;

/**
 * <AUTHOR>
 */
public interface IScriptTemplateService {

    /**
     * 查询【脚本模版】列表
     *
     * @param scriptTemplateDto 【脚本模版实体】
     * @param pageNum 【起始页】
     * @param pageSize 【每页大小】
     * @return 【分页脚本模版】集合
     */
    PageInfo<ScriptTemplateDto> selectScriptTemplateList(ScriptTemplateDto scriptTemplateDto, int pageNum, int pageSize);

    /**
     * 新增【脚本模版】
     *
     * @param scriptTemplateDto 【脚本模版实体】
     * @throws ScriptException 脚本异常
     */
    void insertScriptTemplate(ScriptTemplateDto scriptTemplateDto) throws ScriptException;

    /**
     * 修改【脚本模版信息】
     *
     * @param scriptTemplateDto 【脚本模版实体】
     * @throws ScriptException 脚本异常
     */
    void updateScriptTemplate(ScriptTemplateDto scriptTemplateDto) throws ScriptException;

    /**
     * 批量删除【脚本模版】
     *
     * @param ids 需要删除的【脚本模版】主键集合
     * @throws ScriptException 抛出自定义通知异常
     */
    void deleteScriptTemplateByIds(Long[] ids) throws ScriptException;

    /**
     * 获取脚本模版详细信息
     * @param id    脚本模版主键id
     * @return 脚本模版信息Dto
     */
    ScriptTemplateDto getScriptTemplateDetail(Long id);


}
