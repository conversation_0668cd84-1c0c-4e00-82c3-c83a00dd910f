package com.ideal.script.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.sc.util.CurrentUserUtil;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.ParameterManagerMapper;
import com.ideal.script.model.dto.ParameterManagerDto;
import com.ideal.script.model.entity.ParameterManager;
import com.ideal.script.service.IParameterManagerService;
import com.ideal.system.common.component.model.CurrentUser;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Service业务层处理
 *
 * <AUTHOR>
 */
@Transactional(rollbackFor = Exception.class)
@Service
public class ParameterManagerServiceImpl implements IParameterManagerService {

    private final ParameterManagerMapper parameterManagerMapper;

    public ParameterManagerServiceImpl(ParameterManagerMapper parameterManagerMapper) {
        this.parameterManagerMapper = parameterManagerMapper;
    }

    /**
     * 查询
     *
     * @param id 主键
     * @return 
     */
    @Override
    public ParameterManagerDto selectParameterManagerById(Long id) {
        ParameterManager parameterManager = parameterManagerMapper.selectParameterManagerById(id);
        return BeanUtils.copy(parameterManager, ParameterManagerDto.class);
    }

    /**
     * 查询列表
     *
     * @param parameterManagerDto 
     * @return 
     */
    @Override
    public PageInfo<ParameterManagerDto> selectParameterManagerList(ParameterManagerDto parameterManagerDto, int pageNum, int pageSize) {
        PageMethod.startPage(pageNum, pageSize);
        List<ParameterManager> parameterManagerList = new ArrayList<>();
        if (null != parameterManagerDto) {
            ParameterManager parameterManager = BeanUtils.copy(parameterManagerDto, ParameterManager.class);
            parameterManagerList = parameterManagerMapper.selectParameterManagerList(parameterManager);
        }
        return PageDataUtil.toDtoPage(parameterManagerList, ParameterManagerDto.class);
    }

    /**
     * 新增
     *
     * @param parameterManagerDto 
     */
    @Override
    public void insertParameterManager(ParameterManagerDto parameterManagerDto) throws ScriptException {
        ParameterManager parameterManager = BeanUtils.copy(parameterManagerDto, ParameterManager.class);
        //校验重名
        checkDuplicateName(parameterManagerDto);
        parameterManagerMapper.insertParameterManager(parameterManager);
    }

    /**
     * 修改
     *
     * @param parameterManagerDto 
     */
    @Override
    public void updateParameterManager(ParameterManagerDto parameterManagerDto) throws ScriptException {
        ParameterManager parameterManager = BeanUtils.copy(parameterManagerDto, ParameterManager.class);
        //校验重名
        checkDuplicateName(parameterManagerDto);
        CurrentUser currentUser = CurrentUserUtil.getCurrentUser();
        parameterManager.setUpdatorId(currentUser.getId());
        parameterManager.setUpdatorName(currentUser.getFullName());
        parameterManagerMapper.updateParameterManager(parameterManager);
    }

    /**
     * 批量删除
     *
     * @param ids 需要删除的主键
     */
    @Override
    public void deleteParameterManagerByIds(Long[] ids) throws ScriptException {
        // 检查ID是否存在
        List<Long> existIds = parameterManagerMapper.checkIdsExist(ids);
        if (existIds.isEmpty()) {
            throw new ScriptException("enum.parameter.not.exist");
        }
        
        if (ids.length > existIds.size()) {
            // 找出不存在的ID
            String notExistIdsStr = Arrays.stream(ids)
                    .filter(id -> !existIds.contains(id))
                    .map(Object::toString)
                    .collect(Collectors.joining(","));
            
            if (!notExistIdsStr.isEmpty()) {
                throw new ScriptException("ids:" + notExistIdsStr + "不存在");
            }
        }
        parameterManagerMapper.deleteParameterManagerByIds(ids);
    }

    /**
     * 删除信息
     *
     * @param id 主键
     * @return 结果
     */
    @Override
    public int deleteParameterManagerById(Long id) {
        return parameterManagerMapper.deleteParameterManagerById(id);
    }

    @Override
    public List<ParameterManagerDto> selectParameterManagerForScriptEdit() {
        List<ParameterManager> parameterManagerList = parameterManagerMapper.selectParameterManagerList(new ParameterManager());
        return BeanUtils.copy(parameterManagerList,ParameterManagerDto.class);
    }

    private void checkDuplicateName(ParameterManagerDto parameterManagerDto) throws ScriptException {
        ParameterManager parameterManager = new ParameterManager();
        parameterManager.setParamName(parameterManagerDto.getParamName());
        List<ParameterManager> parameterManagers = parameterManagerMapper.selectSaveNameList(parameterManager);
        boolean isSingleParameterManager = parameterManagers.size() == 1 && !parameterManagers.get(0).getId().equals(parameterManagerDto.getId());
        if( isSingleParameterManager || parameterManagers.size() > 1 ){
            throw new ScriptException("duplicate.paramName");
        }
    }

    @Override
    public Boolean validParamterCheckExist(String paramName) {
        return parameterManagerMapper.validParamterCheckExist(paramName);
    }

    @Override
    public ParameterManagerDto selectParameterManagerByName(String paramName) {
        ParameterManagerDto parameterManagerDto = null;
        List<ParameterManager> parameterManagerList = parameterManagerMapper.selectParameterManagerByName(paramName);
        if(null!=parameterManagerList && !parameterManagerList.isEmpty()){
            parameterManagerDto = BeanUtils.copy(parameterManagerList.get(0),ParameterManagerDto.class);
        }
        return parameterManagerDto;
    }
}
