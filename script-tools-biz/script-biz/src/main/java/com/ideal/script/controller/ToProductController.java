package com.ideal.script.controller;


import com.github.pagehelper.PageInfo;
import com.ideal.audit.producer.annotation.Auditable;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.permission.MenuPermitConstant;
import com.ideal.script.dto.ItsmPublishScriptAuditResultDto;
import com.ideal.script.dto.ItsmPublishScriptDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.ToProductDto;
import com.ideal.script.model.dto.ToProductQueryDto;
import com.ideal.script.model.entity.ItsmProductAttachment;
import com.ideal.script.service.IToProductService;
import com.ideal.system.common.component.aop.MethodPermission;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;


/**
 * 服务投产
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("${app.script-tools-url:}/script/product")
@MethodPermission(MenuPermitConstant.SERVICE_ROLLOUT_PER)
public class ToProductController {

    private final IToProductService toProductService;

    public ToProductController(IToProductService toProductService) {
        this.toProductService = toProductService;
    }

    /**
     * 查询列表
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @PostMapping("/list")
    public R<PageInfo<ToProductDto>> list(@RequestBody TableQueryDto<ToProductQueryDto> tableQueryDto) {
        PageInfo<ToProductDto> list = toProductService.selectToProductList(
                tableQueryDto.getQueryParam(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
        return R.ok(list);
    }

    /**
     * itsm投产提交
     * @tags 中信itsm投产
     * @param itsmPublishScriptDto 参数
     */
    @PostMapping("/publishItsm")
    @Auditable("服务投产|itsm投产")
    public R<Object> publishItsm(@RequestBody ItsmPublishScriptDto itsmPublishScriptDto) throws Exception {
        toProductService.publishItsm(itsmPublishScriptDto);
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE,"","提交成功");
    }

    /**
     * itsm回调开发测试环境审批
     * @tags 中信itsm投产
     * @param itsmPublishScriptAuditResultDto 参数
     */
    @PostMapping("/testEnvironmentAuditResult")
    @Auditable("服务投产|itsm回调开发测试环境审批")
    public R<Object> testEnvironmentAuditResult(@RequestBody ItsmPublishScriptAuditResultDto itsmPublishScriptAuditResultDto) throws ScriptException {
        toProductService.testEnvironmentAuditResult(itsmPublishScriptAuditResultDto);
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE,"","审批成功");
    }

    /**
     * itsm回调生产环境审批
     * @tags 中信itsm投产
     * @param itsmPublishScriptAuditResultDto 参数
     */
    @PostMapping("/productEnvironmentAuditResult")
    @Auditable("服务投产|itsm回调生产环境审批")
    public R<Object> productEnvironmentAuditResult(@RequestBody ItsmPublishScriptAuditResultDto itsmPublishScriptAuditResultDto) throws ScriptException, IOException {
        toProductService.productEnvironmentAuditResult(itsmPublishScriptAuditResultDto);
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE,"","审批成功");
    }

    /**
     * 服务投产获取投产子数据信息
     * @param itsmPublishScriptDto 请求参数
     */
    @PostMapping("/getChildrenDataList")
    public R<Object> getChildrenDataList(@RequestBody ItsmPublishScriptDto itsmPublishScriptDto) throws ScriptException {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, toProductService.getChildrenDataList(itsmPublishScriptDto), Constants.LIST_SUCCESS);
    }

    /**
     * 服务投产获取子数据详情
     * @tags 中信itsm投产
     * @param itsmPublishScriptDto 请求参数
     */
    @PostMapping("/itsmProductDetails")
    public R<Object> itsmProductDetails(@RequestBody ItsmPublishScriptDto itsmPublishScriptDto) throws ScriptException {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, toProductService.itsmProductDetails(itsmPublishScriptDto), Constants.LIST_SUCCESS);
    }

    /**
     * 制品晋级接口
     * @tags 中信itsm投产
     * @param itsmPublishScriptDto 请求参数
     */
    @PostMapping("/promotionProducts")
    @Auditable("服务投产|制品晋级")
    public R<Object> promotionProducts(@RequestBody ItsmPublishScriptDto itsmPublishScriptDto) throws ScriptException {
        toProductService.promotionProducts(itsmPublishScriptDto);
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE,"","晋级成功");
    }

    /**
     * 生产环境下载附件
     * @param id 附件id
     */
    @PostMapping("/downloadAttachment")
    @Auditable("服务投产|生产环境附件下载")
    public void downloadAttachment(@RequestBody Long id, HttpServletResponse response) {
        ItsmProductAttachment itsmProductAttachment = toProductService.selectAttachmentById(id);
        response.reset();
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-disposition", "attachment; filename=" + URLEncoder.encode(itsmProductAttachment.getAttachmentName(), "UTF-8"));
            outputStream.write(itsmProductAttachment.getAttachmentContent());
        } catch (IOException e) {
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
        }
    }

}
