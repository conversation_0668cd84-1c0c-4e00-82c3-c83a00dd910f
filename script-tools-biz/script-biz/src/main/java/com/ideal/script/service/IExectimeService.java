package com.ideal.script.service;

import com.ideal.script.model.dto.ExectimeDto;
import com.github.pagehelper.PageInfo;
/**
 * 脚本执行次数统计Service接口
 * 
 * <AUTHOR>
 */
 public interface IExectimeService
{
    /**
     * 查询脚本执行次数统计
     * 
     * @param id 脚本执行次数统计主键
     * @return 脚本执行次数统计
     */
     ExectimeDto selectExectimeById(Long id);

    /**
     * 查询脚本执行次数统计列表
     * @param pageNum 当前页
     * @param pageSize 每页条数
     * @param exectimeDto 脚本执行次数统计
     * @return 脚本执行次数统计集合
     */
     PageInfo<ExectimeDto> selectExectimeList(ExectimeDto exectimeDto, int pageNum, int pageSize);

    /**
     * 新增脚本执行次数统计
     * 
     * @param exectimeDto 脚本执行次数统计
     * @return 结果
     */
     int insertExectime(ExectimeDto exectimeDto);

    /**
     * 修改脚本执行次数统计
     * 
     * @param exectimeDto 脚本执行次数统计
     * @return 结果
     */
     int updateExectime(ExectimeDto exectimeDto);

    /**
     * 批量删除脚本执行次数统计
     * 
     * @param ids 需要删除的脚本执行次数统计主键集合
     * @return 结果
     */
     int deleteExectimeByIds(Long[] ids);

    /**
     * 删除脚本执行次数统计信息
     * 
     * @param id 脚本执行次数统计主键
     * @return 结果
     */
     int deleteExectimeById(Long id);

     /**
      * 更新执行次数
      * @param status        脚本执行状态
      * @param type
      *     1：agent实例执行成功，总次数和成功次数都更新+1
      *     2：agent执行结果异常或者agent未连接上，仅更新总次数 +1
      *     3：点击任务执行时候只更新taskcount使用次数
      * @param srcScriptUuid ieai_script_info_version表的isrc_script_uuid
      */
     
    void updateScriptExecTime(Integer status, String srcScriptUuid,Integer type);

    /**
     * 查询脚本使用次数、成功数、总数、成功率
     * @param srcScriptUuid 脚本版本Id
     * @return ExectimeDto
     */
    ExectimeDto getTotalAndSuccessRate(String srcScriptUuid);
}
