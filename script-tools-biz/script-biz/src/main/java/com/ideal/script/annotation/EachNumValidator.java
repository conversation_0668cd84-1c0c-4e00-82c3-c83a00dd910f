package com.ideal.script.annotation;

import com.ideal.common.util.spring.SpringUtil;
import com.ideal.script.config.ScriptBusinessConfig;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * 验证脚本服务化任务agent并发数
 *
 * <AUTHOR>
 */
public class EachNumValidator implements ConstraintValidator<EachNum, Integer> {

    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext context) {
        if (value == null) {
            return true;
        }
        if (value <= 0) {
            context.disableDefaultConstraintViolation();
            context.buildConstraintViolationWithTemplate("并发数不能小于1").addConstraintViolation();
            return false;
        }

        ScriptBusinessConfig scriptBusinessConfig = SpringUtil.getBean(ScriptBusinessConfig.class);
        int eachNumLimit = scriptBusinessConfig.getTaskEachNumLimit();
        if (value > eachNumLimit) {
            context.disableDefaultConstraintViolation();
            context.buildConstraintViolationWithTemplate(context.getDefaultConstraintMessageTemplate() + eachNumLimit).addConstraintViolation();
            return false;
        }
        return true;
    }
}
