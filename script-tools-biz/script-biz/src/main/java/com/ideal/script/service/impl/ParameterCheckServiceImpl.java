package com.ideal.script.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.sc.util.CurrentUserUtil;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.ParameterCheckMapper;
import com.ideal.script.model.dto.ParameterCheckDto;
import com.ideal.script.model.entity.ParameterCheck;
import com.ideal.script.service.IParameterCheckService;
import com.ideal.system.common.component.model.CurrentUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 参数验证
 * <AUTHOR>
 */
@Transactional(rollbackFor = Exception.class)
@Service
public class ParameterCheckServiceImpl implements IParameterCheckService {
    private static final Logger logger = LoggerFactory.getLogger(ParameterCheckServiceImpl.class);
    private final ParameterCheckMapper parameterCheckMapper;

    public ParameterCheckServiceImpl(ParameterCheckMapper parameterCheckMapper) {
        this.parameterCheckMapper = parameterCheckMapper;
    }

    /**
     * 查询参数校验规则
     *
     * @param id 规则id
     * @return 参数验证规则dto
     */
    @Override
    public ParameterCheckDto selectParameterCheckById(Long id) {
        ParameterCheck parameterCheck = parameterCheckMapper.selectParameterCheckById(id);
        return BeanUtils.copy(parameterCheck,ParameterCheckDto.class);
    }

    /**
     * 查询参数校验规则
     *
     * @param ruleName 规则名
     * @return ParameterCheckDto
     */
    @Override
    public ParameterCheckDto selectParameterCheckByName(String ruleName) {
        ParameterCheckDto parameterCheckDto = null;
        List<ParameterCheck> parameterCheckList = parameterCheckMapper.selectParameterCheckByName(ruleName);
        if(null!=parameterCheckList && !parameterCheckList.isEmpty()){
            parameterCheckDto = BeanUtils.copy(parameterCheckList.get(0),ParameterCheckDto.class);
        }
        return parameterCheckDto;
    }

    /**
     * 新增参数验证
     * @param parameterCheckDto 参数验证规则dto
     */
    @Override
    public void insertParameterCheck(ParameterCheckDto parameterCheckDto) throws ScriptException {
        ParameterCheck parameterCheck = BeanUtils.copy(parameterCheckDto,ParameterCheck.class);
        //判断参数验证名称重复方法
        checkDuplicateName(parameterCheckDto);
        parameterCheckMapper.insertParameterCheck(parameterCheck);
        parameterCheckDto.setId(parameterCheck.getId());
    }

    /**
     * 修改参数验证
     * @param parameterCheckDto 参数验证规则dto
     */
    @Override
    public void updateParameterCheck(ParameterCheckDto parameterCheckDto) throws ScriptException {
        ParameterCheck parameterCheck = BeanUtils.copy(parameterCheckDto,ParameterCheck.class);
        //判断参数验证名称重复方法
        checkDuplicateName(parameterCheckDto);
        CurrentUser currentUser = CurrentUserUtil.getCurrentUser();
        parameterCheck.setUpdatorId(currentUser.getId());
        parameterCheck.setUpdatorName(currentUser.getFullName());
        parameterCheckMapper.updateParameterCheck(parameterCheck);
    }

    /**
     * 查询参数验证信息
     * @param parameterCheckDto 参数验证规则dto
     * @param pageNum   起始页
     * @param pageSize  每页大小
     * @return  pageInfo
     */
    @Override
    public PageInfo<ParameterCheckDto> selectParameterCheckList(ParameterCheckDto parameterCheckDto, Integer pageNum, Integer pageSize) {
        PageMethod.startPage(pageNum, pageSize);
        List<ParameterCheck> parameterCheckList = new ArrayList<>();
        if (null != parameterCheckDto) {
            ParameterCheck parameterCheck = BeanUtils.copy(parameterCheckDto,ParameterCheck.class);
            parameterCheckList = parameterCheckMapper.selectParameterCheckList(parameterCheck);
        }
        return PageDataUtil.toDtoPage(parameterCheckList,ParameterCheckDto.class);
    }

    /**
     * 删除参数验证
     * @param ids id数组
     */
    @Override
    public void deleteParameterCheckByIds(Long[] ids) {
        parameterCheckMapper.deleteParameterCheckByIds(ids);
    }

    /**
     * 验证规则是否已存在
     *
     * @param ruleName 规则名
     * @return {@link Boolean }
     */
    @Override
    public Boolean validParamterCheckExist(String ruleName) {
        return parameterCheckMapper.validParamterCheckExist(ruleName);
    }

    /**
     * 验证参数验证规则是否有同名数据
     * @param parameterCheckDto 参数验证规则dto
     * @throws ScriptException  自定义脚本异常
     */
    private void checkDuplicateName(ParameterCheckDto parameterCheckDto) throws ScriptException {
        List<ParameterCheck> parameterChecks = parameterCheckMapper.selectParameterCheckByName(parameterCheckDto.getRuleName());
        if(!parameterChecks.isEmpty()){
            if(parameterChecks.size() == 1 && (!parameterChecks.get(0).getId().equals(parameterCheckDto.getId()))) {
                throw new ScriptException("duplicate.ruleName");
            }else if(parameterChecks.size() != 1 ){
                logger.info("paramCheckRule data is error");
                throw new ScriptException("duplicate.ruleName");
            }
        }
    }
}
