package com.ideal.script.service;

import com.github.pagehelper.PageInfo;
import com.ideal.script.dto.AttachmentUploadDto;
import com.ideal.script.dto.ScriptInfoDto;
import com.ideal.script.dto.ScriptInfoQueryDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.AgentInfoDto;
import com.ideal.script.model.dto.ScriptExecAuditDto;
import com.ideal.script.model.dto.ScriptTaskApplyResDto;
import com.ideal.script.model.dto.StartCommonTaskDto;
import com.ideal.script.model.dto.TaskApplyQueryDto;
import com.ideal.script.model.dto.TaskAttachmentDto;
import com.ideal.script.model.dto.TaskStartDto;
import com.ideal.script.model.dto.TaskTemplateDto;
import com.ideal.script.model.dto.interaction.agent.SystemComputerGroupDto;
import com.ideal.system.common.component.model.CurrentUser;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 常用/克隆任务Service接口
 *
 * <AUTHOR>
 */

public interface ITaskTemplateService {

    /**
     * 创建克隆任务
     * @param taskStartDto 克隆任务对象（查询使用）
     * @param user 当前系统用户
     * @throws ScriptException 脚本服务化相关异常
     */
    void createCloneTask(TaskStartDto taskStartDto, CurrentUser user) throws ScriptException;

    /**
     * 任务申请页面常用任务保存
     * @param scriptExecAuditDto 任务参数
     */
    void createCloneTaskFromTaskApply(ScriptExecAuditDto scriptExecAuditDto, CurrentUser user) throws ScriptException;

    /**
     * 获取克隆任务列表
     * @param taskApplyQueryDto 查询条件dto
     * @param pageNum 页码
     * @param pageSize 分页大小
     * @param user 当前系统登录用户
     * @return 返回克隆任务数据分页信息
     */
    PageInfo<TaskTemplateDto> listCloneTask(TaskApplyQueryDto taskApplyQueryDto, Integer pageNum, Integer pageSize,CurrentUser user);

    /**
     * 获取脚本基本信息
     * @param scriptInfoQueryDto 请求参数
     * @return 返回脚本基本信息
     */
    ScriptInfoDto getScriptTemplateDetail(ScriptInfoQueryDto scriptInfoQueryDto) throws ScriptException;

    /**
     * 根据taskId获取常用任务/克隆任务的agent数据
     * @param taskId 任务id
     * @return agent集合
     */
    List<AgentInfoDto> getAllChoseAgent(Long taskId);

    /**
     * 根据taskId获取常用任务/克隆任务的设备组数据
     * @param taskId 任务id
     * @return agent集合
     */
    List<SystemComputerGroupDto> getAllChoseGroup(Long taskId);

    /**
     * 发起常用/克隆任务
     * @param scriptExecAuditDto 执行参数
     * @return 执行结果状态 任务id和双人复核服务id
     */
    Map<String,Long> execAuditTemplateTask(ScriptExecAuditDto scriptExecAuditDto) throws ScriptException;


    /**
     * 保存常用/克隆任务
     * @param scriptExecAuditDto 执行参数
     * @return 执行结果状态
     */
    boolean updateAuditTemplateTask(ScriptExecAuditDto scriptExecAuditDto) throws ScriptException;

    /**
     * 根据taskId删除任务
     * @param taskId 任务id
     * @throws ScriptException 脚本服务化异常
     */
    void deleteTaskTemplate(Long taskId) throws ScriptException;

    /**
     * 下载附件
     * @param id 附件id
     * @return 附件信息
     */
    TaskAttachmentDto selectAttachmentById(Long id);

    /**
     * 上传附件
     *
     * @param taskAttachmentDto  文件
     * @return      dto
     * @throws IOException exception
     */
    TaskAttachmentDto uploadAttachment(TaskAttachmentDto taskAttachmentDto) throws IOException, ScriptException;

    /**
     * 常用任务/克隆任务删除临时附件
     * @param id 附件id
     */
    void deleteAttachmentTemplate(Long id);

    /**
     * 根据常用任务id查询当前常用任务的附件
     * @param scriptTaskId 任务id
     * @return 附件集合
     */
    List<AttachmentUploadDto> getTaskTemplateAttachment(Long scriptTaskId);

    /**
     * 根据常用任务id启动常用任务
     *
     * @param startCommonTaskDto 启动常用任务参数dto
     * @param user               当前用户
     * @return ScriptTaskApplyResDto 任务id和双人复核服务id
     * @throws ScriptException 脚本服务化异常
     */
    ScriptTaskApplyResDto startCommonTask(StartCommonTaskDto startCommonTaskDto, CurrentUser user) throws ScriptException;
}
