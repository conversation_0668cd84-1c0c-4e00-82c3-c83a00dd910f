package com.ideal.script.service.consumer;

import com.alibaba.fastjson.JSON;
import com.ideal.message.center.ISubscriber;
import com.ideal.script.service.resulthandler.IScriptReferrerInfoHandlerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 监听mq中script-referrer-info主题中的消息
 *
 * <AUTHOR>
 */
@Component
public class ScriptReferrerInfoHandler implements ISubscriber {
    private final Logger logger = LoggerFactory.getLogger(ScriptReferrerInfoHandler.class);
    private final IScriptReferrerInfoHandlerService scriptReferrerInfoHandlerService;


    public ScriptReferrerInfoHandler(IScriptReferrerInfoHandlerService scriptReferrerInfoHandlerService) {
        this.scriptReferrerInfoHandlerService = scriptReferrerInfoHandlerService;
    }

    /**
     * 监听mq中script-referrer-info主题中的消息
     *
     * @param message 消息
     */
    @Override
    public void notice(Object message) {
        try {
            if (message instanceof String) {
                scriptReferrerInfoHandlerService.scriptReferrerInfoHandler((String)message);
            }else{
                scriptReferrerInfoHandlerService.scriptReferrerInfoHandler(JSON.toJSONString(message));
            }

        } catch (Exception e) {
            logger.error("Failed to handle the result from script-referrer-info topic.The received message is:{}",message, e);
        }
    }
}
