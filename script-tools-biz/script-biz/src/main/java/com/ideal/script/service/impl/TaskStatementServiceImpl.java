package com.ideal.script.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.script.mapper.TaskStatementMapper;
import com.ideal.script.model.dto.TaskStatementDto;
import com.ideal.script.model.bean.TaskStatementBean;
import com.ideal.script.service.ITaskStatementService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Transactional(rollbackFor = Exception.class)
@Service
public class TaskStatementServiceImpl implements ITaskStatementService {

    private final TaskStatementMapper taskStatementMapper;
    private final CategoryServiceImpl categoryService;

    public TaskStatementServiceImpl(TaskStatementMapper taskStatementMapper, CategoryServiceImpl categoryService) {
        this.taskStatementMapper = taskStatementMapper;
        this.categoryService = categoryService;
    }

    /**
     * 分页查询历史任务报表数据
     * @param taskStatementDto  任务历史报表dto
     * @param pageNum   页码
     * @param pageSize  每页大小
     * @return  结果
     */
    @Override
    public PageInfo<TaskStatementDto> selectTaskStatementPage(TaskStatementDto taskStatementDto, Integer pageNum, Integer pageSize) {
        TaskStatementBean taskStatementBean = BeanUtils.copy(taskStatementDto, TaskStatementBean.class);
        buildStatementBean(taskStatementDto, taskStatementBean);
        PageMethod.startPage(pageNum, pageSize);
        List<TaskStatementBean> taskStatementBeanList = taskStatementMapper.selectTaskStatementPage(taskStatementBean);
        for(TaskStatementBean taskStatementBean1 : taskStatementBeanList){
            String key = taskStatementBean1.getTaskId()+"@"+taskStatementBean1.getInstanceId()+"@"+taskStatementBean1.getAgentIp()+"@"+taskStatementBean1.getAgentPort();
            taskStatementBean1.setKey(key);
        }
        return PageDataUtil.toDtoPage(taskStatementBeanList, TaskStatementDto.class);
    }

    private void buildStatementBean(TaskStatementDto taskStatementDto, TaskStatementBean taskStatementBean){
        //获取分类完整路径
        String categoryPath = categoryService.buildCategoryFullPath(taskStatementDto.getCategoryId());

        //针对%和_这种拼接进行处理
        String escapedLikeCategoryPath = categoryService.handleCategoryPath(categoryPath);

        taskStatementBean.setCategoryPath(categoryPath);
        taskStatementBean.setEscapedLikeCategoryPath(escapedLikeCategoryPath);
    }


}
