package com.ideal.script.service;

import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.ScriptExecAuditDto;
import com.ideal.script.model.dto.TaskDto;

public interface AuditSource {

    void preHandle(ScriptExecAuditDto scriptExecAuditDto);

    Long getRelationId(ScriptExecAuditDto scriptExecAuditDto, TaskDto taskInfo, String srcScriptUuid) throws ScriptException;

    void bindTaskId(ScriptExecAuditDto scriptExecAuditDto);

    boolean isInWhiteList(ScriptExecAuditDto scriptExecAuditDto);

    void saveOrUpdateTask(ScriptExecAuditDto scriptExecAuditDto, TaskDto taskDto) throws ScriptException;

    void preHandleAttachment(Long taskId);
}
