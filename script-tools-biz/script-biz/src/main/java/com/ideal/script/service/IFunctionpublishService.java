package com.ideal.script.service;

import com.ideal.script.model.dto.FunctionpublishDto;
import com.github.pagehelper.PageInfo;
import com.ideal.script.model.dto.VarAndFuncForEditDto;

/**
 * 函数库基础发布Service接口
 * 
 * <AUTHOR>
 */
 public interface IFunctionpublishService
{
    /**
     * 查询函数库基础发布
     * 
     * @param id 函数库基础发布主键
     * @return 函数库基础发布
     */
     FunctionpublishDto selectFunctionpublishById(Long id);

    /**
     * 查询函数库基础发布列表
     * 
     * @param functionpublishDto 函数库基础发布
     * @param pageNum   起始页
     * @param pageSize  每页大小
     * @return 函数库基础发布集合
     */
     PageInfo<FunctionpublishDto> selectFunctionpublishList(FunctionpublishDto functionpublishDto, int pageNum, int pageSize);

    /**
     * 新增函数库基础发布
     *
     * @param functionpublishDto 函数库基础发布
     */
     void insertFunctionpublish(FunctionpublishDto functionpublishDto);

    /**
     * 修改函数库基础发布
     *
     * @param functionpublishDto 函数库基础发布
     */
     void updateFunctionpublish(FunctionpublishDto functionpublishDto);

    /**
     * 批量删除函数库基础发布
     *
     * @param ids 需要删除的函数库基础发布主键集合
     */
     void deleteFunctionpublishByIds(Long[] ids);

    /**
     * 删除函数库基础发布信息
     * 
     * @param id 函数库基础发布主键
     * @return 结果
     */
     int deleteFunctionpublishById(Long id);

    /**
     * 查询函数库基础发布列表
     *
     * @param varAndFuncForEditDto 函数库基础发布
     * @param pageNum   起始页
     * @param pageSize  每页大小
     * @return 函数库基础发布集合
     */
    PageInfo<FunctionpublishDto> selectFunctionpublishListForScriptEdit(VarAndFuncForEditDto varAndFuncForEditDto, int pageNum, int pageSize);
}
