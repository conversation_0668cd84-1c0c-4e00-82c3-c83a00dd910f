package com.ideal.script.service;

import com.github.pagehelper.PageInfo;
import com.ideal.script.model.dto.ScriptVersionShareDto;
import com.ideal.script.model.dto.interaction.agent.UserInfoDto;
import com.ideal.system.dto.RoleApiDto;

import java.util.List;

/**
 * 【脚本共享】Service接口
 *
 * <AUTHOR>
 */
public interface IScriptVersionShareService {

    /**
     * 新增
     *
     * @param scriptVersionShareDto 【共享信息】
     */
    void insertScriptVersionShare(List<ScriptVersionShareDto> scriptVersionShareDto);

    /**
     * 获取共享数据
     * @param scriptVersionShareDto 参数
     * @return 共享数据
     */
    PageInfo<ScriptVersionShareDto> selectShareScriptData(ScriptVersionShareDto scriptVersionShareDto,Integer pageNum,Integer pageSize);

    /**
     * 获取本部门及父部门外所有部门的用户
     * @param userId 用户id
     * @param pageNum 页码
     * @param pageSize 分页大小
     * @return 用户数据
     */
    PageInfo<UserInfoDto> getShareUser(Long userId, Integer pageNum, Integer pageSize, ScriptVersionShareDto scriptVersionShareDto);


    /**
     * 获取未被共享过的角色
     * @param pageNum 分页
     * @param pageSize 页码
     * @param scriptVersionShareDto 入参dto
     * @return 未共享的角色
     */
    PageInfo<RoleApiDto> getNotShareRoles(Integer pageNum, Integer pageSize, ScriptVersionShareDto scriptVersionShareDto);

    /**
     * 删除
     *
     * @param ids 【共享表id】主键
     */
    void deleteScriptVersionShareByIds(Long [] ids);

    /**
     * 获取共享的用户id或者部门id
     * @param scriptVersionId 脚本版本id
     * @param shareType 共享类型 0共享用户，1共享部门，2共享所有人
     * @return 返回共享的用户id或者部门id
     */
    List<String> getObjectIdList(Long scriptVersionId,Short shareType);

}
