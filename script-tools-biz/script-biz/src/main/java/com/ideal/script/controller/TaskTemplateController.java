package com.ideal.script.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.audit.producer.annotation.Auditable;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.common.util.spring.MessageUtil;
import com.ideal.sc.util.ValidationUtils;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.permission.MenuPermitConstant;
import com.ideal.script.common.constant.permission.TaskApplyBtnPermitConstant;
import com.ideal.script.common.constant.permission.TemplateTaskBtnPermitConstant;
import com.ideal.script.common.validation.ExecAuditCreate;
import com.ideal.script.dto.ScriptInfoQueryDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.ScriptExecAuditDto;
import com.ideal.script.model.dto.ScriptTaskApplyResDto;
import com.ideal.script.model.dto.StartCommonTaskDto;
import com.ideal.script.model.dto.TaskApplyQueryDto;
import com.ideal.script.model.dto.TaskAttachmentDto;
import com.ideal.script.model.dto.TaskStartDto;
import com.ideal.script.model.dto.TaskTemplateDto;
import com.ideal.script.service.ITaskTemplateService;
import com.ideal.system.common.component.aop.MethodPermission;
import com.ideal.system.common.component.model.CurrentUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;

import static com.ideal.sc.util.CurrentUserUtil.getCurrentUser;

/**
 * 常用任务
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("${app.script-tools-url:}/taskTemplate")
@MethodPermission(MenuPermitConstant.TEMPLATE_TASK_PER)
public class TaskTemplateController {

    private static final Logger logger = LoggerFactory.getLogger(TaskTemplateController.class);

    private final ITaskTemplateService taskTemplateService;

    public TaskTemplateController(ITaskTemplateService taskTemplateService) {
        this.taskTemplateService = taskTemplateService;
    }


    /**
     * 创建克隆任务
     * @param taskStartDto 任务对象dto
     * @return 返回标识
     */
    @PostMapping("/createCloneTask")
    @Auditable("常用任务|常用克隆任务提交")
    @MethodPermission(TaskApplyBtnPermitConstant.CLONE_TASK_PER)
    public R<Object> createCloneTask(@RequestBody TaskStartDto taskStartDto) throws ScriptException{
        CurrentUser user = getCurrentUser();
        taskTemplateService.createCloneTask(taskStartDto, user);
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "", "clone.script.task.success");
    }

    /**
     * 任务申请页面常用任务保存
     * @param scriptExecAuditDto 任务参数
     * @return 保存结果是否成功
     */
    @PostMapping("/createCloneTaskFromTaskApply")
    @MethodPermission(TaskApplyBtnPermitConstant.CLONE_TASK_PER)
    public R<Object> createCloneTaskFromTaskApply(@RequestBody ScriptExecAuditDto scriptExecAuditDto) throws ScriptException {
        CurrentUser user = getCurrentUser();
        taskTemplateService.createCloneTaskFromTaskApply(scriptExecAuditDto, user);
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "", "clone.script.task.success");
    }

    /**
     * 查询克隆任务数据
     * @tags 对外API接口
     * @param tableQueryDTO 查询条件对象dto
     * @return 返回克隆任务数据
     */
    @PostMapping("/listCloneTask")
    @MethodPermission(MenuPermitConstant.TEMPLATE_TASK_OR_TASK_APPLY_PER)
    public R<PageInfo<TaskTemplateDto>> listTaskApply(@RequestBody TableQueryDto<TaskApplyQueryDto> tableQueryDTO) {
        CurrentUser currentUser = getCurrentUser();
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, taskTemplateService.listCloneTask(tableQueryDTO.getQueryParam(), tableQueryDTO.getPageNum(),
                tableQueryDTO.getPageSize(), currentUser), Constants.LIST_SUCCESS);
    }

    /**
     * 获取常用任务/克隆任务详情
     * @tags 对外API接口
     * @return 返回脚本信息
     */
    @PostMapping("/getScriptTemplateDetail")
    @MethodPermission(MenuPermitConstant.TEMPLATE_TASK_OR_MY_SCRIPT_PER)
    public R<Object> getScriptDetail(@RequestBody ScriptInfoQueryDto scriptInfoQueryDto) {
        try {
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, taskTemplateService.getScriptTemplateDetail(scriptInfoQueryDto), "查询成功");
        } catch (ScriptException e) {
            logger.error("getScriptDetail error",e);
            return ValidationUtils.customFailResult("script",e.getMessage());
        }
    }

    /**
     * 查询创建常用任务时选择的agent数据
     * @tags 对外API接口
     * @param taskStartDto 查询条件
     * @return PageInfo<AgentInfoDto>
     */
    @PostMapping("/queryTemplateAgentInfo")
    @MethodPermission(MenuPermitConstant.TEMPLATE_TASK_OR_MY_SCRIPT_PER)
    public R<Object> queryAgentInfoGroupRole(@RequestBody TaskStartDto taskStartDto) {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, taskTemplateService.getAllChoseAgent(taskStartDto.getScriptTaskId()), "查询成功");
    }

    /**
     * 查询创建常用任务时选择的设备组数据
     * @tags 对外API接口
     * @param taskStartDto 查询条件
     */
    @PostMapping("/queryTemplateGroupInfo")
    @MethodPermission(MenuPermitConstant.TEMPLATE_TASK_OR_MY_SCRIPT_PER)
    public R<Object> queryTemplateGroupInfo(@RequestBody TaskStartDto taskStartDto) {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, taskTemplateService.getAllChoseGroup(taskStartDto.getScriptTaskId()), "查询成功");
    }

    /**
     * 发起常用/克隆任务
     * @tags 对外API接口
     * @param scriptExecAuditDto 执行参数
     * @return 执行结果状态
     */
    @PostMapping("/execAuditTemplateTask")
    @Auditable("常用任务|常用克隆任务提交")
    @MethodPermission(TemplateTaskBtnPermitConstant.PUBLISH_TEMPLATE_PER)
    public R<Object> execAuditTemplateTask(@RequestBody @Validated(ExecAuditCreate.class) ScriptExecAuditDto scriptExecAuditDto) {
        try {
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, taskTemplateService.execAuditTemplateTask(scriptExecAuditDto), MessageUtil.message("script.task.submit.success"));
        } catch (ScriptException e) {
            logger.error("getScriptDetail error",e);
            return ValidationUtils.customFailResult("script",e.getMessage());
        }
    }

    /**
     * 保存常用/克隆任务
     * @tags 对外API接口
     * @param scriptExecAuditDto 执行参数
     * @return 执行结果状态
     */
    @PostMapping("/saveAuditTemplateTask")
    @Auditable("常用任务|常用克隆任务保存")
    @MethodPermission(TemplateTaskBtnPermitConstant.SAVE_TEMPLATE_PER)
    public R<Object> saveAuditTemplateTask(@RequestBody @Validated(ExecAuditCreate.class) ScriptExecAuditDto scriptExecAuditDto) {
        try {
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, taskTemplateService.updateAuditTemplateTask(scriptExecAuditDto), "查询成功");
        } catch (ScriptException e) {
            logger.error("getScriptDetail error",e);
            return ValidationUtils.customFailResult("script",e.getMessage());
        }
    }

    /**
     * 删除常用/克隆任务
     * @param taskId 任务id
     * @return 删除结果
     */
    @PostMapping("/deleteTemplateTask")
    @Auditable("常用任务|常用克隆任务删除")
    @MethodPermission(TemplateTaskBtnPermitConstant.REMOVE_TEMPLATE_TASK_PER)
    public R<Object> deleteTemplateTask(@RequestParam(value = "taskId") Long taskId) {
        try {
            taskTemplateService.deleteTaskTemplate(taskId);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "" , "删除成功");
        } catch (ScriptException e) {
            logger.error("getScriptDetail error",e);
            return ValidationUtils.customFailResult("script",e.getMessage());
        }
    }


    /**
     * 下载附件
     * @param id 附件id
     */
    @PostMapping("/downloadAttachmentTemplate")
    @MethodPermission(MenuPermitConstant.TEMPLATE_TASK_OR_MY_SCRIPT_PER)
    public void downloadAttachmentTemplate(@RequestBody Long id, HttpServletResponse response) {
        TaskAttachmentDto attachmentDto = taskTemplateService.selectAttachmentById(id);
        response.reset();
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-disposition","attachment; filename=" + URLEncoder.encode(attachmentDto.getName(), "UTF-8"));
            outputStream.write(attachmentDto.getContents());
        } catch (IOException e) {
            logger.error("downloadAttachment error:" ,e);
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
        }
    }

    /**
     *  附件上传
     * @param file 附件
     * @param response 响应
     * @param taskId 任务id
     * @return 返回值 TaskAttachmentDto
     */
    @PostMapping("/uploadAttachmentTemplate")
    @MethodPermission(TemplateTaskBtnPermitConstant.SAVE_TEMPLATE_PER)
    public R<Object> uploadAttachmentTemplate(@RequestParam("file") MultipartFile file, HttpServletResponse response, @RequestParam(value = "taskId", required = false) Long taskId) {
        try {
            TaskAttachmentDto attachmentDto = new TaskAttachmentDto();
            attachmentDto.setName(file.getOriginalFilename());
            attachmentDto.setSize(file.getSize());
            attachmentDto.setContents(file.getBytes());
            attachmentDto.setScriptTaskId(taskId);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, taskTemplateService.uploadAttachment(attachmentDto), "upload.success");
        } catch (IOException ioException) {
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            return null;
        } catch (ScriptException e) {
            logger.error("uploadAttachment fail",e);
            return ValidationUtils.customFailResult("file",e.getMessage());
        }
    }

    /**
     * 常用任务/克隆任务 删除临时附件
     * @param id 临时附件id
     * @return 结果
     */
    @GetMapping("/deleteAttachmentTemplate")
    @MethodPermission(TemplateTaskBtnPermitConstant.SAVE_TEMPLATE_PER)
    public R<?> deleteAttachmentTemplate(@RequestParam("id") Long id) {
        taskTemplateService.deleteAttachmentTemplate(id);
        return R.ok();
    }

    /**
     * 根据常用任务id查询当前常用任务的附件
     * @param scriptTaskId 任务id
     * @return 附件集合
     */
    @PostMapping("/getTaskTemplateAttachment")
    @MethodPermission(MenuPermitConstant.TEMPLATE_TASK_OR_MY_SCRIPT_PER)
    public R<?> getTaskTemplateAttachment(@RequestParam("scriptTaskId") Long scriptTaskId) {
        return R.ok(taskTemplateService.getTaskTemplateAttachment(scriptTaskId));
    }

    /**
     * 根据常用任务id启动常用任务
     * @param startCommonTaskDto 启动常用任务参数
     * @return 执行结果状态
     */
    @PostMapping("/startCommonTask")
    @Auditable("常用任务|启动常用任务API接口")
    @MethodPermission(TemplateTaskBtnPermitConstant.PUBLISH_TEMPLATE_PER)
    public R<ScriptTaskApplyResDto> startCommonTask(@RequestBody @Validated StartCommonTaskDto startCommonTaskDto) throws ScriptException {
        CurrentUser user = getCurrentUser();
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, taskTemplateService.startCommonTask(startCommonTaskDto, user), MessageUtil.message("script.task.submit.success"));
    }

}
