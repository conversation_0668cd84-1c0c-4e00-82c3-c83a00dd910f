package com.ideal.script.service;

import com.github.pagehelper.PageInfo;
import com.ideal.script.dto.ScriptInfoDto;
import com.ideal.script.dto.ScriptVersionDto;
import com.ideal.script.model.bean.ScriptInfoQueryBean;
import com.ideal.script.model.bean.TaskApplyBean;
import com.ideal.script.model.dto.interaction.agent.UserInfoDto;
import com.ideal.script.model.entity.InfoVersion;

import java.util.List;

/**
 * Service接口
 *
 * <AUTHOR>
 */
public interface IInfoVersionService {
    /**
     * 查询
     *
     * @param id 主键
     * @return 
     */
    ScriptVersionDto selectInfoVersionById(Long id);

    /**
     * 批量查询版本信息列表
     * @param ids version 表主键
     * @return  脚本集合
     */
    List<ScriptVersionDto> getInfoVersionInfoList(Long[] ids);

    /**
     * 查询
     *
     * @param srcScriptUuid 脚本版本uuid
     * @return 结果
     */
    ScriptVersionDto selectInfoVersionBySrcScriptUuid(String srcScriptUuid);

    /**
     * 查询列表
     *
     * @param infoVersionDto 
     * @param pageSize       
     * @param pageNum        
     * @return 集合
     */
    PageInfo<ScriptVersionDto> selectInfoVersionList(ScriptVersionDto infoVersionDto, int pageNum, int pageSize);

    /**
     * 新增
     *
     * @param infoVersionDto 
     */
    void insertInfoVersion(ScriptVersionDto infoVersionDto);

    /**
     * 修改
     *
     * @param infoVersionDto 
     */
    void updateInfoVersion(ScriptVersionDto infoVersionDto);

    /**
     * 批量删除
     *
     * @param ids 需要删除的主键集合
     */
    void deleteInfoVersionByIds(Long[] ids);

    /**
     * 删除信息
     *
     * @param id 主键
     * @return 结果
     */
    int deleteInfoVersionById(Long id);

    /**
     * 根据本版表uuid获取脚本类型（sh、python...)
     *
     * @param srcScriptUuid 本版表uuid
     * @return String
     * <AUTHOR>
     */

    String getScriptTypeBySrcScriptUuid(String srcScriptUuid);

    /**
     * 验证版本uuid是否存在
     *
     * @param srcScriptUuid 版本uuid
     * @return Boolean
     */
    Boolean validSrcScriptUuidExist(String srcScriptUuid);

    /**
     * 将之前的版本的脚本默认属性置为0
     *
     * @param infoVersion 脚本版本实体类
     * @return 结果
     */
    int updateInfoVersionDefaultValue(InfoVersion infoVersion);

    /**
     * 是否是白名单
     *
     * @param id 主键
     * @return boolean
     */
    boolean isInWhiteList(Long id);

    /**
     * 查询同组用户集合
     *
     * @param userId 用户id
     * @return {@link List }<{@link UserInfoDto }>
     */
    List<UserInfoDto> queryGroupUserInfoListByUserId(Long userId);

    /**
     * 查询脚本信息集合
     *
     * @param scriptInfoQueryBean 脚本查询信息
     * @return {@link List}<{@link TaskApplyBean}>
     */
    List<TaskApplyBean> getInfoVersionList(ScriptInfoQueryBean scriptInfoQueryBean);

    /**
     * 查询默认版本的脚本信息
     *
     * @param scriptInfoQueryBean 脚本信息查询Dto
     * @return {@link InfoVersion}
     */
    TaskApplyBean getInfoDefaultVersion(ScriptInfoQueryBean scriptInfoQueryBean);

    /**
     * 查询的脚本信息
     *
     * @param scriptInfoQueryBean 脚本信息查询Dto
     * @return {@link InfoVersion}
     */
    TaskApplyBean getInfoVersion(ScriptInfoQueryBean scriptInfoQueryBean);

    /**
     * 根据脚本版本uuid获取脚本信息
     * @param srcScriptUuid 版本uuid
     * @return  脚本信息
     */
    ScriptInfoDto getInfoByVersionUuid(String srcScriptUuid);

    /**
     * 根据主表id获取版本表id
     * @param ids   主表id集合
     * @return  版本表id集合
     */
    List<Long> getVersionIdByIds(Long[] ids);

    /**
     * 根据脚本版本uuid查询默认脚本信息
     * @param srcScriptUuid 脚本版本uuid
     * @return  脚本默认版本基本信息
     */
    ScriptVersionDto getInfoDefaultVersionByUuid(String srcScriptUuid);


    InfoVersion getInfoDefaultVersionByUniqueUuid(String uniqueUuid);

    Integer getTaskCountByVersionId(Long versionId);

    Long selectIdBySrcScriptUuid(String srcScriptUuid);
}
