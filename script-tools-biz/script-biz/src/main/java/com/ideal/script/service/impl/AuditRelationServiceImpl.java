package com.ideal.script.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.script.mapper.AuditRelationMapper;
import com.ideal.script.model.dto.AuditRelationDto;
import com.ideal.script.model.entity.AuditRelation;
import com.ideal.script.service.IAuditRelationService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 双人复核与脚本服务化关系Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class AuditRelationServiceImpl implements IAuditRelationService {

    private final AuditRelationMapper auditRelationMapper;

    public AuditRelationServiceImpl(AuditRelationMapper auditRelationMapper) {
        this.auditRelationMapper = auditRelationMapper;
    }

    /**
     * 查询双人复核与脚本服务化关系
     *
     * @param id 双人复核与脚本服务化关系主键
     * @return 双人复核与脚本服务化关系
     */
    @Override
    public AuditRelationDto selectAuditRelationById(Long id) {
        return BeanUtils.copy(auditRelationMapper.selectAuditRelationById(id), AuditRelationDto.class);
    }

    @Override
    public AuditRelationDto selectAuditRelationByTaskId(Long taskId) {
        return BeanUtils.copy(auditRelationMapper.selectAuditRelationByTaskId(taskId), AuditRelationDto.class);
    }

    /**
     * 查询双人复核与脚本服务化关系(通过双人复核主键)
     * @param workItemId    双人复核表主键id
     * @return  双人复核与脚本服务化关系
     */
    @Override
    public AuditRelationDto selectAuditRelationByWorkItemId(Long workItemId){
        return BeanUtils.copy(auditRelationMapper.selectAuditRelationByWorkItemId(workItemId), AuditRelationDto.class);
    }

    /**
     * 根据双人复核id查询脚本id以及版本uuid
     *
     * @param id 双人复核与脚本服务化关系主键
     * @return {@link AuditRelationDto }
     */
    @Override
    public AuditRelationDto selectInfoIdAndSrcScriptUuidByAuditRelationId(Long id){
        return BeanUtils.copy(auditRelationMapper.selectInfoIdAndSrcScriptUuidByAuditRelationId(id), AuditRelationDto.class);
    }
    /**
     * 查询双人复核与脚本服务化关系列表
     *
     * @param auditRelationDto 双人复核与脚本服务化关系
     * @return 双人复核与脚本服务化关系
     */
    @Override
    public PageInfo<AuditRelationDto> selectAuditRelationList(AuditRelationDto auditRelationDto, int pageNum, int pageSize) {
        PageMethod.startPage(pageNum, pageSize);
        List<AuditRelation> auditRelationList = new ArrayList<>();
        if (null != auditRelationDto) {
            AuditRelation auditRelation = BeanUtils.copy(auditRelationDto, AuditRelation.class);
            auditRelationList = auditRelationMapper.selectAuditRelationList(auditRelation);
        }
        return PageDataUtil.toDtoPage(auditRelationList, AuditRelationDto.class);

    }

    /**
     * 新增双人复核与脚本服务化关系
     *
     * @param auditRelationDto 双人复核与脚本服务化关系
     * @return 结果
     */
    @Override
    public int insertAuditRelation(AuditRelationDto auditRelationDto) {
        AuditRelation auditRelation = BeanUtils.copy(auditRelationDto, AuditRelation.class);
        int i = auditRelationMapper.insertAuditRelation(auditRelation);
        auditRelationDto.setId(auditRelation.getId());
        return i;
    }

    /**
     * 根据脚本的uuid更新关系数据
     * @param auditRelation 关系对象
     * @return 关系数据
     */
    @Override
    public AuditRelation selectAuditRelationForAudit(AuditRelation auditRelation){
        return auditRelationMapper.selectAuditRelationForAudit(auditRelation);
    }

    @Override
    public int updateAuditRelationForAudit(AuditRelation auditRelation){
        return auditRelationMapper.updateAuditRelationForAudit(auditRelation);
    }

    /**
     * 修改双人复核与脚本服务化关系
     *
     * @param auditRelationDto 双人复核与脚本服务化关系
     * @return 结果
     */
    @Override
    public int updateAuditRelation(AuditRelationDto auditRelationDto) {
        AuditRelation auditRelation = BeanUtils.copy(auditRelationDto, AuditRelation.class);
        return auditRelationMapper.updateAuditRelation(auditRelation);
    }

    /**
     * 修改双人复核与脚本服务化关系通过workItemId
     *
     * @param auditRelationDto 双人复核与脚本服务化关系
     * @return 结果
     */
    @Override
    public int updateAuditRelationByWorkItemId(AuditRelationDto auditRelationDto){
        AuditRelation auditRelation = BeanUtils.copy(auditRelationDto, AuditRelation.class);
        return auditRelationMapper.updateAuditRelationByWorkItemId(auditRelation);
    }

    /**
     * 批量删除双人复核与脚本服务化关系
     *
     * @param ids 需要删除的双人复核与脚本服务化关系主键
     * @return 结果
     */
    @Override
    public int deleteAuditRelationByIds(Long[] ids) {
        return auditRelationMapper.deleteAuditRelationByIds(ids);
    }

    /**
     * 删除双人复核与脚本服务化关系信息
     *
     * @param id 双人复核与脚本服务化关系主键
     * @return 结果
     */
    @Override
    public int deleteAuditRelationById(Long id) {
        return auditRelationMapper.deleteAuditRelationById(id);
    }
}
