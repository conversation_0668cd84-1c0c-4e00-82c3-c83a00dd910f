package com.ideal.script.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.sc.util.ExcelUtil;
import com.ideal.script.mapper.StatementMapper;
import com.ideal.script.model.dto.StatementDto;
import com.ideal.script.model.bean.StatementBean;
import com.ideal.script.service.IScriptStatementService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Transactional(rollbackFor = Exception.class)
@Service
public class ScriptStatementServiceImpl implements IScriptStatementService {

    private static final Logger logger = LoggerFactory.getLogger(ScriptStatementServiceImpl.class);

    private final StatementMapper statementMapper;

    private final CategoryServiceImpl categoryService;

    public ScriptStatementServiceImpl(StatementMapper statementMapper, CategoryServiceImpl categoryService) {
        this.statementMapper = statementMapper;
        this.categoryService = categoryService;
    }

    @Override
    public PageInfo<StatementDto> selectScriptStatementPage(StatementDto statementDto, int pageNum, int pageSize) {
        StatementBean statementBean = BeanUtils.copy(statementDto, StatementBean.class);
        buildStatementEntity(statementDto, statementBean);
        PageMethod.startPage(pageNum, pageSize);
        List<StatementBean> statementBeanList = statementMapper.selectScriptStatementPage(statementBean);
        return PageDataUtil.toDtoPage(statementBeanList, StatementDto.class);
    }

    private void buildStatementEntity(StatementDto statementDto, StatementBean statementBean){
        //获取分类完整路径
        String categoryPath = getCategoryFullPath(statementDto.getCategoryId());

        //针对%和_这种拼接进行处理
        String escapedLikeCategoryPath = categoryService.handleCategoryPath(categoryPath);

        statementBean.setCategoryPath(categoryPath);
        statementBean.setEscapedLikeCategoryPath(escapedLikeCategoryPath);
    }

    /**
     * 获取分类的全路径拼接
     * @param categoryId    分类id
     * @return      分类全路径
     */
    private String getCategoryFullPath(Long categoryId){
        //拼接分类的全路径（仅拼接到当前的分类）
        //获取分类
        return categoryService.buildCategoryFullPath(categoryId);
    }


    @Override
    public void exportExcel(List<Long> ids, HttpServletResponse response) {
        logger.info("长期未修改脚本报表开始导出");
        //组织数据
        List<StatementBean> statementBeanList = selectScriptStatementByIds(ids);
        //调用导出
        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMddHHmmss");
        String flag = sf.format(new Date());
        String fileName = "长期未修改脚本报表" + flag;
        String sheetName = "长期未修改脚本报表";
        logger.info("查询到的 StatementBean 列表: {}", statementBeanList);
        ExcelUtil.writeExcel(response,statementBeanList,fileName,sheetName, StatementBean.class);

    }


    /**
     * 根据id查报表数据
     * @param ids   ids
     * @return  结果
     */
    public List<StatementBean> selectScriptStatementByIds(List<Long> ids){
        return statementMapper.selectScriptStatementByIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateScriptStatementByIds(StatementDto statementDto,List<Long> ids) {
        StatementBean statementBean = BeanUtils.copy(statementDto, StatementBean.class);
        statementMapper.updateScriptStatementByIds(statementBean,ids);
    }

    @Override
    public StatementDto getScriptStatementByInfoId(Long infoId) {
        return BeanUtils.copy(statementMapper.getScriptStatementByInfoId(infoId),StatementDto.class);
    }
}
