package com.ideal.script.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.common.util.batch.BatchHandler;
import com.ideal.common.util.spring.MessageUtil;
import com.ideal.sc.util.CurrentUserUtil;
import com.ideal.script.dto.CategoryApiDto;
import com.ideal.script.dto.CategoryDto;
import com.ideal.script.dto.ScriptInfoQueryDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.CategoryMapper;
import com.ideal.script.mapper.InfoMapper;
import com.ideal.script.model.bean.CategoryOrgBean;
import com.ideal.script.model.bean.CategoryPermissionAware;
import com.ideal.script.model.bean.CategoryPermissionInfo;
import com.ideal.script.model.bean.CategoryRoleBean;
import com.ideal.script.model.bean.CategoryUserBean;
import com.ideal.script.model.bean.MyScriptBean;
import com.ideal.script.model.bean.OrgBean;
import com.ideal.script.model.bean.UserBean;
import com.ideal.script.model.dto.CategoryOrgDto;
import com.ideal.script.model.dto.CategoryRoleDto;
import com.ideal.script.model.dto.CategoryUserDto;
import com.ideal.script.model.entity.Category;
import com.ideal.script.model.entity.Info;
import com.ideal.script.service.ICategoryService;
import com.ideal.script.service.IMyScriptService;
import com.ideal.script.service.IScriptVersionShareService;
import com.ideal.system.api.IOrgManagement;
import com.ideal.system.api.IRole;
import com.ideal.system.api.IUserInfo;
import com.ideal.system.common.component.model.CurrentUser;
import com.ideal.system.dto.OrgManagementApiDto;
import com.ideal.system.dto.PermissionUserInfoApiDto;
import com.ideal.system.dto.RoleApiDto;
import com.ideal.system.dto.ServicePermissionApiQueryDto;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * Service业务层处理
 *
 * <AUTHOR>
 */
@Transactional(rollbackFor = Exception.class)
@Service
public class CategoryServiceImpl implements ICategoryService {

    private static final Logger logger = LoggerFactory.getLogger(CategoryServiceImpl.class);

    private final CategoryMapper categoryMapper;
    private final InfoMapper infoMapper;

    private final BatchHandler batchHandler;
    private final IUserInfo userInfoApi;

    private final IOrgManagement orgManagementApi;
    private final IScriptVersionShareService scriptVersionShareService;
    private final IMyScriptService myScriptService;

    private final IRole iRoleApi;

    public CategoryServiceImpl(IScriptVersionShareService scriptVersionShareService,CategoryMapper categoryMapper, InfoMapper infoMapper, BatchHandler batchHandler, IUserInfo userInfoApi, IOrgManagement orgManagementApi, IRole iRoleApi,@Lazy IMyScriptService myScriptService) {
        this.myScriptService = myScriptService;
        this.scriptVersionShareService = scriptVersionShareService;
        this.categoryMapper = categoryMapper;
        this.infoMapper = infoMapper;
        this.batchHandler = batchHandler;
        this.userInfoApi = userInfoApi;
        this.orgManagementApi = orgManagementApi;
        this.iRoleApi = iRoleApi;
    }

    /**
     * 查询【类别维护查询分类】
     *
     * @param id 【类别】主键
     * @return 【分类】
     */
    @Override
    public CategoryDto selectCategoryById(Long id) {
        Category category = categoryMapper.selectCategoryById(id);
        return BeanUtils.copy(category, CategoryDto.class);
    }

    /**
     * 获取分类树形列表
     *
     * @param categoryDto 【类别信息】
     * @return 【分页类别信息】
     */
    @Override
    public PageInfo<CategoryDto> selectCategoryList(CategoryDto categoryDto, int pageNum, int pageSize) {
        PageMethod.startPage(pageNum, pageSize);
        List<Category> categories = new ArrayList<>();
        List<Category> categoryList = new ArrayList<>();
        if (null != categoryDto) {
            Category category = BeanUtils.copy(categoryDto, Category.class);
            //当没有查询条件时，保证第一次是一级分类
            if (category.getName() == null) {
                category.setLevel(1);
            }

            categoryList = getCategoryWithChildren(category);

            if (category.getName() != null) {
                categories = buildCategoryTree(categoryList, category, pageNum, pageSize);
            }
        }
        PageInfo<CategoryDto> dtoPage = PageDataUtil.toDtoPage(categoryList, CategoryDto.class);
        if (categoryDto != null && categoryDto.getName() != null) {
            dtoPage.setTotal(categories.size());
        }
        return dtoPage;
    }

    /**
     * 构建分类树
     *
     * @param categoryList 条件筛选的分类列表
     * @param category     分类信息
     * @param pageNum      起始页
     * @param pageSize     每页大小
     * @return 分类树
     */
    private List<Category> buildCategoryTree(List<Category> categoryList, Category category, int pageNum, int pageSize) {
        //获取全部数据按照分页进行筛选
        List<Category> allCategoryList;
        allCategoryList = getCategoryWithChildren(category);
        //拿到分页的数据，pageNum,pageSize,手动改造分页数据
        //过滤后有效数据集
        List<CategoryApiDto> filterCategoryList = new ArrayList<>();
        for (Category currentCategory : allCategoryList) {
            // 添加一个标志变量表示是否需要添加
            boolean alreadyAdded = false;
            while (currentCategory.getParentId() != null && !alreadyAdded) {
                Category parentCategory = categoryMapper.selectCategoryById(currentCategory.getParentId());
                List<CategoryApiDto> childCategories = new ArrayList<>();
                childCategories.add(BeanUtils.copy(currentCategory, CategoryApiDto.class));
                // 找子集并设置标志
                alreadyAdded = buildCategory(filterCategoryList, parentCategory, currentCategory);
                if (alreadyAdded) {
                    continue;
                }
                parentCategory.setChildren(childCategories);
                currentCategory = parentCategory;
            }
            //一级分类不做上面的处理，需要判断将要添加的一级分类在其中有没有
            boolean exist = false;
            if (currentCategory.getLevel().equals(1)) {
                exist = judgeFirstCategoryExist(filterCategoryList, currentCategory);
            }
            if (!alreadyAdded && !exist) {
                filterCategoryList.add(BeanUtils.copy(currentCategory, CategoryApiDto.class));
            }
        }
        //前台页码大于数据数量处理
        while (filterCategoryList.size() - (pageNum - 1) * pageSize < 0) {
            pageNum--;
        }
        int size = Math.min(categoryList.size(), Math.abs(filterCategoryList.size() - (pageNum - 1) * pageSize));
        categoryList.clear();
        for (int i = 0; i < size; i++) {
            //按照分页进行重新填充
            categoryList.add(BeanUtils.copy(filterCategoryList, Category.class).get((pageNum - 1) * pageSize + i));
        }
        return BeanUtils.copy(filterCategoryList, Category.class);
    }

    /**
     * 拼接正确的分类树
     *
     * @param filterCategoryList 正确的分类树列表
     * @param parentCategory     父级分类
     * @param currentCategory    条件筛选根节点组成树
     * @return 分类树
     */
    private boolean buildCategory(List<CategoryApiDto> filterCategoryList, Category parentCategory, Category currentCategory) {
        // 标记是否成功添加子类别
        boolean childAdded = false;

        for (CategoryApiDto category : filterCategoryList) {
            // 如果当前类别不是要找的父类别，则递归查找其子类别
            if (!category.getId().equals(parentCategory.getId())) {
                if (category.getChildren() != null && buildCategory(category.getChildren(), parentCategory, currentCategory)) {
                    // 如果在更深层次添加了子类别，则标记为添加成功
                    childAdded = true;
                }
            } else {
                // 找到了匹配的父类别
                if (categoryContainsCurrentChild(category, currentCategory)) {
                    // 如果当前类别已包含要添加的子类别，则标记为添加成功
                    logger.info("category has add");
                } else {
                    addCurrentCategoryAsChild(category, currentCategory);
                    logger.info("currentCategory has add for child");
                    // 添加当前类别作为子类别，并标记为添加成功
                }
                childAdded = true;
                // 找到匹配的父类别后，结束循环
                break;
            }
        }
        // 返回是否成功添加子类别的标记
        return childAdded;
    }

    /**
     * 判断当前类别是否已包含要添加的子类别
     *
     * @param category        当前分类
     * @param currentCategory 条件筛选根节点组成树
     * @return boolean
     */
    private boolean categoryContainsCurrentChild(CategoryApiDto category, Category currentCategory) {
        // 检查当前类别是否已包含要添加的子类别
        if (category.getChildren() != null) {
            for (CategoryApiDto child : category.getChildren()) {
                if (child.getId().equals(currentCategory.getId())) {
                    // 如果找到匹配的子类别，则返回 true
                    return true;
                }
            }
        }
        // 没有找到匹配的子类别，返回 false
        return false;
    }

    /**
     * 添加子节点
     *
     * @param category        前分类
     * @param currentCategory 条件筛选根节点组成树
     */
    private void addCurrentCategoryAsChild(CategoryApiDto category, Category currentCategory) {
        // 将当前类别作为子类别添加到指定类别中
        List<CategoryApiDto> children = category.getChildren();
        if (children == null) {
            children = new ArrayList<>();
            category.setChildren(children);
        }
        children.add(BeanUtils.copy(currentCategory, CategoryApiDto.class));
    }

    /**
     * 判断将要添加的一级分类是否存在
     *
     * @param filterCategoryList 正确的分类树列表
     * @param currentCategory    条件筛选根节点组成树
     * @return 判定值
     */
    private boolean judgeFirstCategoryExist(List<CategoryApiDto> filterCategoryList, Category currentCategory) {
        for (CategoryApiDto category : filterCategoryList) {
            if (category.getName().equals(currentCategory.getName())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取当前分类以及对应子分类的递归实现
     *
     * @param category      类别信息
     * @param queryChildren 是否查询子分类
     * @return {@link List}<{@link Category}> 类别列表
     */
    @Override
    public List<Category> getCategoryWithChildren(Category category, Boolean queryChildren) {
        List<Category> categoryList = categoryMapper.selectCategoryList(category);
        if (Boolean.TRUE.equals(queryChildren)) {
            for (Category currentCategory : categoryList) {
                // 创建新的 Category 对象而不是重复使用同一个对象
                Category childCategory = new Category();
                childCategory.setParentId(currentCategory.getId());

                List<Category> children = getCategoryWithChildren(childCategory, true);

                if (!children.isEmpty()) {
                    currentCategory.setChildren(BeanUtils.copy(children, CategoryApiDto.class));
                }
            }
        }
        return categoryList;
    }


    /**
     * 获取当前分类以及对应子分类的递归实现
     *
     * @param category 类别信息
     * @return 类别列表
     */
    public List<Category> getCategoryWithChildren(Category category) {
        List<Category> categoryList = categoryMapper.selectCategoryList(category);
        for (Category currentCategory : categoryList) {
            // 创建新的 Category 对象而不是重复使用同一个对象
            Category childCategory = new Category();
            childCategory.setParentId(currentCategory.getId());

            List<Category> children = getCategoryWithChildren(childCategory);

            if (!children.isEmpty()) {
                currentCategory.setChildren(BeanUtils.copy(children, CategoryApiDto.class));
            }
        }
        return categoryList;
    }

    /**
     * 新增【新增分类】
     *
     * @param categoryDto 【类别信息】
     * @return 结果
     */
    @Override
    public int insertCategory(CategoryDto categoryDto) throws ScriptException {
        checkHasRootCategoryName(categoryDto);

        Category category = BeanUtils.copy(categoryDto, Category.class);

        int i = categoryMapper.insertCategory(category);
        categoryDto.setId(category.getId());
        return i;
    }

    /**
     * 修改【修改分类】
     *
     * @param categoryDto 【类别信息】
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateCategory(CategoryDto categoryDto) throws ScriptException {
        checkHasRootCategoryName(categoryDto);
        Category category = BeanUtils.copy(categoryDto, Category.class);
        //修改info表中分类全路径
        updateInfoCategoryPath(category);

        return categoryMapper.updateCategory(category);
    }

    /**
     * 处理更新分类所影响的info分类路径信息
     * @param category  分类信息
     */
    private void updateInfoCategoryPath(Category category){
        //拼接当前分类的全路径
        String categoryPath = buildCategoryPath(category);

        //针对%和_这种拼接进行处理
        String escapedLikeCategoryPath = handleCategoryPath(categoryPath);

        //根据当前的全路径去匹配符合要求的脚本
        Info info = new Info();
        info.setCategoryPath(categoryPath);
        info.setEscapedLikeCategoryPath(escapedLikeCategoryPath);
        List<Info> infoList = infoMapper.selectInfoList(info);
        //修改脚本数据
        List<Info> equalLengthLists = new ArrayList<>();
        List<Info> unequalLengthLists = new ArrayList<>();
        for (Info info1 : infoList) {
            //处理路径信息
            processInfoUpdate(info1,categoryPath,category,equalLengthLists,unequalLengthLists);
        }
        // 批量更新信息
        updateInfoBatch(equalLengthLists, unequalLengthLists);
        logger.info("更新脚本分类完整路径成功");
    }



    /**
     * 处理存在%和_时的路径
     * @param categoryPath  分类路径
     * @return  处理好的路径
     */
    @Override
    public String handleCategoryPath(String categoryPath){
        //如果存在%和_处理路径
        if (categoryPath == null || categoryPath.isEmpty()) {
            return categoryPath;
        }

        StringBuilder escapedPath = new StringBuilder();
        for (int i = 0; i < categoryPath.length(); i++) {
            char currentChar = categoryPath.charAt(i);
            if (currentChar == '%' || currentChar == '_') {
                // 拼接转义字符
                escapedPath.append('\\');
                escapedPath.append('\\');
            }
            // 拼接分类路径
            escapedPath.append(currentChar);
        }

        return escapedPath.toString();
    }

    /**
     * 处理分类路径信息的过程
     * @param info 脚本信息
     * @param categoryPath  分类路径
     * @param category  分类信息
     * @param equalLengthLists  等长列表
     * @param unequalLengthLists  不等长列表
     */
    private void processInfoUpdate(Info info,String categoryPath,Category category,List<Info> equalLengthLists,List<Info> unequalLengthLists){
        logger.info("开始处理路径信息");
        String oldCategoryPath = info.getCategoryPath();
        if (oldCategoryPath != null && categoryPath != null) {

            //取出不同的部分
            String differentPart = findDifferentPart(oldCategoryPath, categoryPath);
            //取出最后一个斜杆之前的内容
            String preCategoryPath = categoryPath.substring(0, categoryPath.lastIndexOf('/') + 1);

            String newCategoryPath = (preCategoryPath + category.getName() + differentPart).trim();

            info.setCategoryPath(newCategoryPath);

            //这里为了保险处理一下，将不是以/开开头的去掉，防止更改到以改路径为子串但不是该分类的数据
            if(!differentPart.isEmpty()){
                if(differentPart.startsWith("/")){
                    unequalLengthLists.add(info);
                }
            }else{
                equalLengthLists.add(info);
            }
        }
    }
    /**
     * 批量更新数据
     * @param equalLengthLists 等长列表
     * @param unequalLengthLists 不等长列表
     */
    private void updateInfoBatch(List<Info> equalLengthLists,List<Info> unequalLengthLists){
        logger.info("开始批量更新数据");
        //分情况批量更改info信息
        Consumer<Info> consumer = infoMapper::updateInfo;

        if(!equalLengthLists.isEmpty()){
            batchHandler.batchData(equalLengthLists,consumer);
        }
        if(!unequalLengthLists.isEmpty()){
            batchHandler.batchData(unequalLengthLists,consumer);
        }
    }


    /**
     * 提取差异部分
     * @param path1 分类路径
     * @param path2 分类路径
     * @return  提取部分
     */
    private static String findDifferentPart(String path1, String path2) {
        if (path1.length() == path2.length()) {
            return "";
        }
        int minLength = Math.min(path1.length(), path2.length());
        int diffIndex = 0;
        for (int i = 0; i < minLength; i++) {
            if (path1.charAt(i) != path2.charAt(i)) {
                break;
            }
            diffIndex = i + 1;
        }
        return path1.substring(diffIndex);
    }


    /**
     * 批量删除【删除类别】
     *
     * @param ids 需要删除的【类别】主键
     * @return 结果
     */
    @Override
    public int deleteCategoryByIds(Long[] ids) throws ScriptException {
        List<Long> existIds =  categoryMapper.checkIdsExist(ids);
        if (existIds.isEmpty()) {
            throw new ScriptException("category.not.exist");
        }
        if (ids.length > existIds.size()) {
            //找出不存在的ID
            String notExistIdsStr = Arrays.stream(ids)
                                                                .filter(id -> !existIds.contains(id))
                                                                .map(Object::toString)
                                                                .collect(Collectors.joining(","));
            if (!notExistIdsStr.isEmpty()) {
                throw new ScriptException(MessageUtil.message("category.not.exist") + "，对应的id为:" + notExistIdsStr);
            }
        }
        //删除的列表（树形结构的删除）
        // 使用 Set 来存储待删除的分类 ID，以保证唯一性
        Set<Long> removeSet = new HashSet<>();
        for (Long id : ids) {
            // 校验分类被脚本引用了，禁止删除
            Boolean categoryReferenced = checkIfCategoryReferenced(id);
            if(Boolean.TRUE.equals(categoryReferenced)){
                throw new ScriptException("id:"+id+","+MessageUtil.message("category.delete.referenced.by.scripts"));
            }
            // 调用递归方法将当前分类及其所有子分类的 ID 加入 removeSet
            addCategoryAndChildrenToSet(id, removeSet);
        }
        return categoryMapper.deleteCategoryByIds(removeSet.toArray(new Long[0]));
    }

    public boolean checkIfCategoryReferenced(Long categoryId) throws ScriptException {
       List<Long> categoryIds =  getAllCategoryIds(categoryId);
       return getCategoryReferencedCount(categoryIds);
    }


    /**
     * 查询分类是否被引用
     *
     * @param categoryIds 分类Id
     * @return {@link Boolean }
     */
    public Boolean getCategoryReferencedCount(List<Long> categoryIds) throws ScriptException {
        try {
            Integer result = categoryMapper.getCategoryReferencedCount(categoryIds);
            return result != null && result > 0;
        } catch (Exception e) {
            logger.error("getCategoryReferencedCount fail！", e);
            throw new ScriptException("getCategoryReferencedCount fail！");
        }
    }

    /**
     * 获取当前类别以及对应子类别的全部id
     *
     * @param categoryId 类别id
     * @param removeSet  删除的id列表
     */
    public void addCategoryAndChildrenToSet(Long categoryId, Set<Long> removeSet) {
        if (removeSet.contains(categoryId)) {
            return;
        }
        removeSet.add(categoryId);
        List<Long> childrenIds = categoryMapper.selectChildCategoryIdList(categoryId);
        for (Long childId : childrenIds) {
            // 递归调用，将子分类及其所有子分类的 ID 加入 removeSet
            addCategoryAndChildrenToSet(childId, removeSet);
        }
    }

    /**
     * 删除【删除类别】信息
     *
     * @param id 【类别】主键
     * @return 结果
     */
    @Override
    public int deleteCategoryById(Long id) {
        return categoryMapper.deleteCategoryById(id);
    }

    @Override
    public List<CategoryDto> listFirstCategory() {
        Category category = new Category();
        category.setLevel(1);
        List<Category> categories = categoryMapper.selectCategoryList(category);
        return BeanUtils.copy(categories, CategoryDto.class);
    }

    /**
     * 多级获取类别信息
     *
     * @param level    类别
     * @param parentId 父id
     * @return 类别列表
     */
    @Override
    public List<CategoryDto> listMultiCategory(Integer level, Long parentId) {
        Category category = new Category();
        category.setLevel(level);
        category.setParentId(parentId);
        List<Category> categories = categoryMapper.selectCategoryList(category);
        //查询下级，是否有下级作为分类是否继续展开标识
        for (Category category1 : categories) {
            category.setLevel(level + 1);
            category.setParentId(category1.getId());
            List<Category> categoryList = categoryMapper.selectCategoryList(category);
            category1.setChildren(categoryList.isEmpty() ? null : BeanUtils.copy(categoryList, CategoryApiDto.class));
        }
        return BeanUtils.copy(categories, CategoryDto.class);
    }

    /**
     * 查询下级分类
     *
     * @param parentId 父类id
     * @return 列表
     */
    @Override
    public List<CategoryDto> listNextCategory(long parentId) {
        Category category = new Category();
        category.setParentId(parentId);
        List<Category> categories = categoryMapper.selectCategoryList(category);
        return BeanUtils.copy(categories, CategoryDto.class);
    }

    /**
     * 查询分类信息
     *
     * @param ids id数组
     * @return 列表
     */
    @Override
    public List<Category> selectCategoryByIds(Long[] ids) {
        return categoryMapper.selectCategoryByIds(ids);

    }

    /**
     * 树形获取分类
     *
     * @param categoryId 分类id
     * @return 分类信息
     */
    public Category getCategory(Long categoryId) {
        //创建最终的 Category 对象
        Category finalcategory = new Category();
        //接收全部分类
        List<Category> categoryList = new ArrayList<>();
        //接收parentId
        Long id = categoryId;
        //循环找分类id
        while (id != -1) {
            // 查询单个 Category 对象
            Category category = categoryMapper.selectCategoryById(id);
            categoryList.add(category);
            if (null == category || category.getParentId() == null) {
                break;
            } else {
                id = category.getParentId();
            }
        }
        if (!categoryList.isEmpty()) {
            // 获取最后一个 Category 作为根节点
            finalcategory = categoryList.get(categoryList.size() - 1);
            // 构建分类树
            finalcategory = buildTree(finalcategory, categoryList, categoryList.size() - 2);
        }
        return finalcategory;
    }

    /**
     * 建立树形结构
     *
     * @param category     分类
     * @param categoryList 分类列表
     * @param key          索引
     * @return 结果
     */
    public Category buildTree(Category category, List<Category> categoryList, int key) {
        // 当 key 小于 0，表示已经构建完整颗分类树，返回当前节点
        if (key < 0) {
            return category;
        }
        if (!categoryList.isEmpty()) {
            //获取下一个子节点
            Category categoryChild = BeanUtils.copy(categoryList.get(key), Category.class);
            // 递归构建子节点的分类树，并将返回的子节点连接到当前节点的子节点列表中
            Category childTree = buildTree(categoryChild, categoryList, key - 1);
            category.setChildren(BeanUtils.copy(Collections.singletonList(childTree), CategoryApiDto.class));
        }
        return category;
    }

    /**
     * 构建分类层次结构，使用"/"拼接分类名称
     *
     * @param myScriptBeans list
     */
    public void buildCategoryHierarchy(List<MyScriptBean> myScriptBeans) {
        //分类拼接中间使用"/"分割
        for (MyScriptBean myScriptBean : myScriptBeans) {
            //获取最后一级id
            Long categoryId = myScriptBean.getCategoryId();
            //获取分类
            String categoryName = buildCategoryFullPath(categoryId);
            myScriptBean.setCategory(categoryName);
        }
    }

    /**
     * 获取当前分类以及所有子类的id
     *
     * @param scriptInfoQueryDto dto
     * @return 结果
     */
    public List<Long> getCurrentAndSubclassIds(ScriptInfoQueryDto scriptInfoQueryDto) {
        //分类筛选脚本
        List<Long> idsList = new ArrayList<>();
        if (scriptInfoQueryDto.getCategoryId() != null) {
            idsList.add(scriptInfoQueryDto.getCategoryId());
        } else {
            idsList = null;
        }
        //判断循环执行条件
        boolean hasNewAdd = true;
        while (hasNewAdd && idsList != null) {
            List<Long> idList = categoryMapper.getCategoryIds(idsList);
            //去重并添加新元素
            List<Long> finalIdsList = idsList;
            List<Long> distinctIds = idList.stream()
                    .filter(id -> !finalIdsList.contains(id))
                    .collect(Collectors.toList());
            idsList.addAll(distinctIds);
            if (idList.isEmpty() || distinctIds.isEmpty()) {
                hasNewAdd = false;
            }
        }
        return idsList;
    }

    /**
     * 功能描述：获取脚本分类全路径
     *
     * @param categoryId 脚本当前分类id
     * @return {@link String }
     */
    @Override
    public String getCategoryFullPath(long categoryId) {
        return getCategoryFullPathRecursive(categoryId, new StringBuilder());
    }

    /**
     * 功能描述： 通过分类等级、分类名称、所属父分类Id查找分类信息
     *
     * @param level    分类等级
     * @param name     分类名称
     * @param parentId 所属父分类Id
     * @return {@link CategoryDto }
     */
    @Override
    public CategoryDto findByLevelAndNameAndParentId(Integer level, String name, Long parentId) throws ScriptException {
        Category categoryQuery = new Category();
        categoryQuery.setName(name);
        categoryQuery.setLevel(level);
        categoryQuery.setParentId(parentId);
        List<Category> categoryList = categoryMapper.findByLevelAndNameAndParentId(categoryQuery);
        if (null != categoryList && categoryList.size() > 1) {
            logger.error("find one more record by level :{}, parentId:{} ,name:{}", level, parentId, name);
            throw new ScriptException("category.record.found.one.more");
        }
        return BeanUtils.copy((null != categoryList && categoryList.size() == 1) ? categoryList.get(0) : new Category(), CategoryDto.class);
    }

    /**
     * 功能描述：递归查询分类全路径
     *
     * @param categoryId 分类id
     * @param fullPath   全路径
     * @return {@link String }
     */
    private String getCategoryFullPathRecursive(long categoryId, StringBuilder fullPath) {
        Category category = categoryMapper.selectCategoryById(categoryId);
        if (category == null) {
            return "";
        }

        if (fullPath.length() > 0) {
            fullPath.insert(0, "/");
        }
        fullPath.insert(0, category.getName());

        if (category.getParentId() != null) {
            getCategoryFullPathRecursive(category.getParentId(), fullPath);
        }

        return fullPath.toString();
    }

    /**
     * 校验分类重复
     *
     * @param categoryDto 分类Dto
     * @throws ScriptException exception
     */
    public void checkHasRootCategoryName(CategoryDto categoryDto) throws ScriptException {
        //同级父级分类下子级分类不重名
        Category category = new Category();
        category.setLevel(categoryDto.getLevel());
        category.setParentId(categoryDto.getParentId());
        List<Category> categoryList = categoryMapper.selectCategoryList(category);
        //如果id为空，说明为新增，此时只要重名就是不对的
        if(null == categoryDto.getId() && !categoryList.isEmpty()){
            for (Category currentCategory : categoryList) {
                if(categoryDto.getName().equals(currentCategory.getName())){
                    throw new ScriptException("duplicate.category");
                }
            }
        //如果id不为空，说明是编辑，此时名称相同可能是本身的值，需要刨除本次数据本身
        }else{
            for (Category currentCategory : categoryList) {
                if (categoryDto.getName().equals(currentCategory.getName()) && !categoryDto.getId().equals(currentCategory.getId())) {
                    throw new ScriptException("duplicate.category");
                }
            }
        }
    }


    /**
     * 递归设置上级分类的子分类信息
     *
     * @param category 当前分类的 Category​对象
     */
    @Override
    public Category setChildrenForCategoryAndParent(Category category) {
        if (category == null) {
            return null;
        }

        Long parentId = category.getParentId();
        if (parentId != null) {
            // 递归获取上级分类并设置当前分类为其子分类
            Category parentCategory = categoryMapper.selectCategoryById(category.getParentId());
            if (null != parentCategory.getId()) {
                List<CategoryApiDto> children = parentCategory.getChildren();
                if (children == null) {
                    children = new ArrayList<>();
                    parentCategory.setChildren(children);
                }
                children.add(BeanUtils.copy(category, CategoryApiDto.class));
                // 递归设置上级分类的子分类信息，并返回最顶层的实体
                return setChildrenForCategoryAndParent(parentCategory);
            } else {
                // 如果没有找到上级分类，则当前分类即为最顶层的实体
                return category;
            }
        } else {
            // 如果当前分类没有上级分类ID，则当前分类即为最顶层的实体
            return category;
        }
    }

    /**
     * 查询当前分类及其子分类Id集合
     *
     * @param categoryId 当前分类Id
     * @return {@link List }<{@link Integer }>
     */
    @Override
    public List<Long> getAllCategoryIds(Long categoryId) {
        List<Long> allCategoryIds = new ArrayList<>();
        retrieveAllDescendantCategoryIds(categoryId, allCategoryIds);
        return allCategoryIds;
    }

    /**
     * 递归地检索所有子分类的方法。
     *
     * @param categoryId     当前分类的ID
     * @param allCategoryIds 结果列表，用于存储检索到的所有子分类的ID
     */
    public void retrieveAllDescendantCategoryIds(Long categoryId, List<Long> allCategoryIds) {
        // 将当前分类id加入结果列表
        allCategoryIds.add(categoryId);

        // 查询子分类并递归调用retrieveAllDescendantCategories方法
        List<Long> subCategoryIds = categoryMapper.getSubCategoryIds(categoryId);
        if (null != subCategoryIds) {
            for (Long subCategoryId : subCategoryIds) {
                retrieveAllDescendantCategoryIds(subCategoryId, allCategoryIds);
            }
        }
    }

    /**
     * 拼接当前分类的全路径
     * @param category2 分类
     * @return  分类全路径
     */
    @Override
    public String buildCategoryPath(Category category2){
        Long categoryId = category2.getId();
        //获取分类
        return buildCategoryFullPath(categoryId);
    }

    /**
     * 构建分类的全路径形式
     * @param categoryId  分类的id
     * @return  全路径字符串
     */
    public String buildCategoryFullPath(Long categoryId) {
        List<String> categoryNameList = new ArrayList<>();
        StringBuilder categoryName = new StringBuilder();
        // 用于记录已访问过的分类ID
        Set<Long> visitedIds = new HashSet<>();
        Category category = new Category();
        category.setId(categoryId);
        while (category.getId() != null && !visitedIds.contains(category.getId())) {
            visitedIds.add(category.getId());
            List<Category> categoryList = categoryMapper.selectCategoryList(category);
            if (!categoryList.isEmpty()) {
                category.setId(categoryList.get(0).getParentId());
                categoryNameList.add(categoryList.get(0).getName());
            } else {
                category.setId(null);
            }
        }
        for (int i = categoryNameList.size() - 1; i >= 0; i--) {
            if (i == categoryNameList.size() - 1) {
                categoryName.append(categoryNameList.get(i));
            } else {
                categoryName.append("/").append(categoryNameList.get(i));
            }
        }
        return String.valueOf(categoryName);
    }

    @Override
    public List<CategoryDto> selectCategoryListNoPage(CategoryDto categoryDto) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<Category> categoryList = new ArrayList<>();
        if (null != categoryDto) {
            Category category = BeanUtils.copy(categoryDto, Category.class);
            //当没有查询条件时，保证第一次是一级分类
            if (category.getName() == null) {
                category.setLevel(1);
            }

            categoryList = getCategoryWithChildren(category);
        }

        stopWatch.stop();
        logger.info("耗时{}", stopWatch.getTotalTimeMillis());
        return BeanUtils.copy(categoryList,CategoryDto.class);
    }

    /**
     * 分类授权部门
     * @param categoryOrgDto 实体dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void assignCategoryToOrg(CategoryOrgDto categoryOrgDto) {
        // 获取已存在的关系数据
        List<OrgBean> existingRelations = categoryMapper.getCategoryOrgRelations(categoryOrgDto.getCategoryId());

        // 获取需要插入的新关系
        List<CategoryOrgBean> newRelations = getNewRelations(categoryOrgDto, existingRelations);

        // 删除取消勾选的数据
        deleteUnselectedRelations(existingRelations, categoryOrgDto.getOrgList());

        // 批量插入新关系
        insertNewRelations(newRelations);
    }

    private List<CategoryOrgBean> getNewRelations(CategoryOrgDto categoryOrgDto, List<OrgBean> existingRelations) {
        if (categoryOrgDto.getOrgList() == null || categoryOrgDto.getOrgList().isEmpty()) {
            return Collections.emptyList();
        }

        return categoryOrgDto.getOrgList().stream()
                .filter(orgBean -> existingRelations.stream()
                        .noneMatch(org -> Objects.equals(org.getCode(), orgBean.getCode())))
                .map(orgBean -> {
                    CategoryOrgBean categoryOrgBean = new CategoryOrgBean();
                    categoryOrgBean.setLevel(categoryOrgDto.getLevel());
                    categoryOrgBean.setCategoryId(categoryOrgDto.getCategoryId());
                    categoryOrgBean.setSysOrgCode(orgBean.getCode());
                    categoryOrgBean.setOrgId(orgBean.getOrgId());
                    return categoryOrgBean;
                })
                .collect(Collectors.toList());
    }

    private void deleteUnselectedRelations(List<OrgBean> existingRelations, List<OrgBean> selectedOrgs) {
        List<Long> idsToDelete = existingRelations.stream()
                .filter(orgBean -> selectedOrgs.stream()
                        .noneMatch(org -> Objects.equals(org.getCode(), orgBean.getCode())))
                .map(OrgBean::getId)
                .collect(Collectors.toList());

        if (!idsToDelete.isEmpty()) {
            categoryMapper.deleteCategoryDepartmentByIds(idsToDelete);
        }
    }

    private void insertNewRelations(List<CategoryOrgBean> newRelations) {
        if (!newRelations.isEmpty()) {
            Consumer<CategoryOrgBean> consumer = categoryMapper::insertCategoryDepartment;
            batchHandler.batchData(newRelations, consumer);
        }
    }

    /**
     * 查询该分类的部门绑定信息
     * @param categoryId    分类id
     * @return  详细信息
     */
    @Override
    public CategoryOrgBean getCategoryOrgRelations(long categoryId) {

        List<OrgBean> orgBeanList = categoryMapper.getCategoryOrgRelations(categoryId);
        CategoryOrgBean categoryOrgBean = new CategoryOrgBean();
        categoryOrgBean.setOrgList(orgBeanList);
        return categoryOrgBean;
    }

    /**
     * 分类绑定审核人（角色权限时执行）
     * @param categoryUserDto 绑定参数
     */
    private void rolePermissionAuditor(CategoryUserDto categoryUserDto){
        //获取分类下的所有子分类
        List<Long> categoryList = new ArrayList<>();
        categoryList.add(categoryUserDto.getCategoryId());
        List<Long> allSonCategoryId = getAllSonCategoryId(categoryList);
        //将父分类本身放到list中
        allSonCategoryId.add(categoryUserDto.getCategoryId());
        //要绑定的审核人
        List<UserBean> userList = categoryUserDto.getUserList();
        //根据分类id删除原有审核人信息
        if(ObjectUtils.notEqual(allSonCategoryId, null) && !allSonCategoryId.isEmpty()){
            categoryMapper.deleteCategoryUserByCategoryIds(allSonCategoryId);
        }
        //组装要新增的数据
        List<CategoryUserBean> categoryUserBeanList = getCategoryUserBeans(allSonCategoryId, userList);

        //批量插入
        Consumer<CategoryUserBean> consumer = categoryMapper::insertCategoryUser;
        if(!categoryUserBeanList.isEmpty()){
            batchHandler.batchData(categoryUserBeanList,consumer);
        }
    }

    /**
     * 组装要新增的审核人-分类数据
     * @param allSonCategoryId 所有分类集合
     * @param userList 审核人集合
     * @return 审核人-分类数据
     */
    private static List<CategoryUserBean> getCategoryUserBeans(List<Long> allSonCategoryId, List<UserBean> userList) {
        List<CategoryUserBean> categoryUserBeanList = new ArrayList<>();
        if(!allSonCategoryId.isEmpty()
                && Objects.nonNull(userList)
                && !userList.isEmpty()){
            for(Long categoryId : allSonCategoryId){
                for(UserBean userBean : userList){
                    CategoryUserBean categoryUserBean = new CategoryUserBean();
                    categoryUserBean.setCategoryId(categoryId);
                    categoryUserBean.setUserId(userBean.getUserId());
                    categoryUserBeanList.add(categoryUserBean);
                }
            }
        }
        return categoryUserBeanList;
    }

    /**
     * 分类授权用户
     * @param categoryUserDto 实体类dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void assignCategoryToUser(CategoryUserDto categoryUserDto) throws ScriptException {

        //开启了角色权限后，在绑定审核人时，需要将其所有子分类都绑定同样的审核人
        if(myScriptService.getRolePermission()){
            rolePermissionAuditor(categoryUserDto);
            return;
        }

        //存在部门将执行保存
        List<UserBean> userBeanList = categoryMapper.getCategoryUsertRelations(categoryUserDto.getCategoryId());

        List<CategoryUserBean> categoryUserBeanList = new ArrayList<>();
        for (UserBean userBean : categoryUserDto.getUserList()) {
            boolean isDuplicate = false;
            for (UserBean user : userBeanList) {
                if(Objects.equals(user.getUserId(), userBean.getUserId())){
                    userBeanList.remove(user);
                    isDuplicate = true;
                    break;
                }
            }
            // 如果没有重复，则添加到新的列表中
            if (!isDuplicate) {
                CategoryUserBean categoryUserBean = new CategoryUserBean();
                categoryUserBean.setCategoryId(categoryUserDto.getCategoryId());
                categoryUserBean.setUserId(userBean.getUserId());
                categoryUserBeanList.add(categoryUserBean);
            }
        }
        //删除取消勾选的数据
        List<Long> ids = new ArrayList<>();
        for (UserBean userBean : userBeanList) {
            ids.add(userBean.getId());
        }
        if(!ids.isEmpty()) {
            categoryMapper.deleteCategoryUserByIds(ids);
        }

        //批量插入
        Consumer<CategoryUserBean> consumer = categoryMapper::insertCategoryUser;
        if(!categoryUserBeanList.isEmpty()){
            batchHandler.batchData(categoryUserBeanList,consumer);
        }
    }

    /**
     * 查询该分类的用户绑定信息
     * @param categoryId    分类id
     * @return  详细信息
     */
    @Override
    public CategoryUserBean getCategoryUserRelations(long categoryId) {

        List<UserBean> userBeanList = categoryMapper.getCategoryUsertRelations(categoryId);

        CategoryUserBean categoryUserBean = new CategoryUserBean();
        categoryUserBean.setUserList(userBeanList);
        return categoryUserBean;
    }


    /**
     * 根据权限查询用户列表（平台管理接口）(不分页)
     * @param categoryUserBean 查询条件
     * @return  用户列表
     */
    @Override
    public List<PermissionUserInfoApiDto> queryPermissionUserInfoList(CategoryUserBean categoryUserBean) {

        ServicePermissionApiQueryDto servicePermissionApiQueryDto = buildPermissionUserInfo(categoryUserBean);

        return userInfoApi.queryPermissionUserInfoList(servicePermissionApiQueryDto);
    }


    /**
     *  根据权限查询用户列表（平台管理接口）(分页)
     * @param categoryUserBean    参数
     * @param pageNum   页码
     * @param pageSize  大小
     * @return  结果
     */
    @Override
    public PageInfo<PermissionUserInfoApiDto> queryPermissionUserInfoPage(CategoryUserBean categoryUserBean, Integer pageNum, Integer pageSize) {

        ServicePermissionApiQueryDto servicePermissionApiQueryDto = buildPermissionUserInfo(categoryUserBean);
        return userInfoApi.queryPermissionUserInfoPage(servicePermissionApiQueryDto, pageNum, pageSize);
    }

    /**
     * 提取方法构建用户查询信息
     * @param categoryUserBean  分类用户绑定信息
     * @return  查询信息
     */
    private ServicePermissionApiQueryDto buildPermissionUserInfo(CategoryUserBean categoryUserBean){
        ServicePermissionApiQueryDto servicePermissionApiQueryDto = categoryUserBean.getServicePermissionApiQueryDto();
        //根据当前选择的分类找到该分类绑定的部门，找到这些部门下面的用户
        List<OrgBean> categoryOrgRelations = categoryMapper.getCategoryOrgRelations(categoryUserBean.getCategoryId());
        List<Long> orgIdlist = new ArrayList<>();
        if(!categoryOrgRelations.isEmpty()) {
            for (OrgBean categoryOrgRelation : categoryOrgRelations) {
                orgIdlist.add(categoryOrgRelation.getOrgId());
            }
        }
        //加上当前用户所在部门的
        CurrentUser currentUser = CurrentUserUtil.getCurrentUser();
        orgIdlist.add(currentUser.getOrgId());

        servicePermissionApiQueryDto.setOrgManagementIds(orgIdlist);
        return servicePermissionApiQueryDto;
    }

    /**
     * 查询部门树型结构
     * @param orgManagementApiDto 部门实体类
     * @return  部门列表（树形）
     */
    @Override
    public List<OrgManagementApiDto> selectOrgManagementTree(OrgManagementApiDto orgManagementApiDto) {
        return orgManagementApi.selectOrgManagementTree(orgManagementApiDto);
    }

    /**
     * 获取未共享脚本的部门
     * @param scriptVersionId 脚本版本id
     * @return 部门树结构
     */
    @Override
    public List<OrgManagementApiDto> selectNotShareOrgManagementTree(Long scriptVersionId) {
        OrgManagementApiDto orgManagementApiDto = new OrgManagementApiDto();
        //获取所有部门树数据
        List<OrgManagementApiDto> orgManagementList = orgManagementApi.selectOrgManagementTree(orgManagementApiDto);
        //查询共享关系表，获取已经共享的部门信息
        List<String> objectIdList = scriptVersionShareService.getObjectIdList(scriptVersionId, (short) 1);
        //如果没有共享部门，则返回所有部门
        if(objectIdList.isEmpty()){
            return orgManagementList;
        }
        //返回树结构，过滤掉脚本已经共享过的部门
        return filterDepartments(orgManagementList, objectIdList);
    }

    /**
     * 过滤共享过的部门
     * @param orgManagementList 所有部门
     * @param objectIdList 共享过的部门
     * @return 没有共享过的部门
     */
    private List<OrgManagementApiDto> filterDepartments(List<OrgManagementApiDto> orgManagementList, List<String> objectIdList) {
        if (orgManagementList == null || orgManagementList.isEmpty()) {
            return new ArrayList<>();
        }
        return orgManagementList.stream()
                .map(org -> {
                    // 创建新的部门对象，并复制所有字段
                    OrgManagementApiDto newOrg = BeanUtils.copy(org,OrgManagementApiDto.class);
                    // 递归处理子部门
                    List<OrgManagementApiDto> children = org.getChildren();
                    if (children == null) {
                        newOrg.setChildren(new ArrayList<>()); // 如果子部门为 null，设置为空列表
                    } else {
                        // 过滤子部门：只保留不在 objectIdList 中的子部门
                        newOrg.setChildren(children.stream()
                                .filter(child -> !objectIdList.contains(child.getCode()))
                                .collect(Collectors.toList()));
                    }
                    // 如果当前部门的 code 在 objectIdList 中，则过滤掉整个部门
                    if (objectIdList.contains(newOrg.getCode())) {
                        return null; // 过滤掉当前部门
                    }
                    return newOrg;
                })
                .filter(org -> org != null) // 过滤掉为 null 的部门
                .collect(Collectors.toList());
    }

    /**
     * 递归查找所有子分类
     */
    private static void findAllChildren(List<Category> allCategories, Long parentId, List<Category> result) {
        for (Category category : allCategories) {
            if (parentId.equals(category.getParentId())) {
                result.add(category);
                // 递归查找当前分类的子分类
                findAllChildren(allCategories, category.getId(), result);
            }
        }
    }

    /**
     * 根据权限深度优先查询，构建分类树形结构
     * @param categoryDto   分类dto
     * @return  分类列表
     */
    @Override
    public List<CategoryDto> selectCategoryListDFS(CategoryDto categoryDto) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        CurrentUser currentUser = CurrentUserUtil.getCurrentUser();
        // 一次性查询所有分类数据
        List<Category> allCategories = categoryMapper.selectAllCategories();
        List<Category> resultList = new ArrayList<>();
        //http查询时，如果指定了分类编码，则查询条件加上分类编码，否则按照原有逻辑查询
        if(StringUtils.isNotBlank(categoryDto.getCode())){
            List<Category> categoryByCode = categoryMapper.getCategoryByCode(categoryDto.getCode());
            for(Category targetCategory  : categoryByCode){
                if (targetCategory != null) {
                    resultList.add(targetCategory);
                    // 递归查找所有子分类
                    findAllChildren(allCategories, targetCategory.getId(), resultList);
                }
            }
            //返回分类数据
            return BeanUtils.copy(resultList, CategoryDto.class);
        }
        // 一次性查询所有分类部门关系
        List<OrgBean> allRelations = new ArrayList<>();
        //如果走的是角色权限，那么就不再查询分类跟部门的关系，后期需要调整
        if(!myScriptService.getRolePermission()){
            allRelations = categoryMapper.selectAllOrgRelations();
        }
//        else{
//            allCategories = categoryMapper.selectAllCategories();
//        }
        //获取权限，如果是走角色权限，那么当前角色绑定的分类与未绑定任何角色的分类数据才返回
//        if(myScriptService.getRolePermission()){
//            //1、根据用户获取所有角色,获取角色id
//            List<RoleApiDto> roleList = iRoleApi.selectRoleListByLoginName(currentUser.getLoginName());
//            List<Long> roleIdList = roleList.stream()
//                    .map(RoleApiDto::getId)
//                    .collect(Collectors.toList());
//            //2、根据角色id获取所有绑定的分类
//            List<Long> categoryIdsByRoleIds = categoryMapper.getCategoryIdsByRoleIds(roleIdList);
//            //3、查询所有未绑定任何角色的分类
//            List<Category> categoryWithOutRoleRelation = categoryMapper.getCategoryWithOutRoleRelation();
//            List<Long> idList = categoryWithOutRoleRelation.stream()
//                    .map(Category::getId)
//                    .collect(Collectors.toList());
//            categoryIdsByRoleIds.addAll(idList);
//            //根据分类id整理结果集
//            List<Category> categories = filterCategoryTree(allCategories, categoryIdsByRoleIds);
//            return BeanUtils.copy(categories, CategoryDto.class);
//        }
        // 构建分类与部门的映射表
        Map<Long, Set<String>> categoryDepartmentMap = allRelations.stream()
                .collect(Collectors.groupingBy(OrgBean::getCategoryId, Collectors.mapping(OrgBean::getCode, Collectors.toSet())));
        // 查询当前部门的分类 ID 集合
        Set<Long> departmentCategoryIds = getDepartmentCategoryIds(currentUser.getOrgCode(), categoryDepartmentMap);
        // 初始化结果列表
        List<Category> result = new ArrayList<>();

        // 使用 Iterator 安全地删除元素
        Iterator<Category> iterator = allCategories.iterator();
        while (iterator.hasNext()) {
            Category category = iterator.next();
            if (category.getLevel() == 1) {
                iterator.remove();
                result.add(category);
            }
        }
        //根据一级分类列表遍历剩余分类，深度递归遍历
        Iterator<Category> iterator1 = result.iterator();
        while (iterator1.hasNext()) {
            Category category = iterator1.next();
            processCategoryTree(category, allCategories, departmentCategoryIds, categoryDepartmentMap);

            // 如果当前节点的所有子节点都不满足条件，返回 false
            if ((category.getChildren() == null || category.getChildren().isEmpty()) && isValidCategory(category, departmentCategoryIds, categoryDepartmentMap)) {
                iterator1.remove();  // 安全地移除元素
            }
        }
        stopWatch.stop();
        logger.info("深度查询耗时{}",stopWatch.getTotalTimeMillis());
        // 将结果转换为 CategoryDto 列表
        return BeanUtils.copy(result, CategoryDto.class);
    }

    public List<Category> filterCategoryTree(List<Category> allCategories, List<Long> resultList) {
        // 1. 获取所有需要保留的categoryId（包括直接匹配的和所有父节点）
        Set<Long> requiredCategoryIds = collectRequiredCategoryIds(allCategories, resultList);
        // 2. 构建ID到Category的映射
        Map<Long, Category> categoryMap = allCategories.stream()
                .collect(Collectors.toMap(Category::getId, c -> c));
        // 3. 构建父节点到子节点的映射
        Map<Long, List<Long>> parentToChildrenMap = allCategories.stream()
                .filter(c -> c.getParentId() != null)
                .collect(Collectors.groupingBy(
                        Category::getParentId,
                        Collectors.mapping(Category::getId, Collectors.toList())
                ));
        // 4. 过滤并重建树形结构
        List<Category> result = new ArrayList<>();
        for (Category category : allCategories) {
            if (requiredCategoryIds.contains(category.getId())) {
                Category cloned = cloneCategory(category);
                // 处理子节点，只保留在requiredCategoryIds中的子节点
                processChildren(cloned, categoryMap, parentToChildrenMap, requiredCategoryIds);
                result.add(cloned);
            }
        }
        return result;
    }

    private Set<Long> collectRequiredCategoryIds(List<Category> allCategories, List<Long> resultList) {
        // 获取resultList中的所有categoryId
        Set<Long> directCategoryIds = new HashSet<>(resultList);

        // 构建子节点到父节点的映射
        Map<Long, Long> childToParentMap = allCategories.stream()
                .filter(c -> c.getParentId() != null)
                .collect(Collectors.toMap(Category::getId, Category::getParentId));

        Set<Long> requiredCategoryIds = new HashSet<>();

        // 遍历所有直接匹配的categoryId，递归查找所有父节点
        for (Long categoryId : directCategoryIds) {
            addWithAncestors(categoryId, childToParentMap, requiredCategoryIds);
        }

        return requiredCategoryIds;
    }

    private void addWithAncestors(Long categoryId, Map<Long, Long> childToParentMap, Set<Long> result) {
        if (categoryId == null || result.contains(categoryId)) {
            return;
        }

        result.add(categoryId);
        Long parentId = childToParentMap.get(categoryId);
        if (parentId != null) {
            addWithAncestors(parentId, childToParentMap, result);
        }
    }

    private void processChildren(Category category,
                                 Map<Long, Category> categoryMap,
                                 Map<Long, List<Long>> parentToChildrenMap,
                                 Set<Long> requiredCategoryIds) {
        if (category.getChildren() == null) {
            return;
        }

        // 获取当前节点的所有子节点ID
        List<Long> childIds = parentToChildrenMap.getOrDefault(category.getId(), Collections.emptyList());

        List<CategoryApiDto> filteredChildren = new ArrayList<>();
        for (Long childId : childIds) {
            if (requiredCategoryIds.contains(childId)) {
                Category child = categoryMap.get(childId);
                if (child != null) {
                    CategoryApiDto childDto = convertToDto(child);
                    // 递归处理子节点
                    processChildrenDto(childDto, categoryMap, parentToChildrenMap, requiredCategoryIds);
                    filteredChildren.add(childDto);
                }
            }
        }
        category.setChildren(filteredChildren);
    }

    private void processChildrenDto(CategoryApiDto categoryDto,
                                    Map<Long, Category> categoryMap,
                                    Map<Long, List<Long>> parentToChildrenMap,
                                    Set<Long> requiredCategoryIds) {
        if (categoryDto.getChildren() == null) {
            return;
        }

        // 获取当前节点的所有子节点ID
        List<Long> childIds = parentToChildrenMap.getOrDefault(categoryDto.getId(), Collections.emptyList());

        List<CategoryApiDto> filteredChildren = new ArrayList<>();
        for (Long childId : childIds) {
            if (requiredCategoryIds.contains(childId)) {
                Category child = categoryMap.get(childId);
                if (child != null) {
                    CategoryApiDto childDto = convertToDto(child);
                    // 递归处理子节点
                    processChildrenDto(childDto, categoryMap, parentToChildrenMap, requiredCategoryIds);
                    filteredChildren.add(childDto);
                }
            }
        }
        categoryDto.setChildren(filteredChildren);
    }

    private Category cloneCategory(Category original) {
        Category clone = new Category();
        clone.setId(original.getId());
        clone.setParentId(original.getParentId());
        clone.setCode(original.getCode());
        clone.setName(original.getName());
        clone.setSort(original.getSort());
        clone.setLevel(original.getLevel());
        clone.setDescription(original.getDescription());
        clone.setCreatorId(original.getCreatorId());
        clone.setCreatorName(original.getCreatorName());
        clone.setUpdatorId(original.getUpdatorId());
        clone.setUpdatorName(original.getUpdatorName());
        clone.setCreateTime(original.getCreateTime());
        clone.setUpdateTime(original.getUpdateTime());
        clone.setChildren(original.getChildren());
        // children需要单独处理
        return clone;
    }

    private CategoryApiDto convertToDto(Category category) {
        CategoryApiDto dto = new CategoryApiDto();
        dto.setId(category.getId());
        dto.setParentId(category.getParentId());
        dto.setCode(category.getCode());
        dto.setName(category.getName());
        dto.setSort(category.getSort());
        dto.setLevel(category.getLevel());
        dto.setDescription(category.getDescription());
        dto.setCreatorId(category.getCreatorId());
        dto.setCreatorName(category.getCreatorName());
        dto.setUpdatorId(category.getUpdatorId());
        dto.setUpdatorName(category.getUpdatorName());
        dto.setCreateTime(category.getCreateTime());
        dto.setUpdateTime(category.getUpdateTime());
        dto.setChildren(category.getChildren());
        // children需要单独处理
        return dto;
    }

    // 主方法，启动递归操作
    public void processCategoryTree(Category root, List<Category> categoryList, Set<Long> departmentCategoryIds, Map<Long, Set<String>> categoryDepartmentMap) {
        // 调用递归方法进行深度优先遍历
        processCategoryDFS(root, categoryList, departmentCategoryIds, categoryDepartmentMap);
    }

    private boolean processCategoryDFS(Category root, List<Category> categoryList, Set<Long> departmentCategoryIds, Map<Long, Set<String>> categoryDepartmentMap) {
        // 递归查找符合条件的子节点
        List<Category> validSubCategories = new ArrayList<>();

        // 遍历所有子节点，深度遍历
        for (Category category : categoryList) {
            if (Objects.equals(category.getParentId(), root.getId()) && processCategoryDFS(category, categoryList, departmentCategoryIds, categoryDepartmentMap)) {
                validSubCategories.add(category);
            }
        }

        // 如果当前节点的所有子节点都不满足条件，返回 false
        if (validSubCategories.isEmpty() && isValidCategory(root, departmentCategoryIds, categoryDepartmentMap)) {
            return false;
        }

        // 如果当前节点或某些子节点符合条件，将其标记为有效子节点
        root.setChildren(validSubCategories.stream()
                .map(cat -> BeanUtils.copy(cat, CategoryApiDto.class))
                .collect(Collectors.toList()));

        // 返回 true，表示当前节点或其子节点符合条件
        return true;
    }

    // 判断分类是否符合条件
    private boolean isValidCategory(Category category, Set<Long> departmentCategoryIds, Map<Long, Set<String>> categoryDepartmentMap) {
        //没有部门绑定关系或者分类绑定了当前部门返回false
        return categoryDepartmentMap.containsKey(category.getId()) && !departmentCategoryIds.contains(category.getId());
    }


    /**
     * 根据当前组织机构编码获取部门下的分类
     * @param orgCode   组织机构编码
     * @param categoryDepartmentMap 分类部门映射
     * @return  分类
     */
    private Set<Long> getDepartmentCategoryIds(String orgCode, Map<Long, Set<String>> categoryDepartmentMap) {
        if(orgCode != null && !orgCode.isEmpty()){
            return categoryDepartmentMap.entrySet().stream()
                    .filter(entry -> entry.getValue().stream().anyMatch(value -> value.startsWith(orgCode)))
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toSet());
        }
        //admin用户没有orgCode,默认全部返回
        return categoryDepartmentMap.keySet();
    }

    @Override
    public List<Long> getCategoryByOrgCode(String orgCode) {
        return categoryMapper.getCategoryByOrgCode(orgCode);
    }

    @Override
    public List<UserBean> getUserByCategoryIds(List<Long> categoryIdList) {
        return categoryMapper.getUserByCategoryIds(categoryIdList);
    }

    /**
     * 为查询条件Bean设置用户组织机构权限相关信息
     * 此方法用于设置Bean对象中的权限相关字段，主要包括：
     * 1. 查询当前分类及其子分类的ID集合
     * 2. 设置用户组织机构编码
     * 3. 针对非管理员用户，计算分类全路径作为权限匹配条件
     *
     * @param categoryId 分类ID
     * @param currentUser 当前用户
     * @return 权限相关信息的映射对象，可用于设置Bean的相关属性
     */
    @Override
    public CategoryPermissionInfo setCategoryPermissionInfo(Long categoryId, CurrentUser currentUser) {
        CategoryPermissionInfo result = new CategoryPermissionInfo();
        
        // 查询当前分类以及子分类的ID集合
        if (categoryId != null && categoryId != 0) {
            List<Long> categoryIdList = getAllCategoryIds(categoryId);
            result.setCategoryIdList(categoryIdList);
        }
        
        // 设置用户组织机构编码
        result.setSysOrgCode(currentUser.getOrgCode());
        
        if (currentUser.getSupervisor()) {
            result.setSuperUser(currentUser.getSupervisor());
            result.setSysOrgCode("####");
        } else {
            List<String> categoryPathList = new ArrayList<>();

            // 计算分类的全路径作为权限匹配脚本
            List<Long> categortIdList = getCategoryByOrgCode(currentUser.getOrgCode());
            for (Long cId : categortIdList) {
                Category category = new Category();
                category.setId(cId);
                String categoryFullPath = buildCategoryPath(category);
                // 针对%和_这种拼接进行处理
                String escapedLikeCategoryPath = handleCategoryPath(categoryFullPath);
                categoryPathList.add(escapedLikeCategoryPath + "%");
            }
            
            result.setOrgCategoryPath(categoryPathList);
        }
        
        return result;
    }

    /**
     * 为查询条件Bean设置用户组织机构权限相关信息
     * 此方法直接将权限信息设置到实现了CategoryPermissionAware接口的Bean对象中
     *
     * @param bean CategoryPermissionAware类型的Bean对象
     * @param currentUser 当前用户
     */
    @Override
    public <T extends CategoryPermissionAware> void setCategoryPermission(T bean, CurrentUser currentUser) {
        //判断是否执行角色查询权限，注意，值班任务申请时不需要处理，保持获取所有数据
        boolean rolePermission = myScriptService.getRolePermission();
        // 获取权限信息
        CategoryPermissionInfo permissionInfo = setCategoryPermissionInfo(bean.getCategoryId(), currentUser);
        if (permissionInfo != null) {
            // 每个属性不为null时，直接设置到Bean对象
            if(rolePermission && !currentUser.getSupervisor()){
                List<String> categoryPathList = new ArrayList<>();
                List<Long> excludeUserIds = new ArrayList<>();
                //1、根据用户获取角色id集合
                List<Long> idList = getShareTypeRoleIdsByLoginName(currentUser.getLoginName());
                //2、根据1中查到的角色去分类角色表、分类表中查询绑定到这些角色上的分类
                if(ObjectUtils.notEqual(idList,null) && !idList.isEmpty()){
                    List<Long> categortIdList = categoryMapper.getCategoryIdsByRoleIds(idList);

                    for (Long categoryId : categortIdList) {
                        String categoryFullPath = getCategoryFullPath(categoryId);
                        //针对%和_这种拼接进行处理
                        String escapedLikeCategoryPath = handleCategoryPath(categoryFullPath);
                        if(StringUtils.isNotBlank(escapedLikeCategoryPath) && !categoryPathList.contains(escapedLikeCategoryPath + "%")){
                            categoryPathList.add(escapedLikeCategoryPath + "%");
                            bean.setOrgCategoryPath(categoryPathList);
                        }
                    }

                    bean.setCategoryIdList(categortIdList);
                    //获取角色下的所有用户
                    ServicePermissionApiQueryDto servicePermissionApiQueryDto = new ServicePermissionApiQueryDto();
                    servicePermissionApiQueryDto.setRoleIds(idList);
                    List<PermissionUserInfoApiDto> permissionUserInfoList =  userInfoApi.queryPermissionUserInfoList(servicePermissionApiQueryDto);
                    //用户id赋值
                    excludeUserIds = permissionUserInfoList.stream()
                            .map(PermissionUserInfoApiDto::getId)
                            .collect(Collectors.toList());
                    //分类id不为空，说名根据分类查询了数据，此时获取路径
                    if(ObjectUtils.notEqual(bean.getCategoryId(),null)){
                        String categoryFullPath = getCategoryFullPath(bean.getCategoryId());
                        //针对%和_这种拼接进行处理
                        String escapedLikeCategoryPath = handleCategoryPath(categoryFullPath);
                        if(StringUtils.isNotBlank(escapedLikeCategoryPath) && !categoryPathList.contains(escapedLikeCategoryPath + "%")){
                            categoryPathList.add(escapedLikeCategoryPath + "%");
                            bean.setOrgCategoryPath(categoryPathList);
                        }
                    }
                }
                //如果未绑定任何分类，则赋值一个完全不存在的值，防止结果将所有分类相关结果查出
                if(categoryPathList.isEmpty()){
                    List<String> emptyPath = new ArrayList<>();
                    emptyPath.add("########");
                    bean.setOrgCategoryPath(emptyPath);
                }

                //走角色权限赋值
                bean.setRoleFlag(true);
                //用户id设置
                if(!ObjectUtils.notEqual(excludeUserIds,null)
                    || excludeUserIds.isEmpty()){
                    excludeUserIds.add(currentUser.getId());
                }
                bean.setUserIdList(excludeUserIds);
                //角色id设置
                bean.setRoleIdList(idList);
                //超级管理员权限赋值
                bean.setSuperUser(currentUser.getSupervisor());

            }else{
                if (CollectionUtils.isNotEmpty(permissionInfo.getCategoryIdList())) {
                    bean.setCategoryIdList(permissionInfo.getCategoryIdList());
                }
                if (Objects.nonNull(permissionInfo.getSuperUser())) {
                    bean.setSuperUser(permissionInfo.getSuperUser());
                }
                if (CollectionUtils.isNotEmpty(permissionInfo.getOrgCategoryPath())) {
                    bean.setOrgCategoryPath(permissionInfo.getOrgCategoryPath());
                }
            }
            if (StringUtils.isNotBlank(permissionInfo.getSysOrgCode())) {
                bean.setSysOrgCode(permissionInfo.getSysOrgCode());
            }
        }
    }

    @Override
    public PageInfo<RoleApiDto> selectRoleManagementList(RoleApiDto queryParam, Integer pageNum, Integer pageSize) {
        queryParam.setDataShare(1);
        return iRoleApi.selectRolePage(pageNum,pageSize,queryParam);
    }

    /**
     * 递归查询所有子分类
     * @param categoryIds 分类id
     * @return 所有子分类id
     */
    private List<Long> getAllSonCategoryId(List<Long> categoryIds) {
        List<Long> categoryIdList = new ArrayList<>();
        for (Long categoryId : categoryIds) {
            List<Long> sonIds = categoryMapper.selectChildCategoryIdList(categoryId);
            if (sonIds != null && !sonIds.isEmpty()) {
                categoryIdList.addAll(sonIds);
                // 合并递归结果
                categoryIdList.addAll(getAllSonCategoryId(sonIds));
            }
        }
        return categoryIdList;
    }

    /**
     * 脚本分类绑定角色
     * @param categoryRoleDto 实体dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void assignCategoryToRole(CategoryRoleDto categoryRoleDto) {
        List<CategoryRoleBean> addList = new ArrayList();
        // 前端保存的绑定的角色信息
        List<RoleApiDto> roleSelList = categoryRoleDto.getRoleList();

        //获取分类下的所有子分类
        List<Long> allCategoryList = new ArrayList<>();
        allCategoryList.add(categoryRoleDto.getCategoryId());
        List<Long> allSonCategoryId = getAllSonCategoryId(allCategoryList);
        //将所选的分类放到list
        allSonCategoryId.add(categoryRoleDto.getCategoryId());
        //如果角色列表为空，说明该分类取消了所有绑定的角色，此时此分类及其子分类删除所有绑定的数据
        if(roleSelList == null || roleSelList.isEmpty()){
            categoryMapper.deleteCategoryRoleByCategoryIds(allSonCategoryId);
            return;
        }
        // 获取需要插入的新关系
        for(RoleApiDto roleApiDto : roleSelList){
            //绑定所选分类所有子类信息
            for(Long categoryId : allSonCategoryId){
                CategoryRoleBean categoryRoleSonBean = new CategoryRoleBean();
                categoryRoleSonBean.setCategoryId(categoryId);
                categoryRoleSonBean.setRoleId(roleApiDto.getId());
                addList.add(categoryRoleSonBean);
            }
        }
        //根据分类id删除  分类与角色绑定关系
        List<Long> categoryList = addList.stream()
                .map(CategoryRoleBean::getCategoryId)
                .collect(Collectors.toList());
        if(ObjectUtils.notEqual(categoryList,null) && !categoryList.isEmpty()){
            categoryMapper.deleteCategoryRoleByCategoryIds(categoryList);
        }
        //新增分类-角色绑定关系
        if (!addList.isEmpty()) {
            Consumer<CategoryRoleBean> consumer = categoryMapper::insertCategoryRole;
            batchHandler.batchData(addList, consumer);
        }
    }

    @Override
    public CategoryRoleDto getCategoryRoleRelations(long categoryId) {
        CategoryRoleDto categoryRoleDto = new CategoryRoleDto();
        List<RoleApiDto> RoleList = new ArrayList<>();
        List<CategoryRoleBean> existingRelations = categoryMapper.getCategoryRoleRelations(categoryId);
        for(CategoryRoleBean categoryRoleBean:existingRelations){
            RoleApiDto roleApiDto = new RoleApiDto();
            roleApiDto.setId(categoryRoleBean.getRoleId());
            RoleList.add(roleApiDto);
        }
        categoryRoleDto.setRoleList(RoleList);
        categoryRoleDto.setCategoryId(categoryId);

        return categoryRoleDto;
    }

    @Override
    public PageInfo<PermissionUserInfoApiDto> queryPermissionUserInfoPageByRole(CategoryRoleDto queryParam, Integer pageNum, Integer pageSize) {
        List<CategoryRoleBean> existingRelations = categoryMapper.getCategoryRoleRelations(queryParam.getCategoryId());
        if(existingRelations.isEmpty()){
            return new PageInfo<>();
        }

        List<Long> roleIds = new ArrayList<>();
        for(CategoryRoleBean categoryRoleBean:existingRelations){
            roleIds.add(categoryRoleBean.getRoleId());
        }
        ServicePermissionApiQueryDto servicePermissionApiQueryDto = new ServicePermissionApiQueryDto();
        servicePermissionApiQueryDto.setRoleIds(roleIds);
        servicePermissionApiQueryDto.setPermissionCodes(queryParam.getServicePermissionApiQueryDto().getPermissionCodes());

        return userInfoApi.queryPermissionUserInfoPage(servicePermissionApiQueryDto,pageNum,pageSize);
    }

    /**
     * 根据登录名获取共享类型角色id集合
     * @param loginName 登录名
     * @return 角色id集合
     */
    @Override
    public List<Long> getShareTypeRoleIdsByLoginName(String loginName){
        List<RoleApiDto> roleApiDtos = iRoleApi.selectRoleListByLoginName(loginName);
        return roleApiDtos.stream()
                .filter(roleApi -> roleApi.getDataShare() == 1)
                .map(RoleApiDto::getId)
                .collect(Collectors.toList());
    }

    /**
     * 根据一级分类名称列表查询一级分类信息列表
     * @param categoryNameList 一级分类名称列表
     * @return 一级分类信息列表
     */
    @Override
    public List<Category> getFirstCategoryByCategoryNameList(List<String> categoryNameList) {
        return categoryMapper.getFirstCategoryByCategoryNameList(categoryNameList);
    }

}
