package com.ideal.script.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.common.util.batch.Batch;
import com.ideal.script.model.dto.ScriptVersionShareDto;
import com.ideal.script.mapper.ScriptVersionShareMapper;
import com.ideal.script.model.dto.interaction.agent.UserInfoDto;
import com.ideal.script.model.entity.ScriptVersionShare;
import com.ideal.script.service.IScriptVersionShareService;
import com.ideal.system.api.IOrgManagement;
import com.ideal.system.api.IRole;
import com.ideal.system.api.IUserInfo;
import com.ideal.system.dto.OrgManagementApiDto;
import com.ideal.system.dto.RoleApiDto;
import com.ideal.system.dto.UserInfoApiDto;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 【脚本共享】Service业务层处理
 *
 * <AUTHOR>
 */
@Transactional(rollbackFor = Exception.class)
@Service
public class ScriptVersionShareServiceImpl implements IScriptVersionShareService, Batch {

    private final ScriptVersionShareMapper scriptVersionShareMapper;
    private final IUserInfo iUserInfoApi;
    private final IOrgManagement orgManagementApi;
    private final IRole iRole;

    public ScriptVersionShareServiceImpl(IRole iRole,IOrgManagement orgManagementApi,IUserInfo iUserInfoApi,ScriptVersionShareMapper scriptVersionShareMapper) {
        this.iRole = iRole;
        this.orgManagementApi = orgManagementApi;
        this.iUserInfoApi = iUserInfoApi;
        this.scriptVersionShareMapper = scriptVersionShareMapper;
    }


    /**
     * 新增
     *
     * @param scriptVersionShareDtoList 【脚本共享信息Dto】
     */
    @Override
    public void insertScriptVersionShare(List<ScriptVersionShareDto> scriptVersionShareDtoList) {
        if(ObjectUtils.notEqual(scriptVersionShareDtoList,null)){
            if(scriptVersionShareDtoList.get(0).getShareType() == 1){
                scriptVersionShareDtoList = filterUpperDepartments(scriptVersionShareDtoList);
            }
        }
        this.batchData(BeanUtils.copy(scriptVersionShareDtoList,ScriptVersionShare.class), scriptVersionShareMapper::insertScriptVersionShare);
    }

    private List<ScriptVersionShareDto> filterUpperDepartments(List<ScriptVersionShareDto> list) {
        // 创建一个新的列表，避免修改原始列表
        List<ScriptVersionShareDto> resultList = new ArrayList<>(list);

        // 将 shareObjectId 存入 HashSet 以便快速查找
        Set<String> idSet = new HashSet<>();
        for (ScriptVersionShareDto dto : list) {
            idSet.add(dto.getShareObjectId());
        }

        // 使用迭代器遍历，方便删除元素
        Iterator<ScriptVersionShareDto> iterator = resultList.iterator();
        while (iterator.hasNext()) {
            ScriptVersionShareDto current = iterator.next();
            String currentId = current.getShareObjectId();

            // 检查是否有其他元素的 shareObjectId 是以当前元素的 shareObjectId 开头的
            boolean isPrefix = idSet.stream()
                    .anyMatch(other -> !other.equals(currentId)
                            && other.startsWith(currentId));

            // 如果当前元素是其他元素的前缀，则移除当前元素
            if (isPrefix) {
                iterator.remove();
            }
        }

        return resultList;
    }

    /**
     * 获取共享数据
     * @param scriptVersionShareDto 参数
     * @return 共享数据
     */
    @Override
    public PageInfo<ScriptVersionShareDto> selectShareScriptData(ScriptVersionShareDto scriptVersionShareDto,Integer pageNum,Integer pageSize){
        //查询已共享的数据
        PageMethod.startPage(pageNum, pageSize);
        ScriptVersionShare scriptVersionShare = BeanUtils.copy(scriptVersionShareDto, ScriptVersionShare.class);
        List<ScriptVersionShare> scriptVersionShares = scriptVersionShareMapper.selectShareScriptData(scriptVersionShare);
        //因为已共享的数据包含共享给用户、部门、所有人，所以要把对应的任命、部门名称查询出来
        List<Long> userList = new ArrayList<>();
        //将共享给用户、部门的数据分开，然后分别查询用户名与部门名称
        //查询用户信息
        List<UserInfoApiDto> userInfoList = iUserInfoApi.getUserInfoList(userList);
        //整合数据
        for(ScriptVersionShare scriptVersionShare1 : scriptVersionShares){
            //共享的为用户
            if(scriptVersionShare1.getShareType() == 0){
                for(UserInfoApiDto userInfoApiDto : userInfoList){
                    if(userInfoApiDto.getId().equals(Long.valueOf(scriptVersionShare1.getShareObjectId()))){
                        scriptVersionShare1.setShareObjectName(userInfoApiDto.getFullName());
                        break;
                    }
                }
            }
            //共享的为部门
            if(scriptVersionShare1.getShareType() == 1){
                //根据部门id查询部门信息
                String[] splitStr = scriptVersionShare1.getShareObjectId().split("#");
                OrgManagementApiDto orgManagementApiDto1 = orgManagementApi.selectOrgManagementById(Long.valueOf(splitStr[splitStr.length-1]));
                scriptVersionShare1.setShareObjectName(orgManagementApiDto1.getName());
            }
            //共享给所有人
            if(scriptVersionShare1.getShareType() == 2){
                scriptVersionShare1.setShareObjectName("所有人");
            }
            //共享给角色
            if(scriptVersionShare1.getShareType() == 3){
                //查询角色信息
                RoleApiDto roleApiDto = new RoleApiDto();
                List<String> roleIds = new ArrayList<>();
                roleIds.add(scriptVersionShare1.getShareObjectId());
                roleApiDto.setAppointRoleIds(roleIds);
                PageInfo<RoleApiDto> roleApiDtoPageInfo = iRole.selectRolePage(1, 1, roleApiDto);
                List<RoleApiDto> list = roleApiDtoPageInfo.getList();
                //给角色名字赋值
                if(!list.isEmpty()){
                    scriptVersionShare1.setShareObjectName(list.get(0).getName());
                }
            }
        }

        return PageDataUtil.toDtoPage(scriptVersionShares, ScriptVersionShareDto.class);
    }

    /**
     * 获取共享用户
     * @param userId 用户id
     * @param pageNum 页码
     * @param pageSize 分页大小
     * @param scriptVersionShareDto 参数
     * @return 用户分页信息
     */
    @Override
    public PageInfo<UserInfoDto> getShareUser(Long userId, Integer pageNum, Integer pageSize, ScriptVersionShareDto scriptVersionShareDto) {
        // 调用接口获取全部数据
        List<UserInfoApiDto> userInfoList = iUserInfoApi.queryUserInfoListByOtherOrgManagement(userId);
        List<UserInfoApiDto> returnUserList = new ArrayList<>();
        //去掉已经共享过的用户
        List<Long> sharedUserIdList = scriptVersionShareMapper.getSharedUser(scriptVersionShareDto.getScriptInfoId());
        for(UserInfoApiDto userInfoApiDto : userInfoList){
            if(!sharedUserIdList.contains(userInfoApiDto.getId())){
                if(ObjectUtils.notEqual(scriptVersionShareDto.getFullName(),null)){
                    if(userInfoApiDto.getFullName().contains(scriptVersionShareDto.getFullName())){
                        returnUserList.add(userInfoApiDto);
                    }
                } else {
                    returnUserList.add(userInfoApiDto);
                }
            }
        }
        // 手动分页
        int total = returnUserList.size(); // 总记录数
        int fromIndex = (pageNum - 1) * pageSize; // 起始索引
        int toIndex = Math.min(fromIndex + pageSize, total); // 结束索引

        // 获取分页后的子列表
        List<UserInfoApiDto> pageList = returnUserList.subList(fromIndex, toIndex);

        // 将分页后的数据转换为目标类型
        List<UserInfoDto> dtoList = BeanUtils.copy(pageList, UserInfoDto.class);

        // 封装分页信息
        PageInfo<UserInfoDto> pageInfo = new PageInfo<>();
        pageInfo.setPageNum(pageNum);
        pageInfo.setPageSize(pageSize);
        pageInfo.setTotal(total); // 总记录数
        pageInfo.setList(dtoList); // 当前页的数据
        return pageInfo;
    }

    /**
     * 获取未被共享过的角色
     * @param pageNum 分页
     * @param pageSize 页码
     * @param scriptVersionShareDto 入参dto
     * @return 未共享的角色
     */
    @Override
    public PageInfo<RoleApiDto> getNotShareRoles(Integer pageNum, Integer pageSize, ScriptVersionShareDto scriptVersionShareDto) {
        //查询已共享的数据
        PageMethod.startPage(pageNum, pageSize);
        //查询本地共享角色关系表，获取所有已共享的角色，然后把这些id传给平台管理，平台管理会返回分页数据，数据为未共享的角色
        List<String> objectIdList = scriptVersionShareMapper.getObjectIdList(scriptVersionShareDto.getScriptInfoId(), scriptVersionShareDto.getShareType());
        List<Long> longList = objectIdList.stream()
                .map(Long::valueOf)
                .collect(Collectors.toList());
        RoleApiDto roleApiDto = new RoleApiDto();
        roleApiDto.setExcludeRoleIds(longList);
        roleApiDto.setName(scriptVersionShareDto.getShareObjectName());
        return iRole.selectRolePage(pageNum, pageSize, roleApiDto);
    }

    /**
     * 删除
     *
     * @param id 【脚本共享】主键
     */
    @Override
    public void deleteScriptVersionShareByIds(Long [] id) {
        scriptVersionShareMapper.deleteScriptVersionShareByIds(id);
    }

    /**
     * 获取共享的用户id或者部门id
     * @param scriptVersionId 脚本版本id
     * @param shareType 共享类型 0共享用户，1共享部门，2共享所有人
     * @return 返回共享的用户id或者部门id
     */
    @Override
    public List<String> getObjectIdList(Long scriptVersionId, Short shareType){
        return scriptVersionShareMapper.getObjectIdList(scriptVersionId,shareType);
    }
}
