package com.ideal.script.controller;

import com.ideal.common.dto.R;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.permission.MenuPermitConstant;
import com.ideal.script.model.dto.AuditRelationDto;
import com.ideal.script.service.IAuditRelationService;
import com.ideal.system.common.component.aop.MethodPermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 双人复核与脚本服务化关系Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("${app.script-tools-url:}/relation")
@MethodPermission(MenuPermitConstant.MY_SCRIPT_OR_DOUBLE_CHECK_PER)
public class AuditRelationController {
    private final IAuditRelationService auditRelationService;
    private static final Logger logger = LoggerFactory.getLogger(AuditRelationController.class);


    public AuditRelationController(IAuditRelationService auditRelationService) {
        this.auditRelationService = auditRelationService;
    }

    /**
     * 根据双人复核id查询脚本id以及版本uuid
     *
     * @param id 双人复核与脚本服务化关系主键
     * @return {@link R }<{@link AuditRelationDto }>
     */
    @GetMapping("/selectInfoIdAndSrcScriptUuidByAuditRelationId")
    public R<AuditRelationDto> selectInfoIdAndSrcScriptUuidByAuditRelationId(@RequestParam(value = "id") Long id) {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, auditRelationService.selectInfoIdAndSrcScriptUuidByAuditRelationId(id), Constants.LIST_SUCCESS);
    }
}
