package com.ideal.script.service;

import com.github.pagehelper.PageInfo;
import com.ideal.script.model.dto.TaskScheduleDto;
import com.ideal.script.model.dto.TaskScheduleQueryDto;

/**
 * Service接口
 *
 * <AUTHOR>
 * @date 2024-04-07
 */
public interface ITaskScheduleService {
    /**
     * 查询
     *
     * @param id 主键
     * @return 
     */
    TaskScheduleDto selectTaskScheduleById(Long id);

    /**
     * 查询列表
     *
     * @param taskScheduleQueryDto 
     * @param pageNum              页码
     * @param pageSize             单页长度
     * @return 集合
     */
    PageInfo<TaskScheduleDto> selectTaskScheduleList(TaskScheduleQueryDto taskScheduleQueryDto, Integer pageNum, Integer pageSize);

    /**
     * 新增
     *
     * @param taskScheduleDto 
     * @return 结果
     */
    int insertTaskSchedule(TaskScheduleDto taskScheduleDto);

    /**
     * 修改
     *
     * @param taskScheduleDto 
     * @return 结果
     */
    int updateTaskSchedule(TaskScheduleDto taskScheduleDto);

    /**
     * 更新调度任务状态
     * @param taskScheduleDto
     * @return int 结果
     */
    int updateTaskScheduleByScheduleId(TaskScheduleDto taskScheduleDto);

    /**
     * 批量删除
     *
     * @param ids 需要删除的主键集合
     * @return 结果
     */
    int deleteTaskScheduleByIds(Long[] ids);

    /**
     * 获取定时任务信息
     *
     * @param taskId 脚本任务Id
     * @return {@link TaskScheduleDto }
     */
    TaskScheduleDto selectTaskScheduleByTaskId(Long taskId);
}
