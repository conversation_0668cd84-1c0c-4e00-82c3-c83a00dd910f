package com.ideal.script.service;

import com.github.pagehelper.PageInfo;
import com.ideal.script.dto.CategoryApiDto;
import com.ideal.script.dto.CategoryQueryDto;
import com.ideal.script.dto.ScriptCategoryIconDto;
import com.ideal.script.dto.ScriptInfoApiDto;
import com.ideal.script.dto.ScriptInfoDto;
import com.ideal.script.dto.ScriptInfoQueryDto;

import java.util.List;

/**
 * 脚本信息Service业务层处理
 *
 * <AUTHOR>
 */
public interface IInfoService {
    /**
     * 查询
     *
     * @param id 主键
     * @return 
     */
    ScriptInfoDto selectInfoById(Long id);

    /**
     * 根据脚本唯一uuid查询脚本信息
     *
     * @param uniqueUuid 脚本uuid
     * @return ScriptInfoDto
     */
    ScriptInfoDto selectInfoByUniqueUuid(String uniqueUuid);


    /**
     * 查询列表
     *
     * @param infoDto  
     * @param pageNum  
     * @param pageSize 
     * @return 集合
     */
    PageInfo<ScriptInfoDto> selectInfoList(ScriptInfoDto infoDto, int pageNum, int pageSize);

    /**
     * 获取脚本列表
     *
     * @param scriptId        脚本id
     * @param scriptName      脚本名称
     * @param excludeScriptId 不需要的脚本id
     * @return 结果
     */
    List<ScriptInfoDto> selectInfoListApi(Long scriptId, String scriptName, List<Long> excludeScriptId);

    /**
     * 新增
     *
     * @param infoDto 
     */
    void insertInfo(ScriptInfoDto infoDto);

    /**
     * 修改
     *
     * @param infoDto 
     */
    void updateInfo(ScriptInfoDto infoDto);

    /**
     * 批量删除
     *
     * @param ids 需要删除的主键集合
     */
    void deleteInfoByIds(Long[] ids);

    /**
     * 删除信息
     *
     * @param id 主键
     * @return 结果
     */
    int deleteInfoById(Long id);


    /**
     * 验证是否存在相同脚本中文名的脚本
     *
     * @param scriptNameZh 脚本中文名
     * @return Boolean
     */

    Boolean validScriptNameZhCountExist(String scriptNameZh);



    /**
     * 查询分类树
     *
     * @param categoryQueryDto 分类查询条件
     * @return {@link List}<{@link CategoryApiDto}>
     */
    List<CategoryApiDto> getCategoryTreeApi(CategoryQueryDto categoryQueryDto);


    /**
     * 查询脚本基础信息列表
     *
     * @param scriptInfoQueryDto 脚本信息查询条件
     * @return {@link List}<{@link ScriptInfoApiDto}>
     */
    List<ScriptInfoApiDto> getScriptInfoListApi(ScriptInfoQueryDto scriptInfoQueryDto);

    /**
     * 分页查询脚本基础信息列表
     *
     * @param scriptInfoQueryDto 脚本信息查询条件
     * @return {@link PageInfo}<{@link ScriptInfoApiDto}>
     */
    PageInfo<ScriptInfoApiDto> getScriptInfoPageListApi(ScriptInfoQueryDto scriptInfoQueryDto);

    /**
     * 根据脚本uuid获取图标
     *
     * @param srcScriptUuids 脚本srcUuid集合
     * @return 图标和脚本uuid对应关系列表
     */
    List<ScriptCategoryIconDto> getScriptCategoryIconList(List<String> srcScriptUuids);

}
