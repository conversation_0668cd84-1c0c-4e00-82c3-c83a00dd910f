package com.ideal.script.service.impl;

import com.ideal.script.model.dto.AgentInfoDto;
import com.ideal.script.model.entity.AgentInfo;
import com.ideal.script.service.IAgentInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 异步Agent查询服务
 * 用于并发执行Agent批量查询操作
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-21
 */
@Service
public class AsyncAgentQueryService {

    private static final Logger logger = LoggerFactory.getLogger(AsyncAgentQueryService.class);

    private final IAgentInfoService agentInfoService;

    public AsyncAgentQueryService(IAgentInfoService agentInfoService) {
        this.agentInfoService = agentInfoService;
    }

    /**
     * 异步批量查询Agent信息
     *
     * @param batchAgents 当前批次的agent列表
     * @return CompletableFuture包装的查询结果
     */
    @Async
    public CompletableFuture<List<AgentInfo>> queryAgentsBatchAsync(List<AgentInfoDto> batchAgents) {
        logger.debug("开始异步查询Agent信息，数量：{}", batchAgents.size());
        long startTime = System.currentTimeMillis();

        try {
            // 执行批量查询
            List<AgentInfo> existingAgents = agentInfoService.selectAgentInfoByIpAndPort(batchAgents);

            long endTime = System.currentTimeMillis();
            logger.debug("完成异步查询Agent信息，耗时：{}ms，查询到：{}个",
                        (endTime - startTime), existingAgents.size());

            return CompletableFuture.completedFuture(existingAgents);
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            logger.error("异步查询Agent信息失败，耗时：{}ms", (endTime - startTime), e);

            // 返回失败的CompletableFuture
            CompletableFuture<List<AgentInfo>> failedFuture = new CompletableFuture<>();
            failedFuture.completeExceptionally(e);
            return failedFuture;
        }
    }


}
