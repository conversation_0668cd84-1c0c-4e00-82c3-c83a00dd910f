package com.ideal.script.service;

import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.DangerCmdDto;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 关键命令(高危命令)Service接口
 * 
 * <AUTHOR>
 */
 public interface IDangerCmdService
{
    /**
     * 查询关键命令(高危命令)
     * 
     * @param id 关键命令(高危命令)主键
     * @return 关键命令(高危命令)
     */
     DangerCmdDto selectDangerCmdById(Long id);

    /**
     * 查询关键命令(高危命令)列表
     * @param pageNum 当前页
     * @param pageSize 每页条数
     * @param dangerCmdDto 关键命令(高危命令)
     * @return 关键命令(高危命令)集合
     */
     PageInfo<DangerCmdDto> selectDangerCmdList(DangerCmdDto dangerCmdDto, int pageNum, int pageSize);

    /**
     * 新增关键命令(高危命令)
     * 
     * @param dangerCmdDto 关键命令(高危命令)
     * @return 结果
     * @throws ScriptException  自定义脚本异常
     */
     int insertDangerCmd(DangerCmdDto dangerCmdDto) throws ScriptException;

    /**
     * 修改关键命令(高危命令)
     * 
     * @param dangerCmdDto 关键命令(高危命令)
     * @return 结果
     * @throws ScriptException  自定义脚本异常
     */
     int updateDangerCmd(DangerCmdDto dangerCmdDto) throws ScriptException;

    /**
     * 批量删除关键命令(高危命令)
     * 
     * @param ids 需要删除的关键命令(高危命令)主键集合
     * @return 结果
     */
     int deleteDangerCmdByIds(Long[] ids);

    /**
     * 删除关键命令(高危命令)信息
     * 
     * @param id 关键命令(高危命令)主键
     * @return 结果
     */
     int deleteDangerCmdById(Long id);

    /**
     * 脚本类型转换
     *
     * @param dangerCmdDto 关键命令实体
     */
    void convertScriptType(DangerCmdDto dangerCmdDto);

    void updateDangerCmdByLabel(List<String> deletedLabels);
}
