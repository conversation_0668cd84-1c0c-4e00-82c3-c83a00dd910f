package com.ideal.script.service;

import com.github.pagehelper.PageInfo;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.ParameterManagerDto;

import java.util.List;

/**
 * Service接口
 * 
 * <AUTHOR>
 */
public interface IParameterManagerService 
{
    /**
     * 查询
     * 
     * @param id 主键
     * @return 
     */
     ParameterManagerDto selectParameterManagerById(Long id);

    /**
     * 查询列表
     * 
     * @param parameterManagerDto 
     * @param pageNum 
     * @param pageSize 
     * @return 集合
     */
     PageInfo<ParameterManagerDto> selectParameterManagerList(ParameterManagerDto parameterManagerDto, int pageNum, int pageSize);

    /**
     * 新增
     *
     * @param parameterManagerDto 
     * @throws  ScriptException 自定义脚本异常
     */
     void insertParameterManager(ParameterManagerDto parameterManagerDto) throws ScriptException;

    /**
     * 修改
     *
     * @param parameterManagerDto 
     * @throws  ScriptException 自定义脚本异常
     */
     void updateParameterManager(ParameterManagerDto parameterManagerDto) throws ScriptException;

    /**
     * 批量删除
     *
     * @param ids 需要删除的主键集合
     */
     void deleteParameterManagerByIds(Long[] ids) throws ScriptException;

    /**
     * 删除信息
     * 
     * @param id 主键
     * @return 结果
     */
     int deleteParameterManagerById(Long id);

    /**
     * 查询
     * @return  结果
     */
     List<ParameterManagerDto> selectParameterManagerForScriptEdit();

    Boolean validParamterCheckExist(String paramName);

    ParameterManagerDto selectParameterManagerByName(String paramName);
}
