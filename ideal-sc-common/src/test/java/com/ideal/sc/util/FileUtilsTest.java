package com.ideal.sc.util;

import com.ideal.common.util.FilePathValidator;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.AccessDeniedException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * FileUtils类的单元测试
 */
public class FileUtilsTest {

    @TempDir
    Path tempDir;

    private File testFile;
    private File testZipFile;

    @BeforeEach
    void setUp() throws Exception {
        // 创建测试文件
        testFile = new File(tempDir.toFile(), "testFile.txt");
        try (FileOutputStream fos = new FileOutputStream(testFile)) {
            fos.write("Test content".getBytes(StandardCharsets.UTF_8));
        }


        // 创建测试压缩文件
        testZipFile = new File(tempDir.toFile(), "test.zip");
        testZipFile.createNewFile();
    }

    @AfterEach
    void tearDown() {
        // 清理测试文件
        if (testFile != null && testFile.exists()) {
            testFile.delete();
        }
        if (testZipFile != null && testZipFile.exists()) {
            testZipFile.delete();
        }
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    void testCreateZipFile(boolean parentDirExists) throws Exception {
        // 设置System.getProperty("user.dir")的返回值
        System.setProperty("user.dir", tempDir.toString());

        // 创建或不创建work目录，根据参数
        File workDir = new File(tempDir.toFile(), "work");
        if (parentDirExists) {
            workDir.mkdir();
        } else if (workDir.exists()) {
            workDir.delete();
        }

        try {
            // 执行测试方法
            File result = FileUtils.createZipFile(testFile);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.getPath().contains(".zip"));
            assertTrue(result.getPath().contains(workDir.getPath()));
            assertTrue(workDir.exists()); // 验证目录已创建

            // 测试文件创建失败的情况
            if (result.exists()) {
                // 先删除文件
                result.delete();

                // 创建一个只读目录，使文件创建失败
                File readOnlyDir = new File(workDir, "readonly");
                readOnlyDir.mkdir();
                readOnlyDir.setReadOnly();

                // 使用反射修改UUID.randomUUID()的返回值，使其返回固定值
                try (MockedStatic<UUID> mockedUUID = Mockito.mockStatic(UUID.class)) {
                    UUID fixedUUID = UUID.randomUUID();
                    mockedUUID.when(UUID::randomUUID).thenReturn(fixedUUID);

                    // 修改System.getProperty("user.dir")的返回值，指向只读目录
                    System.setProperty("user.dir", tempDir.toString() + "/work");

                    // 执行测试方法，这次应该会走到创建文件失败的分支
                    try {
                        File result2 = FileUtils.createZipFile(testFile);
                        // 如果没有抛出异常，也要验证结果
                        assertNotNull(result2);
                    } catch (IOException e) {
                        // 预期可能会抛出IOException，这是正常的
                        // 我们只需要确保代码覆盖了失败路径
                    } finally {
                        // 恢复目录权限
                        readOnlyDir.setWritable(true);
                        readOnlyDir.delete();
                    }
                }
            }
        } finally {
            // 清理
            if (workDir.exists()) {
                workDir.setWritable(true, false); // 确保目录可写
                File[] files = workDir.listFiles();
                if (files != null) {
                    for (File file : files) {
                        if (file.isDirectory()) {
                            file.setWritable(true, false);
                            File[] subFiles = file.listFiles();
                            if (subFiles != null) {
                                for (File subFile : subFiles) {
                                    subFile.delete();
                                }
                            }
                        }
                        file.delete();
                    }
                }
                workDir.delete();
            }
        }
    }

    @Test
    void testDownloadFileToBrowser() throws IOException {
        // 创建模拟HttpServletResponse
        HttpServletResponse mockResponse = mock(HttpServletResponse.class);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ServletOutputStream servletOutputStream = mock(ServletOutputStream.class);
        doAnswer(invocation -> {
            outputStream.write(((Integer) invocation.getArgument(0)).byteValue());
            return null;
        }).when(servletOutputStream).write(anyInt());

        when(mockResponse.getOutputStream()).thenReturn(servletOutputStream);

        // 执行测试方法
        FileUtils.downloadFileToBrowser(testFile, mockResponse, "test.zip");

        // 验证响应头和内容类型
        verify(mockResponse).setContentType("application/zip; charset=UTF-8");
        verify(mockResponse).setCharacterEncoding("UTF-8");
        verify(mockResponse).setHeader(eq("Content-disposition"), Mockito.contains("attachment; filename="));
    }

    @Test
    void testDownloadAndCleanupFiles() throws IOException {
        // 创建模拟HttpServletResponse
        HttpServletResponse mockResponse = mock(HttpServletResponse.class);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ServletOutputStream servletOutputStream = mock(ServletOutputStream.class);
        doAnswer(invocation -> {
            outputStream.write(((Integer) invocation.getArgument(0)).byteValue());
            return null;
        }).when(servletOutputStream).write(anyInt());
        doAnswer(invocation -> {
            byte[] buffer = invocation.getArgument(0);
            int offset = invocation.getArgument(1);
            int length = invocation.getArgument(2);
            outputStream.write(buffer, offset, length);
            return null;
        }).when(servletOutputStream).write(Mockito.any(byte[].class), anyInt(), anyInt());

        when(mockResponse.getOutputStream()).thenReturn(servletOutputStream);

        // 创建测试文件列表
        File testFile1 = new File(tempDir.toFile(), "testFile1.txt");
        testFile1.createNewFile();
        File testFile2 = new File(tempDir.toFile(), "testFile2.txt");
        testFile2.createNewFile();
        List<File> filesToCleanup = new ArrayList<>();
        filesToCleanup.add(testFile1);
        filesToCleanup.add(testFile2);

        // 确保文件存在
        assertTrue(testFile1.exists());
        assertTrue(testFile2.exists());
        assertTrue(testZipFile.exists());

        // 执行测试方法 - 测试downloadAndCleanupFiles方法
        FileUtils.downloadAndCleanupFiles(testZipFile, filesToCleanup, mockResponse, "test.zip");

        // 验证响应头和内容类型
        verify(mockResponse).setContentType("application/octet-stream");
        verify(mockResponse).setCharacterEncoding("UTF-8");
        verify(mockResponse).setHeader(eq("Content-disposition"), Mockito.contains("attachment; filename="));

        // 验证文件已被删除
        assertFalse(testFile1.exists());
        assertFalse(testFile2.exists());
        assertFalse(testZipFile.exists());
    }

    @Test
    void testCleanupFiles() throws Exception {
        // 创建测试文件
        File testCleanupFile = new File(tempDir.toFile(), "testCleanupFile.txt");
        testCleanupFile.createNewFile();
        assertTrue(testCleanupFile.exists());

        List<File> filesToCleanup = new ArrayList<>();
        filesToCleanup.add(testCleanupFile);

        // 直接调用deleteFile方法
        boolean result = FileUtils.deleteFile(testCleanupFile);

        // 验证结果
        assertTrue(result);
        assertFalse(testCleanupFile.exists());
    }

    @Test
    void testDeleteFile_File() throws IOException {
        // 创建一个实际存在的文件
        File fileToDelete = new File(tempDir.toFile(), "fileToDelete.txt");
        fileToDelete.createNewFile();
        assertTrue(fileToDelete.exists());

        // 执行测试方法
        boolean result = FileUtils.deleteFile(fileToDelete);

        // 验证结果
        assertTrue(result);
        assertFalse(fileToDelete.exists());
    }

    @Test
    void testDeleteFile_String() throws IOException {
        // 创建测试目录结构
        File testDir = new File(tempDir.toFile(), "testDir");
        testDir.mkdir();
        File subFile = new File(testDir, "subFile.txt");
        subFile.createNewFile();
        File subDir = new File(testDir, "subDir");
        subDir.mkdir();
        File subSubFile = new File(subDir, "subSubFile.txt");
        subSubFile.createNewFile();

        // 执行测试方法
        FileUtils.deleteFile(testDir.getPath());

        // 验证结果
        assertFalse(subFile.exists());
        assertFalse(subSubFile.exists());
        assertFalse(subDir.exists());
        assertFalse(testDir.exists());
    }

    @Test
    void testDeleteTemporaryFile() throws IOException {
        // 设置user.dir属性
        System.setProperty("user.dir", tempDir.toString());

        // 创建work目录和临时文件
        File workDir = new File(tempDir.toFile(), "work");
        workDir.mkdir();
        File tempFile1 = new File(workDir, "temp_file1.txt");
        File tempFile2 = new File(workDir, "temp_file2.txt");
        File normalFile = new File(workDir, "normal_file.txt");

        tempFile1.createNewFile();
        tempFile2.createNewFile();
        normalFile.createNewFile();

        try {
            // 验证文件创建成功
            assertTrue(workDir.exists());
            assertTrue(tempFile1.exists());
            assertTrue(tempFile2.exists());
            assertTrue(normalFile.exists());

            // 执行测试方法
            FileUtils.deleteTemporaryFile();

            // 验证临时文件被删除，非临时文件保留
            assertFalse(tempFile1.exists());
            assertFalse(tempFile2.exists());
            assertTrue(normalFile.exists());
            assertTrue(workDir.exists());
        } finally {
            // 清理测试文件
            if (normalFile.exists()) normalFile.delete();
            if (workDir.exists()) workDir.delete();
        }
    }

    @Test
    void testConvertMultipartFileToFile() throws IOException {
        // 创建测试MultipartFile
        MultipartFile multipartFile = new TestMultipartFile(
                "testFile", "testFile.txt", "text/plain", "Test content".getBytes());

        // 设置user.dir属性
        System.setProperty("user.dir", tempDir.toString());

        // 执行测试方法
        File result = FileUtils.convertMultipartFileToFile(multipartFile, tempDir.toString());

        try {
            // 验证结果
            assertNotNull(result);
            assertEquals("testFile.txt", result.getName());
            assertTrue(result.exists());
            assertEquals("Test content", new String(Files.readAllBytes(result.toPath())));
        } finally {
            // 清理测试文件
            if (result != null && result.exists()) {
                result.delete();
            }
        }
    }

    @Test
    void testConvertMultipartFileToFile_InvalidPath() throws IOException {
        // 创建测试MultipartFile
        MultipartFile multipartFile = new TestMultipartFile(
                "testFile", "test.txt", "text/plain", "Test content".getBytes());

        // 设置user.dir属性
        System.setProperty("user.dir", tempDir.toString());

        // 模拟FilePathValidator.apply方法返回false
        try (MockedStatic<FilePathValidator> mockedValidator = Mockito.mockStatic(FilePathValidator.class)) {
            mockedValidator.when(() -> FilePathValidator.apply(anyString())).thenReturn(false);

            // 执行测试方法并验证异常
            assertThrows(AccessDeniedException.class, () ->
                FileUtils.convertMultipartFileToFile(multipartFile, "/invalid/path"));

            // 验证FilePathValidator.apply被调用
            mockedValidator.verify(() -> FilePathValidator.apply(anyString()));
        }
    }

    // 自定义MultipartFile实现
    private static class TestMultipartFile implements MultipartFile {
        private final String name;
        private final String originalFilename;
        private final String contentType;
        private final byte[] content;

        public TestMultipartFile(String name, String originalFilename, String contentType, byte[] content) {
            this.name = name;
            this.originalFilename = originalFilename;
            this.contentType = contentType;
            this.content = content;
        }

        @Override
        public String getName() {
            return name;
        }

        @Override
        public String getOriginalFilename() {
            return originalFilename;
        }

        @Override
        public String getContentType() {
            return contentType;
        }

        @Override
        public boolean isEmpty() {
            return content == null || content.length == 0;
        }

        @Override
        public long getSize() {
            return content != null ? content.length : 0;
        }

        @Override
        public byte[] getBytes() throws IOException {
            return content;
        }

        @Override
        public java.io.InputStream getInputStream() throws IOException {
            return new java.io.ByteArrayInputStream(content);
        }

        @Override
        public void transferTo(File dest) throws IOException, IllegalStateException {
            try (FileOutputStream fos = new FileOutputStream(dest)) {
                fos.write(content);
            }
        }
    }

    @Test
    void testCreateFile() {
        // 创建测试路径
        String testPath = tempDir.toString() + "/testCreateDir";
        File testDir = new File(testPath);

        // 确保目录不存在
        if (testDir.exists()) {
            testDir.delete();
        }

        // 执行测试方法
        File result = FileUtils.createFile(testPath);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.exists());
        assertTrue(result.isDirectory());

        // 清理测试目录
        result.delete();
    }

    @Test
    void testDeleteDirectory() throws IOException {
        // 创建测试目录结构
        File testDir = new File(tempDir.toFile(), "testDeleteDir");
        testDir.mkdir();
        File subFile = new File(testDir, "subFile.txt");
        subFile.createNewFile();
        File subDir = new File(testDir, "subDir");
        subDir.mkdir();
        File subSubFile = new File(subDir, "subSubFile.txt");
        subSubFile.createNewFile();

        // 执行测试方法
        FileUtils.deleteDirectory(testDir);

        // 验证结果
        assertFalse(subFile.exists());
        assertFalse(subSubFile.exists());
        assertFalse(subDir.exists());
        assertFalse(testDir.exists());
    }
}
