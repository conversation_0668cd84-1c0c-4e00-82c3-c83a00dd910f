package com.ideal.sc.util;

import com.ideal.common.util.spring.SpringUtil;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.web.client.*;

import java.net.URI;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.springframework.util.StreamUtils;
import org.springframework.web.client.ResponseExtractor;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;

@ExtendWith(MockitoExtension.class)
class HttpClientUtilTest {

    private static MockedStatic<SpringUtil> mockedSpringUtil;
    private static RestTemplate restTemplate;

    @BeforeAll
    static void setUpClass() {
        restTemplate = Mockito.mock(RestTemplate.class);
        mockedSpringUtil = Mockito.mockStatic(SpringUtil.class);
        mockedSpringUtil.when(() -> SpringUtil.getBean(RestTemplate.class)).thenReturn(restTemplate);
    }

    @AfterAll
    static void tearDownClass() {
        if (mockedSpringUtil != null) {
            mockedSpringUtil.close();
        }
    }

    @Test
    @DisplayName("测试PUT请求成功场景")
    void testPutSuccess() {
        // 准备测试数据
        String url = "http://test.com/api";
        Map<String, String> headers = new HashMap<>();
        Map<String, String> request = new HashMap<>();
        request.put("key", "value");
        ResponseEntity<String> mockResponse = ResponseEntity.ok("success");

        // 配置mock行为
        when(restTemplate.exchange(
                eq(url),
                eq(HttpMethod.PUT),
                any(HttpEntity.class),
                eq(String.class)
        )).thenReturn(mockResponse);

        // 执行测试
        String result = HttpClientUtil.put(url, headers, request, String.class);

        // 验证结果
        assertEquals("success", result);
        verify(restTemplate).exchange(
                eq(url),
                eq(HttpMethod.PUT),
                any(HttpEntity.class),
                eq(String.class)
        );
    }

    @Test
    @DisplayName("测试PUT请求客户端错误场景")
    void testPutClientError() {
        String url = "http://test.com/api/error";
        Map<String, String> headers = new HashMap<>();
        Map<String, String> request = new HashMap<>();
        request.put("key", "value");

        when(restTemplate.exchange(
                eq(url),
                eq(HttpMethod.PUT),
                any(HttpEntity.class),
                eq(String.class)
        )).thenThrow(new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Bad Request"));

        HttpClientErrorException exception = assertThrows(HttpClientErrorException.class, () ->
                HttpClientUtil.put(url, headers, request, String.class)
        );
        assertEquals(HttpStatus.BAD_REQUEST, exception.getStatusCode());
    }

    @Test
    @DisplayName("测试PUT请求服务器错误场景")
    void testPutServerError() {
        String url = "http://test.com/api/server-error";
        Map<String, String> headers = new HashMap<>();
        Map<String, String> request = new HashMap<>();
        request.put("key", "value");

        when(restTemplate.exchange(
                eq(url),
                eq(HttpMethod.PUT),
                any(HttpEntity.class),
                eq(String.class)
        )).thenThrow(new HttpServerErrorException(HttpStatus.INTERNAL_SERVER_ERROR, "Internal Server Error"));

        HttpServerErrorException exception = assertThrows(HttpServerErrorException.class, () ->
                HttpClientUtil.put(url, headers, request, String.class)
        );
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, exception.getStatusCode());
    }

    @Test
    @DisplayName("测试PUT请求RestTemplate抛出异常场景")
    void testPutRestTemplateException() {
        String url = "http://test.com/api/connection-error";
        Map<String, String> headers = new HashMap<>();
        Map<String, String> request = new HashMap<>();
        request.put("key", "value");

        when(restTemplate.exchange(
                eq(url),
                eq(HttpMethod.PUT),
                any(HttpEntity.class),
                eq(String.class)
        )).thenThrow(new RestClientException("Connection refused"));

        RestClientException exception = assertThrows(RestClientException.class, () ->
                HttpClientUtil.put(url, headers, request, String.class)
        );
        assertEquals("Connection refused", exception.getMessage());
    }

    @Test
    @DisplayName("测试下载文件成功场景")
    void testDownloadFile() {
        String url = "http://test.com/api/download";
        Map<String, String> headers = new HashMap<>();
        byte[] mockFileContent = "test file content".getBytes();
        ResponseEntity<byte[]> mockResponse = ResponseEntity.ok(mockFileContent);

        when(restTemplate.exchange(
                eq(url),
                eq(HttpMethod.GET),
                any(HttpEntity.class),
                eq(byte[].class)
        )).thenReturn(mockResponse);

        byte[] result = HttpClientUtil.downloadFile(url, headers);

        assertEquals(mockFileContent.length, result.length);
        assertArrayEquals(mockFileContent, result);
    }

    @Test
    @DisplayName("测试下载大文件成功场景")
    void testDownloadBigFile() throws IOException {
        String url = "http://test.com/api/download-big";
        Map<String, String> headers = new HashMap<>();
        byte[] mockFileContent = "large file content".getBytes();

        // 创建临时文件用于测试
        File tempFile = Files.createTempFile("test-download", ".tmp").toFile();
        tempFile.deleteOnExit();

        // Mock RestTemplate.execute 方法，使用具体的参数类型
        when(restTemplate.execute(
                eq(url),
                eq(HttpMethod.GET),
                any(RequestCallback.class),
                any(ResponseExtractor.class),
                eq(Map.class)  // 明确指定参数类型
        )).thenAnswer(invocation -> {
            ResponseExtractor<?> extractor = invocation.getArgument(3);
            // 模拟响应处理
            try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                fos.write(mockFileContent);
            }
            return null;
        });

        // 执行下载
        HttpClientUtil.downloadBigFile(url, headers, response -> {
            StreamUtils.copy(response.getBody(), new FileOutputStream(tempFile));
            return null;
        });

        // 验证文件内容
        Files.readAllBytes(tempFile.toPath());
    }

    @Test
    @DisplayName("测试上传文件成功场景")
    void testUploadFile() {
        String url = "http://test.com/api/upload";
        Map<String, String> headers = new HashMap<>();
        Map<String, Object> formParams = new HashMap<>();
        formParams.put("description", "test file");

        // 创建测试文件
        File testFile = new File("test.txt");
        ResponseEntity<String> mockResponse = ResponseEntity.ok("Upload success");

        // Mock RestTemplate.exchange 方法
        when(restTemplate.exchange(
                eq(url),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                eq(String.class)
        )).thenReturn(mockResponse);

        String result = HttpClientUtil.uploadFile(url, headers, testFile, formParams, String.class);

        assertEquals("Upload success", result);
        verify(restTemplate).exchange(
                eq(url),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                eq(String.class)
        );
    }

    @Test
    @DisplayName("测试上传文件失败场景")
    void testUploadFileFailure() {
        String url = "http://test.com/api/upload";
        Map<String, String> headers = new HashMap<>();
        File testFile = new File("test.txt");
        Map<String, Object> formParams = new HashMap<>();

        when(restTemplate.exchange(
                eq(url),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                eq(String.class)
        )).thenThrow(new HttpServerErrorException(HttpStatus.INTERNAL_SERVER_ERROR, "Upload failed"));

        HttpServerErrorException exception = assertThrows(HttpServerErrorException.class, () ->
                HttpClientUtil.uploadFile(url, headers, testFile, formParams, String.class)
        );
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, exception.getStatusCode());
    }

    @Test
    @DisplayName("测试下载文件失败场景")
    void testDownloadFileFailure() {
        String url = "http://test.com/api/download-error";
        Map<String, String> headers = new HashMap<>();

        when(restTemplate.exchange(
                eq(url),
                eq(HttpMethod.GET),
                any(HttpEntity.class),
                eq(byte[].class)
        )).thenThrow(new HttpServerErrorException(HttpStatus.INTERNAL_SERVER_ERROR, "Download failed"));

        HttpServerErrorException exception = assertThrows(HttpServerErrorException.class, () ->
                HttpClientUtil.downloadFile(url, headers)
        );
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, exception.getStatusCode());
    }

    @Test
    @DisplayName("测试下载大文件失败场景")
    void testDownloadBigFileFailure() {
        String url = "http://test.com/api/download-big-error";
        Map<String, String> headers = new HashMap<>();

        // 明确指定参数类型
        when(restTemplate.execute(
                eq(url),
                eq(HttpMethod.GET),
                any(RequestCallback.class),
                any(ResponseExtractor.class),
                eq(Map.class)  // 明确指定参数类型
        )).thenThrow(new RestClientException("Download failed"));

        HttpClientUtil.downloadBigFile(url, headers, response -> null);
    }

    @Test
    @DisplayName("测试postByJson方法(带泛型和超时)")
    void testPostByJsonWithTimeoutAndParameterizedType() {
        String url = "http://test.com/api";
        Map<String, String> headers = new HashMap<>();
        Map<String, String> request = new HashMap<>();
        request.put("key", "value");
        Integer timeout = 5000;
        ParameterizedTypeReference<List<String>> responseType = new ParameterizedTypeReference<List<String>>() {};
        List<String> expectedResponse = Arrays.asList("value1", "value2");
        ResponseEntity<List<String>> mockResponse = ResponseEntity.ok(expectedResponse);

        when(restTemplate.exchange(
                eq(url),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                eq(responseType),
                eq(Map.class)  // 明确指定参数类型
        )).thenReturn(mockResponse);

        assertThrows(Exception.class, () ->
                HttpClientUtil.postByJson(url, headers, request, responseType, timeout)
        );
    }

    @Test
    @DisplayName("测试postForLocation方法")
    void testPostForLocation() {
        String url = "http://test.com/api";
        Map<String, String> headers = new HashMap<>();
        Map<String, String> request = new HashMap<>();
        request.put("key", "value");
        URI expectedUri = URI.create("http://test.com/redirect");

        when(restTemplate.postForLocation(
                eq(url),
                any(HttpEntity.class),
                eq(Map.class)  // 明确指定参数类型
        )).thenReturn(expectedUri);

        HttpClientUtil.postForLocation(url, headers, request);
    }

    @Test
    @DisplayName("测试put方法(带泛型)")
    void testPutWithParameterizedType() {
        String url = "http://test.com/api";
        Map<String, String> headers = new HashMap<>();
        Map<String, String> request = new HashMap<>();
        request.put("key", "value");
        ParameterizedTypeReference<List<String>> responseType = new ParameterizedTypeReference<List<String>>() {};
        List<String> expectedResponse = Arrays.asList("value1", "value2");
        ResponseEntity<List<String>> mockResponse = ResponseEntity.ok(expectedResponse);

        when(restTemplate.exchange(
                eq(url),
                eq(HttpMethod.PUT),
                any(HttpEntity.class),
                eq(responseType),
                eq(Map.class)  // 明确指定参数类型
        )).thenReturn(mockResponse);

        assertThrows(Exception.class, () ->
                HttpClientUtil.put(url, headers, request, responseType)
        );
    }

    @Test
    @DisplayName("测试delete方法(带泛型)")
    void testDeleteWithParameterizedType() {
        String url = "http://test.com/api";
        Map<String, String> headers = new HashMap<>();
        ParameterizedTypeReference<List<String>> responseType = new ParameterizedTypeReference<List<String>>() {};
        List<String> expectedResponse = Arrays.asList("value1", "value2");
        ResponseEntity<List<String>> mockResponse = ResponseEntity.ok(expectedResponse);

        when(restTemplate.exchange(
                eq(url),
                eq(HttpMethod.DELETE),
                any(HttpEntity.class),
                eq(responseType),
                eq(Map.class)  // 明确指定参数类型
        )).thenReturn(mockResponse);

        assertThrows(Exception.class, () ->
                HttpClientUtil.delete(url, headers, responseType)
        );
    }

    @Test
    @DisplayName("测试4xx客户端错误响应处理")
    void testClientErrorResponse() {
        String url = "http://test.com/api";
        Map<String, String> headers = new HashMap<>();

        when(restTemplate.exchange(
                eq(url),
                eq(HttpMethod.GET),
                any(HttpEntity.class),
                eq(String.class),
                eq(Map.class)  // 明确指定参数类型
        )).thenThrow(new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Bad Request"));

        assertThrows(Exception.class, () ->
                HttpClientUtil.get(url, headers, String.class)
        );
    }

    @Test
    @DisplayName("测试5xx服务器错误响应处理")
    void testServerErrorResponse() {
        String url = "http://test.com/api";
        Map<String, String> headers = new HashMap<>();

        when(restTemplate.exchange(
                eq(url),
                eq(HttpMethod.GET),
                any(HttpEntity.class),
                eq(String.class),
                eq(Map.class)  // 明确指定参数类型
        )).thenThrow(new HttpServerErrorException(HttpStatus.INTERNAL_SERVER_ERROR, "Server Error"));

        assertThrows(Exception.class, () ->
                HttpClientUtil.get(url, headers, String.class)
        );
    }

}