Affect(class count: 1 , method count: 1) cost in 412 ms, listenerId: 19
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:51.829; [cost=15130.699801ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:54.452; [cost=20622.4882ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    @R[
        code=@String[10000],
        message=@String[list.success],
        data=@PageInfo[
            DEFAULT_NAVIGATE_PAGES=@Integer[8],
            EMPTY=@PageInfo[PageInfo{pageNum=1, pageSize=0, size=0, startRow=0, endRow=0, total=0, pages=0, list=[], prePage=0, nextPage=0, isFirstPage=true, isLastPage=true, hasPreviousPage=false, hasNextPage=false, navigatePages=0, navigateFirstPage=0, navigateLastPage=0, navigatepageNums=[]}],
            pageNum=@Integer[1],
            pageSize=@Integer[10],
            size=@Integer[6],
            startRow=@Long[0],
            endRow=@Long[10],
            pages=@Integer[1],
            prePage=@Integer[0],
            nextPage=@Integer[0],
            isFirstPage=@Boolean[true],
            isLastPage=@Boolean[true],
            hasPreviousPage=@Boolean[false],
            hasNextPage=@Boolean[false],
            navigatePages=@Integer[8],
            navigatepageNums=@int[][isEmpty=false;size=1],
            navigateFirstPage=@Integer[1],
            navigateLastPage=@Integer[1],
            serialVersionUID=@Long[1],
            total=@Long[6],
            list=@ArrayList[isEmpty=false;size=6],
        ],
    ],
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:54.460; [cost=20624.8446ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    @R[
        code=@String[10000],
        message=@String[list.success],
        data=@PageInfo[
            DEFAULT_NAVIGATE_PAGES=@Integer[8],
            EMPTY=@PageInfo[PageInfo{pageNum=1, pageSize=0, size=0, startRow=0, endRow=0, total=0, pages=0, list=[], prePage=0, nextPage=0, isFirstPage=true, isLastPage=true, hasPreviousPage=false, hasNextPage=false, navigatePages=0, navigateFirstPage=0, navigateLastPage=0, navigatepageNums=[]}],
            pageNum=@Integer[1],
            pageSize=@Integer[10],
            size=@Integer[6],
            startRow=@Long[0],
            endRow=@Long[10],
            pages=@Integer[1],
            prePage=@Integer[0],
            nextPage=@Integer[0],
            isFirstPage=@Boolean[true],
            isLastPage=@Boolean[true],
            hasPreviousPage=@Boolean[false],
            hasNextPage=@Boolean[false],
            navigatePages=@Integer[8],
            navigatepageNums=@int[][isEmpty=false;size=1],
            navigateFirstPage=@Integer[1],
            navigateLastPage=@Integer[1],
            serialVersionUID=@Long[1],
            total=@Long[6],
            list=@ArrayList[isEmpty=false;size=6],
        ],
    ],
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:54.461; [cost=20631.674001ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    @R[
        code=@String[10000],
        message=@String[list.success],
        data=@PageInfo[
            DEFAULT_NAVIGATE_PAGES=@Integer[8],
            EMPTY=@PageInfo[PageInfo{pageNum=1, pageSize=0, size=0, startRow=0, endRow=0, total=0, pages=0, list=[], prePage=0, nextPage=0, isFirstPage=true, isLastPage=true, hasPreviousPage=false, hasNextPage=false, navigatePages=0, navigateFirstPage=0, navigateLastPage=0, navigatepageNums=[]}],
            pageNum=@Integer[1],
            pageSize=@Integer[10],
            size=@Integer[6],
            startRow=@Long[0],
            endRow=@Long[10],
            pages=@Integer[1],
            prePage=@Integer[0],
            nextPage=@Integer[0],
            isFirstPage=@Boolean[true],
            isLastPage=@Boolean[true],
            hasPreviousPage=@Boolean[false],
            hasNextPage=@Boolean[false],
            navigatePages=@Integer[8],
            navigatepageNums=@int[][isEmpty=false;size=1],
            navigateFirstPage=@Integer[1],
            navigateLastPage=@Integer[1],
            serialVersionUID=@Long[1],
            total=@Long[6],
            list=@ArrayList[isEmpty=false;size=6],
        ],
    ],
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:54.522; [cost=17425.486101ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExceptionExit
ts=2025-08-26 15:46:57.856; [cost=20664.330399ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    org.springframework.dao.DataAccessResourceFailureException: 
### Error updating database.  Cause: org.postgresql.util.PSQLException: An I/O error occurred while sending to the backend.
### The error may exist in file [D:\1.project\entegor_v9\ieai-script-service\script-tools-biz\script-biz\target\classes\mapper\TaskInstanceMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: update ieai_script_task_instance set istatus = ?,         iend_time =  now()          where iid = ?                  and istatus not in          (               ?          ,              ?          ,              ?          ,              ?          )
### Cause: org.postgresql.util.PSQLException: An I/O error occurred while sending to the backend.
; An I/O error occurred while sending to the backend.; nested exception is org.postgresql.util.PSQLException: An I/O error occurred while sending to the backend.
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at com.sun.proxy.$Proxy164.update(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:67)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:152)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:85)
	at com.sun.proxy.$Proxy209.updateState(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor2512.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at com.alibaba.druid.support.spring.stat.DruidStatInterceptor.invoke(DruidStatInterceptor.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:241)
	at com.sun.proxy.$Proxy210.updateState(Unknown Source)
	at com.ideal.script.service.impl.TaskInstanceServiceImpl.updateState(TaskInstanceServiceImpl.java:183)
	at com.ideal.script.service.impl.TaskInstanceServiceImpl$$FastClassBySpringCGLIB$$10436783.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.ideal.script.service.impl.TaskInstanceServiceImpl$$EnhancerBySpringCGLIB$$9e8d5d85.updateState(<generated>)
	at com.ideal.script.service.impl.TaskRuntimeServiceImpl.updateScriptTaskRuntime(TaskRuntimeServiceImpl.java:484)
	at com.ideal.script.service.impl.TaskRuntimeServiceImpl$$FastClassBySpringCGLIB$$cea8ba3a.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.ideal.script.service.impl.TaskRuntimeServiceImpl$$EnhancerBySpringCGLIB$$56cf400a.updateScriptTaskRuntime(<generated>)
	at com.ideal.script.service.impl.TaskRuntimeServiceImpl.handleScriptExecuteResult(TaskRuntimeServiceImpl.java:513)
	at com.ideal.script.service.impl.TaskRuntimeServiceImpl$$FastClassBySpringCGLIB$$cea8ba3a.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.ideal.script.service.impl.TaskRuntimeServiceImpl$$EnhancerBySpringCGLIB$$56cf400a.handleScriptExecuteResult(<generated>)
	at com.ideal.script.service.impl.resulthandler.ScriptResultHandlerServiceImpl.processScriptError(ScriptResultHandlerServiceImpl.java:271)
	at com.ideal.script.service.impl.resulthandler.ScriptResultHandlerServiceImpl.handleScriptErrorResult(ScriptResultHandlerServiceImpl.java:254)
	at com.ideal.script.service.consumer.ScriptErrorResultHandler.notice(ScriptErrorResultHandler.java:38)
	at com.ideal.script.subscriber.ScriptMqRegisterConfig.lambda$scriptErrorResult$2(ScriptMqRegisterConfig.java:50)
	at org.springframework.cloud.function.context.catalog.SimpleFunctionRegistry$FunctionInvocationWrapper.invokeConsumer(SimpleFunctionRegistry.java:976)
	at org.springframework.cloud.function.context.catalog.SimpleFunctionRegistry$FunctionInvocationWrapper.doApply(SimpleFunctionRegistry.java:705)
	at org.springframework.cloud.function.context.catalog.SimpleFunctionRegistry$FunctionInvocationWrapper.apply(SimpleFunctionRegistry.java:551)
	at org.springframework.cloud.stream.function.PartitionAwareFunctionWrapper.apply(PartitionAwareFunctionWrapper.java:84)
	at org.springframework.cloud.stream.function.FunctionConfiguration$FunctionWrapper.apply(FunctionConfiguration.java:790)
	at org.springframework.cloud.stream.function.FunctionConfiguration$FunctionToDestinationBinder$1.handleMessageInternal(FunctionConfiguration.java:622)
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55)
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115)
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133)
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106)
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72)
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317)
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272)
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187)
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166)
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47)
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109)
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215)
	at com.alibaba.cloud.stream.binder.rocketmq.integration.RocketMQInboundChannelAdapter.access$300(RocketMQInboundChannelAdapter.java:42)
	at com.alibaba.cloud.stream.binder.rocketmq.integration.RocketMQInboundChannelAdapter$BindingRocketMQListener.lambda$onMessage$0(RocketMQInboundChannelAdapter.java:148)
	at org.springframework.retry.support.RetryTemplate.doExecute(RetryTemplate.java:329)
	at org.springframework.retry.support.RetryTemplate.execute(RetryTemplate.java:225)
	at com.alibaba.cloud.stream.binder.rocketmq.integration.RocketMQInboundChannelAdapter$BindingRocketMQListener.onMessage(RocketMQInboundChannelAdapter.java:147)
	at com.alibaba.cloud.stream.binder.rocketmq.integration.RocketMQInboundChannelAdapter$BindingRocketMQListener.onMessage(RocketMQInboundChannelAdapter.java:140)
	at com.alibaba.cloud.stream.binder.rocketmq.consuming.RocketMQListenerBindingContainer$DefaultMessageListenerConcurrently.consumeMessage(RocketMQListenerBindingContainer.java:420)
	at org.apache.rocketmq.client.impl.consumer.ConsumeMessageConcurrentlyService$ConsumeRequest.run(ConsumeMessageConcurrentlyService.java:417)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.postgresql.util.PSQLException: An I/O error occurred while sending to the backend.
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:383)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:496)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:413)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:190)
	at org.postgresql.jdbc.PgPreparedStatement.execute(PgPreparedStatement.java:177)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3446)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at sun.reflect.GeneratedMethodAccessor485.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.ideal.sc.interceptor.GenerateInterceptor.intercept(GenerateInterceptor.java:57)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at com.sun.proxy.$Proxy384.update(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor485.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy384.update(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor485.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.ideal.snowflake.interceptor.IdInterceptor.intercept(IdInterceptor.java:34)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at com.sun.proxy.$Proxy384.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at sun.reflect.GeneratedMethodAccessor1700.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 90 more
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at org.postgresql.core.VisibleBufferedInputStream.readMore(VisibleBufferedInputStream.java:161)
	at org.postgresql.core.VisibleBufferedInputStream.ensureBytes(VisibleBufferedInputStream.java:128)
	at org.postgresql.core.VisibleBufferedInputStream.ensureBytes(VisibleBufferedInputStream.java:113)
	at org.postgresql.core.VisibleBufferedInputStream.read(VisibleBufferedInputStream.java:73)
	at org.postgresql.core.PGStream.receiveChar(PGStream.java:465)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2120)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:356)
	... 130 more
,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:57.872; [cost=23549.3971ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExceptionExit
ts=2025-08-26 15:46:57.901; [cost=20522.2862ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    org.springframework.dao.DataAccessResourceFailureException: 
### Error updating database.  Cause: org.postgresql.util.PSQLException: An I/O error occurred while sending to the backend.
### The error may exist in file [D:\1.project\entegor_v9\ieai-script-service\script-tools-biz\script-biz\target\classes\mapper\TaskInstanceMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: update ieai_script_task_instance set istatus = ?,         iend_time =  now()          where iid = ?                  and istatus not in          (               ?          ,              ?          ,              ?          ,              ?          )
### Cause: org.postgresql.util.PSQLException: An I/O error occurred while sending to the backend.
; An I/O error occurred while sending to the backend.; nested exception is org.postgresql.util.PSQLException: An I/O error occurred while sending to the backend.
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at com.sun.proxy.$Proxy164.update(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:67)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:152)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:85)
	at com.sun.proxy.$Proxy209.updateState(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor2512.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at com.alibaba.druid.support.spring.stat.DruidStatInterceptor.invoke(DruidStatInterceptor.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:241)
	at com.sun.proxy.$Proxy210.updateState(Unknown Source)
	at com.ideal.script.service.impl.TaskInstanceServiceImpl.updateState(TaskInstanceServiceImpl.java:183)
	at com.ideal.script.service.impl.TaskInstanceServiceImpl$$FastClassBySpringCGLIB$$10436783.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.ideal.script.service.impl.TaskInstanceServiceImpl$$EnhancerBySpringCGLIB$$9e8d5d85.updateState(<generated>)
	at com.ideal.script.service.impl.TaskRuntimeServiceImpl.updateScriptTaskRuntime(TaskRuntimeServiceImpl.java:484)
	at com.ideal.script.service.impl.TaskRuntimeServiceImpl$$FastClassBySpringCGLIB$$cea8ba3a.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.ideal.script.service.impl.TaskRuntimeServiceImpl$$EnhancerBySpringCGLIB$$56cf400a.updateScriptTaskRuntime(<generated>)
	at com.ideal.script.service.impl.TaskRuntimeServiceImpl.handleScriptExecuteResult(TaskRuntimeServiceImpl.java:513)
	at com.ideal.script.service.impl.TaskRuntimeServiceImpl$$FastClassBySpringCGLIB$$cea8ba3a.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.ideal.script.service.impl.TaskRuntimeServiceImpl$$EnhancerBySpringCGLIB$$56cf400a.handleScriptExecuteResult(<generated>)
	at com.ideal.script.service.impl.resulthandler.ScriptResultHandlerServiceImpl.processScriptError(ScriptResultHandlerServiceImpl.java:271)
	at com.ideal.script.service.impl.resulthandler.ScriptResultHandlerServiceImpl.handleScriptErrorResult(ScriptResultHandlerServiceImpl.java:254)
	at com.ideal.script.service.consumer.ScriptErrorResultHandler.notice(ScriptErrorResultHandler.java:38)
	at com.ideal.script.subscriber.ScriptMqRegisterConfig.lambda$scriptErrorResult$2(ScriptMqRegisterConfig.java:50)
	at org.springframework.cloud.function.context.catalog.SimpleFunctionRegistry$FunctionInvocationWrapper.invokeConsumer(SimpleFunctionRegistry.java:976)
	at org.springframework.cloud.function.context.catalog.SimpleFunctionRegistry$FunctionInvocationWrapper.doApply(SimpleFunctionRegistry.java:705)
	at org.springframework.cloud.function.context.catalog.SimpleFunctionRegistry$FunctionInvocationWrapper.apply(SimpleFunctionRegistry.java:551)
	at org.springframework.cloud.stream.function.PartitionAwareFunctionWrapper.apply(PartitionAwareFunctionWrapper.java:84)
	at org.springframework.cloud.stream.function.FunctionConfiguration$FunctionWrapper.apply(FunctionConfiguration.java:790)
	at org.springframework.cloud.stream.function.FunctionConfiguration$FunctionToDestinationBinder$1.handleMessageInternal(FunctionConfiguration.java:622)
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55)
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115)
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133)
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106)
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72)
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317)
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272)
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187)
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166)
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47)
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109)
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215)
	at com.alibaba.cloud.stream.binder.rocketmq.integration.RocketMQInboundChannelAdapter.access$300(RocketMQInboundChannelAdapter.java:42)
	at com.alibaba.cloud.stream.binder.rocketmq.integration.RocketMQInboundChannelAdapter$BindingRocketMQListener.lambda$onMessage$0(RocketMQInboundChannelAdapter.java:148)
	at org.springframework.retry.support.RetryTemplate.doExecute(RetryTemplate.java:329)
	at org.springframework.retry.support.RetryTemplate.execute(RetryTemplate.java:225)
	at com.alibaba.cloud.stream.binder.rocketmq.integration.RocketMQInboundChannelAdapter$BindingRocketMQListener.onMessage(RocketMQInboundChannelAdapter.java:147)
	at com.alibaba.cloud.stream.binder.rocketmq.integration.RocketMQInboundChannelAdapter$BindingRocketMQListener.onMessage(RocketMQInboundChannelAdapter.java:140)
	at com.alibaba.cloud.stream.binder.rocketmq.consuming.RocketMQListenerBindingContainer$DefaultMessageListenerConcurrently.consumeMessage(RocketMQListenerBindingContainer.java:420)
	at org.apache.rocketmq.client.impl.consumer.ConsumeMessageConcurrentlyService$ConsumeRequest.run(ConsumeMessageConcurrentlyService.java:417)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.postgresql.util.PSQLException: An I/O error occurred while sending to the backend.
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:383)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:496)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:413)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:190)
	at org.postgresql.jdbc.PgPreparedStatement.execute(PgPreparedStatement.java:177)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3446)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at sun.reflect.GeneratedMethodAccessor485.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.ideal.sc.interceptor.GenerateInterceptor.intercept(GenerateInterceptor.java:57)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at com.sun.proxy.$Proxy384.update(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor485.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy384.update(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor485.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.ideal.snowflake.interceptor.IdInterceptor.intercept(IdInterceptor.java:34)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at com.sun.proxy.$Proxy384.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at sun.reflect.GeneratedMethodAccessor1700.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 90 more
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at org.postgresql.core.VisibleBufferedInputStream.readMore(VisibleBufferedInputStream.java:161)
	at org.postgresql.core.VisibleBufferedInputStream.ensureBytes(VisibleBufferedInputStream.java:128)
	at org.postgresql.core.VisibleBufferedInputStream.ensureBytes(VisibleBufferedInputStream.java:113)
	at org.postgresql.core.VisibleBufferedInputStream.read(VisibleBufferedInputStream.java:73)
	at org.postgresql.core.PGStream.receiveChar(PGStream.java:465)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2120)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:356)
	... 130 more
,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:57.925; [cost=23601.1048ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:57.951; [cost=23625.2063ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:57.957; [cost=23630.9229ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:57.986; [cost=23659.1031ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:57.992; [cost=23664.5168ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:58.013; [cost=23684.525801ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
ts=2025-08-26 15:46:58.013; [cost=23686.8994ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:58.033; [cost=23703.5716ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
ts=2025-08-26 15:46:58.033; [cost=23700.73ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:58.042; [cost=23713.6431ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:58.046; [cost=23707.109199ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:58.061; [cost=23720.5768ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:58.073; [cost=23734.410199ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:58.074; [cost=23729.8987ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:58.097; [cost=23769.3967ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:58.102; [cost=23751.0694ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:58.116; [cost=23777.6614ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:58.124; [cost=23770.5274ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:58.140; [cost=23785.328201ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
ts=2025-08-26 15:46:58.140; [cost=23787.4018ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:58.146; [cost=23788.111199ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:58.146; [cost=23788.181999ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:58.150; [cost=23794.8941ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:58.173; [cost=23817.3661ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:58.180; [cost=23824.7258ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:58.184; [cost=23825.613099ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:58.201; [cost=23838.320601ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:58.213; [cost=23853.9204ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:58.223; [cost=23867.6335ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:58.226; [cost=23866.2325ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:58.247; [cost=23884.2041ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:58.266; [cost=23905.029701ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:58.266; [cost=23907.698801ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:58.271; [cost=23911.917601ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:58.272; [cost=23912.3144ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:58.276; [cost=23912.6205ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.265; [cost=24899.495099ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.310; [cost=24943.4185ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
ts=2025-08-26 15:46:59.310; [cost=24932.150899ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.324; [cost=24951.5589ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.357; [cost=24977.4374ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.381; [cost=25003.2877ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.389; [cost=25009.2366ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.389; [cost=25014.7485ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.395; [cost=25019.7623ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.403; [cost=25031.116799ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
ts=2025-08-26 15:46:59.403; [cost=25026.347599ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
ts=2025-08-26 15:46:59.403; [cost=25032.1082ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.404; [cost=25025.7652ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.404; [cost=25032.152899ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.404; [cost=25032.6862ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.405; [cost=25029.103299ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.405; [cost=25033.332ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.407; [cost=25018.3338ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.408; [cost=25018.4621ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.409; [cost=25031.707999ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.409; [cost=25038.278499ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.409; [cost=25037.4877ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.411; [cost=25018.356201ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.414; [cost=25026.861501ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.417; [cost=25029.095801ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.420; [cost=25027.063401ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.422; [cost=25032.2017ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.425; [cost=25038.762999ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.427; [cost=25041.2008ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.430; [cost=25038.9745ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.431; [cost=25040.5036ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.434; [cost=25040.420801ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.437; [cost=25043.6725ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.439; [cost=25044.617501ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.441; [cost=25046.8283ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.443; [cost=25046.5756ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.442; [cost=25046.3905ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.443; [cost=25038.3954ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.444; [cost=25048.529801ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.446; [cost=25048.8179ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.447; [cost=25053.0296ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.449; [cost=25051.8973ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.449; [cost=25050.7516ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.452; [cost=25054.497299ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.455; [cost=25057.428ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.457; [cost=25058.3585ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.459; [cost=25066.420799ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.463; [cost=25066.4074ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.467; [cost=25060.2529ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.468; [cost=25068.589899ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.468; [cost=25068.684901ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.472; [cost=25074.034501ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.484; [cost=25079.947099ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.486; [cost=25081.6259ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.486; [cost=25081.1049ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.487; [cost=25085.5198ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.487; [cost=25082.226399ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.489; [cost=25081.9055ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.489; [cost=25082.1363ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.490; [cost=25082.265799ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.490; [cost=25082.5288ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.490; [cost=25082.8447ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.490; [cost=25081.833701ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.492; [cost=25084.099099ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.492; [cost=25079.6987ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.493; [cost=25087.653399ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.494; [cost=25081.1705ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.494; [cost=25081.084599ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.495; [cost=25082.142401ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.495; [cost=25082.2478ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.496; [cost=25080.4336ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.496; [cost=25080.806601ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.497; [cost=25082.3454ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.498; [cost=25085.046401ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.498; [cost=25082.990299ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.499; [cost=25080.3858ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.499; [cost=25078.526999ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.500; [cost=25084.342699ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.501; [cost=25078.9705ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.502; [cost=25085.2015ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.502; [cost=25084.061ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.501; [cost=25087.0742ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.506; [cost=25089.373101ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.510; [cost=25086.0746ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.518; [cost=25096.2588ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.531; [cost=25108.7215ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.593; [cost=25171.0319ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.597; [cost=25176.6327ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.599; [cost=25176.255599ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.851; [cost=25428.161201ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.853; [cost=25431.1732ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.856; [cost=25437.8439ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.859; [cost=25435.761099ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.928; [cost=25503.7387ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.932; [cost=25508.7899ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.934; [cost=25511.156299ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.936; [cost=25507.9336ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:46:59.991; [cost=25563.0609ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:00.114; [cost=25685.183901ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:00.117; [cost=25688.9371ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:00.121; [cost=25691.599201ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:00.126; [cost=25696.2263ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:00.130; [cost=25701.7693ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:00.367; [cost=25936.2276ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:00.367; [cost=25936.721799ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:00.382; [cost=25938.2198ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:00.438; [cost=26007.6096ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:00.495; [cost=26063.7461ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:00.509; [cost=26063.5082ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:00.621; [cost=26180.2669ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:00.685; [cost=26250.8009ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:00.691; [cost=26255.9329ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
ts=2025-08-26 15:47:00.691; [cost=26256.723001ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:00.732; [cost=26295.642899ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:00.767; [cost=26327.0661ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:00.793; [cost=26354.3099ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:00.793; [cost=26358.0366ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:00.799; [cost=26359.569ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:00.826; [cost=26387.7351ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:00.862; [cost=26423.9716ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:00.866; [cost=26421.1237ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
ts=2025-08-26 15:47:00.866; [cost=26421.227099ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:00.926; [cost=26483.841801ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:01.075; [cost=26632.4727ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:01.075; [cost=26632.0571ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:01.136; [cost=26693.0458ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:01.146; [cost=26698.9159ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:01.181; [cost=26730.838201ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:01.240; [cost=26796.0843ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:01.245; [cost=26797.251999ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:01.291; [cost=26837.977499ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
ts=2025-08-26 15:47:01.291; [cost=26838.2068ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:01.334; [cost=26882.128ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:01.403; [cost=26951.4713ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:01.404; [cost=26954.965601ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
ts=2025-08-26 15:47:01.403; [cost=26950.868201ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:01.475; [cost=27028.2159ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:01.509; [cost=27057.501ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:01.536; [cost=27087.875201ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:01.556; [cost=27105.2705ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
ts=2025-08-26 15:47:01.553; [cost=27106.016099ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
ts=2025-08-26 15:47:01.556; [cost=27104.7703ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:01.560; [cost=27108.523399ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:01.594; [cost=27143.6347ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:01.597; [cost=27147.9177ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:01.632; [cost=27180.903699ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:01.662; [cost=27209.130199ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:01.686; [cost=27238.9419ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:01.715; [cost=27268.3146ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:01.742; [cost=27296.3028ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExceptionExit
ts=2025-08-26 15:47:02.839; [cost=28451.8561ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    org.springframework.dao.DataAccessResourceFailureException: 
### Error updating database.  Cause: org.postgresql.util.PSQLException: An I/O error occurred while sending to the backend.
### The error may exist in file [D:\1.project\entegor_v9\ieai-script-service\script-tools-biz\script-biz\target\classes\mapper\TaskInstanceMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: update ieai_script_task_instance set istatus = ?,         iend_time =  now()          where iid = ?                  and istatus not in          (               ?          ,              ?          ,              ?          ,              ?          )
### Cause: org.postgresql.util.PSQLException: An I/O error occurred while sending to the backend.
; An I/O error occurred while sending to the backend.; nested exception is org.postgresql.util.PSQLException: An I/O error occurred while sending to the backend.
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at com.sun.proxy.$Proxy164.update(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:67)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:152)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:85)
	at com.sun.proxy.$Proxy209.updateState(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor2512.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at com.alibaba.druid.support.spring.stat.DruidStatInterceptor.invoke(DruidStatInterceptor.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:241)
	at com.sun.proxy.$Proxy210.updateState(Unknown Source)
	at com.ideal.script.service.impl.TaskInstanceServiceImpl.updateState(TaskInstanceServiceImpl.java:183)
	at com.ideal.script.service.impl.TaskInstanceServiceImpl$$FastClassBySpringCGLIB$$10436783.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.ideal.script.service.impl.TaskInstanceServiceImpl$$EnhancerBySpringCGLIB$$9e8d5d85.updateState(<generated>)
	at com.ideal.script.service.impl.TaskRuntimeServiceImpl.updateScriptTaskRuntime(TaskRuntimeServiceImpl.java:484)
	at com.ideal.script.service.impl.TaskRuntimeServiceImpl$$FastClassBySpringCGLIB$$cea8ba3a.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.ideal.script.service.impl.TaskRuntimeServiceImpl$$EnhancerBySpringCGLIB$$56cf400a.updateScriptTaskRuntime(<generated>)
	at com.ideal.script.service.impl.TaskRuntimeServiceImpl.handleScriptExecuteResult(TaskRuntimeServiceImpl.java:513)
	at com.ideal.script.service.impl.TaskRuntimeServiceImpl$$FastClassBySpringCGLIB$$cea8ba3a.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.ideal.script.service.impl.TaskRuntimeServiceImpl$$EnhancerBySpringCGLIB$$56cf400a.handleScriptExecuteResult(<generated>)
	at com.ideal.script.service.impl.resulthandler.ScriptResultHandlerServiceImpl.processScriptError(ScriptResultHandlerServiceImpl.java:271)
	at com.ideal.script.service.impl.resulthandler.ScriptResultHandlerServiceImpl.handleScriptErrorResult(ScriptResultHandlerServiceImpl.java:254)
	at com.ideal.script.service.consumer.ScriptErrorResultHandler.notice(ScriptErrorResultHandler.java:38)
	at com.ideal.script.subscriber.ScriptMqRegisterConfig.lambda$scriptErrorResult$2(ScriptMqRegisterConfig.java:50)
	at org.springframework.cloud.function.context.catalog.SimpleFunctionRegistry$FunctionInvocationWrapper.invokeConsumer(SimpleFunctionRegistry.java:976)
	at org.springframework.cloud.function.context.catalog.SimpleFunctionRegistry$FunctionInvocationWrapper.doApply(SimpleFunctionRegistry.java:705)
	at org.springframework.cloud.function.context.catalog.SimpleFunctionRegistry$FunctionInvocationWrapper.apply(SimpleFunctionRegistry.java:551)
	at org.springframework.cloud.stream.function.PartitionAwareFunctionWrapper.apply(PartitionAwareFunctionWrapper.java:84)
	at org.springframework.cloud.stream.function.FunctionConfiguration$FunctionWrapper.apply(FunctionConfiguration.java:790)
	at org.springframework.cloud.stream.function.FunctionConfiguration$FunctionToDestinationBinder$1.handleMessageInternal(FunctionConfiguration.java:622)
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55)
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115)
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133)
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106)
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72)
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317)
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272)
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187)
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166)
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47)
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109)
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215)
	at com.alibaba.cloud.stream.binder.rocketmq.integration.RocketMQInboundChannelAdapter.access$300(RocketMQInboundChannelAdapter.java:42)
	at com.alibaba.cloud.stream.binder.rocketmq.integration.RocketMQInboundChannelAdapter$BindingRocketMQListener.lambda$onMessage$0(RocketMQInboundChannelAdapter.java:148)
	at org.springframework.retry.support.RetryTemplate.doExecute(RetryTemplate.java:329)
	at org.springframework.retry.support.RetryTemplate.execute(RetryTemplate.java:225)
	at com.alibaba.cloud.stream.binder.rocketmq.integration.RocketMQInboundChannelAdapter$BindingRocketMQListener.onMessage(RocketMQInboundChannelAdapter.java:147)
	at com.alibaba.cloud.stream.binder.rocketmq.integration.RocketMQInboundChannelAdapter$BindingRocketMQListener.onMessage(RocketMQInboundChannelAdapter.java:140)
	at com.alibaba.cloud.stream.binder.rocketmq.consuming.RocketMQListenerBindingContainer$DefaultMessageListenerConcurrently.consumeMessage(RocketMQListenerBindingContainer.java:420)
	at org.apache.rocketmq.client.impl.consumer.ConsumeMessageConcurrentlyService$ConsumeRequest.run(ConsumeMessageConcurrentlyService.java:417)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.postgresql.util.PSQLException: An I/O error occurred while sending to the backend.
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:383)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:496)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:413)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:190)
	at org.postgresql.jdbc.PgPreparedStatement.execute(PgPreparedStatement.java:177)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3446)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at sun.reflect.GeneratedMethodAccessor485.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.ideal.sc.interceptor.GenerateInterceptor.intercept(GenerateInterceptor.java:57)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at com.sun.proxy.$Proxy384.update(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor485.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy384.update(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor485.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.ideal.snowflake.interceptor.IdInterceptor.intercept(IdInterceptor.java:34)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at com.sun.proxy.$Proxy384.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at sun.reflect.GeneratedMethodAccessor1700.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 90 more
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at org.postgresql.core.VisibleBufferedInputStream.readMore(VisibleBufferedInputStream.java:161)
	at org.postgresql.core.VisibleBufferedInputStream.ensureBytes(VisibleBufferedInputStream.java:128)
	at org.postgresql.core.VisibleBufferedInputStream.ensureBytes(VisibleBufferedInputStream.java:113)
	at org.postgresql.core.VisibleBufferedInputStream.read(VisibleBufferedInputStream.java:73)
	at org.postgresql.core.PGStream.receiveChar(PGStream.java:465)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2120)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:356)
	... 130 more
,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExceptionExit
ts=2025-08-26 15:47:03.220; [cost=28830.375999ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    org.springframework.dao.DataAccessResourceFailureException: 
### Error updating database.  Cause: org.postgresql.util.PSQLException: An I/O error occurred while sending to the backend.
### The error may exist in file [D:\1.project\entegor_v9\ieai-script-service\script-tools-biz\script-biz\target\classes\mapper\TaskInstanceMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: update ieai_script_task_instance set istatus = ?,         iend_time =  now()          where iid = ?                  and istatus not in          (               ?          ,              ?          ,              ?          ,              ?          )
### Cause: org.postgresql.util.PSQLException: An I/O error occurred while sending to the backend.
; An I/O error occurred while sending to the backend.; nested exception is org.postgresql.util.PSQLException: An I/O error occurred while sending to the backend.
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)
	at com.sun.proxy.$Proxy164.update(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:67)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:152)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:85)
	at com.sun.proxy.$Proxy209.updateState(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor2512.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at com.alibaba.druid.support.spring.stat.DruidStatInterceptor.invoke(DruidStatInterceptor.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:241)
	at com.sun.proxy.$Proxy210.updateState(Unknown Source)
	at com.ideal.script.service.impl.TaskInstanceServiceImpl.updateState(TaskInstanceServiceImpl.java:183)
	at com.ideal.script.service.impl.TaskInstanceServiceImpl$$FastClassBySpringCGLIB$$10436783.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.ideal.script.service.impl.TaskInstanceServiceImpl$$EnhancerBySpringCGLIB$$9e8d5d85.updateState(<generated>)
	at com.ideal.script.service.impl.TaskRuntimeServiceImpl.updateScriptTaskRuntime(TaskRuntimeServiceImpl.java:484)
	at com.ideal.script.service.impl.TaskRuntimeServiceImpl$$FastClassBySpringCGLIB$$cea8ba3a.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.ideal.script.service.impl.TaskRuntimeServiceImpl$$EnhancerBySpringCGLIB$$56cf400a.updateScriptTaskRuntime(<generated>)
	at com.ideal.script.service.impl.TaskRuntimeServiceImpl.handleScriptExecuteResult(TaskRuntimeServiceImpl.java:513)
	at com.ideal.script.service.impl.TaskRuntimeServiceImpl$$FastClassBySpringCGLIB$$cea8ba3a.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.ideal.script.service.impl.TaskRuntimeServiceImpl$$EnhancerBySpringCGLIB$$56cf400a.handleScriptExecuteResult(<generated>)
	at com.ideal.script.service.impl.resulthandler.ScriptResultHandlerServiceImpl.processScriptError(ScriptResultHandlerServiceImpl.java:271)
	at com.ideal.script.service.impl.resulthandler.ScriptResultHandlerServiceImpl.handleScriptErrorResult(ScriptResultHandlerServiceImpl.java:254)
	at com.ideal.script.service.consumer.ScriptErrorResultHandler.notice(ScriptErrorResultHandler.java:38)
	at com.ideal.script.subscriber.ScriptMqRegisterConfig.lambda$scriptErrorResult$2(ScriptMqRegisterConfig.java:50)
	at org.springframework.cloud.function.context.catalog.SimpleFunctionRegistry$FunctionInvocationWrapper.invokeConsumer(SimpleFunctionRegistry.java:976)
	at org.springframework.cloud.function.context.catalog.SimpleFunctionRegistry$FunctionInvocationWrapper.doApply(SimpleFunctionRegistry.java:705)
	at org.springframework.cloud.function.context.catalog.SimpleFunctionRegistry$FunctionInvocationWrapper.apply(SimpleFunctionRegistry.java:551)
	at org.springframework.cloud.stream.function.PartitionAwareFunctionWrapper.apply(PartitionAwareFunctionWrapper.java:84)
	at org.springframework.cloud.stream.function.FunctionConfiguration$FunctionWrapper.apply(FunctionConfiguration.java:790)
	at org.springframework.cloud.stream.function.FunctionConfiguration$FunctionToDestinationBinder$1.handleMessageInternal(FunctionConfiguration.java:622)
	at org.springframework.integration.handler.AbstractMessageHandler.handleMessage(AbstractMessageHandler.java:55)
	at org.springframework.integration.dispatcher.AbstractDispatcher.tryOptimizedDispatch(AbstractDispatcher.java:115)
	at org.springframework.integration.dispatcher.UnicastingDispatcher.doDispatch(UnicastingDispatcher.java:133)
	at org.springframework.integration.dispatcher.UnicastingDispatcher.dispatch(UnicastingDispatcher.java:106)
	at org.springframework.integration.channel.AbstractSubscribableChannel.doSend(AbstractSubscribableChannel.java:72)
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:317)
	at org.springframework.integration.channel.AbstractMessageChannel.send(AbstractMessageChannel.java:272)
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:187)
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:166)
	at org.springframework.messaging.core.GenericMessagingTemplate.doSend(GenericMessagingTemplate.java:47)
	at org.springframework.messaging.core.AbstractMessageSendingTemplate.send(AbstractMessageSendingTemplate.java:109)
	at org.springframework.integration.endpoint.MessageProducerSupport.sendMessage(MessageProducerSupport.java:215)
	at com.alibaba.cloud.stream.binder.rocketmq.integration.RocketMQInboundChannelAdapter.access$300(RocketMQInboundChannelAdapter.java:42)
	at com.alibaba.cloud.stream.binder.rocketmq.integration.RocketMQInboundChannelAdapter$BindingRocketMQListener.lambda$onMessage$0(RocketMQInboundChannelAdapter.java:148)
	at org.springframework.retry.support.RetryTemplate.doExecute(RetryTemplate.java:329)
	at org.springframework.retry.support.RetryTemplate.execute(RetryTemplate.java:225)
	at com.alibaba.cloud.stream.binder.rocketmq.integration.RocketMQInboundChannelAdapter$BindingRocketMQListener.onMessage(RocketMQInboundChannelAdapter.java:147)
	at com.alibaba.cloud.stream.binder.rocketmq.integration.RocketMQInboundChannelAdapter$BindingRocketMQListener.onMessage(RocketMQInboundChannelAdapter.java:140)
	at com.alibaba.cloud.stream.binder.rocketmq.consuming.RocketMQListenerBindingContainer$DefaultMessageListenerConcurrently.consumeMessage(RocketMQListenerBindingContainer.java:420)
	at org.apache.rocketmq.client.impl.consumer.ConsumeMessageConcurrentlyService$ConsumeRequest.run(ConsumeMessageConcurrentlyService.java:417)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.postgresql.util.PSQLException: An I/O error occurred while sending to the backend.
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:383)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:496)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:413)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:190)
	at org.postgresql.jdbc.PgPreparedStatement.execute(PgPreparedStatement.java:177)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3446)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at sun.reflect.GeneratedMethodAccessor485.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.ideal.sc.interceptor.GenerateInterceptor.intercept(GenerateInterceptor.java:57)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at com.sun.proxy.$Proxy384.update(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor485.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy384.update(Unknown Source)
	at sun.reflect.GeneratedMethodAccessor485.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.ideal.snowflake.interceptor.IdInterceptor.intercept(IdInterceptor.java:34)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at com.sun.proxy.$Proxy384.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at sun.reflect.GeneratedMethodAccessor1700.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)
	... 90 more
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at org.postgresql.core.VisibleBufferedInputStream.readMore(VisibleBufferedInputStream.java:161)
	at org.postgresql.core.VisibleBufferedInputStream.ensureBytes(VisibleBufferedInputStream.java:128)
	at org.postgresql.core.VisibleBufferedInputStream.ensureBytes(VisibleBufferedInputStream.java:113)
	at org.postgresql.core.VisibleBufferedInputStream.read(VisibleBufferedInputStream.java:73)
	at org.postgresql.core.PGStream.receiveChar(PGStream.java:465)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2120)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:356)
	... 130 more
,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:04.640; [cost=30297.7963ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:08.967; [cost=17048.1402ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:11.184; [cost=36806.1011ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:16.441; [cost=21916.5109ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:17.253; [cost=42869.9595ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:18.264; [cost=17982.429399ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:18.300; [cost=17929.3338ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:18.312; [cost=17807.4145ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:18.374; [cost=17636.5731ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:18.380; [cost=17577.221999ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:18.401; [cost=17470.7577ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:19.288; [cost=26204.7869ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:19.349; [cost=26244.4383ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:19.367; [cost=26256.8598ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:19.450; [cost=26293.5227ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:19.460; [cost=26298.4237ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
ts=2025-08-26 15:47:19.460; [cost=26299.2525ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:19.992; [cost=19064.1498ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:20.534; [cost=19099.172801ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:20.556; [cost=18850.086101ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:20.566; [cost=19423.748999ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:20.577; [cost=19390.5525ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:20.589; [cost=18986.695101ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:20.663; [cost=18944.114799ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:20.729; [cost=19348.563001ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:20.754; [cost=18952.2676ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:20.768; [cost=19388.2649ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:20.782; [cost=19558.745201ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:20.794; [cost=19227.1912ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:21.555; [cost=28394.4195ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:21.765; [cost=28366.9139ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:21.771; [cost=28339.792401ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:21.782; [cost=28613.232599ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:21.788; [cost=28620.024299ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:21.796; [cost=28391.9631ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:21.799; [cost=28367.204599ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:21.802; [cost=28411.0391ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:21.810; [cost=28377.9376ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:21.820; [cost=28599.779701ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:21.833; [cost=28664.345501ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:21.839; [cost=28433.9963ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.046; [cost=24167.3302ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.090; [cost=24157.2889ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.142; [cost=24186.420101ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.194; [cost=24232.7766ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.271; [cost=24279.341401ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.321; [cost=24301.399401ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.355; [cost=24357.8622ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.372; [cost=24333.405999ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
ts=2025-08-26 15:47:22.372; [cost=24354.7551ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.376; [cost=24326.380499ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.385; [cost=24341.3234ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.429; [cost=24374.782099ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.442; [cost=24335.7223ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.448; [cost=24179.484899ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.488; [cost=24338.3624ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.490; [cost=24347.0954ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.494; [cost=24342.212299ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.502; [cost=24253.0734ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.504; [cost=24325.807099ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.508; [cost=24405.9557ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
ts=2025-08-26 15:47:22.508; [cost=24239.4365ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.509; [cost=24431.4784ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
ts=2025-08-26 15:47:22.509; [cost=24379.4485ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.511; [cost=24238.4285ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.512; [cost=24284.2631ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.514; [cost=24237.281301ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.516; [cost=24373.944799ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.521; [cost=24241.147801ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
ts=2025-08-26 15:47:22.522; [cost=24314.9269ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.522; [cost=24399.686199ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.537; [cost=24473.2147ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.539; [cost=24321.5934ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.554; [cost=23284.045201ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
ts=2025-08-26 15:47:22.554; [cost=24325.2601ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.570; [cost=24494.5777ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.572; [cost=24416.3108ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.572; [cost=24385.8333ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.589; [cost=24402.583ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.609; [cost=23294.030101ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.615; [cost=23286.9593ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.619; [cost=23302.075899ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.637; [cost=23275.375699ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.671; [cost=23287.4705ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.673; [cost=23280.661601ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.688; [cost=23287.6281ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.689; [cost=23298.401299ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
ts=2025-08-26 15:47:22.689; [cost=23282.7585ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.692; [cost=23283.7349ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.709; [cost=23301.8371ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.712; [cost=23302.0335ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.727; [cost=23320.4139ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.727; [cost=23320.6445ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.728; [cost=23319.4955ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.728; [cost=23320.141299ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.728; [cost=23317.5977ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.728; [cost=23318.856201ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.729; [cost=23317.2775ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.735; [cost=23323.7232ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.737; [cost=23325.791599ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.738; [cost=23315.9499ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.739; [cost=23319.076999ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.740; [cost=23326.464899ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.741; [cost=23316.1763ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.743; [cost=23326.8251ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.743; [cost=23303.862001ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.746; [cost=23308.6383ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.747; [cost=23298.5619ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.749; [cost=23317.1001ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.749; [cost=23321.644701ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.750; [cost=23320.334901ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.750; [cost=23305.6962ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.751; [cost=23309.220701ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.753; [cost=23302.9278ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
ts=2025-08-26 15:47:22.753; [cost=23305.727601ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.755; [cost=23308.3421ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.760; [cost=23314.0785ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.761; [cost=23326.431399ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.762; [cost=23311.326ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.763; [cost=23320.2662ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.764; [cost=23301.878601ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.764; [cost=23294.0016ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.764; [cost=23306.764901ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.765; [cost=23294.909201ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.765; [cost=23310.7152ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.765; [cost=23314.954601ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.767; [cost=23300.324701ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.769; [cost=23298.019399ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.770; [cost=23280.3195ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.771; [cost=23279.6517ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.772; [cost=23280.4399ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.773; [cost=23281.9671ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.773; [cost=23283.5785ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.773; [cost=23297.3836ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.775; [cost=23281.6199ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.775; [cost=23283.692899ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.775; [cost=23277.4325ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.777; [cost=23283.117799ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.795; [cost=23294.205401ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.796; [cost=23307.3969ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.803; [cost=23309.949ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.822; [cost=23313.197501ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.840; [cost=22984.9528ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.840; [cost=23342.579899ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.840; [cost=23232.393599ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.840; [cost=23337.5114ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.841; [cost=23345.0314ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.842; [cost=22984.414501ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.842; [cost=23232.7224ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.843; [cost=23336.989999ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.843; [cost=23343.846001ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.861; [cost=23001.727001ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.862; [cost=22990.810201ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.863; [cost=23350.0174ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.864; [cost=23358.9266ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.865; [cost=23268.067701ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.866; [cost=23362.1022ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.867; [cost=23368.987199ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.867; [cost=23372.0987ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.867; [cost=23375.5351ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.868; [cost=23345.310801ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.868; [cost=23324.6804ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.869; [cost=23372.8906ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.886; [cost=23391.5452ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.887; [cost=23385.9596ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.887; [cost=23375.276699ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.887; [cost=23386.550101ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.887; [cost=23387.833699ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.888; [cost=23398.7253ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.888; [cost=22957.0521ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.888; [cost=22951.2284ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.889; [cost=22955.015601ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.890; [cost=22939.929501ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.890; [cost=22771.6561ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.908; [cost=22903.4248ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.908; [cost=22761.355ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.908; [cost=22526.213201ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.908; [cost=22787.6996ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.909; [cost=22784.484201ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.909; [cost=22527.2172ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.909; [cost=22761.187301ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.909; [cost=22456.723001ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.928; [cost=22543.718ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.928; [cost=22417.9542ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.929; [cost=22416.4744ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.966; [cost=22340.4617ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:22.982; [cost=22292.7462ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:23.013; [cost=22315.687799ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:23.016; [cost=22319.129201ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:23.017; [cost=22281.220101ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:23.077; [cost=22275.006ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:23.139; [cost=22338.257199ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:23.255; [cost=22450.930899ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:23.311; [cost=22539.9773ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:26.146; [cost=20699.4717ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:26.571; [cost=27069.7908ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:26.581; [cost=27121.561ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:26.678; [cost=23833.0758ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:26.681; [cost=23441.6774ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:26.753; [cost=22109.124ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:30.848; [cost=19798.0089ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:30.853; [cost=19667.599ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    null,
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:31.285; [cost=22717.507ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    @R[
        code=@String[10000],
        message=@String[list.success],
        data=@PageInfo[
            DEFAULT_NAVIGATE_PAGES=@Integer[8],
            EMPTY=@PageInfo[PageInfo{pageNum=1, pageSize=0, size=0, startRow=0, endRow=0, total=0, pages=0, list=[], prePage=0, nextPage=0, isFirstPage=true, isLastPage=true, hasPreviousPage=false, hasNextPage=false, navigatePages=0, navigateFirstPage=0, navigateLastPage=0, navigatepageNums=[]}],
            pageNum=@Integer[1],
            pageSize=@Integer[10],
            size=@Integer[7],
            startRow=@Long[0],
            endRow=@Long[10],
            pages=@Integer[1],
            prePage=@Integer[0],
            nextPage=@Integer[0],
            isFirstPage=@Boolean[true],
            isLastPage=@Boolean[true],
            hasPreviousPage=@Boolean[false],
            hasNextPage=@Boolean[false],
            navigatePages=@Integer[8],
            navigatepageNums=@int[][isEmpty=false;size=1],
            navigateFirstPage=@Integer[1],
            navigateLastPage=@Integer[1],
            serialVersionUID=@Long[1],
            total=@Long[7],
            list=@ArrayList[isEmpty=false;size=7],
        ],
    ],
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:31.286; [cost=22718.2904ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    @R[
        code=@String[10000],
        message=@String[list.success],
        data=@PageInfo[
            DEFAULT_NAVIGATE_PAGES=@Integer[8],
            EMPTY=@PageInfo[PageInfo{pageNum=1, pageSize=0, size=0, startRow=0, endRow=0, total=0, pages=0, list=[], prePage=0, nextPage=0, isFirstPage=true, isLastPage=true, hasPreviousPage=false, hasNextPage=false, navigatePages=0, navigateFirstPage=0, navigateLastPage=0, navigatepageNums=[]}],
            pageNum=@Integer[1],
            pageSize=@Integer[10],
            size=@Integer[7],
            startRow=@Long[0],
            endRow=@Long[10],
            pages=@Integer[1],
            prePage=@Integer[0],
            nextPage=@Integer[0],
            isFirstPage=@Boolean[true],
            isLastPage=@Boolean[true],
            hasPreviousPage=@Boolean[false],
            hasNextPage=@Boolean[false],
            navigatePages=@Integer[8],
            navigatepageNums=@int[][isEmpty=false;size=1],
            navigateFirstPage=@Integer[1],
            navigateLastPage=@Integer[1],
            serialVersionUID=@Long[1],
            total=@Long[7],
            list=@ArrayList[isEmpty=false;size=7],
        ],
    ],
    null,
]
method=org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed location=AtExit
ts=2025-08-26 15:47:31.286; [cost=22718.5445ms] result=@ArrayList[
    @Object[][isEmpty=true;size=0],
    @R[
        code=@String[10000],
        message=@String[list.success],
        data=@PageInfo[
            DEFAULT_NAVIGATE_PAGES=@Integer[8],
            EMPTY=@PageInfo[PageInfo{pageNum=1, pageSize=0, size=0, startRow=0, endRow=0, total=0, pages=0, list=[], prePage=0, nextPage=0, isFirstPage=true, isLastPage=true, hasPreviousPage=false, hasNextPage=false, navigatePages=0, navigateFirstPage=0, navigateLastPage=0, navigatepageNums=[]}],
            pageNum=@Integer[1],
            pageSize=@Integer[10],
            size=@Integer[7],
            startRow=@Long[0],
            endRow=@Long[10],
            pages=@Integer[1],
            prePage=@Integer[0],
            nextPage=@Integer[0],
            isFirstPage=@Boolean[true],
            isLastPage=@Boolean[true],
            hasPreviousPage=@Boolean[false],
            hasNextPage=@Boolean[false],
            navigatePages=@Integer[8],
            navigatepageNums=@int[][isEmpty=false;size=1],
            navigateFirstPage=@Integer[1],
            navigateLastPage=@Integer[1],
            serialVersionUID=@Long[1],
            total=@Long[7],
            list=@ArrayList[isEmpty=false;size=7],
        ],
    ],
    null,
]
