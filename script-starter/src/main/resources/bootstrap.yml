spring:
  profiles:
    active: aptzyh
  application:
    name: script
  cloud:
#    zookeeper:
#      connect-string: localhost:2181
    nacos:
      config:

        serverAddr: ************:8848
        file-extension: yml
        group: SCRIPT_GROUP
        namespace: 123456
        enabled: false
      discovery:
        namespace: 123456
        serverAddr: ************:8848
        group: DEFAULT_GROUP
        ip: *********
#logging:
#  config: http://${spring.cloud.nacos.discovery.server-addr}/nacos/v1/cs/configs?group=${spring.cloud.nacos.config.group}&dataId=logback-script-es.xml
#app:
#  script-tools-url: script
