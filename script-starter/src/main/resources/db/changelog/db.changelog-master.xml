<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <include file="script/1.0/changelog_dev.yaml" relativeToChangelogFile="true"/>
    <include file="script/1.1/changelog_dev.yaml" relativeToChangelogFile="true"/>
    <include file="script/1.2/changelog_dev.yaml" relativeToChangelogFile="true"/>
    <include file="script/1.3/changelog_dev.yaml" relativeToChangelogFile="true"/>
    <include file="script/1.5/changelog_dev.yaml" relativeToChangelogFile="true"/>
    <include file="script/1.6/changelog_dev.yaml" relativeToChangelogFile="true"/>
    <include file="script/1.7/changelog_dev.yaml" relativeToChangelogFile="true"/>
    <include file="script/1.8/changelog_dev.yaml" relativeToChangelogFile="true"/>
    <include file="script/1.11/changelog_dev.yaml" relativeToChangelogFile="true"/>
    <include file="script/1.12/changelog_dev.yaml" relativeToChangelogFile="true"/>
    <include file="script/1.13/changelog_dev.yaml" relativeToChangelogFile="true"/>
    <include file="script/1.14/changelog_dev.yaml" relativeToChangelogFile="true" />
    <include file="script/1.16/changelog_dev.yaml" relativeToChangelogFile="true" />

    <includeAll path="tools" relativeToChangelogFile="true" errorIfMissingOrEmpty="false"/>
</databaseChangeLog>