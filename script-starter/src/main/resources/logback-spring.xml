<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false" scan="true" scanPeriod="10 seconds">
    <!--定义日志文件的存储地址 -->
    <property name="LOG_HOME" value="${user.dir}/logs"/>
    <!--读取项目启动的环境是哪一个 -->
    <springProperty scope="context" name="active" source="spring.profiles.active" defaultValue="dev"/>

    <!-- 控制台输出 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
                <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%tid] [%thread] %-5level %logger{50} -%msg%n</Pattern>
            </layout>
        </encoder>
    </appender>

    <!-- 按照每天生成日志文件 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!--实时日志-->
        <file>${LOG_HOME}/script.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!--日志文件输出的文件名 -->
            <FileNamePattern>${LOG_HOME}/script-%d{yyyy-MM-dd}.%i.log</FileNamePattern>
            <!--日志文件保留天数 -->
            <MaxHistory>30</MaxHistory>
            <maxFileSize>10MB</maxFileSize>
        </rollingPolicy>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
                <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%tid] [%thread] %-5level %logger{50} -%msg%n</Pattern>
            </layout>
        </encoder>
    </appender>
    <!-- 追加到skywalking OAP服务LOG -->
    <appender name="grpc-log" class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.log.GRPCLogClientAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.mdc.TraceIdMDCPatternLogbackLayout">
                <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%X{tid}] [%thread] %-5level %logger{50} -%msg%n</Pattern>
            </layout>
        </encoder>
    </appender>
<!--

    <appender name="ELASTIC" class="com.internetitem.logback.elasticsearch.ElasticsearchAppender">
        <url>******************************************/_bulk</url>
        <index>script-log-${active}</index>   &lt;!&ndash; 根据启动环境为索引命名,区分各个环境日志 &ndash;&gt;
        <type>${active}</type>
        <connectTimeout>30000</connectTimeout> &lt;!&ndash; optional (in ms, default 30000) &ndash;&gt;
        <errorsToStderr>false</errorsToStderr> &lt;!&ndash; optional (default false) &ndash;&gt;
        <includeCallerData>false</includeCallerData> &lt;!&ndash; optional (default false) &ndash;&gt;
        <logsToStderr>false</logsToStderr> &lt;!&ndash; optional (default false) &ndash;&gt;
        <maxQueueSize>104857600</maxQueueSize> &lt;!&ndash; optional (default 104857600) &ndash;&gt;
        <maxRetries>3</maxRetries> &lt;!&ndash; optional (default 3) &ndash;&gt;
        <readTimeout>30000</readTimeout> &lt;!&ndash; optional (in ms, default 30000) &ndash;&gt;
        <sleepTime>250</sleepTime> &lt;!&ndash; optional (in ms, default 250) &ndash;&gt;
        <rawJsonMessage>false</rawJsonMessage> &lt;!&ndash; optional (default false) &ndash;&gt;
        <includeMdc>false</includeMdc> &lt;!&ndash; optional (default false) &ndash;&gt;
        <maxMessageSize>-1</maxMessageSize> &lt;!&ndash; optional (default -1 &ndash;&gt;
        <authentication class="com.internetitem.logback.elasticsearch.config.BasicAuthentication"/> &lt;!&ndash; optional &ndash;&gt;
        <properties>
            <property>
                <name>host</name>
                <value>${HOSTNAME}</value>
                <allowEmpty>false</allowEmpty>
            </property>
            <property>
                <name>level</name>
                <value>%level</value>
            </property>
            <property>
                <name>thread</name>
                <value>%thread</value>
            </property>
            <property>
                <name>stacktrace</name>
                <value>%ex</value>
            </property>
            <property>
                <name>logger</name>
                <value>%logger</value>
            </property>
            <property>
                <name>env</name>
                <value>${active}</value>
            </property>
        </properties>
        <headers>
            <header>
                <name>Content-Type</name>
                <value>application/json</value>
            </header>
        </headers>
    </appender>
-->

    <springProfile name="dev,apt,aptzyh">
        <root level="INFO">
            <appender-ref ref="grpc-log"/>
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="FILE"/>
<!--            <appender-ref ref="ELASTIC"/>-->
        </root>
    </springProfile>

</configuration>