package com.ideal.script.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;


/**
 * 开启spring async和Scheduled 功能
 *
 * <AUTHOR>
 **/
@Configuration
@EnableAsync
@EnableScheduling
public class AsyncConfiguration {

    private static final Logger log = LoggerFactory.getLogger(AsyncConfiguration.class);

    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;

    public AsyncConfiguration(@Lazy @Qualifier(TaskExecutionAutoConfiguration.APPLICATION_TASK_EXECUTOR_BEAN_NAME)
                              ThreadPoolTaskExecutor threadPoolTaskExecutor) {
        this.threadPoolTaskExecutor = threadPoolTaskExecutor;
    }

    /**
     * 监控打印async线程池使用情况，5分钟打印一次
     */
    @Scheduled(cron = "0 */1 * * * ?")
    public void providerEagerThreadPoolPrint() {
        StringBuilder msg = new StringBuilder();
        ThreadPoolExecutor threadPoolExecutor = threadPoolTaskExecutor.getThreadPoolExecutor();
        msg.append(" max:").append(threadPoolExecutor.getMaximumPoolSize())
           .append(", core:").append(threadPoolExecutor.getCorePoolSize())
           .append(", currentThread:").append(threadPoolExecutor.getPoolSize())
           .append(", largest:").append(threadPoolExecutor.getLargestPoolSize())
           .append(", active:").append(threadPoolExecutor.getActiveCount())
           .append(", queue:").append(threadPoolExecutor.getQueue().size())
           .append(", taskCount:").append(threadPoolExecutor.getTaskCount())
           .append(", completedTaskCount:").append(threadPoolExecutor.getCompletedTaskCount());
        log.info("spring async threadPoolExecutor status:{}", msg);
    }

}
