package com.ideal.script.api;

import com.github.pagehelper.PageInfo;
import com.ideal.script.common.validation.Create;
import com.ideal.script.common.validation.Update;
import com.ideal.script.dto.AttachmentDto;
import com.ideal.script.dto.CategoryApiDto;
import com.ideal.script.dto.CategoryQueryDto;
import com.ideal.script.dto.PublishDto;
import com.ideal.script.dto.RetryScriptInstanceApiDto;
import com.ideal.script.dto.ScriptAttachmentTempMegDto;
import com.ideal.script.dto.ScriptCategoryIconDto;
import com.ideal.script.dto.ScriptDubboInfoDto;
import com.ideal.script.dto.ScriptFileAttachmentTempApiDto;
import com.ideal.script.dto.ScriptFileImportExportApiDto;
import com.ideal.script.dto.ScriptInfoApiDto;
import com.ideal.script.dto.ScriptInfoDto;
import com.ideal.script.dto.ScriptInfoQueryDto;
import com.ideal.script.dto.TaskRuntimeApiDto;
import com.ideal.script.dto.TaskRuntimeQueryApiDto;
import com.ideal.script.exception.ScriptException;
import org.apache.dubbo.validation.MethodValidated;

import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 获取脚本信息接口
 *
 * <AUTHOR>
 */
public interface IScriptInfo {


    /**
     * 根据脚本详情信息获取脚本内容接口
     *
     * @param scriptInfoQueryDto 脚本信息查询条件
     * @return ScriptInfoDto
     * <AUTHOR>
     */
    ScriptDubboInfoDto getScriptInfo(ScriptInfoQueryDto scriptInfoQueryDto) throws ScriptException;

    /**
     * 查询分类树
     *
     * @param categoryQueryDto 分类查询条件
     * @return {@link List}<{@link CategoryApiDto}>
     */
    List<CategoryApiDto> getCategoryTree(CategoryQueryDto categoryQueryDto);


    /**
     * 查询脚本基础信息列表
     *
     * @param scriptInfoQueryDto 脚本信息查询条件
     * @return {@link List}<{@link ScriptInfoApiDto}>
     */
    List<ScriptInfoApiDto> getScriptInfoList(ScriptInfoQueryDto scriptInfoQueryDto);

    /**
     * 分页查询脚本基础信息列表
     *
     * @param scriptInfoQueryDto 脚本信息查询条件
     * @return {@link PageInfo}<{@link ScriptInfoApiDto}>
     */
    PageInfo<ScriptInfoApiDto> getScriptInfoPageList(ScriptInfoQueryDto scriptInfoQueryDto);

    /**
     * 根据上级分类id，获取分类列表
     *
     * @param categoryQueryDto 分类查询条件
     * @return {@link List}<{@link CategoryApiDto}> 结果
     */
    List<CategoryApiDto> getMultiCategoryList(CategoryQueryDto categoryQueryDto);

    /**
     * 新增脚本
     * @param scriptInfoDto  脚本基本信息
     * @throws ScriptException 脚本自定义异常
     * @return  脚本版本id
     */
    @MethodValidated(Create.class)
    String saveMyScript(ScriptInfoDto scriptInfoDto) throws ScriptException;

    /**
     * 更新脚本
     * @param scriptInfoDto 脚本基本信息
     * @throws ScriptException 自定义脚本异常
     * @return  脚本版本id
     */
    @MethodValidated(Update.class)
    String updateMyScript(ScriptInfoDto scriptInfoDto) throws ScriptException;

    /**
     * 脚本发布
     * @param publishDto    发布信息dto
     * @throws ScriptException  自定义脚本异常
     */
    @MethodValidated
    void publishScript(PublishDto publishDto) throws ScriptException;

    /**
     * 脚本自动发布，跳过双人复核，目前仅为工具箱提供
     * @param publishDto    发布信息dto
     * @throws ScriptException  自定义脚本异常
     */
    @MethodValidated
    void publishScriptAuto(PublishDto publishDto) throws ScriptException;


    /**
     * 调用脚本服务化导出脚本功能
     * @param srcScriptUuids 脚本的uuid
     * @return {@link ScriptFileImportExportApiDto} 结果
     */
    ScriptFileImportExportApiDto exportScriptProduction(@NotNull List<String> srcScriptUuids);

    /**
     * 调用脚本服务化导入脚本功能（服务投产）
     * @param scriptFileImportExportApiDto dto数据
     * @return {@link Map} 结果
     */
    Map<String,String> importScriptProduction(@NotNull ScriptFileImportExportApiDto scriptFileImportExportApiDto);


    /**
     * 根据实例id获取本次任务的agent
     * @param taskRuntimeQueryApiDto 查询条件
     * @return {@link TaskRuntimeApiDto} 返回结果
     */
    PageInfo<TaskRuntimeApiDto> getTaskRuntimeInfoByInstanceId(TaskRuntimeQueryApiDto taskRuntimeQueryApiDto);

    /**
     * 根据实例id获取本次任务的agent标准输出
     * @param taskRuntimeId agent运行实例id
     * @return {@link Long} 返回结果
     * @throws ScriptException 异常
     */
    String getStdoutByTaskRuntimeId (@NotNull Long taskRuntimeId) throws ScriptException;

    /**
     * 根据任意版本uuid查询默认版本信息
     * @param scriptInfoQueryDto   脚本信息查询Dto
     * @return  {@link ScriptDubboInfoDto}
     */
    ScriptDubboInfoDto getDefaultScriptInfo(ScriptInfoQueryDto scriptInfoQueryDto) throws ScriptException;

    /**
     * 上传附件dubbo接口
     * @param attachmentDto   附件信息列表
     * @return  {@link AttachmentDto}
     */
    AttachmentDto uploadAttachment(AttachmentDto attachmentDto) throws ScriptException, IOException;

    /**
     * 上传临时附件dubbo接口
     * @param scriptFileAttachmentTempList 附件参数
     * @return 上传成功附件id信息
     * @throws ScriptException 脚本服务化异常
     * @throws IOException IO异常
     */
    ScriptAttachmentTempMegDto uploadAttachmentTemp(List<ScriptFileAttachmentTempApiDto> scriptFileAttachmentTempList) throws ScriptException, IOException;

    /**
     * 获取agent实例标准输出
     * @param retryScriptInstanceApiDto 参数
     * @return 标准输出
     */
    String getAgentStdoutByTaskIdAndAddress(RetryScriptInstanceApiDto retryScriptInstanceApiDto);

    /**
     * 根据脚本uuid获取图标
     * @param srcScriptUuids 脚本srcUuid集合
     * @return 图标和脚本uuid对应关系列表
     */
    List<ScriptCategoryIconDto> getScriptCategoryIconList(List<String> srcScriptUuids);
}
