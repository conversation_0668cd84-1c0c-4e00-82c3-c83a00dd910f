Affect(class count: 2 , method count: 4) cost in 298 ms, listenerId: 18
`---ts=2025-08-26 15:28:51.459;thread_name=http-nio-9420-exec-2;id=428;is_daemon=true;priority=5;TCCL=org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedWebappClassLoader@6e22d6bf
    `---[5272.066901ms] com.ideal.script.service.impl.CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39:getCategoryFullPath()
        `---[100.00% 5272.038701ms ] org.springframework.cglib.proxy.MethodInterceptor:intercept()
            `---[0.05% 2.544ms ] com.ideal.script.service.impl.CategoryServiceImpl:getCategoryFullPath()
                +---[0.13% 0.003301ms ] java.lang.StringBuilder:<init>() #752
                `---[99.15% 2.5224ms ] com.ideal.script.service.impl.CategoryServiceImpl:getCategoryFullPathRecursive() #752

`---ts=2025-08-26 15:29:07.727;thread_name=http-nio-9420-exec-5;id=431;is_daemon=true;priority=5;TCCL=org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedWebappClassLoader@6e22d6bf
    `---[4405.0411ms] com.ideal.script.service.impl.CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39:getCategoryFullPath()
        `---[100.00% 4405.031099ms ] org.springframework.cglib.proxy.MethodInterceptor:intercept()
            `---[0.02% 1.1ms ] com.ideal.script.service.impl.CategoryServiceImpl:getCategoryFullPath()
                +---[0.16% 0.0018ms ] java.lang.StringBuilder:<init>() #752
                `---[98.66% 1.0853ms ] com.ideal.script.service.impl.CategoryServiceImpl:getCategoryFullPathRecursive() #752

`---ts=2025-08-26 15:32:12.646;thread_name=http-nio-9420-exec-7;id=433;is_daemon=true;priority=5;TCCL=org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedWebappClassLoader@6e22d6bf
    `---[4683.217299ms] org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor:intercept()
        +---[0.00% 0.032ms ] org.springframework.aop.framework.AdvisedSupport:getTargetSource() #683
        +---[0.00% 0.005499ms ] org.springframework.aop.TargetSource:getTarget() #691
        +---[0.00% 0.008801ms ] java.lang.Object:getClass() #692
        +---[0.00% 0.017799ms ] org.springframework.aop.framework.AdvisedSupport:getInterceptorsAndDynamicInterceptionAdvice() #693
        +---[0.00% 0.013899ms ] java.util.List:isEmpty() #697
        +---[0.00% 0.021199ms ] org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation:<init>() #707
        +---[99.99% 4682.9699ms ] org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation:proceed() #707
        |   `---[99.67% 4667.694ms ] org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor:intercept()
        |       +---[0.00% 0.0024ms ] org.springframework.aop.framework.AdvisedSupport:getTargetSource() #683
        |       +---[0.00% 0.0024ms ] org.springframework.aop.TargetSource:getTarget() #691
        |       +---[0.00% 0.002ms ] java.lang.Object:getClass() #692
        |       +---[0.00% 0.006199ms ] org.springframework.aop.framework.AdvisedSupport:getInterceptorsAndDynamicInterceptionAdvice() #693
        |       +---[0.00% 0.001401ms ] java.util.List:isEmpty() #697
        |       +---[0.00% 0.0086ms ] org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation:isMethodProxyCompatible() #697
        |       +---[0.00% 0.0122ms ] org.springframework.aop.framework.AopProxyUtils:adaptArgumentsIfNecessary() #702
        |       +---[100.00% 4667.6218ms ] org.springframework.aop.framework.CglibAopProxy:access$000() #703
        |       |   `---[99.96% 4665.9071ms ] org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor:intercept()
        |       |       +---[0.00% 0.002099ms ] org.springframework.aop.framework.AdvisedSupport:getTargetSource() #683
        |       |       +---[0.00% 0.0019ms ] org.springframework.aop.TargetSource:getTarget() #691
        |       |       +---[0.00% 0.0015ms ] java.lang.Object:getClass() #692
        |       |       +---[0.00% 0.002299ms ] org.springframework.aop.framework.AdvisedSupport:getInterceptorsAndDynamicInterceptionAdvice() #693
        |       |       +---[0.00% 0.001099ms ] java.util.List:isEmpty() #697
        |       |       +---[0.00% 0.0034ms ] org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation:<init>() #707
        |       |       +---[100.00% 4665.8615ms ] org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation:proceed() #707
        |       |       |   `---[13.07% min=0.042ms,max=571.9009ms,total=609.848997ms,count=10] org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor:intercept()
        |       |       |       +---[0.00% min=0.0012ms,max=0.0026ms,total=0.019199ms,count=10] org.springframework.aop.framework.AdvisedSupport:getTargetSource() #683
        |       |       |       +---[0.00% min=9.99E-4ms,max=0.001999ms,total=0.0144ms,count=10] org.springframework.aop.TargetSource:getTarget() #691
        |       |       |       +---[0.00% min=0.001099ms,max=0.002301ms,total=0.0171ms,count=10] java.lang.Object:getClass() #692
        |       |       |       +---[0.00% min=0.002ms,max=0.0038ms,total=0.029601ms,count=10] org.springframework.aop.framework.AdvisedSupport:getInterceptorsAndDynamicInterceptionAdvice() #693
        |       |       |       +---[0.00% min=9.0E-4ms,max=0.0018ms,total=0.013501ms,count=10] java.util.List:isEmpty() #697
        |       |       |       +---[0.01% min=0.0025ms,max=0.023601ms,total=0.046499ms,count=7] org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation:<init>() #707
        |       |       |       +---[99.22% min=0.2309ms,max=571.8619ms,total=605.118399ms,count=7] org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation:proceed() #707
        |       |       |       |   `---[93.48% min=4.221101ms,max=554.9519ms,total=565.641401ms,count=3] org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor:intercept()
        |       |       |       |       +---[0.00% min=0.0013ms,max=0.0018ms,total=0.0046ms,count=3] org.springframework.aop.framework.AdvisedSupport:getTargetSource() #683
        |       |       |       |       +---[0.00% min=8.99E-4ms,max=0.001701ms,total=0.0043ms,count=3] org.springframework.aop.TargetSource:getTarget() #691
        |       |       |       |       +---[0.00% min=0.001101ms,max=0.0023ms,total=0.004701ms,count=3] java.lang.Object:getClass() #692
        |       |       |       |       +---[0.00% min=0.0021ms,max=0.0034ms,total=0.0079ms,count=3] org.springframework.aop.framework.AdvisedSupport:getInterceptorsAndDynamicInterceptionAdvice() #693
        |       |       |       |       +---[0.00% min=0.001ms,max=0.0018ms,total=0.0039ms,count=3] java.util.List:isEmpty() #697
        |       |       |       |       +---[0.00% min=0.0018ms,max=0.0049ms,total=0.0067ms,count=2] org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation:<init>() #707
        |       |       |       |       +---[1.87% min=4.149399ms,max=6.4372ms,total=10.586599ms,count=2] org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation:proceed() #707
        |       |       |       |       +---[0.00% min=0.002099ms,max=0.003ms,total=0.007999ms,count=3] org.springframework.aop.framework.CglibAopProxy:access$100() #709
        |       |       |       |       +---[0.00% min=0.002ms,max=0.0021ms,total=0.0061ms,count=3] org.springframework.aop.TargetSource:isStatic() #713
        |       |       |       |       +---[0.00% 0.0014ms ] org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation:isMethodProxyCompatible() #697
        |       |       |       |       +---[0.00% 0.001299ms ] org.springframework.aop.framework.AopProxyUtils:adaptArgumentsIfNecessary() #702
        |       |       |       |       `---[98.10% 554.9096ms ] org.springframework.aop.framework.CglibAopProxy:access$000() #703
        |       |       |       |           `---[24.27% min=0.0887ms,max=98.045999ms,total=134.672699ms,count=11] org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor:intercept()
        |       |       |       |               +---[0.02% min=0.0016ms,max=0.0041ms,total=0.025702ms,count=11] org.springframework.aop.framework.AdvisedSupport:getTargetSource() #683
        |       |       |       |               +---[0.01% min=0.001ms,max=0.0022ms,total=0.015998ms,count=11] org.springframework.aop.TargetSource:getTarget() #691
        |       |       |       |               +---[0.01% min=9.0E-4ms,max=0.0027ms,total=0.017ms,count=11] java.lang.Object:getClass() #692
        |       |       |       |               +---[0.02% min=0.0016ms,max=0.0043ms,total=0.029901ms,count=11] org.springframework.aop.framework.AdvisedSupport:getInterceptorsAndDynamicInterceptionAdvice() #693
        |       |       |       |               +---[0.01% min=9.0E-4ms,max=0.001999ms,total=0.013497ms,count=11] java.util.List:isEmpty() #697
        |       |       |       |               +---[0.01% min=0.001499ms,max=0.001899ms,total=0.006797ms,count=4] org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation:isMethodProxyCompatible() #697
        |       |       |       |               +---[0.00% min=0.001499ms,max=0.0018ms,total=0.0064ms,count=4] org.springframework.aop.framework.AopProxyUtils:adaptArgumentsIfNecessary() #702
        |       |       |       |               +---[6.30% min=0.0542ms,max=7.790799ms,total=8.482398ms,count=4] org.springframework.aop.framework.CglibAopProxy:access$000() #703
        |       |       |       |               +---[0.02% min=0.001299ms,max=0.0028ms,total=0.0215ms,count=11] org.springframework.aop.framework.CglibAopProxy:access$100() #709
        |       |       |       |               +---[0.01% min=0.001ms,max=0.0022ms,total=0.0177ms,count=11] org.springframework.aop.TargetSource:isStatic() #713
        |       |       |       |               +---[0.02% min=0.0026ms,max=0.0039ms,total=0.021498ms,count=7] org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation:<init>() #707
        |       |       |       |               `---[93.36% min=2.9799ms,max=98.0029ms,total=125.735198ms,count=7] org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation:proceed() #707
        |       |       |       +---[0.01% min=0.0014ms,max=0.0148ms,total=0.0334ms,count=10] org.springframework.aop.framework.CglibAopProxy:access$100() #709
        |       |       |       +---[0.00% min=0.001ms,max=0.0058ms,total=0.021697ms,count=10] org.springframework.aop.TargetSource:isStatic() #713
        |       |       |       +---[0.00% min=0.0016ms,max=0.002101ms,total=0.005301ms,count=3] org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation:isMethodProxyCompatible() #697
        |       |       |       +---[0.00% min=0.0015ms,max=0.0021ms,total=0.0052ms,count=3] org.springframework.aop.framework.AopProxyUtils:adaptArgumentsIfNecessary() #702
        |       |       |       `---[0.68% min=0.0049ms,max=3.5593ms,total=4.1695ms,count=3] org.springframework.aop.framework.CglibAopProxy:access$000() #703
        |       |       +---[0.00% 0.0023ms ] org.springframework.aop.framework.CglibAopProxy:access$100() #709
        |       |       `---[0.00% 0.003301ms ] org.springframework.aop.TargetSource:isStatic() #713
        |       +---[0.00% 0.0015ms ] org.springframework.aop.framework.CglibAopProxy:access$100() #709
        |       `---[0.00% 0.0012ms ] org.springframework.aop.TargetSource:isStatic() #713
        +---[0.00% 0.0015ms ] org.springframework.aop.framework.CglibAopProxy:access$100() #709
        `---[0.00% 9.0E-4ms ] org.springframework.aop.TargetSource:isStatic() #713

