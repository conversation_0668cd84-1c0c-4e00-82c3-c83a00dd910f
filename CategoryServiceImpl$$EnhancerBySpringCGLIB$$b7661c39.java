
ClassLoader:                                                                                                                  
+-sun.misc.Launcher$AppClassLoader@18b4aac2                                                                                   
  +-sun.misc.Launcher$ExtClassLoader@441772e                                                                                  

Location:                                                                                                                     
/D:/1.project/entegor_v9/ieai-script-service/script-tools-biz/script-biz/target/classes/                                      

/*
 * Decompiled with CFR.
 * 
 * Could not load the following classes:
 *  com.github.pagehelper.PageInfo
 *  com.ideal.common.util.batch.BatchHandler
 *  com.ideal.script.dto.CategoryDto
 *  com.ideal.script.dto.ScriptInfoQueryDto
 *  com.ideal.script.exception.ScriptException
 *  com.ideal.script.mapper.CategoryMapper
 *  com.ideal.script.mapper.InfoMapper
 *  com.ideal.script.model.bean.CategoryOrgBean
 *  com.ideal.script.model.bean.CategoryPermissionAware
 *  com.ideal.script.model.bean.CategoryPermissionInfo
 *  com.ideal.script.model.bean.CategoryUserBean
 *  com.ideal.script.model.dto.CategoryOrgDto
 *  com.ideal.script.model.dto.CategoryRoleDto
 *  com.ideal.script.model.dto.CategoryUserDto
 *  com.ideal.script.model.entity.Category
 *  com.ideal.script.service.IMyScriptService
 *  com.ideal.script.service.IScriptVersionShareService
 *  com.ideal.script.service.impl.CategoryServiceImpl
 *  com.ideal.system.api.IOrgManagement
 *  com.ideal.system.api.IRole
 *  com.ideal.system.api.IUserInfo
 *  com.ideal.system.common.component.model.CurrentUser
 *  com.ideal.system.dto.OrgManagementApiDto
 *  com.ideal.system.dto.RoleApiDto
 *  org.aopalliance.aop.Advice
 *  org.springframework.aop.Advisor
 *  org.springframework.aop.SpringProxy
 *  org.springframework.aop.TargetClassAware
 *  org.springframework.aop.TargetSource
 *  org.springframework.aop.framework.Advised
 *  org.springframework.aop.framework.AopConfigException
 *  org.springframework.cglib.core.ReflectUtils
 *  org.springframework.cglib.core.Signature
 *  org.springframework.cglib.proxy.Callback
 *  org.springframework.cglib.proxy.Dispatcher
 *  org.springframework.cglib.proxy.Factory
 *  org.springframework.cglib.proxy.MethodProxy
 *  org.springframework.cglib.proxy.NoOp
 */
package com.ideal.script.service.impl;

import com.github.pagehelper.PageInfo;
import com.ideal.common.util.batch.BatchHandler;
import com.ideal.script.dto.CategoryDto;
import com.ideal.script.dto.ScriptInfoQueryDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.CategoryMapper;
import com.ideal.script.mapper.InfoMapper;
import com.ideal.script.model.bean.CategoryOrgBean;
import com.ideal.script.model.bean.CategoryPermissionAware;
import com.ideal.script.model.bean.CategoryPermissionInfo;
import com.ideal.script.model.bean.CategoryUserBean;
import com.ideal.script.model.dto.CategoryOrgDto;
import com.ideal.script.model.dto.CategoryRoleDto;
import com.ideal.script.model.dto.CategoryUserDto;
import com.ideal.script.model.entity.Category;
import com.ideal.script.service.IMyScriptService;
import com.ideal.script.service.IScriptVersionShareService;
import com.ideal.script.service.impl.CategoryServiceImpl;
import com.ideal.system.api.IOrgManagement;
import com.ideal.system.api.IRole;
import com.ideal.system.api.IUserInfo;
import com.ideal.system.common.component.model.CurrentUser;
import com.ideal.system.dto.OrgManagementApiDto;
import com.ideal.system.dto.RoleApiDto;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.aopalliance.aop.Advice;
import org.springframework.aop.Advisor;
import org.springframework.aop.SpringProxy;
import org.springframework.aop.TargetClassAware;
import org.springframework.aop.TargetSource;
import org.springframework.aop.framework.Advised;
import org.springframework.aop.framework.AopConfigException;
import org.springframework.cglib.core.ReflectUtils;
import org.springframework.cglib.core.Signature;
import org.springframework.cglib.proxy.Callback;
import org.springframework.cglib.proxy.Dispatcher;
import org.springframework.cglib.proxy.Factory;
import org.springframework.cglib.proxy.MethodInterceptor;
import org.springframework.cglib.proxy.MethodProxy;
import org.springframework.cglib.proxy.NoOp;

public class CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39
extends CategoryServiceImpl
implements SpringProxy,
Advised,
Factory {
    private boolean CGLIB$BOUND;
    public static Object CGLIB$FACTORY_DATA;
    private static final ThreadLocal CGLIB$THREAD_CALLBACKS;
    private static final Callback[] CGLIB$STATIC_CALLBACKS;
    private MethodInterceptor CGLIB$CALLBACK_0;
    private MethodInterceptor CGLIB$CALLBACK_1;
    private NoOp CGLIB$CALLBACK_2;
    private Dispatcher CGLIB$CALLBACK_3;
    private Dispatcher CGLIB$CALLBACK_4;
    private MethodInterceptor CGLIB$CALLBACK_5;
    private MethodInterceptor CGLIB$CALLBACK_6;
    private static Object CGLIB$CALLBACK_FILTER;
    private static final Method CGLIB$deleteCategoryByIds$0$Method;
    private static final MethodProxy CGLIB$deleteCategoryByIds$0$Proxy;
    private static final Object[] CGLIB$emptyArgs;
    private static final Method CGLIB$selectCategoryListNoPage$1$Method;
    private static final MethodProxy CGLIB$selectCategoryListNoPage$1$Proxy;
    private static final Method CGLIB$selectCategoryList$2$Method;
    private static final MethodProxy CGLIB$selectCategoryList$2$Proxy;
    private static final Method CGLIB$listMultiCategory$3$Method;
    private static final MethodProxy CGLIB$listMultiCategory$3$Proxy;
    private static final Method CGLIB$getCategoryRoleRelations$4$Method;
    private static final MethodProxy CGLIB$getCategoryRoleRelations$4$Proxy;
    private static final Method CGLIB$assignCategoryToUser$5$Method;
    private static final MethodProxy CGLIB$assignCategoryToUser$5$Proxy;
    private static final Method CGLIB$queryPermissionUserInfoPage$6$Method;
    private static final MethodProxy CGLIB$queryPermissionUserInfoPage$6$Proxy;
    private static final Method CGLIB$selectOrgManagementTree$7$Method;
    private static final MethodProxy CGLIB$selectOrgManagementTree$7$Proxy;
    private static final Method CGLIB$selectCategoryListDFS$8$Method;
    private static final MethodProxy CGLIB$selectCategoryListDFS$8$Proxy;
    private static final Method CGLIB$queryPermissionUserInfoList$9$Method;
    private static final MethodProxy CGLIB$queryPermissionUserInfoList$9$Proxy;
    private static final Method CGLIB$assignCategoryToOrg$10$Method;
    private static final MethodProxy CGLIB$assignCategoryToOrg$10$Proxy;
    private static final Method CGLIB$listFirstCategory$11$Method;
    private static final MethodProxy CGLIB$listFirstCategory$11$Proxy;
    private static final Method CGLIB$getCategoryOrgRelations$12$Method;
    private static final MethodProxy CGLIB$getCategoryOrgRelations$12$Proxy;
    private static final Method CGLIB$selectNotShareOrgManagementTree$13$Method;
    private static final MethodProxy CGLIB$selectNotShareOrgManagementTree$13$Proxy;
    private static final Method CGLIB$getCategoryFullPath$14$Method;
    private static final MethodProxy CGLIB$getCategoryFullPath$14$Proxy;
    private static final Method CGLIB$selectRoleManagementList$15$Method;
    private static final MethodProxy CGLIB$selectRoleManagementList$15$Proxy;
    private static final Method CGLIB$getCategoryUserRelations$16$Method;
    private static final MethodProxy CGLIB$getCategoryUserRelations$16$Proxy;
    private static final Method CGLIB$assignCategoryToRole$17$Method;
    private static final MethodProxy CGLIB$assignCategoryToRole$17$Proxy;
    private static final Method CGLIB$queryPermissionUserInfoPageByRole$18$Method;
    private static final MethodProxy CGLIB$queryPermissionUserInfoPageByRole$18$Proxy;
    private static final Method CGLIB$listNextCategory$19$Method;
    private static final MethodProxy CGLIB$listNextCategory$19$Proxy;
    private static final Method CGLIB$updateCategory$20$Method;
    private static final MethodProxy CGLIB$updateCategory$20$Proxy;
    private static final Method CGLIB$insertCategory$21$Method;
    private static final MethodProxy CGLIB$insertCategory$21$Proxy;
    private static final Method CGLIB$getCategory$22$Method;
    private static final MethodProxy CGLIB$getCategory$22$Proxy;
    private static final Method CGLIB$buildTree$23$Method;
    private static final MethodProxy CGLIB$buildTree$23$Proxy;
    private static final Method CGLIB$filterCategoryTree$24$Method;
    private static final MethodProxy CGLIB$filterCategoryTree$24$Proxy;
    private static final Method CGLIB$processCategoryTree$25$Method;
    private static final MethodProxy CGLIB$processCategoryTree$25$Proxy;
    private static final Method CGLIB$selectCategoryById$26$Method;
    private static final MethodProxy CGLIB$selectCategoryById$26$Proxy;
    private static final Method CGLIB$checkIfCategoryReferenced$27$Method;
    private static final MethodProxy CGLIB$checkIfCategoryReferenced$27$Proxy;
    private static final Method CGLIB$addCategoryAndChildrenToSet$28$Method;
    private static final MethodProxy CGLIB$addCategoryAndChildrenToSet$28$Proxy;
    private static final Method CGLIB$getCurrentAndSubclassIds$29$Method;
    private static final MethodProxy CGLIB$getCurrentAndSubclassIds$29$Proxy;
    private static final Method CGLIB$handleCategoryPath$30$Method;
    private static final MethodProxy CGLIB$handleCategoryPath$30$Proxy;
    private static final Method CGLIB$findByLevelAndNameAndParentId$31$Method;
    private static final MethodProxy CGLIB$findByLevelAndNameAndParentId$31$Proxy;
    private static final Method CGLIB$getCategoryReferencedCount$32$Method;
    private static final MethodProxy CGLIB$getCategoryReferencedCount$32$Proxy;
    private static final Method CGLIB$deleteCategoryById$33$Method;
    private static final MethodProxy CGLIB$deleteCategoryById$33$Proxy;
    private static final Method CGLIB$buildCategoryHierarchy$34$Method;
    private static final MethodProxy CGLIB$buildCategoryHierarchy$34$Proxy;
    private static final Method CGLIB$checkHasRootCategoryName$35$Method;
    private static final MethodProxy CGLIB$checkHasRootCategoryName$35$Proxy;
    private static final Method CGLIB$retrieveAllDescendantCategoryIds$36$Method;
    private static final MethodProxy CGLIB$retrieveAllDescendantCategoryIds$36$Proxy;
    private static final Method CGLIB$getAllCategoryIds$37$Method;
    private static final MethodProxy CGLIB$getAllCategoryIds$37$Proxy;
    private static final Method CGLIB$buildCategoryFullPath$38$Method;
    private static final MethodProxy CGLIB$buildCategoryFullPath$38$Proxy;
    private static final Method CGLIB$setChildrenForCategoryAndParent$39$Method;
    private static final MethodProxy CGLIB$setChildrenForCategoryAndParent$39$Proxy;
    private static final Method CGLIB$buildCategoryPath$40$Method;
    private static final MethodProxy CGLIB$buildCategoryPath$40$Proxy;
    private static final Method CGLIB$getCategoryWithChildren$41$Method;
    private static final MethodProxy CGLIB$getCategoryWithChildren$41$Proxy;
    private static final Method CGLIB$getCategoryWithChildren$42$Method;
    private static final MethodProxy CGLIB$getCategoryWithChildren$42$Proxy;
    private static final Method CGLIB$selectCategoryByIds$43$Method;
    private static final MethodProxy CGLIB$selectCategoryByIds$43$Proxy;
    private static final Method CGLIB$setCategoryPermission$44$Method;
    private static final MethodProxy CGLIB$setCategoryPermission$44$Proxy;
    private static final Method CGLIB$getShareTypeRoleIdsByLoginName$45$Method;
    private static final MethodProxy CGLIB$getShareTypeRoleIdsByLoginName$45$Proxy;
    private static final Method CGLIB$setCategoryPermissionInfo$46$Method;
    private static final MethodProxy CGLIB$setCategoryPermissionInfo$46$Proxy;
    private static final Method CGLIB$getUserByCategoryIds$47$Method;
    private static final MethodProxy CGLIB$getUserByCategoryIds$47$Proxy;
    private static final Method CGLIB$getCategoryByOrgCode$48$Method;
    private static final MethodProxy CGLIB$getCategoryByOrgCode$48$Proxy;
    private static final Method CGLIB$getFirstCategoryByCategoryNameList$49$Method;
    private static final MethodProxy CGLIB$getFirstCategoryByCategoryNameList$49$Proxy;
    private static final Method CGLIB$equals$50$Method;
    private static final MethodProxy CGLIB$equals$50$Proxy;
    private static final Method CGLIB$toString$51$Method;
    private static final MethodProxy CGLIB$toString$51$Proxy;
    private static final Method CGLIB$hashCode$52$Method;
    private static final MethodProxy CGLIB$hashCode$52$Proxy;
    private static final Method CGLIB$clone$53$Method;
    private static final MethodProxy CGLIB$clone$53$Proxy;

    static void CGLIB$STATICHOOK100() {
        CGLIB$THREAD_CALLBACKS = new ThreadLocal();
        CGLIB$emptyArgs = new Object[0];
        Class<?> clazz = Class.forName("com.ideal.script.service.impl.CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39");
        Class<?> clazz2 = Class.forName("com.ideal.script.service.impl.CategoryServiceImpl");
        Method[] methodArray = ReflectUtils.findMethods((String[])new String[]{"deleteCategoryByIds", "([Ljava/lang/Long;)I", "selectCategoryListNoPage", "(Lcom/ideal/script/dto/CategoryDto;)Ljava/util/List;", "selectCategoryList", "(Lcom/ideal/script/dto/CategoryDto;II)Lcom/github/pagehelper/PageInfo;", "listMultiCategory", "(Ljava/lang/Integer;Ljava/lang/Long;)Ljava/util/List;", "getCategoryRoleRelations", "(J)Lcom/ideal/script/model/dto/CategoryRoleDto;", "assignCategoryToUser", "(Lcom/ideal/script/model/dto/CategoryUserDto;)V", "queryPermissionUserInfoPage", "(Lcom/ideal/script/model/bean/CategoryUserBean;Ljava/lang/Integer;Ljava/lang/Integer;)Lcom/github/pagehelper/PageInfo;", "selectOrgManagementTree", "(Lcom/ideal/system/dto/OrgManagementApiDto;)Ljava/util/List;", "selectCategoryListDFS", "(Lcom/ideal/script/dto/CategoryDto;)Ljava/util/List;", "queryPermissionUserInfoList", "(Lcom/ideal/script/model/bean/CategoryUserBean;)Ljava/util/List;", "assignCategoryToOrg", "(Lcom/ideal/script/model/dto/CategoryOrgDto;)V", "listFirstCategory", "()Ljava/util/List;", "getCategoryOrgRelations", "(J)Lcom/ideal/script/model/bean/CategoryOrgBean;", "selectNotShareOrgManagementTree", "(Ljava/lang/Long;)Ljava/util/List;", "getCategoryFullPath", "(J)Ljava/lang/String;", "selectRoleManagementList", "(Lcom/ideal/system/dto/RoleApiDto;Ljava/lang/Integer;Ljava/lang/Integer;)Lcom/github/pagehelper/PageInfo;", "getCategoryUserRelations", "(J)Lcom/ideal/script/model/bean/CategoryUserBean;", "assignCategoryToRole", "(Lcom/ideal/script/model/dto/CategoryRoleDto;)V", "queryPermissionUserInfoPageByRole", "(Lcom/ideal/script/model/dto/CategoryRoleDto;Ljava/lang/Integer;Ljava/lang/Integer;)Lcom/github/pagehelper/PageInfo;", "listNextCategory", "(J)Ljava/util/List;", "updateCategory", "(Lcom/ideal/script/dto/CategoryDto;)I", "insertCategory", "(Lcom/ideal/script/dto/CategoryDto;)I", "getCategory", "(Ljava/lang/Long;)Lcom/ideal/script/model/entity/Category;", "buildTree", "(Lcom/ideal/script/model/entity/Category;Ljava/util/List;I)Lcom/ideal/script/model/entity/Category;", "filterCategoryTree", "(Ljava/util/List;Ljava/util/List;)Ljava/util/List;", "processCategoryTree", "(Lcom/ideal/script/model/entity/Category;Ljava/util/List;Ljava/util/Set;Ljava/util/Map;)V", "selectCategoryById", "(Ljava/lang/Long;)Lcom/ideal/script/dto/CategoryDto;", "checkIfCategoryReferenced", "(Ljava/lang/Long;)Z", "addCategoryAndChildrenToSet", "(Ljava/lang/Long;Ljava/util/Set;)V", "getCurrentAndSubclassIds", "(Lcom/ideal/script/dto/ScriptInfoQueryDto;)Ljava/util/List;", "handleCategoryPath", "(Ljava/lang/String;)Ljava/lang/String;", "findByLevelAndNameAndParentId", "(Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/Long;)Lcom/ideal/script/dto/CategoryDto;", "getCategoryReferencedCount", "(Ljava/util/List;)Ljava/lang/Boolean;", "deleteCategoryById", "(Ljava/lang/Long;)I", "buildCategoryHierarchy", "(Ljava/util/List;)V", "checkHasRootCategoryName", "(Lcom/ideal/script/dto/CategoryDto;)V", "retrieveAllDescendantCategoryIds", "(Ljava/lang/Long;Ljava/util/List;)V", "getAllCategoryIds", "(Ljava/lang/Long;)Ljava/util/List;", "buildCategoryFullPath", "(Ljava/lang/Long;)Ljava/lang/String;", "setChildrenForCategoryAndParent", "(Lcom/ideal/script/model/entity/Category;)Lcom/ideal/script/model/entity/Category;", "buildCategoryPath", "(Lcom/ideal/script/model/entity/Category;)Ljava/lang/String;", "getCategoryWithChildren", "(Lcom/ideal/script/model/entity/Category;Ljava/lang/Boolean;)Ljava/util/List;", "getCategoryWithChildren", "(Lcom/ideal/script/model/entity/Category;)Ljava/util/List;", "selectCategoryByIds", "([Ljava/lang/Long;)Ljava/util/List;", "setCategoryPermission", "(Lcom/ideal/script/model/bean/CategoryPermissionAware;Lcom/ideal/system/common/component/model/CurrentUser;)V", "getShareTypeRoleIdsByLoginName", "(Ljava/lang/String;)Ljava/util/List;", "setCategoryPermissionInfo", "(Ljava/lang/Long;Lcom/ideal/system/common/component/model/CurrentUser;)Lcom/ideal/script/model/bean/CategoryPermissionInfo;", "getUserByCategoryIds", "(Ljava/util/List;)Ljava/util/List;", "getCategoryByOrgCode", "(Ljava/lang/String;)Ljava/util/List;", "getFirstCategoryByCategoryNameList", "(Ljava/util/List;)Ljava/util/List;"}, (Method[])clazz2.getDeclaredMethods());
        CGLIB$deleteCategoryByIds$0$Method = methodArray[0];
        CGLIB$deleteCategoryByIds$0$Proxy = MethodProxy.create(clazz2, clazz, (String)"([Ljava/lang/Long;)I", (String)"deleteCategoryByIds", (String)"CGLIB$deleteCategoryByIds$0");
        CGLIB$selectCategoryListNoPage$1$Method = methodArray[1];
        CGLIB$selectCategoryListNoPage$1$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Lcom/ideal/script/dto/CategoryDto;)Ljava/util/List;", (String)"selectCategoryListNoPage", (String)"CGLIB$selectCategoryListNoPage$1");
        CGLIB$selectCategoryList$2$Method = methodArray[2];
        CGLIB$selectCategoryList$2$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Lcom/ideal/script/dto/CategoryDto;II)Lcom/github/pagehelper/PageInfo;", (String)"selectCategoryList", (String)"CGLIB$selectCategoryList$2");
        CGLIB$listMultiCategory$3$Method = methodArray[3];
        CGLIB$listMultiCategory$3$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Ljava/lang/Integer;Ljava/lang/Long;)Ljava/util/List;", (String)"listMultiCategory", (String)"CGLIB$listMultiCategory$3");
        CGLIB$getCategoryRoleRelations$4$Method = methodArray[4];
        CGLIB$getCategoryRoleRelations$4$Proxy = MethodProxy.create(clazz2, clazz, (String)"(J)Lcom/ideal/script/model/dto/CategoryRoleDto;", (String)"getCategoryRoleRelations", (String)"CGLIB$getCategoryRoleRelations$4");
        CGLIB$assignCategoryToUser$5$Method = methodArray[5];
        CGLIB$assignCategoryToUser$5$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Lcom/ideal/script/model/dto/CategoryUserDto;)V", (String)"assignCategoryToUser", (String)"CGLIB$assignCategoryToUser$5");
        CGLIB$queryPermissionUserInfoPage$6$Method = methodArray[6];
        CGLIB$queryPermissionUserInfoPage$6$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Lcom/ideal/script/model/bean/CategoryUserBean;Ljava/lang/Integer;Ljava/lang/Integer;)Lcom/github/pagehelper/PageInfo;", (String)"queryPermissionUserInfoPage", (String)"CGLIB$queryPermissionUserInfoPage$6");
        CGLIB$selectOrgManagementTree$7$Method = methodArray[7];
        CGLIB$selectOrgManagementTree$7$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Lcom/ideal/system/dto/OrgManagementApiDto;)Ljava/util/List;", (String)"selectOrgManagementTree", (String)"CGLIB$selectOrgManagementTree$7");
        CGLIB$selectCategoryListDFS$8$Method = methodArray[8];
        CGLIB$selectCategoryListDFS$8$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Lcom/ideal/script/dto/CategoryDto;)Ljava/util/List;", (String)"selectCategoryListDFS", (String)"CGLIB$selectCategoryListDFS$8");
        CGLIB$queryPermissionUserInfoList$9$Method = methodArray[9];
        CGLIB$queryPermissionUserInfoList$9$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Lcom/ideal/script/model/bean/CategoryUserBean;)Ljava/util/List;", (String)"queryPermissionUserInfoList", (String)"CGLIB$queryPermissionUserInfoList$9");
        CGLIB$assignCategoryToOrg$10$Method = methodArray[10];
        CGLIB$assignCategoryToOrg$10$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Lcom/ideal/script/model/dto/CategoryOrgDto;)V", (String)"assignCategoryToOrg", (String)"CGLIB$assignCategoryToOrg$10");
        CGLIB$listFirstCategory$11$Method = methodArray[11];
        CGLIB$listFirstCategory$11$Proxy = MethodProxy.create(clazz2, clazz, (String)"()Ljava/util/List;", (String)"listFirstCategory", (String)"CGLIB$listFirstCategory$11");
        CGLIB$getCategoryOrgRelations$12$Method = methodArray[12];
        CGLIB$getCategoryOrgRelations$12$Proxy = MethodProxy.create(clazz2, clazz, (String)"(J)Lcom/ideal/script/model/bean/CategoryOrgBean;", (String)"getCategoryOrgRelations", (String)"CGLIB$getCategoryOrgRelations$12");
        CGLIB$selectNotShareOrgManagementTree$13$Method = methodArray[13];
        CGLIB$selectNotShareOrgManagementTree$13$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Ljava/lang/Long;)Ljava/util/List;", (String)"selectNotShareOrgManagementTree", (String)"CGLIB$selectNotShareOrgManagementTree$13");
        CGLIB$getCategoryFullPath$14$Method = methodArray[14];
        CGLIB$getCategoryFullPath$14$Proxy = MethodProxy.create(clazz2, clazz, (String)"(J)Ljava/lang/String;", (String)"getCategoryFullPath", (String)"CGLIB$getCategoryFullPath$14");
        CGLIB$selectRoleManagementList$15$Method = methodArray[15];
        CGLIB$selectRoleManagementList$15$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Lcom/ideal/system/dto/RoleApiDto;Ljava/lang/Integer;Ljava/lang/Integer;)Lcom/github/pagehelper/PageInfo;", (String)"selectRoleManagementList", (String)"CGLIB$selectRoleManagementList$15");
        CGLIB$getCategoryUserRelations$16$Method = methodArray[16];
        CGLIB$getCategoryUserRelations$16$Proxy = MethodProxy.create(clazz2, clazz, (String)"(J)Lcom/ideal/script/model/bean/CategoryUserBean;", (String)"getCategoryUserRelations", (String)"CGLIB$getCategoryUserRelations$16");
        CGLIB$assignCategoryToRole$17$Method = methodArray[17];
        CGLIB$assignCategoryToRole$17$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Lcom/ideal/script/model/dto/CategoryRoleDto;)V", (String)"assignCategoryToRole", (String)"CGLIB$assignCategoryToRole$17");
        CGLIB$queryPermissionUserInfoPageByRole$18$Method = methodArray[18];
        CGLIB$queryPermissionUserInfoPageByRole$18$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Lcom/ideal/script/model/dto/CategoryRoleDto;Ljava/lang/Integer;Ljava/lang/Integer;)Lcom/github/pagehelper/PageInfo;", (String)"queryPermissionUserInfoPageByRole", (String)"CGLIB$queryPermissionUserInfoPageByRole$18");
        CGLIB$listNextCategory$19$Method = methodArray[19];
        CGLIB$listNextCategory$19$Proxy = MethodProxy.create(clazz2, clazz, (String)"(J)Ljava/util/List;", (String)"listNextCategory", (String)"CGLIB$listNextCategory$19");
        CGLIB$updateCategory$20$Method = methodArray[20];
        CGLIB$updateCategory$20$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Lcom/ideal/script/dto/CategoryDto;)I", (String)"updateCategory", (String)"CGLIB$updateCategory$20");
        CGLIB$insertCategory$21$Method = methodArray[21];
        CGLIB$insertCategory$21$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Lcom/ideal/script/dto/CategoryDto;)I", (String)"insertCategory", (String)"CGLIB$insertCategory$21");
        CGLIB$getCategory$22$Method = methodArray[22];
        CGLIB$getCategory$22$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Ljava/lang/Long;)Lcom/ideal/script/model/entity/Category;", (String)"getCategory", (String)"CGLIB$getCategory$22");
        CGLIB$buildTree$23$Method = methodArray[23];
        CGLIB$buildTree$23$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Lcom/ideal/script/model/entity/Category;Ljava/util/List;I)Lcom/ideal/script/model/entity/Category;", (String)"buildTree", (String)"CGLIB$buildTree$23");
        CGLIB$filterCategoryTree$24$Method = methodArray[24];
        CGLIB$filterCategoryTree$24$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Ljava/util/List;Ljava/util/List;)Ljava/util/List;", (String)"filterCategoryTree", (String)"CGLIB$filterCategoryTree$24");
        CGLIB$processCategoryTree$25$Method = methodArray[25];
        CGLIB$processCategoryTree$25$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Lcom/ideal/script/model/entity/Category;Ljava/util/List;Ljava/util/Set;Ljava/util/Map;)V", (String)"processCategoryTree", (String)"CGLIB$processCategoryTree$25");
        CGLIB$selectCategoryById$26$Method = methodArray[26];
        CGLIB$selectCategoryById$26$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Ljava/lang/Long;)Lcom/ideal/script/dto/CategoryDto;", (String)"selectCategoryById", (String)"CGLIB$selectCategoryById$26");
        CGLIB$checkIfCategoryReferenced$27$Method = methodArray[27];
        CGLIB$checkIfCategoryReferenced$27$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Ljava/lang/Long;)Z", (String)"checkIfCategoryReferenced", (String)"CGLIB$checkIfCategoryReferenced$27");
        CGLIB$addCategoryAndChildrenToSet$28$Method = methodArray[28];
        CGLIB$addCategoryAndChildrenToSet$28$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Ljava/lang/Long;Ljava/util/Set;)V", (String)"addCategoryAndChildrenToSet", (String)"CGLIB$addCategoryAndChildrenToSet$28");
        CGLIB$getCurrentAndSubclassIds$29$Method = methodArray[29];
        CGLIB$getCurrentAndSubclassIds$29$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Lcom/ideal/script/dto/ScriptInfoQueryDto;)Ljava/util/List;", (String)"getCurrentAndSubclassIds", (String)"CGLIB$getCurrentAndSubclassIds$29");
        CGLIB$handleCategoryPath$30$Method = methodArray[30];
        CGLIB$handleCategoryPath$30$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Ljava/lang/String;)Ljava/lang/String;", (String)"handleCategoryPath", (String)"CGLIB$handleCategoryPath$30");
        CGLIB$findByLevelAndNameAndParentId$31$Method = methodArray[31];
        CGLIB$findByLevelAndNameAndParentId$31$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/Long;)Lcom/ideal/script/dto/CategoryDto;", (String)"findByLevelAndNameAndParentId", (String)"CGLIB$findByLevelAndNameAndParentId$31");
        CGLIB$getCategoryReferencedCount$32$Method = methodArray[32];
        CGLIB$getCategoryReferencedCount$32$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Ljava/util/List;)Ljava/lang/Boolean;", (String)"getCategoryReferencedCount", (String)"CGLIB$getCategoryReferencedCount$32");
        CGLIB$deleteCategoryById$33$Method = methodArray[33];
        CGLIB$deleteCategoryById$33$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Ljava/lang/Long;)I", (String)"deleteCategoryById", (String)"CGLIB$deleteCategoryById$33");
        CGLIB$buildCategoryHierarchy$34$Method = methodArray[34];
        CGLIB$buildCategoryHierarchy$34$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Ljava/util/List;)V", (String)"buildCategoryHierarchy", (String)"CGLIB$buildCategoryHierarchy$34");
        CGLIB$checkHasRootCategoryName$35$Method = methodArray[35];
        CGLIB$checkHasRootCategoryName$35$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Lcom/ideal/script/dto/CategoryDto;)V", (String)"checkHasRootCategoryName", (String)"CGLIB$checkHasRootCategoryName$35");
        CGLIB$retrieveAllDescendantCategoryIds$36$Method = methodArray[36];
        CGLIB$retrieveAllDescendantCategoryIds$36$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Ljava/lang/Long;Ljava/util/List;)V", (String)"retrieveAllDescendantCategoryIds", (String)"CGLIB$retrieveAllDescendantCategoryIds$36");
        CGLIB$getAllCategoryIds$37$Method = methodArray[37];
        CGLIB$getAllCategoryIds$37$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Ljava/lang/Long;)Ljava/util/List;", (String)"getAllCategoryIds", (String)"CGLIB$getAllCategoryIds$37");
        CGLIB$buildCategoryFullPath$38$Method = methodArray[38];
        CGLIB$buildCategoryFullPath$38$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Ljava/lang/Long;)Ljava/lang/String;", (String)"buildCategoryFullPath", (String)"CGLIB$buildCategoryFullPath$38");
        CGLIB$setChildrenForCategoryAndParent$39$Method = methodArray[39];
        CGLIB$setChildrenForCategoryAndParent$39$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Lcom/ideal/script/model/entity/Category;)Lcom/ideal/script/model/entity/Category;", (String)"setChildrenForCategoryAndParent", (String)"CGLIB$setChildrenForCategoryAndParent$39");
        CGLIB$buildCategoryPath$40$Method = methodArray[40];
        CGLIB$buildCategoryPath$40$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Lcom/ideal/script/model/entity/Category;)Ljava/lang/String;", (String)"buildCategoryPath", (String)"CGLIB$buildCategoryPath$40");
        CGLIB$getCategoryWithChildren$41$Method = methodArray[41];
        CGLIB$getCategoryWithChildren$41$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Lcom/ideal/script/model/entity/Category;Ljava/lang/Boolean;)Ljava/util/List;", (String)"getCategoryWithChildren", (String)"CGLIB$getCategoryWithChildren$41");
        CGLIB$getCategoryWithChildren$42$Method = methodArray[42];
        CGLIB$getCategoryWithChildren$42$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Lcom/ideal/script/model/entity/Category;)Ljava/util/List;", (String)"getCategoryWithChildren", (String)"CGLIB$getCategoryWithChildren$42");
        CGLIB$selectCategoryByIds$43$Method = methodArray[43];
        CGLIB$selectCategoryByIds$43$Proxy = MethodProxy.create(clazz2, clazz, (String)"([Ljava/lang/Long;)Ljava/util/List;", (String)"selectCategoryByIds", (String)"CGLIB$selectCategoryByIds$43");
        CGLIB$setCategoryPermission$44$Method = methodArray[44];
        CGLIB$setCategoryPermission$44$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Lcom/ideal/script/model/bean/CategoryPermissionAware;Lcom/ideal/system/common/component/model/CurrentUser;)V", (String)"setCategoryPermission", (String)"CGLIB$setCategoryPermission$44");
        CGLIB$getShareTypeRoleIdsByLoginName$45$Method = methodArray[45];
        CGLIB$getShareTypeRoleIdsByLoginName$45$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Ljava/lang/String;)Ljava/util/List;", (String)"getShareTypeRoleIdsByLoginName", (String)"CGLIB$getShareTypeRoleIdsByLoginName$45");
        CGLIB$setCategoryPermissionInfo$46$Method = methodArray[46];
        CGLIB$setCategoryPermissionInfo$46$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Ljava/lang/Long;Lcom/ideal/system/common/component/model/CurrentUser;)Lcom/ideal/script/model/bean/CategoryPermissionInfo;", (String)"setCategoryPermissionInfo", (String)"CGLIB$setCategoryPermissionInfo$46");
        CGLIB$getUserByCategoryIds$47$Method = methodArray[47];
        CGLIB$getUserByCategoryIds$47$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Ljava/util/List;)Ljava/util/List;", (String)"getUserByCategoryIds", (String)"CGLIB$getUserByCategoryIds$47");
        CGLIB$getCategoryByOrgCode$48$Method = methodArray[48];
        CGLIB$getCategoryByOrgCode$48$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Ljava/lang/String;)Ljava/util/List;", (String)"getCategoryByOrgCode", (String)"CGLIB$getCategoryByOrgCode$48");
        CGLIB$getFirstCategoryByCategoryNameList$49$Method = methodArray[49];
        CGLIB$getFirstCategoryByCategoryNameList$49$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Ljava/util/List;)Ljava/util/List;", (String)"getFirstCategoryByCategoryNameList", (String)"CGLIB$getFirstCategoryByCategoryNameList$49");
        clazz2 = Class.forName("java.lang.Object");
        Method[] methodArray2 = ReflectUtils.findMethods((String[])new String[]{"equals", "(Ljava/lang/Object;)Z", "toString", "()Ljava/lang/String;", "hashCode", "()I", "clone", "()Ljava/lang/Object;"}, (Method[])clazz2.getDeclaredMethods());
        CGLIB$equals$50$Method = methodArray2[0];
        CGLIB$equals$50$Proxy = MethodProxy.create(clazz2, clazz, (String)"(Ljava/lang/Object;)Z", (String)"equals", (String)"CGLIB$equals$50");
        CGLIB$toString$51$Method = methodArray2[1];
        CGLIB$toString$51$Proxy = MethodProxy.create(clazz2, clazz, (String)"()Ljava/lang/String;", (String)"toString", (String)"CGLIB$toString$51");
        CGLIB$hashCode$52$Method = methodArray2[2];
        CGLIB$hashCode$52$Proxy = MethodProxy.create(clazz2, clazz, (String)"()I", (String)"hashCode", (String)"CGLIB$hashCode$52");
        CGLIB$clone$53$Method = methodArray2[3];
        CGLIB$clone$53$Proxy = MethodProxy.create(clazz2, clazz, (String)"()Ljava/lang/Object;", (String)"clone", (String)"CGLIB$clone$53");
    }

    final int CGLIB$deleteCategoryByIds$0(Long[] longArray) throws ScriptException {
        return super.deleteCategoryByIds(longArray);
    }

    public final int deleteCategoryByIds(Long[] longArray) throws ScriptException {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            Object object = methodInterceptor.intercept((Object)this, CGLIB$deleteCategoryByIds$0$Method, new Object[]{longArray}, CGLIB$deleteCategoryByIds$0$Proxy);
            return object == null ? 0 : ((Number)object).intValue();
        }
        return super.deleteCategoryByIds(longArray);
    }

    final List CGLIB$selectCategoryListNoPage$1(CategoryDto categoryDto) {
        return super.selectCategoryListNoPage(categoryDto);
    }

    public final List selectCategoryListNoPage(CategoryDto categoryDto) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            return (List)methodInterceptor.intercept((Object)this, CGLIB$selectCategoryListNoPage$1$Method, new Object[]{categoryDto}, CGLIB$selectCategoryListNoPage$1$Proxy);
        }
        return super.selectCategoryListNoPage(categoryDto);
    }

    final PageInfo CGLIB$selectCategoryList$2(CategoryDto categoryDto, int n, int n2) {
        return super.selectCategoryList(categoryDto, n, n2);
    }

    public final PageInfo selectCategoryList(CategoryDto categoryDto, int n, int n2) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            return (PageInfo)methodInterceptor.intercept((Object)this, CGLIB$selectCategoryList$2$Method, new Object[]{categoryDto, new Integer(n), new Integer(n2)}, CGLIB$selectCategoryList$2$Proxy);
        }
        return super.selectCategoryList(categoryDto, n, n2);
    }

    final List CGLIB$listMultiCategory$3(Integer n, Long l) {
        return super.listMultiCategory(n, l);
    }

    public final List listMultiCategory(Integer n, Long l) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            return (List)methodInterceptor.intercept((Object)this, CGLIB$listMultiCategory$3$Method, new Object[]{n, l}, CGLIB$listMultiCategory$3$Proxy);
        }
        return super.listMultiCategory(n, l);
    }

    final CategoryRoleDto CGLIB$getCategoryRoleRelations$4(long l) {
        return super.getCategoryRoleRelations(l);
    }

    public final CategoryRoleDto getCategoryRoleRelations(long l) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            Object[] objectArray = new Object[1];
            objectArray[0] = new Long(l);
            return (CategoryRoleDto)methodInterceptor.intercept((Object)this, CGLIB$getCategoryRoleRelations$4$Method, objectArray, CGLIB$getCategoryRoleRelations$4$Proxy);
        }
        return super.getCategoryRoleRelations(l);
    }

    final void CGLIB$assignCategoryToUser$5(CategoryUserDto categoryUserDto) throws ScriptException {
        super.assignCategoryToUser(categoryUserDto);
    }

    public final void assignCategoryToUser(CategoryUserDto categoryUserDto) throws ScriptException {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            Object object = methodInterceptor.intercept((Object)this, CGLIB$assignCategoryToUser$5$Method, new Object[]{categoryUserDto}, CGLIB$assignCategoryToUser$5$Proxy);
            return;
        }
        super.assignCategoryToUser(categoryUserDto);
    }

    final PageInfo CGLIB$queryPermissionUserInfoPage$6(CategoryUserBean categoryUserBean, Integer n, Integer n2) {
        return super.queryPermissionUserInfoPage(categoryUserBean, n, n2);
    }

    public final PageInfo queryPermissionUserInfoPage(CategoryUserBean categoryUserBean, Integer n, Integer n2) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            return (PageInfo)methodInterceptor.intercept((Object)this, CGLIB$queryPermissionUserInfoPage$6$Method, new Object[]{categoryUserBean, n, n2}, CGLIB$queryPermissionUserInfoPage$6$Proxy);
        }
        return super.queryPermissionUserInfoPage(categoryUserBean, n, n2);
    }

    final List CGLIB$selectOrgManagementTree$7(OrgManagementApiDto orgManagementApiDto) {
        return super.selectOrgManagementTree(orgManagementApiDto);
    }

    public final List selectOrgManagementTree(OrgManagementApiDto orgManagementApiDto) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            return (List)methodInterceptor.intercept((Object)this, CGLIB$selectOrgManagementTree$7$Method, new Object[]{orgManagementApiDto}, CGLIB$selectOrgManagementTree$7$Proxy);
        }
        return super.selectOrgManagementTree(orgManagementApiDto);
    }

    final List CGLIB$selectCategoryListDFS$8(CategoryDto categoryDto) {
        return super.selectCategoryListDFS(categoryDto);
    }

    public final List selectCategoryListDFS(CategoryDto categoryDto) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            return (List)methodInterceptor.intercept((Object)this, CGLIB$selectCategoryListDFS$8$Method, new Object[]{categoryDto}, CGLIB$selectCategoryListDFS$8$Proxy);
        }
        return super.selectCategoryListDFS(categoryDto);
    }

    final List CGLIB$queryPermissionUserInfoList$9(CategoryUserBean categoryUserBean) {
        return super.queryPermissionUserInfoList(categoryUserBean);
    }

    public final List queryPermissionUserInfoList(CategoryUserBean categoryUserBean) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            return (List)methodInterceptor.intercept((Object)this, CGLIB$queryPermissionUserInfoList$9$Method, new Object[]{categoryUserBean}, CGLIB$queryPermissionUserInfoList$9$Proxy);
        }
        return super.queryPermissionUserInfoList(categoryUserBean);
    }

    final void CGLIB$assignCategoryToOrg$10(CategoryOrgDto categoryOrgDto) {
        super.assignCategoryToOrg(categoryOrgDto);
    }

    public final void assignCategoryToOrg(CategoryOrgDto categoryOrgDto) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            Object object = methodInterceptor.intercept((Object)this, CGLIB$assignCategoryToOrg$10$Method, new Object[]{categoryOrgDto}, CGLIB$assignCategoryToOrg$10$Proxy);
            return;
        }
        super.assignCategoryToOrg(categoryOrgDto);
    }

    final List CGLIB$listFirstCategory$11() {
        return super.listFirstCategory();
    }

    public final List listFirstCategory() {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            return (List)methodInterceptor.intercept((Object)this, CGLIB$listFirstCategory$11$Method, CGLIB$emptyArgs, CGLIB$listFirstCategory$11$Proxy);
        }
        return super.listFirstCategory();
    }

    final CategoryOrgBean CGLIB$getCategoryOrgRelations$12(long l) {
        return super.getCategoryOrgRelations(l);
    }

    public final CategoryOrgBean getCategoryOrgRelations(long l) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            Object[] objectArray = new Object[1];
            objectArray[0] = new Long(l);
            return (CategoryOrgBean)methodInterceptor.intercept((Object)this, CGLIB$getCategoryOrgRelations$12$Method, objectArray, CGLIB$getCategoryOrgRelations$12$Proxy);
        }
        return super.getCategoryOrgRelations(l);
    }

    final List CGLIB$selectNotShareOrgManagementTree$13(Long l) {
        return super.selectNotShareOrgManagementTree(l);
    }

    public final List selectNotShareOrgManagementTree(Long l) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            return (List)methodInterceptor.intercept((Object)this, CGLIB$selectNotShareOrgManagementTree$13$Method, new Object[]{l}, CGLIB$selectNotShareOrgManagementTree$13$Proxy);
        }
        return super.selectNotShareOrgManagementTree(l);
    }

    final String CGLIB$getCategoryFullPath$14(long l) {
        return super.getCategoryFullPath(l);
    }

    public final String getCategoryFullPath(long l) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            Object[] objectArray = new Object[1];
            objectArray[0] = new Long(l);
            return (String)methodInterceptor.intercept((Object)this, CGLIB$getCategoryFullPath$14$Method, objectArray, CGLIB$getCategoryFullPath$14$Proxy);
        }
        return super.getCategoryFullPath(l);
    }

    final PageInfo CGLIB$selectRoleManagementList$15(RoleApiDto roleApiDto, Integer n, Integer n2) {
        return super.selectRoleManagementList(roleApiDto, n, n2);
    }

    public final PageInfo selectRoleManagementList(RoleApiDto roleApiDto, Integer n, Integer n2) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            return (PageInfo)methodInterceptor.intercept((Object)this, CGLIB$selectRoleManagementList$15$Method, new Object[]{roleApiDto, n, n2}, CGLIB$selectRoleManagementList$15$Proxy);
        }
        return super.selectRoleManagementList(roleApiDto, n, n2);
    }

    final CategoryUserBean CGLIB$getCategoryUserRelations$16(long l) {
        return super.getCategoryUserRelations(l);
    }

    public final CategoryUserBean getCategoryUserRelations(long l) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            Object[] objectArray = new Object[1];
            objectArray[0] = new Long(l);
            return (CategoryUserBean)methodInterceptor.intercept((Object)this, CGLIB$getCategoryUserRelations$16$Method, objectArray, CGLIB$getCategoryUserRelations$16$Proxy);
        }
        return super.getCategoryUserRelations(l);
    }

    final void CGLIB$assignCategoryToRole$17(CategoryRoleDto categoryRoleDto) {
        super.assignCategoryToRole(categoryRoleDto);
    }

    public final void assignCategoryToRole(CategoryRoleDto categoryRoleDto) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            Object object = methodInterceptor.intercept((Object)this, CGLIB$assignCategoryToRole$17$Method, new Object[]{categoryRoleDto}, CGLIB$assignCategoryToRole$17$Proxy);
            return;
        }
        super.assignCategoryToRole(categoryRoleDto);
    }

    final PageInfo CGLIB$queryPermissionUserInfoPageByRole$18(CategoryRoleDto categoryRoleDto, Integer n, Integer n2) {
        return super.queryPermissionUserInfoPageByRole(categoryRoleDto, n, n2);
    }

    public final PageInfo queryPermissionUserInfoPageByRole(CategoryRoleDto categoryRoleDto, Integer n, Integer n2) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            return (PageInfo)methodInterceptor.intercept((Object)this, CGLIB$queryPermissionUserInfoPageByRole$18$Method, new Object[]{categoryRoleDto, n, n2}, CGLIB$queryPermissionUserInfoPageByRole$18$Proxy);
        }
        return super.queryPermissionUserInfoPageByRole(categoryRoleDto, n, n2);
    }

    final List CGLIB$listNextCategory$19(long l) {
        return super.listNextCategory(l);
    }

    public final List listNextCategory(long l) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            Object[] objectArray = new Object[1];
            objectArray[0] = new Long(l);
            return (List)methodInterceptor.intercept((Object)this, CGLIB$listNextCategory$19$Method, objectArray, CGLIB$listNextCategory$19$Proxy);
        }
        return super.listNextCategory(l);
    }

    final int CGLIB$updateCategory$20(CategoryDto categoryDto) throws ScriptException {
        return super.updateCategory(categoryDto);
    }

    public final int updateCategory(CategoryDto categoryDto) throws ScriptException {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            Object object = methodInterceptor.intercept((Object)this, CGLIB$updateCategory$20$Method, new Object[]{categoryDto}, CGLIB$updateCategory$20$Proxy);
            return object == null ? 0 : ((Number)object).intValue();
        }
        return super.updateCategory(categoryDto);
    }

    final int CGLIB$insertCategory$21(CategoryDto categoryDto) throws ScriptException {
        return super.insertCategory(categoryDto);
    }

    public final int insertCategory(CategoryDto categoryDto) throws ScriptException {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            Object object = methodInterceptor.intercept((Object)this, CGLIB$insertCategory$21$Method, new Object[]{categoryDto}, CGLIB$insertCategory$21$Proxy);
            return object == null ? 0 : ((Number)object).intValue();
        }
        return super.insertCategory(categoryDto);
    }

    final Category CGLIB$getCategory$22(Long l) {
        return super.getCategory(l);
    }

    public final Category getCategory(Long l) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            return (Category)methodInterceptor.intercept((Object)this, CGLIB$getCategory$22$Method, new Object[]{l}, CGLIB$getCategory$22$Proxy);
        }
        return super.getCategory(l);
    }

    final Category CGLIB$buildTree$23(Category category, List list, int n) {
        return super.buildTree(category, list, n);
    }

    public final Category buildTree(Category category, List list, int n) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            return (Category)methodInterceptor.intercept((Object)this, CGLIB$buildTree$23$Method, new Object[]{category, list, new Integer(n)}, CGLIB$buildTree$23$Proxy);
        }
        return super.buildTree(category, list, n);
    }

    final List CGLIB$filterCategoryTree$24(List list, List list2) {
        return super.filterCategoryTree(list, list2);
    }

    public final List filterCategoryTree(List list, List list2) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            return (List)methodInterceptor.intercept((Object)this, CGLIB$filterCategoryTree$24$Method, new Object[]{list, list2}, CGLIB$filterCategoryTree$24$Proxy);
        }
        return super.filterCategoryTree(list, list2);
    }

    final void CGLIB$processCategoryTree$25(Category category, List list, Set set, Map map) {
        super.processCategoryTree(category, list, set, map);
    }

    public final void processCategoryTree(Category category, List list, Set set, Map map) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            Object object = methodInterceptor.intercept((Object)this, CGLIB$processCategoryTree$25$Method, new Object[]{category, list, set, map}, CGLIB$processCategoryTree$25$Proxy);
            return;
        }
        super.processCategoryTree(category, list, set, map);
    }

    final CategoryDto CGLIB$selectCategoryById$26(Long l) {
        return super.selectCategoryById(l);
    }

    public final CategoryDto selectCategoryById(Long l) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            return (CategoryDto)methodInterceptor.intercept((Object)this, CGLIB$selectCategoryById$26$Method, new Object[]{l}, CGLIB$selectCategoryById$26$Proxy);
        }
        return super.selectCategoryById(l);
    }

    final boolean CGLIB$checkIfCategoryReferenced$27(Long l) throws ScriptException {
        return super.checkIfCategoryReferenced(l);
    }

    public final boolean checkIfCategoryReferenced(Long l) throws ScriptException {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            Object object = methodInterceptor.intercept((Object)this, CGLIB$checkIfCategoryReferenced$27$Method, new Object[]{l}, CGLIB$checkIfCategoryReferenced$27$Proxy);
            return object == null ? false : (Boolean)object;
        }
        return super.checkIfCategoryReferenced(l);
    }

    final void CGLIB$addCategoryAndChildrenToSet$28(Long l, Set set) {
        super.addCategoryAndChildrenToSet(l, set);
    }

    public final void addCategoryAndChildrenToSet(Long l, Set set) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            Object object = methodInterceptor.intercept((Object)this, CGLIB$addCategoryAndChildrenToSet$28$Method, new Object[]{l, set}, CGLIB$addCategoryAndChildrenToSet$28$Proxy);
            return;
        }
        super.addCategoryAndChildrenToSet(l, set);
    }

    final List CGLIB$getCurrentAndSubclassIds$29(ScriptInfoQueryDto scriptInfoQueryDto) {
        return super.getCurrentAndSubclassIds(scriptInfoQueryDto);
    }

    public final List getCurrentAndSubclassIds(ScriptInfoQueryDto scriptInfoQueryDto) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            return (List)methodInterceptor.intercept((Object)this, CGLIB$getCurrentAndSubclassIds$29$Method, new Object[]{scriptInfoQueryDto}, CGLIB$getCurrentAndSubclassIds$29$Proxy);
        }
        return super.getCurrentAndSubclassIds(scriptInfoQueryDto);
    }

    final String CGLIB$handleCategoryPath$30(String string) {
        return super.handleCategoryPath(string);
    }

    public final String handleCategoryPath(String string) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            return (String)methodInterceptor.intercept((Object)this, CGLIB$handleCategoryPath$30$Method, new Object[]{string}, CGLIB$handleCategoryPath$30$Proxy);
        }
        return super.handleCategoryPath(string);
    }

    final CategoryDto CGLIB$findByLevelAndNameAndParentId$31(Integer n, String string, Long l) throws ScriptException {
        return super.findByLevelAndNameAndParentId(n, string, l);
    }

    public final CategoryDto findByLevelAndNameAndParentId(Integer n, String string, Long l) throws ScriptException {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            return (CategoryDto)methodInterceptor.intercept((Object)this, CGLIB$findByLevelAndNameAndParentId$31$Method, new Object[]{n, string, l}, CGLIB$findByLevelAndNameAndParentId$31$Proxy);
        }
        return super.findByLevelAndNameAndParentId(n, string, l);
    }

    final Boolean CGLIB$getCategoryReferencedCount$32(List list) throws ScriptException {
        return super.getCategoryReferencedCount(list);
    }

    public final Boolean getCategoryReferencedCount(List list) throws ScriptException {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            return (Boolean)methodInterceptor.intercept((Object)this, CGLIB$getCategoryReferencedCount$32$Method, new Object[]{list}, CGLIB$getCategoryReferencedCount$32$Proxy);
        }
        return super.getCategoryReferencedCount(list);
    }

    final int CGLIB$deleteCategoryById$33(Long l) {
        return super.deleteCategoryById(l);
    }

    public final int deleteCategoryById(Long l) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            Object object = methodInterceptor.intercept((Object)this, CGLIB$deleteCategoryById$33$Method, new Object[]{l}, CGLIB$deleteCategoryById$33$Proxy);
            return object == null ? 0 : ((Number)object).intValue();
        }
        return super.deleteCategoryById(l);
    }

    final void CGLIB$buildCategoryHierarchy$34(List list) {
        super.buildCategoryHierarchy(list);
    }

    public final void buildCategoryHierarchy(List list) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            Object object = methodInterceptor.intercept((Object)this, CGLIB$buildCategoryHierarchy$34$Method, new Object[]{list}, CGLIB$buildCategoryHierarchy$34$Proxy);
            return;
        }
        super.buildCategoryHierarchy(list);
    }

    final void CGLIB$checkHasRootCategoryName$35(CategoryDto categoryDto) throws ScriptException {
        super.checkHasRootCategoryName(categoryDto);
    }

    public final void checkHasRootCategoryName(CategoryDto categoryDto) throws ScriptException {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            Object object = methodInterceptor.intercept((Object)this, CGLIB$checkHasRootCategoryName$35$Method, new Object[]{categoryDto}, CGLIB$checkHasRootCategoryName$35$Proxy);
            return;
        }
        super.checkHasRootCategoryName(categoryDto);
    }

    final void CGLIB$retrieveAllDescendantCategoryIds$36(Long l, List list) {
        super.retrieveAllDescendantCategoryIds(l, list);
    }

    public final void retrieveAllDescendantCategoryIds(Long l, List list) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            Object object = methodInterceptor.intercept((Object)this, CGLIB$retrieveAllDescendantCategoryIds$36$Method, new Object[]{l, list}, CGLIB$retrieveAllDescendantCategoryIds$36$Proxy);
            return;
        }
        super.retrieveAllDescendantCategoryIds(l, list);
    }

    final List CGLIB$getAllCategoryIds$37(Long l) {
        return super.getAllCategoryIds(l);
    }

    public final List getAllCategoryIds(Long l) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            return (List)methodInterceptor.intercept((Object)this, CGLIB$getAllCategoryIds$37$Method, new Object[]{l}, CGLIB$getAllCategoryIds$37$Proxy);
        }
        return super.getAllCategoryIds(l);
    }

    final String CGLIB$buildCategoryFullPath$38(Long l) {
        return super.buildCategoryFullPath(l);
    }

    public final String buildCategoryFullPath(Long l) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            return (String)methodInterceptor.intercept((Object)this, CGLIB$buildCategoryFullPath$38$Method, new Object[]{l}, CGLIB$buildCategoryFullPath$38$Proxy);
        }
        return super.buildCategoryFullPath(l);
    }

    final Category CGLIB$setChildrenForCategoryAndParent$39(Category category) {
        return super.setChildrenForCategoryAndParent(category);
    }

    public final Category setChildrenForCategoryAndParent(Category category) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            return (Category)methodInterceptor.intercept((Object)this, CGLIB$setChildrenForCategoryAndParent$39$Method, new Object[]{category}, CGLIB$setChildrenForCategoryAndParent$39$Proxy);
        }
        return super.setChildrenForCategoryAndParent(category);
    }

    final String CGLIB$buildCategoryPath$40(Category category) {
        return super.buildCategoryPath(category);
    }

    public final String buildCategoryPath(Category category) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            return (String)methodInterceptor.intercept((Object)this, CGLIB$buildCategoryPath$40$Method, new Object[]{category}, CGLIB$buildCategoryPath$40$Proxy);
        }
        return super.buildCategoryPath(category);
    }

    final List CGLIB$getCategoryWithChildren$41(Category category, Boolean bl) {
        return super.getCategoryWithChildren(category, bl);
    }

    public final List getCategoryWithChildren(Category category, Boolean bl) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            return (List)methodInterceptor.intercept((Object)this, CGLIB$getCategoryWithChildren$41$Method, new Object[]{category, bl}, CGLIB$getCategoryWithChildren$41$Proxy);
        }
        return super.getCategoryWithChildren(category, bl);
    }

    final List CGLIB$getCategoryWithChildren$42(Category category) {
        return super.getCategoryWithChildren(category);
    }

    public final List getCategoryWithChildren(Category category) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            return (List)methodInterceptor.intercept((Object)this, CGLIB$getCategoryWithChildren$42$Method, new Object[]{category}, CGLIB$getCategoryWithChildren$42$Proxy);
        }
        return super.getCategoryWithChildren(category);
    }

    final List CGLIB$selectCategoryByIds$43(Long[] longArray) {
        return super.selectCategoryByIds(longArray);
    }

    public final List selectCategoryByIds(Long[] longArray) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            return (List)methodInterceptor.intercept((Object)this, CGLIB$selectCategoryByIds$43$Method, new Object[]{longArray}, CGLIB$selectCategoryByIds$43$Proxy);
        }
        return super.selectCategoryByIds(longArray);
    }

    final void CGLIB$setCategoryPermission$44(CategoryPermissionAware categoryPermissionAware, CurrentUser currentUser) {
        super.setCategoryPermission(categoryPermissionAware, currentUser);
    }

    public final void setCategoryPermission(CategoryPermissionAware categoryPermissionAware, CurrentUser currentUser) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            Object object = methodInterceptor.intercept((Object)this, CGLIB$setCategoryPermission$44$Method, new Object[]{categoryPermissionAware, currentUser}, CGLIB$setCategoryPermission$44$Proxy);
            return;
        }
        super.setCategoryPermission(categoryPermissionAware, currentUser);
    }

    final List CGLIB$getShareTypeRoleIdsByLoginName$45(String string) {
        return super.getShareTypeRoleIdsByLoginName(string);
    }

    public final List getShareTypeRoleIdsByLoginName(String string) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            return (List)methodInterceptor.intercept((Object)this, CGLIB$getShareTypeRoleIdsByLoginName$45$Method, new Object[]{string}, CGLIB$getShareTypeRoleIdsByLoginName$45$Proxy);
        }
        return super.getShareTypeRoleIdsByLoginName(string);
    }

    final CategoryPermissionInfo CGLIB$setCategoryPermissionInfo$46(Long l, CurrentUser currentUser) {
        return super.setCategoryPermissionInfo(l, currentUser);
    }

    public final CategoryPermissionInfo setCategoryPermissionInfo(Long l, CurrentUser currentUser) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            return (CategoryPermissionInfo)methodInterceptor.intercept((Object)this, CGLIB$setCategoryPermissionInfo$46$Method, new Object[]{l, currentUser}, CGLIB$setCategoryPermissionInfo$46$Proxy);
        }
        return super.setCategoryPermissionInfo(l, currentUser);
    }

    final List CGLIB$getUserByCategoryIds$47(List list) {
        return super.getUserByCategoryIds(list);
    }

    public final List getUserByCategoryIds(List list) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            return (List)methodInterceptor.intercept((Object)this, CGLIB$getUserByCategoryIds$47$Method, new Object[]{list}, CGLIB$getUserByCategoryIds$47$Proxy);
        }
        return super.getUserByCategoryIds(list);
    }

    final List CGLIB$getCategoryByOrgCode$48(String string) {
        return super.getCategoryByOrgCode(string);
    }

    public final List getCategoryByOrgCode(String string) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            return (List)methodInterceptor.intercept((Object)this, CGLIB$getCategoryByOrgCode$48$Method, new Object[]{string}, CGLIB$getCategoryByOrgCode$48$Proxy);
        }
        return super.getCategoryByOrgCode(string);
    }

    final List CGLIB$getFirstCategoryByCategoryNameList$49(List list) {
        return super.getFirstCategoryByCategoryNameList(list);
    }

    public final List getFirstCategoryByCategoryNameList(List list) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            return (List)methodInterceptor.intercept((Object)this, CGLIB$getFirstCategoryByCategoryNameList$49$Method, new Object[]{list}, CGLIB$getFirstCategoryByCategoryNameList$49$Proxy);
        }
        return super.getFirstCategoryByCategoryNameList(list);
    }

    final boolean CGLIB$equals$50(Object object) {
        return super.equals(object);
    }

    public final boolean equals(Object object) {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_5;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_5;
        }
        if (methodInterceptor != null) {
            Object object2 = methodInterceptor.intercept((Object)this, CGLIB$equals$50$Method, new Object[]{object}, CGLIB$equals$50$Proxy);
            return object2 == null ? false : (Boolean)object2;
        }
        return super.equals(object);
    }

    final String CGLIB$toString$51() {
        return super.toString();
    }

    public final String toString() {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            return (String)methodInterceptor.intercept((Object)this, CGLIB$toString$51$Method, CGLIB$emptyArgs, CGLIB$toString$51$Proxy);
        }
        return super.toString();
    }

    final int CGLIB$hashCode$52() {
        return super.hashCode();
    }

    public final int hashCode() {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_6;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_6;
        }
        if (methodInterceptor != null) {
            Object object = methodInterceptor.intercept((Object)this, CGLIB$hashCode$52$Method, CGLIB$emptyArgs, CGLIB$hashCode$52$Proxy);
            return object == null ? 0 : ((Number)object).intValue();
        }
        return super.hashCode();
    }

    final Object CGLIB$clone$53() throws CloneNotSupportedException {
        return super.clone();
    }

    protected final Object clone() throws CloneNotSupportedException {
        MethodInterceptor methodInterceptor = this.CGLIB$CALLBACK_0;
        if (methodInterceptor == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            methodInterceptor = this.CGLIB$CALLBACK_0;
        }
        if (methodInterceptor != null) {
            return methodInterceptor.intercept((Object)this, CGLIB$clone$53$Method, CGLIB$emptyArgs, CGLIB$clone$53$Proxy);
        }
        return super.clone();
    }

    public static MethodProxy CGLIB$findMethodProxy(Signature signature) {
        String string = signature.toString();
        switch (string.hashCode()) {
            case -2093860713: {
                if (!string.equals("findByLevelAndNameAndParentId(Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/Long;)Lcom/ideal/script/dto/CategoryDto;")) break;
                return CGLIB$findByLevelAndNameAndParentId$31$Proxy;
            }
            case -2024299139: {
                if (!string.equals("assignCategoryToOrg(Lcom/ideal/script/model/dto/CategoryOrgDto;)V")) break;
                return CGLIB$assignCategoryToOrg$10$Proxy;
            }
            case -2014490300: {
                if (!string.equals("getCategoryWithChildren(Lcom/ideal/script/model/entity/Category;)Ljava/util/List;")) break;
                return CGLIB$getCategoryWithChildren$42$Proxy;
            }
            case -1706324788: {
                if (!string.equals("handleCategoryPath(Ljava/lang/String;)Ljava/lang/String;")) break;
                return CGLIB$handleCategoryPath$30$Proxy;
            }
            case -1664752378: {
                if (!string.equals("getShareTypeRoleIdsByLoginName(Ljava/lang/String;)Ljava/util/List;")) break;
                return CGLIB$getShareTypeRoleIdsByLoginName$45$Proxy;
            }
            case -1642653006: {
                if (!string.equals("checkHasRootCategoryName(Lcom/ideal/script/dto/CategoryDto;)V")) break;
                return CGLIB$checkHasRootCategoryName$35$Proxy;
            }
            case -1639427651: {
                if (!string.equals("buildCategoryHierarchy(Ljava/util/List;)V")) break;
                return CGLIB$buildCategoryHierarchy$34$Proxy;
            }
            case -1382325589: {
                if (!string.equals("getCurrentAndSubclassIds(Lcom/ideal/script/dto/ScriptInfoQueryDto;)Ljava/util/List;")) break;
                return CGLIB$getCurrentAndSubclassIds$29$Proxy;
            }
            case -1150034332: {
                if (!string.equals("filterCategoryTree(Ljava/util/List;Ljava/util/List;)Ljava/util/List;")) break;
                return CGLIB$filterCategoryTree$24$Proxy;
            }
            case -1058120624: {
                if (!string.equals("selectNotShareOrgManagementTree(Ljava/lang/Long;)Ljava/util/List;")) break;
                return CGLIB$selectNotShareOrgManagementTree$13$Proxy;
            }
            case -1029777715: {
                if (!string.equals("retrieveAllDescendantCategoryIds(Ljava/lang/Long;Ljava/util/List;)V")) break;
                return CGLIB$retrieveAllDescendantCategoryIds$36$Proxy;
            }
            case -967672472: {
                if (!string.equals("processCategoryTree(Lcom/ideal/script/model/entity/Category;Ljava/util/List;Ljava/util/Set;Ljava/util/Map;)V")) break;
                return CGLIB$processCategoryTree$25$Proxy;
            }
            case -955089077: {
                if (!string.equals("insertCategory(Lcom/ideal/script/dto/CategoryDto;)I")) break;
                return CGLIB$insertCategory$21$Proxy;
            }
            case -937673808: {
                if (!string.equals("getCategoryRoleRelations(J)Lcom/ideal/script/model/dto/CategoryRoleDto;")) break;
                return CGLIB$getCategoryRoleRelations$4$Proxy;
            }
            case -917239983: {
                if (!string.equals("selectCategoryListDFS(Lcom/ideal/script/dto/CategoryDto;)Ljava/util/List;")) break;
                return CGLIB$selectCategoryListDFS$8$Proxy;
            }
            case -676207787: {
                if (!string.equals("queryPermissionUserInfoPageByRole(Lcom/ideal/script/model/dto/CategoryRoleDto;Ljava/lang/Integer;Ljava/lang/Integer;)Lcom/github/pagehelper/PageInfo;")) break;
                return CGLIB$queryPermissionUserInfoPageByRole$18$Proxy;
            }
            case -660369864: {
                if (!string.equals("selectRoleManagementList(Lcom/ideal/system/dto/RoleApiDto;Ljava/lang/Integer;Ljava/lang/Integer;)Lcom/github/pagehelper/PageInfo;")) break;
                return CGLIB$selectRoleManagementList$15$Proxy;
            }
            case -660061385: {
                if (!string.equals("getFirstCategoryByCategoryNameList(Ljava/util/List;)Ljava/util/List;")) break;
                return CGLIB$getFirstCategoryByCategoryNameList$49$Proxy;
            }
            case -658216466: {
                if (!string.equals("getCategoryByOrgCode(Ljava/lang/String;)Ljava/util/List;")) break;
                return CGLIB$getCategoryByOrgCode$48$Proxy;
            }
            case -638136837: {
                if (!string.equals("addCategoryAndChildrenToSet(Ljava/lang/Long;Ljava/util/Set;)V")) break;
                return CGLIB$addCategoryAndChildrenToSet$28$Proxy;
            }
            case -620641652: {
                if (!string.equals("deleteCategoryById(Ljava/lang/Long;)I")) break;
                return CGLIB$deleteCategoryById$33$Proxy;
            }
            case -527417373: {
                if (!string.equals("getUserByCategoryIds(Ljava/util/List;)Ljava/util/List;")) break;
                return CGLIB$getUserByCategoryIds$47$Proxy;
            }
            case -516116025: {
                if (!string.equals("queryPermissionUserInfoList(Lcom/ideal/script/model/bean/CategoryUserBean;)Ljava/util/List;")) break;
                return CGLIB$queryPermissionUserInfoList$9$Proxy;
            }
            case -509469182: {
                if (!string.equals("listFirstCategory()Ljava/util/List;")) break;
                return CGLIB$listFirstCategory$11$Proxy;
            }
            case -508378822: {
                if (!string.equals("clone()Ljava/lang/Object;")) break;
                return CGLIB$clone$53$Proxy;
            }
            case -506610747: {
                if (!string.equals("queryPermissionUserInfoPage(Lcom/ideal/script/model/bean/CategoryUserBean;Ljava/lang/Integer;Ljava/lang/Integer;)Lcom/github/pagehelper/PageInfo;")) break;
                return CGLIB$queryPermissionUserInfoPage$6$Proxy;
            }
            case -177994168: {
                if (!string.equals("selectCategoryById(Ljava/lang/Long;)Lcom/ideal/script/dto/CategoryDto;")) break;
                return CGLIB$selectCategoryById$26$Proxy;
            }
            case -137336467: {
                if (!string.equals("assignCategoryToUser(Lcom/ideal/script/model/dto/CategoryUserDto;)V")) break;
                return CGLIB$assignCategoryToUser$5$Proxy;
            }
            case -120477444: {
                if (!string.equals("checkIfCategoryReferenced(Ljava/lang/Long;)Z")) break;
                return CGLIB$checkIfCategoryReferenced$27$Proxy;
            }
            case -116256075: {
                if (!string.equals("buildCategoryPath(Lcom/ideal/script/model/entity/Category;)Ljava/lang/String;")) break;
                return CGLIB$buildCategoryPath$40$Proxy;
            }
            case 4364961: {
                if (!string.equals("getCategoryReferencedCount(Ljava/util/List;)Ljava/lang/Boolean;")) break;
                return CGLIB$getCategoryReferencedCount$32$Proxy;
            }
            case 27501371: {
                if (!string.equals("updateCategory(Lcom/ideal/script/dto/CategoryDto;)I")) break;
                return CGLIB$updateCategory$20$Proxy;
            }
            case 61277122: {
                if (!string.equals("getAllCategoryIds(Ljava/lang/Long;)Ljava/util/List;")) break;
                return CGLIB$getAllCategoryIds$37$Proxy;
            }
            case 93836105: {
                if (!string.equals("listNextCategory(J)Ljava/util/List;")) break;
                return CGLIB$listNextCategory$19$Proxy;
            }
            case 165655088: {
                if (!string.equals("selectOrgManagementTree(Lcom/ideal/system/dto/OrgManagementApiDto;)Ljava/util/List;")) break;
                return CGLIB$selectOrgManagementTree$7$Proxy;
            }
            case 281862659: {
                if (!string.equals("setCategoryPermissionInfo(Ljava/lang/Long;Lcom/ideal/system/common/component/model/CurrentUser;)Lcom/ideal/script/model/bean/CategoryPermissionInfo;")) break;
                return CGLIB$setCategoryPermissionInfo$46$Proxy;
            }
            case 356911484: {
                if (!string.equals("setChildrenForCategoryAndParent(Lcom/ideal/script/model/entity/Category;)Lcom/ideal/script/model/entity/Category;")) break;
                return CGLIB$setChildrenForCategoryAndParent$39$Proxy;
            }
            case 497489690: {
                if (!string.equals("setCategoryPermission(Lcom/ideal/script/model/bean/CategoryPermissionAware;Lcom/ideal/system/common/component/model/CurrentUser;)V")) break;
                return CGLIB$setCategoryPermission$44$Proxy;
            }
            case 645561661: {
                if (!string.equals("getCategoryWithChildren(Lcom/ideal/script/model/entity/Category;Ljava/lang/Boolean;)Ljava/util/List;")) break;
                return CGLIB$getCategoryWithChildren$41$Proxy;
            }
            case 723850860: {
                if (!string.equals("buildCategoryFullPath(Ljava/lang/Long;)Ljava/lang/String;")) break;
                return CGLIB$buildCategoryFullPath$38$Proxy;
            }
            case 729137757: {
                if (!string.equals("getCategory(Ljava/lang/Long;)Lcom/ideal/script/model/entity/Category;")) break;
                return CGLIB$getCategory$22$Proxy;
            }
            case 850943626: {
                if (!string.equals("deleteCategoryByIds([Ljava/lang/Long;)I")) break;
                return CGLIB$deleteCategoryByIds$0$Proxy;
            }
            case 889849677: {
                if (!string.equals("listMultiCategory(Ljava/lang/Integer;Ljava/lang/Long;)Ljava/util/List;")) break;
                return CGLIB$listMultiCategory$3$Proxy;
            }
            case 1030644137: {
                if (!string.equals("getCategoryFullPath(J)Ljava/lang/String;")) break;
                return CGLIB$getCategoryFullPath$14$Proxy;
            }
            case 1349599976: {
                if (!string.equals("getCategoryUserRelations(J)Lcom/ideal/script/model/bean/CategoryUserBean;")) break;
                return CGLIB$getCategoryUserRelations$16$Proxy;
            }
            case 1363701070: {
                if (!string.equals("getCategoryOrgRelations(J)Lcom/ideal/script/model/bean/CategoryOrgBean;")) break;
                return CGLIB$getCategoryOrgRelations$12$Proxy;
            }
            case 1471675425: {
                if (!string.equals("selectCategoryByIds([Ljava/lang/Long;)Ljava/util/List;")) break;
                return CGLIB$selectCategoryByIds$43$Proxy;
            }
            case 1614610363: {
                if (!string.equals("buildTree(Lcom/ideal/script/model/entity/Category;Ljava/util/List;I)Lcom/ideal/script/model/entity/Category;")) break;
                return CGLIB$buildTree$23$Proxy;
            }
            case 1826985398: {
                if (!string.equals("equals(Ljava/lang/Object;)Z")) break;
                return CGLIB$equals$50$Proxy;
            }
            case 1837800909: {
                if (!string.equals("assignCategoryToRole(Lcom/ideal/script/model/dto/CategoryRoleDto;)V")) break;
                return CGLIB$assignCategoryToRole$17$Proxy;
            }
            case 1913648695: {
                if (!string.equals("toString()Ljava/lang/String;")) break;
                return CGLIB$toString$51$Proxy;
            }
            case 1984935277: {
                if (!string.equals("hashCode()I")) break;
                return CGLIB$hashCode$52$Proxy;
            }
            case 2018075965: {
                if (!string.equals("selectCategoryList(Lcom/ideal/script/dto/CategoryDto;II)Lcom/github/pagehelper/PageInfo;")) break;
                return CGLIB$selectCategoryList$2$Proxy;
            }
            case 2060406400: {
                if (!string.equals("selectCategoryListNoPage(Lcom/ideal/script/dto/CategoryDto;)Ljava/util/List;")) break;
                return CGLIB$selectCategoryListNoPage$1$Proxy;
            }
        }
        return null;
    }

    public final int indexOf(Advice advice) {
        Dispatcher dispatcher = this.CGLIB$CALLBACK_4;
        if (dispatcher == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            dispatcher = this.CGLIB$CALLBACK_4;
        }
        return ((Advised)dispatcher.loadObject()).indexOf(advice);
    }

    public final int indexOf(Advisor advisor) {
        Dispatcher dispatcher = this.CGLIB$CALLBACK_4;
        if (dispatcher == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            dispatcher = this.CGLIB$CALLBACK_4;
        }
        return ((Advised)dispatcher.loadObject()).indexOf(advisor);
    }

    public final boolean isFrozen() {
        Dispatcher dispatcher = this.CGLIB$CALLBACK_4;
        if (dispatcher == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            dispatcher = this.CGLIB$CALLBACK_4;
        }
        return ((Advised)dispatcher.loadObject()).isFrozen();
    }

    public final Class[] getProxiedInterfaces() {
        Dispatcher dispatcher = this.CGLIB$CALLBACK_4;
        if (dispatcher == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            dispatcher = this.CGLIB$CALLBACK_4;
        }
        return ((Advised)dispatcher.loadObject()).getProxiedInterfaces();
    }

    public final String toProxyConfigString() {
        Dispatcher dispatcher = this.CGLIB$CALLBACK_4;
        if (dispatcher == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            dispatcher = this.CGLIB$CALLBACK_4;
        }
        return ((Advised)dispatcher.loadObject()).toProxyConfigString();
    }

    public final boolean isInterfaceProxied(Class clazz) {
        Dispatcher dispatcher = this.CGLIB$CALLBACK_4;
        if (dispatcher == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            dispatcher = this.CGLIB$CALLBACK_4;
        }
        return ((Advised)dispatcher.loadObject()).isInterfaceProxied(clazz);
    }

    public final boolean isProxyTargetClass() {
        Dispatcher dispatcher = this.CGLIB$CALLBACK_4;
        if (dispatcher == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            dispatcher = this.CGLIB$CALLBACK_4;
        }
        return ((Advised)dispatcher.loadObject()).isProxyTargetClass();
    }

    public final boolean isExposeProxy() {
        Dispatcher dispatcher = this.CGLIB$CALLBACK_4;
        if (dispatcher == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            dispatcher = this.CGLIB$CALLBACK_4;
        }
        return ((Advised)dispatcher.loadObject()).isExposeProxy();
    }

    public final boolean isPreFiltered() {
        Dispatcher dispatcher = this.CGLIB$CALLBACK_4;
        if (dispatcher == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            dispatcher = this.CGLIB$CALLBACK_4;
        }
        return ((Advised)dispatcher.loadObject()).isPreFiltered();
    }

    public final void setPreFiltered(boolean bl) {
        Dispatcher dispatcher = this.CGLIB$CALLBACK_4;
        if (dispatcher == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            dispatcher = this.CGLIB$CALLBACK_4;
        }
        ((Advised)dispatcher.loadObject()).setPreFiltered(bl);
    }

    public final boolean replaceAdvisor(Advisor advisor, Advisor advisor2) throws AopConfigException {
        Dispatcher dispatcher = this.CGLIB$CALLBACK_4;
        if (dispatcher == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            dispatcher = this.CGLIB$CALLBACK_4;
        }
        return ((Advised)dispatcher.loadObject()).replaceAdvisor(advisor, advisor2);
    }

    public final void addAdvisor(int n, Advisor advisor) throws AopConfigException {
        Dispatcher dispatcher = this.CGLIB$CALLBACK_4;
        if (dispatcher == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            dispatcher = this.CGLIB$CALLBACK_4;
        }
        ((Advised)dispatcher.loadObject()).addAdvisor(n, advisor);
    }

    public final void addAdvisor(Advisor advisor) throws AopConfigException {
        Dispatcher dispatcher = this.CGLIB$CALLBACK_4;
        if (dispatcher == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            dispatcher = this.CGLIB$CALLBACK_4;
        }
        ((Advised)dispatcher.loadObject()).addAdvisor(advisor);
    }

    public final boolean removeAdvice(Advice advice) {
        Dispatcher dispatcher = this.CGLIB$CALLBACK_4;
        if (dispatcher == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            dispatcher = this.CGLIB$CALLBACK_4;
        }
        return ((Advised)dispatcher.loadObject()).removeAdvice(advice);
    }

    public final void setExposeProxy(boolean bl) {
        Dispatcher dispatcher = this.CGLIB$CALLBACK_4;
        if (dispatcher == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            dispatcher = this.CGLIB$CALLBACK_4;
        }
        ((Advised)dispatcher.loadObject()).setExposeProxy(bl);
    }

    public final void removeAdvisor(int n) throws AopConfigException {
        Dispatcher dispatcher = this.CGLIB$CALLBACK_4;
        if (dispatcher == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            dispatcher = this.CGLIB$CALLBACK_4;
        }
        ((Advised)dispatcher.loadObject()).removeAdvisor(n);
    }

    public final boolean removeAdvisor(Advisor advisor) {
        Dispatcher dispatcher = this.CGLIB$CALLBACK_4;
        if (dispatcher == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            dispatcher = this.CGLIB$CALLBACK_4;
        }
        return ((Advised)dispatcher.loadObject()).removeAdvisor(advisor);
    }

    public final void addAdvice(int n, Advice advice) throws AopConfigException {
        Dispatcher dispatcher = this.CGLIB$CALLBACK_4;
        if (dispatcher == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            dispatcher = this.CGLIB$CALLBACK_4;
        }
        ((Advised)dispatcher.loadObject()).addAdvice(n, advice);
    }

    public final void addAdvice(Advice advice) throws AopConfigException {
        Dispatcher dispatcher = this.CGLIB$CALLBACK_4;
        if (dispatcher == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            dispatcher = this.CGLIB$CALLBACK_4;
        }
        ((Advised)dispatcher.loadObject()).addAdvice(advice);
    }

    public final TargetSource getTargetSource() {
        Dispatcher dispatcher = this.CGLIB$CALLBACK_4;
        if (dispatcher == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            dispatcher = this.CGLIB$CALLBACK_4;
        }
        return ((Advised)dispatcher.loadObject()).getTargetSource();
    }

    public final Advisor[] getAdvisors() {
        Dispatcher dispatcher = this.CGLIB$CALLBACK_4;
        if (dispatcher == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            dispatcher = this.CGLIB$CALLBACK_4;
        }
        return ((Advised)dispatcher.loadObject()).getAdvisors();
    }

    public final int getAdvisorCount() {
        Dispatcher dispatcher = this.CGLIB$CALLBACK_4;
        if (dispatcher == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            dispatcher = this.CGLIB$CALLBACK_4;
        }
        return ((Advised)dispatcher.loadObject()).getAdvisorCount();
    }

    public final void setTargetSource(TargetSource targetSource) {
        Dispatcher dispatcher = this.CGLIB$CALLBACK_4;
        if (dispatcher == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            dispatcher = this.CGLIB$CALLBACK_4;
        }
        ((Advised)dispatcher.loadObject()).setTargetSource(targetSource);
    }

    public final Class getTargetClass() {
        Dispatcher dispatcher = this.CGLIB$CALLBACK_4;
        if (dispatcher == null) {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
            dispatcher = this.CGLIB$CALLBACK_4;
        }
        return ((TargetClassAware)dispatcher.loadObject()).getTargetClass();
    }

    public CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39(IScriptVersionShareService iScriptVersionShareService, CategoryMapper categoryMapper, InfoMapper infoMapper, BatchHandler batchHandler, IUserInfo iUserInfo, IOrgManagement iOrgManagement, IRole iRole, IMyScriptService iMyScriptService) {
        CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39 categoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39 = this;
        super(iScriptVersionShareService, categoryMapper, infoMapper, batchHandler, iUserInfo, iOrgManagement, iRole, iMyScriptService);
        CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)categoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39);
    }

    public static void CGLIB$SET_THREAD_CALLBACKS(Callback[] callbackArray) {
        CGLIB$THREAD_CALLBACKS.set(callbackArray);
    }

    public static void CGLIB$SET_STATIC_CALLBACKS(Callback[] callbackArray) {
        CGLIB$STATIC_CALLBACKS = callbackArray;
    }

    private static final void CGLIB$BIND_CALLBACKS(Object object) {
        block2: {
            Object object2;
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39 categoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39;
            block3: {
                categoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39 = (CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39)((Object)object);
                if (categoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BOUND) break block2;
                categoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BOUND = true;
                object2 = CGLIB$THREAD_CALLBACKS.get();
                if (object2 != null) break block3;
                object2 = CGLIB$STATIC_CALLBACKS;
                if (CGLIB$STATIC_CALLBACKS == null) break block2;
            }
            Callback[] callbackArray = (Callback[])object2;
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39 categoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c392 = categoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39;
            categoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c392.CGLIB$CALLBACK_6 = (MethodInterceptor)callbackArray[6];
            categoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c392.CGLIB$CALLBACK_5 = (MethodInterceptor)callbackArray[5];
            categoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c392.CGLIB$CALLBACK_4 = (Dispatcher)callbackArray[4];
            categoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c392.CGLIB$CALLBACK_3 = (Dispatcher)callbackArray[3];
            categoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c392.CGLIB$CALLBACK_2 = (NoOp)callbackArray[2];
            categoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c392.CGLIB$CALLBACK_1 = (MethodInterceptor)callbackArray[1];
            categoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c392.CGLIB$CALLBACK_0 = (MethodInterceptor)callbackArray[0];
        }
    }

    public Object newInstance(Callback[] callbackArray) {
        CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$SET_THREAD_CALLBACKS(callbackArray);
        CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39 categoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39 = new CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39();
        CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$SET_THREAD_CALLBACKS(null);
        return categoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39;
    }

    public Object newInstance(Callback callback) {
        throw new IllegalStateException("More than one callback object required");
    }

    public Object newInstance(Class[] classArray, Object[] objectArray, Callback[] callbackArray) {
        CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39 categoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39;
        block3: {
            CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$SET_THREAD_CALLBACKS(callbackArray);
            Class[] classArray2 = classArray;
            switch (classArray.length) {
                case 8: {
                    classArray2 = classArray2;
                    if (!classArray2[0].getName().equals("com.ideal.script.service.IScriptVersionShareService")) break;
                    classArray2 = classArray2;
                    if (!classArray2[1].getName().equals("com.ideal.script.mapper.CategoryMapper")) break;
                    classArray2 = classArray2;
                    if (!classArray2[2].getName().equals("com.ideal.script.mapper.InfoMapper")) break;
                    classArray2 = classArray2;
                    if (!classArray2[3].getName().equals("com.ideal.common.util.batch.BatchHandler")) break;
                    classArray2 = classArray2;
                    if (!classArray2[4].getName().equals("com.ideal.system.api.IUserInfo")) break;
                    classArray2 = classArray2;
                    if (!classArray2[5].getName().equals("com.ideal.system.api.IOrgManagement")) break;
                    classArray2 = classArray2;
                    if (!classArray2[6].getName().equals("com.ideal.system.api.IRole")) break;
                    classArray2 = classArray2;
                    if (!classArray2[7].getName().equals("com.ideal.script.service.IMyScriptService")) break;
                    categoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39 = new CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39((IScriptVersionShareService)objectArray[0], (CategoryMapper)objectArray[1], (InfoMapper)objectArray[2], (BatchHandler)objectArray[3], (IUserInfo)objectArray[4], (IOrgManagement)objectArray[5], (IRole)objectArray[6], (IMyScriptService)objectArray[7]);
                    break block3;
                }
            }
            throw new IllegalArgumentException("Constructor not found");
        }
        CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$SET_THREAD_CALLBACKS(null);
        return categoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39;
    }

    public Callback getCallback(int n) {
        MethodInterceptor methodInterceptor;
        CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
        CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39 categoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39 = this;
        switch (n) {
            case 0: {
                methodInterceptor = categoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$CALLBACK_0;
                break;
            }
            case 1: {
                methodInterceptor = categoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$CALLBACK_1;
                break;
            }
            case 2: {
                methodInterceptor = categoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$CALLBACK_2;
                break;
            }
            case 3: {
                methodInterceptor = categoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$CALLBACK_3;
                break;
            }
            case 4: {
                methodInterceptor = categoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$CALLBACK_4;
                break;
            }
            case 5: {
                methodInterceptor = categoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$CALLBACK_5;
                break;
            }
            case 6: {
                methodInterceptor = categoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$CALLBACK_6;
                break;
            }
            default: {
                methodInterceptor = null;
            }
        }
        return methodInterceptor;
    }

    public void setCallback(int n, Callback callback) {
        switch (n) {
            case 0: {
                this.CGLIB$CALLBACK_0 = (MethodInterceptor)callback;
                break;
            }
            case 1: {
                this.CGLIB$CALLBACK_1 = (MethodInterceptor)callback;
                break;
            }
            case 2: {
                this.CGLIB$CALLBACK_2 = (NoOp)callback;
                break;
            }
            case 3: {
                this.CGLIB$CALLBACK_3 = (Dispatcher)callback;
                break;
            }
            case 4: {
                this.CGLIB$CALLBACK_4 = (Dispatcher)callback;
                break;
            }
            case 5: {
                this.CGLIB$CALLBACK_5 = (MethodInterceptor)callback;
                break;
            }
            case 6: {
                this.CGLIB$CALLBACK_6 = (MethodInterceptor)callback;
                break;
            }
        }
    }

    public Callback[] getCallbacks() {
        CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$BIND_CALLBACKS((Object)this);
        CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39 categoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39 = this;
        return new Callback[]{this.CGLIB$CALLBACK_0, this.CGLIB$CALLBACK_1, this.CGLIB$CALLBACK_2, this.CGLIB$CALLBACK_3, this.CGLIB$CALLBACK_4, this.CGLIB$CALLBACK_5, this.CGLIB$CALLBACK_6};
    }

    public void setCallbacks(Callback[] callbackArray) {
        this.CGLIB$CALLBACK_0 = (MethodInterceptor)callbackArray[0];
        this.CGLIB$CALLBACK_1 = (MethodInterceptor)callbackArray[1];
        this.CGLIB$CALLBACK_2 = (NoOp)callbackArray[2];
        this.CGLIB$CALLBACK_3 = (Dispatcher)callbackArray[3];
        this.CGLIB$CALLBACK_4 = (Dispatcher)callbackArray[4];
        this.CGLIB$CALLBACK_5 = (MethodInterceptor)callbackArray[5];
        Callback[] callbackArray2 = callbackArray;
        CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39 categoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39 = this;
        this.CGLIB$CALLBACK_6 = (MethodInterceptor)callbackArray[6];
    }

    static {
        CategoryServiceImpl$$EnhancerBySpringCGLIB$$b7661c39.CGLIB$STATICHOOK100();
    }
}

Affect(row-cnt:2) cost in 630 ms.
