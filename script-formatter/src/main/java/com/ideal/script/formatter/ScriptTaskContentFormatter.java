package com.ideal.script.formatter;

import com.ideal.agent.gateway.formatter.TaskContentFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.*;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service("scriptTaskContentFormatter")
public class ScriptTaskContentFormatter implements TaskContentFormatter {

    private static final Logger log = LoggerFactory.getLogger(ScriptTaskContentFormatter.class);

    @Override
    @SuppressWarnings("unchecked")
    public Vector<Object> get(String bizId, String content) {
        byte[] data = Base64.getDecoder().decode(content);

        List<Object> params = null;
        // 将 byte[] 数组反序列化为对象
        try (ByteArrayInputStream bis = new ByteArrayInputStream(data);
             ObjectInputStream ois = new ObjectInputStream(bis)) {
            params = (List<Object>) ois.readObject();
        } catch (IOException | ClassNotFoundException e) {
            log.error("ScriptTaskContentFormatter.get is error : " , e);
        }

        return (Vector<Object>) convertToVectorAndHashtable(params);
    }

    @SuppressWarnings({"java:S1149"})
    private Object convertToVectorAndHashtable(Object object) {
        if (object instanceof List) {
            List<?> list = (List<?>) object;

            Vector<Object> vector = new Vector<>();
            for (Object element : list) {
                // 递归转换
                vector.add(convertToVectorAndHashtable(element));
            }
            return vector;
        } else if (object instanceof Map) {
            Map<?, ?> map = (Map<?, ?>) object;
            Hashtable<Object, Object> hashtable = new Hashtable<>();
            for (Map.Entry<?, ?> entry : map.entrySet()) {
                // 递归转换
                hashtable.put(entry.getKey(), convertToVectorAndHashtable(entry.getValue()));
            }
            return hashtable;
        }
        // 其他类型则直接返回
        return object;
    }
}
